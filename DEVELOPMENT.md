# 开发指南

本文档为AI法律助手系统的开发者提供详细的开发环境搭建、代码规范和贡献指南。

## 🛠️ 开发环境搭建

### 系统要求

- **Python**: 3.11+
- **Node.js**: 18+
- **PostgreSQL**: 15+
- **Redis**: 7+
- **Git**: 2.30+

### 1. 克隆项目

```bash
git clone <repository-url>
cd ai-legal-assistant
```

### 2. 后端开发环境

#### 安装Python依赖

```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 开发依赖
```

#### 配置环境变量

```bash
cp .env.example .env
```

编辑 `.env` 文件：
```bash
# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/legal_assistant_dev
REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-development-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 开发模式
DEBUG=True
ENVIRONMENT=development
```

#### 数据库设置

```bash
# 启动PostgreSQL (如果使用Docker)
docker run --name postgres-dev -e POSTGRES_PASSWORD=password -p 5432:5432 -d postgres:15

# 创建数据库
createdb legal_assistant_dev

# 运行迁移
alembic upgrade head

# 初始化数据
python scripts/init_permissions.py
python scripts/create_test_data.py  # 创建测试数据
```

#### 启动后端服务

```bash
# 开发模式启动
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 或使用脚本
python scripts/run_dev.py
```

### 3. 前端开发环境

#### 安装Node.js依赖

```bash
cd frontend/vite-app

# 安装依赖
npm install
```

#### 配置环境变量

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件：
```bash
# API配置
VITE_API_BASE_URL=http://localhost:8000/api/v1

# 应用配置
VITE_APP_TITLE=AI法律助手 (开发版)
VITE_APP_VERSION=1.0.0-dev
VITE_DEBUG=true
```

#### 启动前端服务

```bash
# 开发模式启动
npm run dev

# 指定端口
npm run dev -- --port 3000
```

### 4. 开发工具配置

#### VS Code配置

创建 `.vscode/settings.json`：
```json
{
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": true,
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length=88"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "eslint.workingDirectories": ["frontend/vite-app"]
}
```

#### Git Hooks配置

```bash
# 安装pre-commit
pip install pre-commit

# 安装hooks
pre-commit install

# 手动运行检查
pre-commit run --all-files
```

## 📋 代码规范

### Python代码规范

#### 1. 代码格式化

使用 **Black** 进行代码格式化：
```bash
black backend/ --line-length=88
```

#### 2. 代码检查

使用 **flake8** 进行代码检查：
```bash
flake8 backend/ --max-line-length=88 --ignore=E203,W503
```

#### 3. 类型检查

使用 **mypy** 进行类型检查：
```bash
mypy backend/app/
```

#### 4. 导入排序

使用 **isort** 排序导入：
```bash
isort backend/ --profile black
```

#### 5. 代码风格示例

```python
"""
模块文档字符串
描述模块的功能和用途
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.models.user import User
from app.schemas.user import UserResponse

logger = logging.getLogger(__name__)
router = APIRouter()


class UserService:
    """用户服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """
        根据ID获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户对象或None
        """
        # 实现逻辑
        pass


@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    db: AsyncSession = Depends(get_db)
) -> UserResponse:
    """获取用户信息"""
    
    service = UserService(db)
    user = await service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return UserResponse.from_orm(user)
```

### TypeScript/React代码规范

#### 1. ESLint配置

`.eslintrc.js`:
```javascript
module.exports = {
  extends: [
    '@antfu',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
  ],
  rules: {
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    'prefer-const': 'error',
  },
}
```

#### 2. Prettier配置

`.prettierrc`:
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100
}
```

#### 3. 代码风格示例

```typescript
// 组件文件示例
import React, { useState, useEffect } from 'react'
import { Button, Card, message } from 'antd'
import type { User } from '@/types/user'
import { useAuthStore } from '@/store/auth'
import { userApi } from '@/api/user'

interface UserCardProps {
  userId: string
  onUpdate?: (user: User) => void
}

const UserCard: React.FC<UserCardProps> = ({ userId, onUpdate }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const { currentUser } = useAuthStore()

  useEffect(() => {
    fetchUser()
  }, [userId])

  const fetchUser = async () => {
    try {
      setLoading(true)
      const userData = await userApi.getUser(userId)
      setUser(userData)
    } catch (error) {
      message.error('获取用户信息失败')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdate = async () => {
    if (!user) return

    try {
      const updatedUser = await userApi.updateUser(user.id, user)
      setUser(updatedUser)
      onUpdate?.(updatedUser)
      message.success('更新成功')
    } catch (error) {
      message.error('更新失败')
    }
  }

  if (!user) {
    return <div>用户不存在</div>
  }

  return (
    <Card
      title={user.fullName}
      loading={loading}
      extra={
        <Button type="primary" onClick={handleUpdate}>
          更新
        </Button>
      }
    >
      <p>邮箱: {user.email}</p>
      <p>类型: {user.userType}</p>
    </Card>
  )
}

export default UserCard
```

## 🧪 测试

### 后端测试

#### 1. 单元测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_user_service.py

# 运行测试并生成覆盖率报告
pytest --cov=app tests/

# 生成HTML覆盖率报告
pytest --cov=app --cov-report=html tests/
```

#### 2. 测试示例

```python
# tests/test_user_service.py
import pytest
from unittest.mock import AsyncMock
from app.services.user import UserService
from app.models.user import User


@pytest.fixture
async def user_service():
    """用户服务fixture"""
    db_mock = AsyncMock()
    return UserService(db_mock)


@pytest.mark.asyncio
async def test_get_user_by_id(user_service):
    """测试根据ID获取用户"""
    # 准备测试数据
    user_id = "test-user-id"
    expected_user = User(id=user_id, username="testuser")
    
    # 模拟数据库查询
    user_service.db.execute.return_value.scalar_one_or_none.return_value = expected_user
    
    # 执行测试
    result = await user_service.get_user_by_id(user_id)
    
    # 验证结果
    assert result == expected_user
    assert result.username == "testuser"
```

### 前端测试

#### 1. 单元测试

```bash
# 运行测试
npm run test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

#### 2. 测试示例

```typescript
// src/components/__tests__/UserCard.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import UserCard from '../UserCard'
import { userApi } from '@/api/user'

// Mock API
vi.mock('@/api/user', () => ({
  userApi: {
    getUser: vi.fn(),
    updateUser: vi.fn(),
  },
}))

describe('UserCard', () => {
  const mockUser = {
    id: '1',
    username: 'testuser',
    fullName: '测试用户',
    email: '<EMAIL>',
    userType: 'individual',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render user information', async () => {
    // Mock API响应
    vi.mocked(userApi.getUser).mockResolvedValue(mockUser)

    // 渲染组件
    render(<UserCard userId="1" />)

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试用户')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })
  })

  it('should handle update user', async () => {
    vi.mocked(userApi.getUser).mockResolvedValue(mockUser)
    vi.mocked(userApi.updateUser).mockResolvedValue(mockUser)

    render(<UserCard userId="1" />)

    await waitFor(() => {
      expect(screen.getByText('测试用户')).toBeInTheDocument()
    })

    // 点击更新按钮
    const updateButton = screen.getByText('更新')
    fireEvent.click(updateButton)

    // 验证API调用
    await waitFor(() => {
      expect(userApi.updateUser).toHaveBeenCalledWith('1', mockUser)
    })
  })
})
```

## 🔄 Git工作流

### 分支策略

- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成功能
- `feature/*`: 功能分支
- `bugfix/*`: 修复分支
- `hotfix/*`: 热修复分支

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 功能开发
git commit -m "feat: 添加用户注册功能"

# 修复bug
git commit -m "fix: 修复登录验证问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 代码重构
git commit -m "refactor: 重构用户服务代码"

# 性能优化
git commit -m "perf: 优化数据库查询性能"

# 测试相关
git commit -m "test: 添加用户服务单元测试"
```

### 开发流程

1. **创建功能分支**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/user-management
   ```

2. **开发和提交**
   ```bash
   # 开发代码
   git add .
   git commit -m "feat: 实现用户管理功能"
   ```

3. **推送分支**
   ```bash
   git push origin feature/user-management
   ```

4. **创建Pull Request**
   - 在GitHub/GitLab上创建PR
   - 填写详细的描述
   - 请求代码审查

5. **合并到develop**
   ```bash
   git checkout develop
   git pull origin develop
   git merge feature/user-management
   git push origin develop
   ```

## 📦 构建和部署

### 开发环境构建

```bash
# 后端
cd backend
python -m build

# 前端
cd frontend/vite-app
npm run build
```

### Docker构建

```bash
# 构建所有服务
docker-compose build

# 构建特定服务
docker-compose build backend
docker-compose build frontend
```

### 生产环境部署

参考 [DEPLOYMENT.md](DEPLOYMENT.md) 文档。

## 🐛 调试技巧

### 后端调试

#### 1. 使用调试器

```python
# 在代码中添加断点
import pdb; pdb.set_trace()

# 或使用ipdb (更友好的界面)
import ipdb; ipdb.set_trace()
```

#### 2. 日志调试

```python
import logging

logger = logging.getLogger(__name__)

# 在关键位置添加日志
logger.debug(f"用户ID: {user_id}")
logger.info(f"处理请求: {request.url}")
logger.warning(f"警告信息: {warning_msg}")
logger.error(f"错误信息: {error_msg}")
```

### 前端调试

#### 1. 浏览器开发者工具

- 使用 `console.log()` 输出调试信息
- 使用 `debugger` 语句设置断点
- 使用 React Developer Tools 检查组件状态

#### 2. VS Code调试

配置 `.vscode/launch.json`：
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug React App",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/frontend/vite-app/node_modules/.bin/vite",
      "args": ["dev"],
      "cwd": "${workspaceFolder}/frontend/vite-app",
      "console": "integratedTerminal"
    }
  ]
}
```

## 🤝 贡献指南

### 1. 报告问题

- 使用GitHub Issues报告bug
- 提供详细的复现步骤
- 包含错误日志和截图

### 2. 提交功能请求

- 描述功能的用途和价值
- 提供详细的需求说明
- 考虑实现的复杂度

### 3. 代码贡献

- Fork项目到个人仓库
- 创建功能分支进行开发
- 确保代码通过所有测试
- 提交Pull Request

### 4. 代码审查

- 检查代码质量和规范
- 验证功能是否正确实现
- 确保测试覆盖率足够
- 提供建设性的反馈

## 📚 学习资源

### 技术文档

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [React官方文档](https://reactjs.org/docs/)
- [TypeScript官方文档](https://www.typescriptlang.org/docs/)
- [Ant Design文档](https://ant.design/docs/react/introduce)

### 最佳实践

- [Python代码风格指南](https://pep8.org/)
- [React最佳实践](https://reactjs.org/docs/thinking-in-react.html)
- [API设计指南](https://restfulapi.net/)
- [数据库设计原则](https://www.postgresql.org/docs/current/ddl-basics.html)

## 💬 社区支持

- **GitHub Discussions**: 技术讨论和问答
- **Slack频道**: 实时交流和协作
- **邮件列表**: 重要通知和更新
- **技术博客**: 分享开发经验和技巧

---

欢迎加入AI法律助手的开发团队！🚀
