# API文档

AI法律助手系统API接口文档，提供完整的RESTful API服务。

## 📋 基本信息

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔐 认证

### 获取访问令牌

所有需要认证的API都需要在请求头中包含JWT令牌：

```http
Authorization: Bearer <your_jwt_token>
```

### 令牌刷新

访问令牌有效期为30分钟，可使用刷新令牌获取新的访问令牌。

## 📚 API端点

### 1. 认证相关 `/auth`

#### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "测试用户",
  "user_type": "individual"
}
```

**响应示例**:
```json
{
  "id": "uuid",
  "username": "testuser",
  "email": "<EMAIL>",
  "full_name": "测试用户",
  "user_type": "individual",
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z"
}
```

#### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/x-www-form-urlencoded

username=testuser&password=password123
```

**响应示例**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

#### 刷新令牌
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

### 2. 用户管理 `/users`

#### 获取当前用户信息
```http
GET /api/v1/users/me
Authorization: Bearer <access_token>
```

#### 更新用户信息
```http
PUT /api/v1/users/me
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "full_name": "新的姓名",
  "email": "<EMAIL>"
}
```

### 3. 智能问答 `/qa`

#### 提交问题
```http
POST /api/v1/qa/ask
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "question": "合同违约的法律责任有哪些？",
  "category": "合同法",
  "session_id": "optional-session-id"
}
```

**响应示例**:
```json
{
  "id": "qa-record-uuid",
  "question": "合同违约的法律责任有哪些？",
  "answer": "根据《合同法》相关规定，合同违约责任主要包括：1. 继续履行；2. 采取补救措施；3. 赔偿损失。",
  "category": "合同法",
  "confidence_score": 0.85,
  "sources": [
    {
      "title": "合同法相关法律条文",
      "type": "法律条文",
      "confidence": 0.85
    }
  ],
  "suggestions": [
    "建议提供更详细的情况描述",
    "可以咨询专业律师获得更准确的建议"
  ],
  "created_at": "2024-01-01T12:00:00Z"
}
```

#### 获取问答历史
```http
GET /api/v1/qa/history?page=1&page_size=20
Authorization: Bearer <access_token>
```

### 4. 案例搜索 `/case-search`

#### 搜索案例
```http
GET /api/v1/case-search/search?q=合同纠纷&case_type=civil&page=1&page_size=20
Authorization: Bearer <access_token>
```

**查询参数**:
- `q`: 搜索关键词
- `case_type`: 案件类型 (civil, criminal, administrative等)
- `court_name`: 法院名称
- `precedent_value`: 判例价值 (高, 中, 低)
- `date_from`: 开始日期 (YYYY-MM-DD)
- `date_to`: 结束日期 (YYYY-MM-DD)
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (默认20)
- `sort_by`: 排序方式 (relevance, date_desc, date_asc, citation_desc)

**响应示例**:
```json
{
  "cases": [
    {
      "id": "case-uuid",
      "case_number": "（2023）京01民终1234号",
      "title": "张某与李某合同纠纷案",
      "court_name": "北京市第一中级人民法院",
      "case_type": "civil",
      "judgment_date": "2023-06-15",
      "case_summary": "本案涉及买卖合同违约责任的认定和赔偿标准问题。",
      "keywords": ["合同纠纷", "违约责任", "赔偿"],
      "precedent_value": "中",
      "citation_count": 15,
      "score": 0.95,
      "created_at": "2023-06-20T10:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "page_size": 20,
  "total_pages": 5,
  "has_next": true,
  "has_prev": false
}
```

#### 获取案例详情
```http
GET /api/v1/case-search/{case_id}
Authorization: Bearer <access_token>
```

#### 获取相似案例
```http
GET /api/v1/case-search/{case_id}/similar?limit=5
Authorization: Bearer <access_token>
```

### 5. 合同模板 `/contract-templates`

#### 获取模板列表
```http
GET /api/v1/contract-templates/templates?category=labor
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "templates": [
    {
      "id": "labor_contract",
      "name": "劳动合同模板",
      "category": "labor",
      "description": "标准劳动合同模板，适用于一般企业员工聘用",
      "difficulty_level": "easy",
      "usage_count": 0,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1
}
```

#### 获取模板详情
```http
GET /api/v1/contract-templates/templates/{template_id}
Authorization: Bearer <access_token>
```

#### 生成合同
```http
POST /api/v1/contract-templates/generate
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "template_id": "labor_contract",
  "variables": {
    "employer_name": "北京科技有限公司",
    "employee_name": "张三",
    "position": "软件工程师",
    "salary": "15000",
    "start_date": "2024-02-01"
  }
}
```

#### 获取我的合同
```http
GET /api/v1/contract-templates/my-contracts?page=1&page_size=20
Authorization: Bearer <access_token>
```

### 6. 系统监控 `/monitoring` (管理员)

#### 健康检查
```http
GET /api/v1/monitoring/health
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "environment": "production"
}
```

#### 系统指标
```http
GET /api/v1/monitoring/metrics
Authorization: Bearer <admin_token>
```

#### 性能指标
```http
GET /api/v1/monitoring/performance?hours=24
Authorization: Bearer <admin_token>
```

### 7. 系统配置 `/system` (管理员)

#### 获取配置
```http
GET /api/v1/system/configs?prefix=api
Authorization: Bearer <admin_token>
```

#### 更新配置
```http
PUT /api/v1/system/configs/{key}
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "value": "new_value",
  "description": "配置描述"
}
```

## 📊 状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 🚫 错误响应格式

```json
{
  "error": "错误描述",
  "detail": "详细错误信息",
  "request_id": "请求ID",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 📝 请求限制

- **频率限制**: 每分钟100次请求
- **文件上传**: 最大10MB
- **请求超时**: 30秒
- **并发连接**: 每用户最多10个

## 🔧 SDK和工具

### Python SDK示例

```python
import requests

class LegalAssistantAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {token}"}
    
    def ask_question(self, question, category=None):
        url = f"{self.base_url}/qa/ask"
        data = {"question": question, "category": category}
        response = requests.post(url, json=data, headers=self.headers)
        return response.json()
    
    def search_cases(self, query, **filters):
        url = f"{self.base_url}/case-search/search"
        params = {"q": query, **filters}
        response = requests.get(url, params=params, headers=self.headers)
        return response.json()

# 使用示例
api = LegalAssistantAPI("http://localhost:8000/api/v1", "your_token")
result = api.ask_question("合同违约怎么办？")
```

### JavaScript SDK示例

```javascript
class LegalAssistantAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async askQuestion(question, category = null) {
        const response = await fetch(`${this.baseUrl}/qa/ask`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({ question, category })
        });
        return response.json();
    }
    
    async searchCases(query, filters = {}) {
        const params = new URLSearchParams({ q: query, ...filters });
        const response = await fetch(`${this.baseUrl}/case-search/search?${params}`, {
            headers: this.headers
        });
        return response.json();
    }
}

// 使用示例
const api = new LegalAssistantAPI('http://localhost:8000/api/v1', 'your_token');
const result = await api.askQuestion('合同违约怎么办？');
```

## 📖 更多资源

- **Swagger文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc
- **OpenAPI规范**: http://localhost:8000/openapi.json

## 🆘 技术支持

如有API使用问题，请联系：
- GitHub Issues: [项目Issues页面]
- 邮件: <EMAIL>
- 文档: [在线API文档]
