/**
 * 智能问答页面
 */

import React, { useState, useRef, useEffect } from 'react';
import { NextPage } from 'next';
import { 
  Card, 
  Input, 
  Button, 
  List, 
  Typography, 
  Space, 
  Avatar, 
  Spin,
  message,
  Row,
  Col,
  Tag,
  Divider
} from 'antd';
import { 
  SendOutlined, 
  UserOutlined, 
  RobotOutlined,
  QuestionCircleOutlined,
  HistoryOutlined,
  BulbOutlined
} from '@ant-design/icons';
import { useAuth } from '@/contexts/AuthContext';
import { qaAPI } from '@/services/api/qa';
import { QARecord } from '@/types/qa';

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;

const QAPage: NextPage = () => {
  const { user, isAuthenticated } = useAuth();
  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [qaHistory, setQaHistory] = useState<QARecord[]>([]);
  const [currentAnswer, setCurrentAnswer] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [qaHistory, currentAnswer]);

  // 加载历史记录
  useEffect(() => {
    if (isAuthenticated) {
      loadHistory();
    }
  }, [isAuthenticated]);

  const loadHistory = async () => {
    try {
      const history = await qaAPI.getQAHistory();
      setQaHistory(history);
    } catch (error) {
      console.error('加载历史记录失败:', error);
    }
  };

  // 提交问题
  const handleSubmit = async () => {
    if (!question.trim()) {
      message.warning('请输入您的问题');
      return;
    }

    if (!isAuthenticated) {
      message.warning('请先登录后再提问');
      return;
    }

    setLoading(true);
    setCurrentAnswer('');

    try {
      // 添加用户问题到历史记录
      const userQuestion: QARecord = {
        id: Date.now().toString(),
        question: question.trim(),
        answer: '',
        userId: user?.id || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'pending',
        category: 'general',
        tags: [],
        rating: null,
        feedback: null,
      };

      setQaHistory(prev => [...prev, userQuestion]);
      
      // 发送问题到后端
      const response = await qaAPI.askQuestion({
        question: question.trim(),
        category: 'general',
      });

      // 更新答案
      const updatedRecord = {
        ...userQuestion,
        id: response.id,
        answer: response.answer,
        status: 'completed' as const,
        category: response.category,
        tags: response.tags || [],
      };

      setQaHistory(prev => 
        prev.map(item => 
          item.id === userQuestion.id ? updatedRecord : item
        )
      );

      setQuestion('');
    } catch (error: any) {
      message.error(error.message || '提问失败，请重试');
      // 移除失败的问题
      setQaHistory(prev => prev.filter(item => item.id !== Date.now().toString()));
    } finally {
      setLoading(false);
    }
  };

  // 快捷问题
  const quickQuestions = [
    '如何起草一份租房合同？',
    '劳动合同纠纷如何处理？',
    '交通事故责任如何认定？',
    '如何申请商标注册？',
    '离婚财产如何分割？',
    '公司注册需要什么条件？',
  ];

  const handleQuickQuestion = (q: string) => {
    setQuestion(q);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Row gutter={[24, 24]}>
        {/* 主要问答区域 */}
        <Col xs={24} lg={16}>
          <Card 
            title={
              <Space>
                <QuestionCircleOutlined />
                <span>智能问答</span>
              </Space>
            }
            style={{ height: '80vh', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}
          >
            {/* 对话历史 */}
            <div style={{ 
              flex: 1, 
              overflowY: 'auto', 
              padding: '16px',
              background: '#fafafa'
            }}>
              {qaHistory.length === 0 && !loading && (
                <div style={{ 
                  textAlign: 'center', 
                  padding: '60px 20px',
                  color: '#999'
                }}>
                  <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                  <Title level={4} style={{ color: '#999' }}>
                    欢迎使用AI法律助手
                  </Title>
                  <Paragraph style={{ color: '#999' }}>
                    请在下方输入您的法律问题，我将为您提供专业的解答
                  </Paragraph>
                </div>
              )}

              <List
                dataSource={qaHistory}
                renderItem={(item) => (
                  <div key={item.id} style={{ marginBottom: '24px' }}>
                    {/* 用户问题 */}
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'flex-end',
                      marginBottom: '8px'
                    }}>
                      <div style={{
                        maxWidth: '70%',
                        background: '#1890ff',
                        color: 'white',
                        padding: '12px 16px',
                        borderRadius: '18px 18px 4px 18px',
                        wordBreak: 'break-word'
                      }}>
                        {item.question}
                      </div>
                      <Avatar 
                        icon={<UserOutlined />} 
                        style={{ marginLeft: '8px' }}
                      />
                    </div>

                    {/* AI回答 */}
                    {item.answer && (
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'flex-start',
                        alignItems: 'flex-start'
                      }}>
                        <Avatar 
                          icon={<RobotOutlined />} 
                          style={{ marginRight: '8px', background: '#52c41a' }}
                        />
                        <div style={{
                          maxWidth: '70%',
                          background: 'white',
                          border: '1px solid #d9d9d9',
                          padding: '12px 16px',
                          borderRadius: '18px 18px 18px 4px',
                          wordBreak: 'break-word'
                        }}>
                          <div style={{ whiteSpace: 'pre-wrap' }}>
                            {item.answer}
                          </div>
                          {item.tags && item.tags.length > 0 && (
                            <div style={{ marginTop: '8px' }}>
                              {item.tags.map(tag => (
                                <Tag key={tag} size="small">{tag}</Tag>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* 加载状态 */}
                    {item.status === 'pending' && (
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        marginTop: '8px'
                      }}>
                        <Avatar 
                          icon={<RobotOutlined />} 
                          style={{ marginRight: '8px', background: '#52c41a' }}
                        />
                        <div style={{
                          background: 'white',
                          border: '1px solid #d9d9d9',
                          padding: '12px 16px',
                          borderRadius: '18px 18px 18px 4px',
                        }}>
                          <Spin size="small" />
                          <span style={{ marginLeft: '8px' }}>正在思考中...</span>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              />
              <div ref={messagesEndRef} />
            </div>

            {/* 输入区域 */}
            <div style={{ 
              padding: '16px', 
              borderTop: '1px solid #f0f0f0',
              background: 'white'
            }}>
              <Space.Compact style={{ width: '100%' }}>
                <TextArea
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  placeholder="请输入您的法律问题..."
                  autoSize={{ minRows: 1, maxRows: 4 }}
                  onPressEnter={(e) => {
                    if (!e.shiftKey) {
                      e.preventDefault();
                      handleSubmit();
                    }
                  }}
                  style={{ resize: 'none' }}
                />
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  loading={loading}
                  onClick={handleSubmit}
                  style={{ height: 'auto' }}
                >
                  发送
                </Button>
              </Space.Compact>
              <div style={{ 
                marginTop: '8px', 
                fontSize: '12px', 
                color: '#999',
                textAlign: 'center'
              }}>
                按 Enter 发送，Shift + Enter 换行
              </div>
            </div>
          </Card>
        </Col>

        {/* 侧边栏 */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {/* 快捷问题 */}
            <Card 
              title={
                <Space>
                  <BulbOutlined />
                  <span>常见问题</span>
                </Space>
              }
              size="small"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {quickQuestions.map((q, index) => (
                  <Button
                    key={index}
                    type="text"
                    size="small"
                    onClick={() => handleQuickQuestion(q)}
                    style={{ 
                      textAlign: 'left', 
                      height: 'auto',
                      padding: '8px 12px',
                      whiteSpace: 'normal',
                      wordBreak: 'break-all'
                    }}
                  >
                    {q}
                  </Button>
                ))}
              </Space>
            </Card>

            {/* 使用提示 */}
            <Card 
              title="使用提示" 
              size="small"
            >
              <Space direction="vertical" size="small">
                <Text type="secondary">
                  • 请尽量详细描述您的法律问题
                </Text>
                <Text type="secondary">
                  • 提供相关的事实和背景信息
                </Text>
                <Text type="secondary">
                  • AI回答仅供参考，重要事项请咨询专业律师
                </Text>
                <Text type="secondary">
                  • 您的问答记录将被保存以便后续查看
                </Text>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default QAPage;
