/**
 * 用户仪表板页面
 */

import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Typography, 
  Space, 
  Button,
  List,
  Avatar,
  Tag,
  Progress,
  Calendar,
  Badge,
  Divider
} from 'antd';
import { 
  QuestionCircleOutlined,
  FileTextOutlined,
  SearchOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  UserOutlined,
  RightOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import { qaAPI } from '@/services/api/qa';
import { QARecord } from '@/types/qa';
import type { Moment } from 'moment';

const { Title, Text, Paragraph } = Typography;

const DashboardPage: NextPage = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [recentQA, setRecentQA] = useState<QARecord[]>([]);
  const [stats, setStats] = useState({
    totalQuestions: 0,
    todayQuestions: 0,
    totalContracts: 0,
    totalCases: 0,
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // 加载最近的问答记录
      const qaHistory = await qaAPI.getQAHistory(5);
      setRecentQA(qaHistory);

      // 模拟统计数据（实际应该从API获取）
      setStats({
        totalQuestions: qaHistory.length,
        todayQuestions: qaHistory.filter(qa => {
          const today = new Date().toDateString();
          const qaDate = new Date(qa.createdAt).toDateString();
          return today === qaDate;
        }).length,
        totalContracts: 0,
        totalCases: 0,
      });
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 快捷操作
  const quickActions = [
    {
      title: '智能问答',
      description: '快速获得法律问题解答',
      icon: <QuestionCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      action: () => router.push('/qa'),
    },
    {
      title: '案例检索',
      description: '查找相关法律案例',
      icon: <SearchOutlined style={{ fontSize: '24px', color: '#52c41a' }} />,
      action: () => router.push('/cases'),
    },
    {
      title: '合同工具',
      description: '生成和审查合同',
      icon: <FileTextOutlined style={{ fontSize: '24px', color: '#faad14' }} />,
      action: () => router.push('/contracts'),
    },
  ];

  // 获取用户等级信息
  const getUserLevel = () => {
    const questionCount = stats.totalQuestions;
    if (questionCount < 10) return { level: '新手', progress: questionCount * 10, color: '#52c41a' };
    if (questionCount < 50) return { level: '进阶', progress: ((questionCount - 10) / 40) * 100, color: '#1890ff' };
    if (questionCount < 100) return { level: '专家', progress: ((questionCount - 50) / 50) * 100, color: '#faad14' };
    return { level: '大师', progress: 100, color: '#f5222d' };
  };

  const userLevel = getUserLevel();

  // 日历数据（示例）
  const getCalendarData = (value: Moment) => {
    // 这里应该从API获取实际数据
    const date = value.date();
    if (date < 15) {
      return { type: 'success', content: `${date % 3}个问题` };
    }
    return null;
  };

  const dateCellRender = (value: Moment) => {
    const data = getCalendarData(value);
    return data ? (
      <Badge status={data.type as any} text={data.content} />
    ) : null;
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 欢迎区域 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          欢迎回来，{user?.fullName || user?.username}！
        </Title>
        <Text type="secondary">
          今天是 {new Date().toLocaleDateString('zh-CN', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
          })}
        </Text>
      </div>

      <Row gutter={[24, 24]}>
        {/* 统计卡片 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总提问数"
              value={stats.totalQuestions}
              prefix={<QuestionCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日提问"
              value={stats.todayQuestions}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="合同数量"
              value={stats.totalContracts}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="案例收藏"
              value={stats.totalCases}
              prefix={<SearchOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>

        {/* 快捷操作 */}
        <Col xs={24} lg={12}>
          <Card title="快捷操作" extra={<Button type="link">查看全部</Button>}>
            <Row gutter={[16, 16]}>
              {quickActions.map((action, index) => (
                <Col xs={24} key={index}>
                  <Card 
                    hoverable
                    size="small"
                    onClick={action.action}
                    style={{ cursor: 'pointer' }}
                  >
                    <Space>
                      {action.icon}
                      <div>
                        <div style={{ fontWeight: 'bold' }}>{action.title}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {action.description}
                        </Text>
                      </div>
                      <RightOutlined style={{ marginLeft: 'auto' }} />
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        {/* 用户等级 */}
        <Col xs={24} lg={12}>
          <Card title="用户等级" extra={<TrophyOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <Avatar 
                  size={64} 
                  icon={<UserOutlined />}
                  style={{ background: userLevel.color }}
                />
                <Title level={4} style={{ margin: '8px 0', color: userLevel.color }}>
                  {userLevel.level}
                </Title>
              </div>
              <Progress 
                percent={userLevel.progress} 
                strokeColor={userLevel.color}
                showInfo={false}
              />
              <Text type="secondary" style={{ textAlign: 'center', display: 'block' }}>
                继续提问以提升等级
              </Text>
            </Space>
          </Card>
        </Col>

        {/* 最近问答 */}
        <Col xs={24} lg={12}>
          <Card 
            title="最近问答" 
            extra={
              <Button 
                type="link" 
                onClick={() => router.push('/qa')}
              >
                查看全部
              </Button>
            }
          >
            <List
              dataSource={recentQA}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<QuestionCircleOutlined />} />}
                    title={
                      <Text ellipsis style={{ maxWidth: '200px' }}>
                        {item.question}
                      </Text>
                    }
                    description={
                      <Space>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {new Date(item.createdAt).toLocaleDateString()}
                        </Text>
                        {item.category && (
                          <Tag size="small">{item.category}</Tag>
                        )}
                      </Space>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无问答记录' }}
            />
          </Card>
        </Col>

        {/* 活动日历 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <CalendarOutlined />
                <span>活动日历</span>
              </Space>
            }
          >
            <Calendar 
              fullscreen={false}
              dateCellRender={dateCellRender}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
