/**
 * 案例检索页面
 */

import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import { 
  Row, 
  Col, 
  Card, 
  Input, 
  Button, 
  List, 
  Typography, 
  Space, 
  Tag,
  Select,
  DatePicker,
  Pagination,
  Empty,
  Spin,
  Divider,
  Tooltip
} from 'antd';
import { 
  SearchOutlined,
  FilterOutlined,
  BookOutlined,
  CalendarOutlined,
  TagOutlined,
  EyeOutlined,
  HeartOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/router';

const { Search } = Input;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 模拟案例数据类型
interface CaseItem {
  id: string;
  title: string;
  summary: string;
  court: string;
  caseNumber: string;
  date: string;
  category: string;
  tags: string[];
  viewCount: number;
  favoriteCount: number;
  relevanceScore?: number;
}

const CasesPage: NextPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [cases, setCases] = useState<CaseItem[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [filters, setFilters] = useState({
    category: '',
    court: '',
    dateRange: null as any,
  });

  // 模拟案例数据
  const mockCases: CaseItem[] = [
    {
      id: '1',
      title: '某公司与员工劳动合同纠纷案',
      summary: '本案涉及劳动合同解除、经济补偿金计算等问题。法院认为用人单位应当依法支付经济补偿金...',
      court: '北京市朝阳区人民法院',
      caseNumber: '(2023)京0105民初12345号',
      date: '2023-10-15',
      category: '劳动争议',
      tags: ['劳动合同', '经济补偿', '违法解除'],
      viewCount: 1250,
      favoriteCount: 89,
      relevanceScore: 95,
    },
    {
      id: '2',
      title: '房屋买卖合同纠纷案',
      summary: '买方因卖方逾期交房要求解除合同并赔偿损失。法院支持了买方的诉讼请求...',
      court: '上海市浦东新区人民法院',
      caseNumber: '(2023)沪0115民初67890号',
      date: '2023-09-28',
      category: '合同纠纷',
      tags: ['房屋买卖', '逾期交房', '违约责任'],
      viewCount: 980,
      favoriteCount: 67,
      relevanceScore: 88,
    },
    {
      id: '3',
      title: '交通事故人身损害赔偿案',
      summary: '机动车与行人发生交通事故，涉及责任认定、医疗费、误工费等赔偿问题...',
      court: '广州市天河区人民法院',
      caseNumber: '(2023)粤0106民初11111号',
      date: '2023-11-02',
      category: '侵权责任',
      tags: ['交通事故', '人身损害', '赔偿标准'],
      viewCount: 1580,
      favoriteCount: 123,
      relevanceScore: 92,
    },
  ];

  useEffect(() => {
    loadCases();
  }, [currentPage, filters]);

  const loadCases = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 根据搜索条件过滤案例
      let filteredCases = mockCases;
      
      if (searchText) {
        filteredCases = filteredCases.filter(c => 
          c.title.includes(searchText) || 
          c.summary.includes(searchText) ||
          c.tags.some(tag => tag.includes(searchText))
        );
      }
      
      if (filters.category) {
        filteredCases = filteredCases.filter(c => c.category === filters.category);
      }
      
      if (filters.court) {
        filteredCases = filteredCases.filter(c => c.court.includes(filters.court));
      }
      
      setCases(filteredCases);
      setTotal(filteredCases.length);
    } catch (error) {
      console.error('加载案例失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
    loadCases();
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const categories = [
    '合同纠纷',
    '劳动争议',
    '侵权责任',
    '婚姻家庭',
    '公司法务',
    '知识产权',
    '刑事案件',
    '行政诉讼',
  ];

  const courts = [
    '最高人民法院',
    '北京市高级人民法院',
    '上海市高级人民法院',
    '广东省高级人民法院',
    '浙江省高级人民法院',
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <BookOutlined style={{ marginRight: '8px' }} />
          案例检索
        </Title>
        <Text type="secondary">
          搜索海量法律案例，找到最相关的判例参考
        </Text>
      </div>

      {/* 搜索和筛选区域 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 搜索框 */}
          <Search
            placeholder="请输入关键词搜索案例..."
            allowClear
            enterButton={
              <Button type="primary" icon={<SearchOutlined />}>
                搜索
              </Button>
            }
            size="large"
            onSearch={handleSearch}
            style={{ maxWidth: '600px' }}
          />

          {/* 筛选条件 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8} md={6}>
              <Select
                placeholder="案例类别"
                allowClear
                style={{ width: '100%' }}
                onChange={(value) => handleFilterChange('category', value)}
              >
                {categories.map(cat => (
                  <Option key={cat} value={cat}>{cat}</Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Select
                placeholder="审理法院"
                allowClear
                style={{ width: '100%' }}
                onChange={(value) => handleFilterChange('court', value)}
              >
                {courts.map(court => (
                  <Option key={court} value={court}>{court}</Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                style={{ width: '100%' }}
                onChange={(dates) => handleFilterChange('dateRange', dates)}
              />
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Button 
                icon={<FilterOutlined />}
                onClick={() => {
                  setFilters({ category: '', court: '', dateRange: null });
                  setSearchText('');
                  setCurrentPage(1);
                }}
              >
                重置筛选
              </Button>
            </Col>
          </Row>
        </Space>
      </Card>

      <Row gutter={[24, 24]}>
        {/* 搜索结果 */}
        <Col xs={24} lg={18}>
          <Card 
            title={`搜索结果 (共 ${total} 条)`}
            extra={
              <Select defaultValue="relevance" style={{ width: 120 }}>
                <Option value="relevance">相关度</Option>
                <Option value="date">时间</Option>
                <Option value="views">浏览量</Option>
              </Select>
            }
          >
            <Spin spinning={loading}>
              {cases.length > 0 ? (
                <>
                  <List
                    dataSource={cases}
                    renderItem={(item) => (
                      <List.Item
                        actions={[
                          <Tooltip title="查看详情">
                            <Button 
                              type="text" 
                              icon={<EyeOutlined />}
                              onClick={() => router.push(`/cases/${item.id}`)}
                            />
                          </Tooltip>,
                          <Tooltip title="收藏">
                            <Button type="text" icon={<HeartOutlined />} />
                          </Tooltip>,
                          <Tooltip title="分享">
                            <Button type="text" icon={<ShareAltOutlined />} />
                          </Tooltip>,
                        ]}
                      >
                        <List.Item.Meta
                          title={
                            <Space>
                              <a 
                                onClick={() => router.push(`/cases/${item.id}`)}
                                style={{ fontSize: '16px', fontWeight: 'bold' }}
                              >
                                {item.title}
                              </a>
                              {item.relevanceScore && (
                                <Tag color="blue">
                                  相关度 {item.relevanceScore}%
                                </Tag>
                              )}
                            </Space>
                          }
                          description={
                            <Space direction="vertical" style={{ width: '100%' }}>
                              <Paragraph 
                                ellipsis={{ rows: 2, expandable: true }}
                                style={{ margin: 0, color: '#666' }}
                              >
                                {item.summary}
                              </Paragraph>
                              
                              <Space wrap>
                                <Text type="secondary">
                                  <CalendarOutlined /> {item.date}
                                </Text>
                                <Text type="secondary">
                                  {item.court}
                                </Text>
                                <Text type="secondary">
                                  {item.caseNumber}
                                </Text>
                              </Space>
                              
                              <Space wrap>
                                <Tag color="processing">{item.category}</Tag>
                                {item.tags.map(tag => (
                                  <Tag key={tag}>{tag}</Tag>
                                ))}
                              </Space>
                              
                              <Space>
                                <Text type="secondary">
                                  <EyeOutlined /> {item.viewCount}
                                </Text>
                                <Text type="secondary">
                                  <HeartOutlined /> {item.favoriteCount}
                                </Text>
                              </Space>
                            </Space>
                          }
                        />
                      </List.Item>
                    )}
                  />
                  
                  <div style={{ textAlign: 'center', marginTop: '24px' }}>
                    <Pagination
                      current={currentPage}
                      total={total}
                      pageSize={pageSize}
                      onChange={setCurrentPage}
                      showSizeChanger={false}
                      showQuickJumper
                      showTotal={(total, range) => 
                        `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                      }
                    />
                  </div>
                </>
              ) : (
                <Empty 
                  description="暂无相关案例"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Spin>
          </Card>
        </Col>

        {/* 侧边栏 */}
        <Col xs={24} lg={6}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {/* 热门标签 */}
            <Card title="热门标签" size="small">
              <Space wrap>
                {['劳动合同', '房屋买卖', '交通事故', '离婚纠纷', '合同违约', '知识产权', '公司纠纷', '刑事辩护'].map(tag => (
                  <Tag 
                    key={tag} 
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleSearch(tag)}
                  >
                    {tag}
                  </Tag>
                ))}
              </Space>
            </Card>

            {/* 搜索提示 */}
            <Card title="搜索提示" size="small">
              <Space direction="vertical" size="small">
                <Text type="secondary">• 使用关键词组合可以获得更精确的结果</Text>
                <Text type="secondary">• 可以搜索案例标题、摘要或标签</Text>
                <Text type="secondary">• 使用筛选条件缩小搜索范围</Text>
                <Text type="secondary">• 点击热门标签快速搜索</Text>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default CasesPage;
