/**
 * 用户登录页面
 */

import React, { useState } from 'react';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { Form, Input, Button, Card, Typography, message, Divider, Space } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

const { Title, Text } = Typography;

interface LoginForm {
  email: string;
  password: string;
}

const LoginPage: NextPage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { login } = useAuth();

  const onFinish = async (values: LoginForm) => {
    setLoading(true);
    try {
      await login(values.email, values.password);
      message.success('登录成功！');
      
      // 获取重定向地址
      const redirect = router.query.redirect as string;
      router.push(redirect || '/dashboard');
    } catch (error: any) {
      message.error(error.message || '登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: '400px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title level={2} style={{ marginBottom: '8px' }}>
            用户登录
          </Title>
          <Text type="secondary">
            登录您的AI法律助手账户
          </Text>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={onFinish}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            label="邮箱地址"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="请输入邮箱地址"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码长度至少6位' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{ height: '48px', fontSize: '16px' }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider>或</Divider>

        <div style={{ textAlign: 'center' }}>
          <Space direction="vertical" size="middle">
            <Text>
              还没有账户？
              <Link href="/auth/register">
                <a style={{ marginLeft: '8px' }}>立即注册</a>
              </Link>
            </Text>
            
            <Link href="/auth/forgot-password">
              <a>忘记密码？</a>
            </Link>
          </Space>
        </div>

        <div style={{ 
          marginTop: '32px', 
          padding: '16px', 
          background: '#f5f5f5', 
          borderRadius: '6px',
          textAlign: 'center'
        }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            登录即表示您同意我们的
            <Link href="/terms">
              <a style={{ margin: '0 4px' }}>服务条款</a>
            </Link>
            和
            <Link href="/privacy">
              <a style={{ margin: '0 4px' }}>隐私政策</a>
            </Link>
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default LoginPage;
