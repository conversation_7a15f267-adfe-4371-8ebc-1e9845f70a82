/**
 * AI法律助手首页
 */

import React from 'react';
import { NextPage } from 'next';
import { Row, Col, Card, Button, Typography, Space, Statistic } from 'antd';
import { 
  QuestionCircleOutlined, 
  FileTextOutlined, 
  SearchOutlined,
  TeamOutlined,
  RocketOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';

const { Title, Paragraph } = Typography;

const HomePage: NextPage = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  const features = [
    {
      icon: <QuestionCircleOutlined style={{ fontSize: '48px', color: '#1890ff' }} />,
      title: '智能问答',
      description: '基于AI的法律问题智能解答，快速获得专业法律建议',
      action: () => router.push('/qa'),
    },
    {
      icon: <SearchOutlined style={{ fontSize: '48px', color: '#52c41a' }} />,
      title: '案例检索',
      description: '海量法律案例数据库，精准检索相关判例',
      action: () => router.push('/cases'),
    },
    {
      icon: <FileTextOutlined style={{ fontSize: '48px', color: '#faad14' }} />,
      title: '合同工具',
      description: '智能合同生成、审查和风险评估',
      action: () => router.push('/contracts'),
    },
  ];

  const stats = [
    { title: '累计用户', value: 10000, suffix: '+' },
    { title: '解答问题', value: 50000, suffix: '+' },
    { title: '案例数量', value: 100000, suffix: '+' },
    { title: '合同模板', value: 500, suffix: '+' },
  ];

  return (
    <div style={{ padding: '0' }}>
      {/* 英雄区域 */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '80px 0',
        color: 'white',
        textAlign: 'center'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px' }}>
          <Title level={1} style={{ color: 'white', fontSize: '48px', marginBottom: '24px' }}>
            AI法律助手
          </Title>
          <Paragraph style={{ 
            color: 'white', 
            fontSize: '20px', 
            marginBottom: '40px',
            opacity: 0.9
          }}>
            专业的AI驱动法律服务平台，为您提供智能问答、案例检索、合同工具等全方位法律服务
          </Paragraph>
          <Space size="large">
            {!isAuthenticated ? (
              <>
                <Button 
                  type="primary" 
                  size="large"
                  onClick={() => router.push('/auth/register')}
                  style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
                >
                  立即注册
                </Button>
                <Button 
                  size="large"
                  onClick={() => router.push('/auth/login')}
                  style={{ 
                    height: '48px', 
                    padding: '0 32px', 
                    fontSize: '16px',
                    background: 'transparent',
                    borderColor: 'white',
                    color: 'white'
                  }}
                >
                  用户登录
                </Button>
              </>
            ) : (
              <Button 
                type="primary" 
                size="large"
                onClick={() => router.push('/dashboard')}
                style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
              >
                进入控制台
              </Button>
            )}
          </Space>
        </div>
      </div>

      {/* 统计数据 */}
      <div style={{ padding: '60px 0', background: '#f5f5f5' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px' }}>
          <Row gutter={[32, 32]}>
            {stats.map((stat, index) => (
              <Col xs={12} sm={6} key={index}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    suffix={stat.suffix}
                    valueStyle={{ color: '#1890ff', fontSize: '32px' }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 核心功能 */}
      <div style={{ padding: '80px 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px' }}>
          <div style={{ textAlign: 'center', marginBottom: '60px' }}>
            <Title level={2}>核心功能</Title>
            <Paragraph style={{ fontSize: '16px', color: '#666' }}>
              为您提供全方位的AI法律服务
            </Paragraph>
          </div>
          
          <Row gutter={[32, 32]}>
            {features.map((feature, index) => (
              <Col xs={24} md={8} key={index}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  onClick={feature.action}
                >
                  <div style={{ marginBottom: '24px' }}>
                    {feature.icon}
                  </div>
                  <Title level={4}>{feature.title}</Title>
                  <Paragraph style={{ color: '#666' }}>
                    {feature.description}
                  </Paragraph>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 优势特点 */}
      <div style={{ padding: '80px 0', background: '#f5f5f5' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px' }}>
          <div style={{ textAlign: 'center', marginBottom: '60px' }}>
            <Title level={2}>为什么选择我们</Title>
          </div>
          
          <Row gutter={[32, 32]}>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <RocketOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
                <Title level={4}>高效智能</Title>
                <Paragraph style={{ color: '#666' }}>
                  基于先进AI技术，快速准确地解答法律问题
                </Paragraph>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <SafetyOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} />
                <Title level={4}>专业可靠</Title>
                <Paragraph style={{ color: '#666' }}>
                  专业法律团队支持，确保服务质量和准确性
                </Paragraph>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <TeamOutlined style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }} />
                <Title level={4}>全面服务</Title>
                <Paragraph style={{ color: '#666' }}>
                  涵盖问答、检索、合同等多种法律服务场景
                </Paragraph>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
