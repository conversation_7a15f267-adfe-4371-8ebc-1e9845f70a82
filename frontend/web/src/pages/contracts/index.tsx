/**
 * 合同工具页面
 */

import React, { useState } from 'react';
import { NextPage } from 'next';
import { 
  Row, 
  Col, 
  Card, 
  Button, 
  Typography, 
  Space, 
  List,
  Tag,
  Upload,
  Modal,
  Form,
  Input,
  Select,
  message,
  Divider,
  Progress,
  Alert
} from 'antd';
import { 
  FileTextOutlined,
  PlusOutlined,
  UploadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/router';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 合同模板类型
interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: 'simple' | 'medium' | 'complex';
  estimatedTime: string;
  fields: string[];
  popular: boolean;
}

// 用户合同类型
interface UserContract {
  id: string;
  name: string;
  template: string;
  status: 'draft' | 'completed' | 'signed';
  createdAt: string;
  updatedAt: string;
  progress: number;
}

const ContractsPage: NextPage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'templates' | 'my-contracts' | 'review'>('templates');
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 模拟合同模板数据
  const contractTemplates: ContractTemplate[] = [
    {
      id: '1',
      name: '劳动合同模板',
      description: '标准劳动合同模板，包含基本条款和常用约定',
      category: '劳动法',
      difficulty: 'simple',
      estimatedTime: '10-15分钟',
      fields: ['员工姓名', '职位', '薪资', '工作地点', '合同期限'],
      popular: true,
    },
    {
      id: '2',
      name: '房屋租赁合同',
      description: '住宅/商业房屋租赁合同模板',
      category: '房地产',
      difficulty: 'medium',
      estimatedTime: '15-20分钟',
      fields: ['出租方', '承租方', '房屋地址', '租金', '租期', '押金'],
      popular: true,
    },
    {
      id: '3',
      name: '服务外包合同',
      description: 'IT服务、咨询服务等外包合同模板',
      category: '商业合同',
      difficulty: 'complex',
      estimatedTime: '25-30分钟',
      fields: ['服务内容', '交付标准', '付款方式', '知识产权', '保密条款'],
      popular: false,
    },
    {
      id: '4',
      name: '买卖合同',
      description: '商品买卖合同通用模板',
      category: '商业合同',
      difficulty: 'medium',
      estimatedTime: '15-20分钟',
      fields: ['买方', '卖方', '商品描述', '价格', '交付方式', '质量标准'],
      popular: true,
    },
  ];

  // 模拟用户合同数据
  const userContracts: UserContract[] = [
    {
      id: '1',
      name: '张三劳动合同',
      template: '劳动合同模板',
      status: 'completed',
      createdAt: '2023-11-01',
      updatedAt: '2023-11-02',
      progress: 100,
    },
    {
      id: '2',
      name: '办公室租赁合同',
      template: '房屋租赁合同',
      status: 'draft',
      createdAt: '2023-11-03',
      updatedAt: '2023-11-03',
      progress: 60,
    },
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'simple': return 'green';
      case 'medium': return 'orange';
      case 'complex': return 'red';
      default: return 'blue';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft': return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'completed': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'signed': return <CheckCircleOutlined style={{ color: '#1890ff' }} />;
      default: return <ClockCircleOutlined />;
    }
  };

  const handleCreateContract = (templateId: string) => {
    const template = contractTemplates.find(t => t.id === templateId);
    if (template) {
      setCreateModalVisible(true);
      form.setFieldsValue({ template: template.name });
    }
  };

  const handleCreateSubmit = async (values: any) => {
    try {
      // 这里应该调用API创建合同
      message.success('合同创建成功！');
      setCreateModalVisible(false);
      form.resetFields();
      // 跳转到合同编辑页面
      router.push('/contracts/edit/new');
    } catch (error) {
      message.error('创建失败，请重试');
    }
  };

  const renderTemplates = () => (
    <Row gutter={[24, 24]}>
      {contractTemplates.map(template => (
        <Col xs={24} sm={12} lg={8} key={template.id}>
          <Card
            hoverable
            actions={[
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => handleCreateContract(template.id)}
              >
                使用模板
              </Button>,
              <Button 
                type="text" 
                icon={<EyeOutlined />}
                onClick={() => router.push(`/contracts/template/${template.id}`)}
              >
                预览
              </Button>,
            ]}
          >
            <Card.Meta
              title={
                <Space>
                  {template.name}
                  {template.popular && <Tag color="red">热门</Tag>}
                </Space>
              }
              description={
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Paragraph ellipsis={{ rows: 2 }}>
                    {template.description}
                  </Paragraph>
                  <Space wrap>
                    <Tag color="blue">{template.category}</Tag>
                    <Tag color={getDifficultyColor(template.difficulty)}>
                      {template.difficulty === 'simple' ? '简单' : 
                       template.difficulty === 'medium' ? '中等' : '复杂'}
                    </Tag>
                  </Space>
                  <Text type="secondary">
                    预计用时：{template.estimatedTime}
                  </Text>
                </Space>
              }
            />
          </Card>
        </Col>
      ))}
    </Row>
  );

  const renderMyContracts = () => (
    <List
      dataSource={userContracts}
      renderItem={(item) => (
        <List.Item
          actions={[
            <Button 
              type="text" 
              icon={<EditOutlined />}
              onClick={() => router.push(`/contracts/edit/${item.id}`)}
            >
              编辑
            </Button>,
            <Button type="text" icon={<DownloadOutlined />}>
              下载
            </Button>,
            <Button type="text" icon={<DeleteOutlined />} danger>
              删除
            </Button>,
          ]}
        >
          <List.Item.Meta
            avatar={getStatusIcon(item.status)}
            title={item.name}
            description={
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space>
                  <Text type="secondary">模板：{item.template}</Text>
                  <Tag color={item.status === 'completed' ? 'green' : 'orange'}>
                    {item.status === 'draft' ? '草稿' : 
                     item.status === 'completed' ? '已完成' : '已签署'}
                  </Tag>
                </Space>
                <Progress percent={item.progress} size="small" />
                <Text type="secondary">
                  创建时间：{item.createdAt} | 更新时间：{item.updatedAt}
                </Text>
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );

  const renderReview = () => (
    <div>
      <Alert
        message="合同审查功能"
        description="上传您的合同文件，AI将为您分析潜在风险和改进建议"
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />
      
      <Card title="上传合同文件">
        <Upload.Dragger
          name="contract"
          multiple={false}
          accept=".pdf,.doc,.docx"
          beforeUpload={() => false}
          onChange={(info) => {
            if (info.file.status === 'done') {
              message.success('文件上传成功');
            }
          }}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 PDF、Word 格式，文件大小不超过 10MB
          </p>
        </Upload.Dragger>
        
        <div style={{ marginTop: '24px', textAlign: 'center' }}>
          <Button type="primary" size="large">
            开始审查
          </Button>
        </div>
      </Card>
    </div>
  );

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <FileTextOutlined style={{ marginRight: '8px' }} />
          合同工具
        </Title>
        <Text type="secondary">
          智能合同生成、审查和管理工具
        </Text>
      </div>

      {/* 功能选项卡 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space size="large">
          <Button 
            type={activeTab === 'templates' ? 'primary' : 'text'}
            onClick={() => setActiveTab('templates')}
          >
            合同模板
          </Button>
          <Button 
            type={activeTab === 'my-contracts' ? 'primary' : 'text'}
            onClick={() => setActiveTab('my-contracts')}
          >
            我的合同
          </Button>
          <Button 
            type={activeTab === 'review' ? 'primary' : 'text'}
            onClick={() => setActiveTab('review')}
          >
            合同审查
          </Button>
        </Space>
      </Card>

      {/* 内容区域 */}
      <Card>
        {activeTab === 'templates' && renderTemplates()}
        {activeTab === 'my-contracts' && renderMyContracts()}
        {activeTab === 'review' && renderReview()}
      </Card>

      {/* 创建合同模态框 */}
      <Modal
        title="创建新合同"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateSubmit}
        >
          <Form.Item
            name="name"
            label="合同名称"
            rules={[{ required: true, message: '请输入合同名称' }]}
          >
            <Input placeholder="请输入合同名称" />
          </Form.Item>
          
          <Form.Item
            name="template"
            label="使用模板"
          >
            <Input disabled />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="合同描述"
          >
            <TextArea 
              rows={3} 
              placeholder="请简要描述合同用途和要求"
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建合同
              </Button>
              <Button onClick={() => setCreateModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ContractsPage;
