import React from 'react'
import { Card, Row, Col, Statistic, Typography, Space, Button } from 'antd'
import {
  QuestionCircleOutlined,
  FileSearchOutlined,
  FileTextOutlined,
  FileDoneOutlined,
  UserOutlined,
  TrophyOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

import { useAuthStore } from '@/store/auth'

const { Title, Paragraph } = Typography

const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()

  const quickActions = [
    {
      title: 'AI问答',
      description: '智能法律咨询，快速获得专业解答',
      icon: <QuestionCircleOutlined style={{ fontSize: 24, color: '#1890ff' }} />,
      path: '/qa',
      color: '#1890ff',
    },
    {
      title: '案例检索',
      description: '海量法律案例，精准搜索匹配',
      icon: <FileSearchOutlined style={{ fontSize: 24, color: '#52c41a' }} />,
      path: '/cases',
      color: '#52c41a',
    },
    {
      title: '合同工具',
      description: '智能合同审查，风险识别提醒',
      icon: <FileTextOutlined style={{ fontSize: 24, color: '#faad14' }} />,
      path: '/contracts',
      color: '#faad14',
    },
    {
      title: '文书工具',
      description: '法律文书生成，模板化快速制作',
      icon: <FileDoneOutlined style={{ fontSize: 24, color: '#f5222d' }} />,
      path: '/documents',
      color: '#f5222d',
    },
  ]

  const stats = [
    {
      title: '累计提问',
      value: 1234,
      suffix: '次',
      icon: <QuestionCircleOutlined />,
    },
    {
      title: '案例检索',
      value: 567,
      suffix: '次',
      icon: <FileSearchOutlined />,
    },
    {
      title: '合同审查',
      value: 89,
      suffix: '份',
      icon: <FileTextOutlined />,
    },
    {
      title: '文书生成',
      value: 45,
      suffix: '份',
      icon: <FileDoneOutlined />,
    },
  ]

  return (
    <div className="page-container">
      {/* 欢迎区域 */}
      <Card style={{ marginBottom: 24 }}>
        <Row align="middle">
          <Col flex="auto">
            <Space direction="vertical" size={8}>
              <Title level={3} style={{ margin: 0 }}>
                欢迎回来，{user?.full_name || user?.username}！
              </Title>
              <Paragraph style={{ margin: 0, color: 'rgba(0, 0, 0, 0.65)' }}>
                今天是 {new Date().toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long'
                })}，开始您的智能法律服务之旅吧
              </Paragraph>
            </Space>
          </Col>
          <Col>
            <UserOutlined style={{ fontSize: 48, color: '#1890ff' }} />
          </Col>
        </Row>
      </Card>

      {/* 统计数据 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {stats.map((stat, index) => (
          <Col xs={12} sm={12} md={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                suffix={stat.suffix}
                prefix={stat.icon}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} md={6} key={index}>
              <Card
                hoverable
                style={{
                  textAlign: 'center',
                  borderColor: action.color,
                  transition: 'all 0.3s ease',
                }}
                bodyStyle={{ padding: '24px 16px' }}
                onClick={() => navigate(action.path)}
              >
                <Space direction="vertical" size={12} style={{ width: '100%' }}>
                  {action.icon}
                  <Title level={5} style={{ margin: 0, color: action.color }}>
                    {action.title}
                  </Title>
                  <Paragraph
                    style={{
                      margin: 0,
                      color: 'rgba(0, 0, 0, 0.65)',
                      fontSize: 12,
                    }}
                  >
                    {action.description}
                  </Paragraph>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 最近活动 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card title="最近提问" extra={<Button type="link">查看全部</Button>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                <div style={{ fontWeight: 500, marginBottom: 4 }}>
                  合同违约责任如何承担？
                </div>
                <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
                  2小时前
                </div>
              </div>
              <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                <div style={{ fontWeight: 500, marginBottom: 4 }}>
                  劳动争议仲裁程序是什么？
                </div>
                <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
                  1天前
                </div>
              </div>
              <div style={{ padding: '8px 0' }}>
                <div style={{ fontWeight: 500, marginBottom: 4 }}>
                  知识产权侵权如何认定？
                </div>
                <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
                  3天前
                </div>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card title="系统公告" extra={<Button type="link">查看全部</Button>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                <div style={{ fontWeight: 500, marginBottom: 4 }}>
                  <TrophyOutlined style={{ color: '#faad14', marginRight: 8 }} />
                  新增合同模板功能
                </div>
                <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
                  系统新增了多种合同模板，支持在线编辑和下载
                </div>
              </div>
              <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                <div style={{ fontWeight: 500, marginBottom: 4 }}>
                  案例数据库更新
                </div>
                <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
                  新增10万+最新法律案例，提升检索准确性
                </div>
              </div>
              <div style={{ padding: '8px 0' }}>
                <div style={{ fontWeight: 500, marginBottom: 4 }}>
                  AI问答功能优化
                </div>
                <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
                  优化AI模型，提升回答准确性和响应速度
                </div>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default HomePage
