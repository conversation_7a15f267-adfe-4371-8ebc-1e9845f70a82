import React from 'react'
import { <PERSON>, Typography, Button, Space } from 'antd'
import { FileTextOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

const ContractsPage: React.FC = () => {
  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          <FileTextOutlined style={{ marginRight: 8 }} />
          合同工具
        </Title>
        <Paragraph className="page-description">
          智能合同审查与风险识别工具
        </Paragraph>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <FileTextOutlined style={{ fontSize: 64, color: '#faad14', marginBottom: 16 }} />
          <Title level={3}>合同工具功能</Title>
          <Paragraph style={{ marginBottom: 24 }}>
            该功能正在开发中，敬请期待...
          </Paragraph>
          <Space>
            <Button type="primary">上传合同</Button>
            <Button>合同模板</Button>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default ContractsPage
