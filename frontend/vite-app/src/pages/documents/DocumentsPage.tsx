import React from 'react'
import { <PERSON>, Typography, Button, Space } from 'antd'
import { FileDoneOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

const DocumentsPage: React.FC = () => {
  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          <FileDoneOutlined style={{ marginRight: 8 }} />
          文书工具
        </Title>
        <Paragraph className="page-description">
          法律文书智能生成与模板管理
        </Paragraph>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <FileDoneOutlined style={{ fontSize: 64, color: '#f5222d', marginBottom: 16 }} />
          <Title level={3}>文书工具功能</Title>
          <Paragraph style={{ marginBottom: 24 }}>
            该功能正在开发中，敬请期待...
          </Paragraph>
          <Space>
            <Button type="primary">创建文书</Button>
            <Button>文书模板</Button>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default DocumentsPage
