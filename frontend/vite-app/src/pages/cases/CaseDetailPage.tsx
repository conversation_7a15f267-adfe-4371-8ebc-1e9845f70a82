import React from 'react'
import { Card, Typography, Button } from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeftOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

const CaseDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  return (
    <div className="page-container">
      <div className="page-header">
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/cases')}
          style={{ marginBottom: 16 }}
        >
          返回案例列表
        </Button>
        <Title level={2} className="page-title">
          案例详情
        </Title>
        <Paragraph className="page-description">
          案例ID: {id}
        </Paragraph>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <Title level={3}>案例详情页面</Title>
          <Paragraph style={{ marginBottom: 24 }}>
            该功能正在开发中，敬请期待...
          </Paragraph>
        </div>
      </Card>
    </div>
  )
}

export default CaseDetailPage
