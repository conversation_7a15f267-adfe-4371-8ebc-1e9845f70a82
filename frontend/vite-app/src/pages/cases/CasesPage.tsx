import React from 'react'
import { <PERSON>, Typography, Button, Space } from 'antd'
import { FileSearchOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

const CasesPage: React.FC = () => {
  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          <FileSearchOutlined style={{ marginRight: 8 }} />
          案例检索
        </Title>
        <Paragraph className="page-description">
          海量法律案例数据库，精准搜索相关判例
        </Paragraph>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <FileSearchOutlined style={{ fontSize: 64, color: '#52c41a', marginBottom: 16 }} />
          <Title level={3}>案例检索功能</Title>
          <Paragraph style={{ marginBottom: 24 }}>
            该功能正在开发中，敬请期待...
          </Paragraph>
          <Space>
            <Button type="primary">开始搜索</Button>
            <Button>高级搜索</Button>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default CasesPage
