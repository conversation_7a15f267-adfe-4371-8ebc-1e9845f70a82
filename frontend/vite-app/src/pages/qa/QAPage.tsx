import React from 'react'
import { <PERSON>, Typography, Button, Space } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

const QAPage: React.FC = () => {
  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          <QuestionCircleOutlined style={{ marginRight: 8 }} />
          AI问答
        </Title>
        <Paragraph className="page-description">
          智能法律咨询服务，为您提供专业的法律问题解答
        </Paragraph>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <QuestionCircleOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 16 }} />
          <Title level={3}>AI问答功能</Title>
          <Paragraph style={{ marginBottom: 24 }}>
            该功能正在开发中，敬请期待...
          </Paragraph>
          <Space>
            <Button type="primary">开始提问</Button>
            <Button>查看历史</Button>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default QAPage
