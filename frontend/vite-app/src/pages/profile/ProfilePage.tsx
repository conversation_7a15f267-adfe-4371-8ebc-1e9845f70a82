import React from 'react'
import { <PERSON>, Typography, Button, Space } from 'antd'
import { UserOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

const ProfilePage: React.FC = () => {
  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          <UserOutlined style={{ marginRight: 8 }} />
          个人资料
        </Title>
        <Paragraph className="page-description">
          管理您的个人信息和账户设置
        </Paragraph>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <UserOutlined style={{ fontSize: 64, color: '#722ed1', marginBottom: 16 }} />
          <Title level={3}>个人资料管理</Title>
          <Paragraph style={{ marginBottom: 24 }}>
            该功能正在开发中，敬请期待...
          </Paragraph>
          <Space>
            <Button type="primary">编辑资料</Button>
            <Button>修改密码</Button>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default ProfilePage
