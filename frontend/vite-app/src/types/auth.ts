// 用户类型枚举
export enum UserType {
  INDIVIDUAL = 'individual',
  ENTERPRISE = 'enterprise',
  LAWYER = 'lawyer',
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

// 用户信息接口
export interface User {
  id: string
  username: string
  email: string
  full_name?: string
  phone?: string
  user_type: UserType
  status: UserStatus
  email_verified: boolean
  phone_verified: boolean
  created_at: string
  last_login_at?: string
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
}

// 注册请求接口
export interface RegisterRequest {
  username: string
  email: string
  password: string
  full_name?: string
  phone?: string
  user_type?: UserType
}

// 登录响应接口
export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

// 刷新token请求接口
export interface RefreshTokenRequest {
  refresh_token: string
}

// 刷新token响应接口
export interface RefreshTokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

// 密码重置请求接口
export interface PasswordResetRequest {
  email: string
}

// 密码重置确认接口
export interface PasswordResetConfirm {
  token: string
  new_password: string
}

// 邮箱验证请求接口
export interface EmailVerificationRequest {
  email: string
}

// 用户资料更新接口
export interface UserProfileUpdate {
  full_name?: string
  phone?: string
  avatar_url?: string
  bio?: string
  location?: string
  specialization?: string
  license_number?: string
  company_name?: string
  company_position?: string
}
