/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f5f5f5;
}

#root {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

/* 间距工具类 */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 8px !important; }
.mb-2 { margin-bottom: 16px !important; }
.mb-3 { margin-bottom: 24px !important; }
.mb-4 { margin-bottom: 32px !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 8px !important; }
.mt-2 { margin-top: 16px !important; }
.mt-3 { margin-top: 24px !important; }
.mt-4 { margin-top: 32px !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 8px !important; }
.ml-2 { margin-left: 16px !important; }
.ml-3 { margin-left: 24px !important; }
.ml-4 { margin-left: 32px !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 8px !important; }
.mr-2 { margin-right: 16px !important; }
.mr-3 { margin-right: 24px !important; }
.mr-4 { margin-right: 32px !important; }

/* 自定义组件样式 */
.page-container {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 8px;
}

.page-description {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

/* 卡片样式 */
.custom-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.custom-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

/* 表单样式 */
.form-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 40px 24px;
}

.form-title {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 32px;
  color: rgba(0, 0, 0, 0.85);
}

.form-footer {
  text-align: center;
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .form-container {
    padding: 24px 16px;
  }
  
  .page-title {
    font-size: 18px;
  }
}

/* 加载动画 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 错误状态 */
.error-container {
  text-align: center;
  padding: 40px 24px;
}

.error-title {
  font-size: 18px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.error-description {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 24px;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 40px 24px;
  color: rgba(0, 0, 0, 0.45);
}

/* 高亮文本 */
.highlight {
  background-color: #fff2e8;
  color: #fa8c16;
  padding: 2px 4px;
  border-radius: 3px;
}

/* 标签样式 */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 搜索结果样式 */
.search-result-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-result-item:hover {
  background-color: #fafafa;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-title {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 8px;
  text-decoration: none;
}

.search-result-title:hover {
  text-decoration: underline;
}

.search-result-summary {
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.6;
  margin-bottom: 8px;
}

.search-result-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}
