/* 全局样式 */

/* CSS 变量定义 */
:root {
  /* 颜色变量 */
  --color-primary: #1f4e79;
  --color-secondary: #2c5aa0;
  --color-accent: #f39c12;
  --color-success: #27ae60;
  --color-warning: #f39c12;
  --color-error: #e74c3c;
  --color-info: #3498db;
  
  /* 文本颜色 */
  --color-text-primary: #2c3e50;
  --color-text-secondary: #7f8c8d;
  --color-text-disabled: #bdc3c7;
  
  /* 背景颜色 */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8f9fa;
  --color-bg-tertiary: #ecf0f1;
  
  /* 边框颜色 */
  --color-border-light: #e9ecef;
  --color-border-medium: #dee2e6;
  --color-border-dark: #adb5bd;
  
  /* 间距变量 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 圆角变量 */
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  
  /* 阴影变量 */
  --box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
  --box-shadow-md: 0 2px 8px rgba(0, 0, 0, 0.15);
  --box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* 字体变量 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  
  /* 行高变量 */
  --line-height-sm: 1.2;
  --line-height-md: 1.5;
  --line-height-lg: 1.8;
}

/* 重置样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-md);
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* 选择文本样式 */
::selection {
  background-color: var(--color-primary);
  color: white;
}

::-moz-selection {
  background-color: var(--color-primary);
  color: white;
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-secondary);
}

a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 按钮样式增强 */
.ant-btn {
  transition: all 0.2s ease;
}

.ant-btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 卡片样式增强 */
.ant-card {
  box-shadow: var(--box-shadow-sm);
  transition: box-shadow 0.2s ease;
}

.ant-card:hover {
  box-shadow: var(--box-shadow-md);
}

/* 表格样式增强 */
.ant-table {
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: var(--color-bg-secondary);
  border-bottom: 2px solid var(--color-border-light);
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background-color: var(--color-bg-secondary);
}

/* 表单样式增强 */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input:focus,
.ant-input-focused {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(31, 78, 121, 0.2);
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: var(--color-primary);
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(31, 78, 121, 0.2);
}

/* 消息和通知样式 */
.ant-message {
  z-index: 9999;
}

.ant-notification {
  z-index: 9999;
}

/* 模态框样式增强 */
.ant-modal {
  border-radius: var(--border-radius-lg);
}

.ant-modal-header {
  border-bottom: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.ant-modal-footer {
  border-top: 1px solid var(--color-border-light);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* 抽屉样式增强 */
.ant-drawer-header {
  border-bottom: 1px solid var(--color-border-light);
}

.ant-drawer-footer {
  border-top: 1px solid var(--color-border-light);
}

/* 标签样式增强 */
.ant-tag {
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-error {
  color: var(--color-error);
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-secondary {
  background-color: var(--color-bg-secondary);
}

.bg-white {
  background-color: var(--color-bg-primary);
}

.border-light {
  border: 1px solid var(--color-border-light);
}

.border-medium {
  border: 1px solid var(--color-border-medium);
}

.border-dark {
  border: 1px solid var(--color-border-dark);
}

.rounded-sm {
  border-radius: var(--border-radius-sm);
}

.rounded-md {
  border-radius: var(--border-radius-md);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.shadow-sm {
  box-shadow: var(--box-shadow-sm);
}

.shadow-md {
  box-shadow: var(--box-shadow-md);
}

.shadow-lg {
  box-shadow: var(--box-shadow-lg);
}

.p-xs {
  padding: var(--spacing-xs);
}

.p-sm {
  padding: var(--spacing-sm);
}

.p-md {
  padding: var(--spacing-md);
}

.p-lg {
  padding: var(--spacing-lg);
}

.p-xl {
  padding: var(--spacing-xl);
}

.m-xs {
  margin: var(--spacing-xs);
}

.m-sm {
  margin: var(--spacing-sm);
}

.m-md {
  margin: var(--spacing-md);
}

.m-lg {
  margin: var(--spacing-lg);
}

.m-xl {
  margin: var(--spacing-xl);
}

.mb-0 {
  margin-bottom: 0;
}

.mb-xs {
  margin-bottom: var(--spacing-xs);
}

.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-lg {
  margin-bottom: var(--spacing-lg);
}

.mb-xl {
  margin-bottom: var(--spacing-xl);
}

.mt-0 {
  margin-top: 0;
}

.mt-xs {
  margin-top: var(--spacing-xs);
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

.mt-xl {
  margin-top: var(--spacing-xl);
}

/* 响应式工具类 */
@media (max-width: 576px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .hidden-md {
    display: none !important;
  }
}

@media (max-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-in-out;
}

.slide-in-down {
  animation: slideInDown 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
