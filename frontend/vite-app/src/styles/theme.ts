import type { ThemeConfig } from 'antd'

// 主色调配置
export const primaryColors = {
  blue: '#1890ff',
  green: '#52c41a',
  red: '#ff4d4f',
  orange: '#fa8c16',
  purple: '#722ed1',
  cyan: '#13c2c2',
  gold: '#faad14',
}

// 法律主题色彩
export const legalColors = {
  primary: '#1f4e79', // 深蓝色，代表专业和信任
  secondary: '#2c5aa0', // 中蓝色
  accent: '#f39c12', // 金色，代表权威
  success: '#27ae60', // 绿色
  warning: '#f39c12', // 橙色
  error: '#e74c3c', // 红色
  info: '#3498db', // 浅蓝色
  text: {
    primary: '#2c3e50',
    secondary: '#7f8c8d',
    disabled: '#bdc3c7',
  },
  background: {
    primary: '#ffffff',
    secondary: '#f8f9fa',
    tertiary: '#ecf0f1',
  },
  border: {
    light: '#e9ecef',
    medium: '#dee2e6',
    dark: '#adb5bd',
  }
}

// Ant Design 主题配置
export const antdTheme: ThemeConfig = {
  token: {
    // 主色调
    colorPrimary: legalColors.primary,
    colorSuccess: legalColors.success,
    colorWarning: legalColors.warning,
    colorError: legalColors.error,
    colorInfo: legalColors.info,
    
    // 字体
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
    fontSize: 14,
    fontSizeHeading1: 38,
    fontSizeHeading2: 30,
    fontSizeHeading3: 24,
    fontSizeHeading4: 20,
    fontSizeHeading5: 16,
    
    // 圆角
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    
    // 间距
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    
    // 阴影
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    boxShadowSecondary: '0 4px 12px rgba(0, 0, 0, 0.15)',
    
    // 线条
    lineWidth: 1,
    lineType: 'solid',
    
    // 动画
    motionDurationFast: '0.1s',
    motionDurationMid: '0.2s',
    motionDurationSlow: '0.3s',
  },
  components: {
    // 按钮组件
    Button: {
      borderRadius: 6,
      controlHeight: 36,
      paddingContentHorizontal: 16,
    },
    
    // 输入框组件
    Input: {
      borderRadius: 6,
      controlHeight: 36,
      paddingInline: 12,
    },
    
    // 选择器组件
    Select: {
      borderRadius: 6,
      controlHeight: 36,
    },
    
    // 卡片组件
    Card: {
      borderRadius: 8,
      paddingLG: 24,
    },
    
    // 表格组件
    Table: {
      borderRadius: 6,
      headerBg: legalColors.background.secondary,
      headerColor: legalColors.text.primary,
      rowHoverBg: legalColors.background.secondary,
    },
    
    // 菜单组件
    Menu: {
      itemBorderRadius: 6,
      itemHeight: 40,
      itemPaddingInline: 16,
    },
    
    // 布局组件
    Layout: {
      headerBg: legalColors.primary,
      headerColor: '#ffffff',
      siderBg: '#ffffff',
      bodyBg: legalColors.background.secondary,
    },
    
    // 消息组件
    Message: {
      borderRadius: 6,
    },
    
    // 通知组件
    Notification: {
      borderRadius: 8,
    },
    
    // 模态框组件
    Modal: {
      borderRadius: 8,
      headerBg: legalColors.background.primary,
    },
    
    // 抽屉组件
    Drawer: {
      borderRadius: 0,
    },
    
    // 标签组件
    Tag: {
      borderRadius: 4,
    },
    
    // 徽章组件
    Badge: {
      borderRadius: 10,
    },
    
    // 进度条组件
    Progress: {
      remainingColor: legalColors.background.tertiary,
    },
    
    // 分页组件
    Pagination: {
      itemBorderRadius: 6,
    },
    
    // 面包屑组件
    Breadcrumb: {
      itemColor: legalColors.text.secondary,
      lastItemColor: legalColors.text.primary,
      linkColor: legalColors.primary,
      linkHoverColor: legalColors.secondary,
    },
    
    // 步骤条组件
    Steps: {
      colorPrimary: legalColors.primary,
    },
    
    // 时间轴组件
    Timeline: {
      tailColor: legalColors.border.light,
      dotBorderWidth: 2,
    },
    
    // 锚点组件
    Anchor: {
      linkPaddingBlock: 8,
      linkPaddingInlineStart: 16,
    },
    
    // 回到顶部组件
    BackTop: {
      borderRadius: 20,
    },
  },
}

// 暗色主题配置
export const darkTheme: ThemeConfig = {
  ...antdTheme,
  algorithm: 'darkAlgorithm' as any,
  token: {
    ...antdTheme.token,
    colorBgBase: '#141414',
    colorTextBase: '#ffffff',
  },
}

// 紧凑主题配置
export const compactTheme: ThemeConfig = {
  ...antdTheme,
  algorithm: 'compactAlgorithm' as any,
  token: {
    ...antdTheme.token,
    sizeStep: 3,
    sizeUnit: 3,
  },
}

// 主题类型
export type ThemeType = 'light' | 'dark' | 'compact'

// 获取主题配置
export const getThemeConfig = (theme: ThemeType): ThemeConfig => {
  switch (theme) {
    case 'dark':
      return darkTheme
    case 'compact':
      return compactTheme
    default:
      return antdTheme
  }
}

// CSS 变量
export const cssVariables = {
  // 颜色变量
  '--color-primary': legalColors.primary,
  '--color-secondary': legalColors.secondary,
  '--color-accent': legalColors.accent,
  '--color-success': legalColors.success,
  '--color-warning': legalColors.warning,
  '--color-error': legalColors.error,
  '--color-info': legalColors.info,
  
  // 文本颜色
  '--color-text-primary': legalColors.text.primary,
  '--color-text-secondary': legalColors.text.secondary,
  '--color-text-disabled': legalColors.text.disabled,
  
  // 背景颜色
  '--color-bg-primary': legalColors.background.primary,
  '--color-bg-secondary': legalColors.background.secondary,
  '--color-bg-tertiary': legalColors.background.tertiary,
  
  // 边框颜色
  '--color-border-light': legalColors.border.light,
  '--color-border-medium': legalColors.border.medium,
  '--color-border-dark': legalColors.border.dark,
  
  // 间距变量
  '--spacing-xs': '4px',
  '--spacing-sm': '8px',
  '--spacing-md': '16px',
  '--spacing-lg': '24px',
  '--spacing-xl': '32px',
  
  // 圆角变量
  '--border-radius-sm': '4px',
  '--border-radius-md': '6px',
  '--border-radius-lg': '8px',
  
  // 阴影变量
  '--box-shadow-sm': '0 1px 3px rgba(0, 0, 0, 0.12)',
  '--box-shadow-md': '0 2px 8px rgba(0, 0, 0, 0.15)',
  '--box-shadow-lg': '0 4px 12px rgba(0, 0, 0, 0.15)',
  
  // 字体变量
  '--font-size-xs': '12px',
  '--font-size-sm': '14px',
  '--font-size-md': '16px',
  '--font-size-lg': '18px',
  '--font-size-xl': '20px',
  
  // 行高变量
  '--line-height-sm': '1.2',
  '--line-height-md': '1.5',
  '--line-height-lg': '1.8',
}
