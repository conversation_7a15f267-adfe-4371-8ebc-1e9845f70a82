import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import Cookies from 'js-cookie'

import { User, LoginRequest, RegisterRequest } from '@/types/auth'
import { authService } from '@/services/auth'

interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>
  register: (userData: RegisterRequest) => Promise<void>
  logout: () => void
  refreshAuth: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
  updateUser: (user: Partial<User>) => void
}

type AuthStore = AuthState & AuthActions

const TOKEN_KEY = 'auth_token'
const REFRESH_TOKEN_KEY = 'refresh_token'

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: Cookies.get(TOKEN_KEY) || null,
      refreshToken: Cookies.get(REFRESH_TOKEN_KEY) || null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // 登录
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authService.login(credentials)
          const { access_token, refresh_token, user } = response
          
          // 保存token到cookie
          Cookies.set(TOKEN_KEY, access_token, { expires: 7 })
          Cookies.set(REFRESH_TOKEN_KEY, refresh_token, { expires: 30 })
          
          set({
            user,
            token: access_token,
            refreshToken: refresh_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '登录失败',
          })
          throw error
        }
      },

      // 注册
      register: async (userData: RegisterRequest) => {
        set({ isLoading: true, error: null })
        
        try {
          const user = await authService.register(userData)
          
          set({
            user,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '注册失败',
          })
          throw error
        }
      },

      // 登出
      logout: () => {
        // 清除cookie
        Cookies.remove(TOKEN_KEY)
        Cookies.remove(REFRESH_TOKEN_KEY)
        
        // 调用后端登出接口
        const { token } = get()
        if (token) {
          authService.logout().catch(console.error)
        }
        
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          error: null,
        })
      },

      // 刷新认证状态
      refreshAuth: async () => {
        const { refreshToken } = get()
        
        if (!refreshToken) {
          get().logout()
          return
        }
        
        try {
          const response = await authService.refreshToken(refreshToken)
          const { access_token } = response
          
          // 更新token
          Cookies.set(TOKEN_KEY, access_token, { expires: 7 })
          
          set({
            token: access_token,
            isAuthenticated: true,
            error: null,
          })
          
          // 获取用户信息
          try {
            const user = await authService.getCurrentUser()
            set({ user })
          } catch (error) {
            console.error('获取用户信息失败:', error)
          }
        } catch (error: any) {
          console.error('刷新token失败:', error)
          get().logout()
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null })
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData },
          })
        }
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// 初始化认证状态
export const initializeAuth = async () => {
  const token = Cookies.get(TOKEN_KEY)
  const refreshToken = Cookies.get(REFRESH_TOKEN_KEY)
  
  if (token && refreshToken) {
    const store = useAuthStore.getState()
    
    try {
      // 验证token是否有效
      const user = await authService.getCurrentUser()
      store.updateUser(user)
      useAuthStore.setState({
        token,
        refreshToken,
        isAuthenticated: true,
      })
    } catch (error) {
      // token无效，尝试刷新
      await store.refreshAuth()
    }
  }
}
