import React from 'react'
import { Ava<PERSON>, Typography, Space, Tag, Button, Divider } from 'antd'
import { UserOutlined, RobotOutlined, CopyOutlined, LikeOutlined, DislikeOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'

const { Text, Paragraph } = Typography

interface ChatMessageProps {
  message: {
    id: string
    type: 'user' | 'assistant'
    content: string
    timestamp: string
    category?: string
    confidence?: number
    sources?: Array<{
      title: string
      url?: string
      type: string
    }>
  }
  onCopy?: (content: string) => void
  onLike?: (messageId: string) => void
  onDislike?: (messageId: string) => void
  showActions?: boolean
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onCopy,
  onLike,
  onDislike,
  showActions = true,
}) => {
  const isUser = message.type === 'user'

  const handleCopy = () => {
    navigator.clipboard.writeText(message.content)
    onCopy?.(message.content)
  }

  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'default'
    if (confidence >= 0.8) return 'green'
    if (confidence >= 0.6) return 'orange'
    return 'red'
  }

  const getConfidenceText = (confidence?: number) => {
    if (!confidence) return '未知'
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: isUser ? 'row-reverse' : 'row',
        alignItems: 'flex-start',
        marginBottom: 24,
        gap: 12,
      }}
    >
      {/* 头像 */}
      <Avatar
        size={40}
        icon={isUser ? <UserOutlined /> : <RobotOutlined />}
        style={{
          backgroundColor: isUser ? '#1890ff' : '#52c41a',
          flexShrink: 0,
        }}
      />

      {/* 消息内容 */}
      <div
        style={{
          maxWidth: '70%',
          minWidth: '200px',
        }}
      >
        {/* 消息气泡 */}
        <div
          style={{
            padding: '12px 16px',
            borderRadius: '12px',
            backgroundColor: isUser ? '#1890ff' : '#f5f5f5',
            color: isUser ? 'white' : 'rgba(0, 0, 0, 0.85)',
            wordBreak: 'break-word',
          }}
        >
          <Paragraph
            style={{
              margin: 0,
              color: isUser ? 'white' : 'rgba(0, 0, 0, 0.85)',
            }}
          >
            {message.content}
          </Paragraph>
        </div>

        {/* 助手消息的额外信息 */}
        {!isUser && (
          <div style={{ marginTop: 8 }}>
            {/* 分类和置信度 */}
            <Space size={8}>
              {message.category && (
                <Tag size="small" color="blue">
                  {message.category}
                </Tag>
              )}
              {message.confidence !== undefined && (
                <Tag size="small" color={getConfidenceColor(message.confidence)}>
                  置信度：{getConfidenceText(message.confidence)}
                </Tag>
              )}
            </Space>

            {/* 参考来源 */}
            {message.sources && message.sources.length > 0 && (
              <div style={{ marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  参考来源：
                </Text>
                <div style={{ marginTop: 4 }}>
                  {message.sources.map((source, index) => (
                    <div key={index} style={{ marginBottom: 4 }}>
                      <Tag size="small" color="default">
                        {source.type}
                      </Tag>
                      {source.url ? (
                        <a
                          href={source.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ fontSize: 12 }}
                        >
                          {source.title}
                        </a>
                      ) : (
                        <Text style={{ fontSize: 12 }}>{source.title}</Text>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 时间戳和操作按钮 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: 8,
          }}
        >
          <Text type="secondary" style={{ fontSize: 12 }}>
            {dayjs(message.timestamp).format('HH:mm')}
          </Text>

          {showActions && (
            <Space size={4}>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={handleCopy}
                style={{ fontSize: 12 }}
              />
              {!isUser && (
                <>
                  <Button
                    type="text"
                    size="small"
                    icon={<LikeOutlined />}
                    onClick={() => onLike?.(message.id)}
                    style={{ fontSize: 12 }}
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<DislikeOutlined />}
                    onClick={() => onDislike?.(message.id)}
                    style={{ fontSize: 12 }}
                  />
                </>
              )}
            </Space>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChatMessage
