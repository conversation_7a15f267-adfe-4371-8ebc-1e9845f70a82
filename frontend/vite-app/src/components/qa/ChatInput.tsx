import React, { useState, useRef } from 'react'
import { Input, Button, Space, Upload, Tooltip, message } from 'antd'
import { SendOutlined, PaperClipOutlined, MicrophoneOutlined } from '@ant-design/icons'
import type { UploadFile } from 'antd'

const { TextArea } = Input

interface ChatInputProps {
  onSend?: (content: string, attachments?: UploadFile[]) => void
  loading?: boolean
  placeholder?: string
  maxLength?: number
  allowAttachment?: boolean
  allowVoice?: boolean
  disabled?: boolean
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSend,
  loading = false,
  placeholder = '请输入您的法律问题...',
  maxLength = 1000,
  allowAttachment = true,
  allowVoice = false,
  disabled = false,
}) => {
  const [content, setContent] = useState('')
  const [attachments, setAttachments] = useState<UploadFile[]>([])
  const [isRecording, setIsRecording] = useState(false)
  const textAreaRef = useRef<any>(null)

  const handleSend = () => {
    if (!content.trim() && attachments.length === 0) {
      message.warning('请输入问题内容或上传附件')
      return
    }

    onSend?.(content.trim(), attachments)
    setContent('')
    setAttachments([])
    
    // 聚焦到输入框
    setTimeout(() => {
      textAreaRef.current?.focus()
    }, 100)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (!loading && !disabled) {
        handleSend()
      }
    }
  }

  const handleAttachmentChange = (info: any) => {
    let fileList = [...info.fileList]
    
    // 限制文件数量
    fileList = fileList.slice(-3)
    
    // 限制文件大小 (10MB)
    fileList = fileList.filter(file => {
      if (file.size && file.size > 10 * 1024 * 1024) {
        message.error(`${file.name} 文件大小超过10MB`)
        return false
      }
      return true
    })

    setAttachments(fileList)
  }

  const handleVoiceRecord = () => {
    if (isRecording) {
      // 停止录音
      setIsRecording(false)
      // TODO: 实现语音识别功能
      message.info('语音识别功能开发中...')
    } else {
      // 开始录音
      setIsRecording(true)
      // TODO: 实现录音功能
      message.info('开始录音...')
    }
  }

  const uploadProps = {
    beforeUpload: () => false, // 阻止自动上传
    onChange: handleAttachmentChange,
    fileList: attachments,
    multiple: true,
    showUploadList: {
      showPreviewIcon: false,
      showDownloadIcon: false,
    },
  }

  return (
    <div
      style={{
        padding: '16px',
        borderTop: '1px solid #f0f0f0',
        backgroundColor: '#fff',
      }}
    >
      {/* 附件列表 */}
      {attachments.length > 0 && (
        <div style={{ marginBottom: 12 }}>
          <Space wrap>
            {attachments.map((file, index) => (
              <div
                key={index}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '4px',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                }}
              >
                <PaperClipOutlined />
                <span>{file.name}</span>
                <Button
                  type="text"
                  size="small"
                  onClick={() => {
                    setAttachments(attachments.filter((_, i) => i !== index))
                  }}
                  style={{ padding: 0, minWidth: 'auto', height: 'auto' }}
                >
                  ×
                </Button>
              </div>
            ))}
          </Space>
        </div>
      )}

      {/* 输入区域 */}
      <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-end' }}>
        <div style={{ flex: 1 }}>
          <TextArea
            ref={textAreaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            maxLength={maxLength}
            showCount
            autoSize={{ minRows: 1, maxRows: 4 }}
            disabled={disabled || loading}
          />
        </div>

        <Space>
          {/* 附件上传 */}
          {allowAttachment && (
            <Upload {...uploadProps}>
              <Tooltip title="上传附件">
                <Button
                  type="text"
                  icon={<PaperClipOutlined />}
                  disabled={disabled || loading}
                />
              </Tooltip>
            </Upload>
          )}

          {/* 语音输入 */}
          {allowVoice && (
            <Tooltip title={isRecording ? '停止录音' : '语音输入'}>
              <Button
                type="text"
                icon={<MicrophoneOutlined />}
                onClick={handleVoiceRecord}
                disabled={disabled || loading}
                style={{
                  color: isRecording ? '#ff4d4f' : undefined,
                }}
              />
            </Tooltip>
          )}

          {/* 发送按钮 */}
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            loading={loading}
            disabled={disabled || (!content.trim() && attachments.length === 0)}
          >
            发送
          </Button>
        </Space>
      </div>
    </div>
  )
}

export default ChatInput
