import React from 'react'
import { <PERSON>, Tag, Space, Typo<PERSON>, Button, Divider } from 'antd'
import { EyeOutlined, EditOutlined, CopyOutlined, DownloadOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text, Paragraph } = Typography

interface TemplateCardProps {
  template: {
    id: string
    name: string
    category: string
    description?: string
    tags?: string[]
    usage_count: number
    created_at: string
    updated_at: string
    is_premium?: boolean
    difficulty_level?: 'easy' | 'medium' | 'hard'
  }
  onView?: (templateId: string) => void
  onEdit?: (templateId: string) => void
  onUse?: (templateId: string) => void
  onDownload?: (templateId: string) => void
  showActions?: boolean
  hoverable?: boolean
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onView,
  onEdit,
  onUse,
  onDownload,
  showActions = true,
  hoverable = true,
}) => {
  const getCategoryColor = (category: string) => {
    const colorMap: Record<string, string> = {
      labor: 'blue',
      sales: 'green',
      service: 'orange',
      lease: 'purple',
      partnership: 'cyan',
      loan: 'red',
      other: 'default',
    }
    return colorMap[category] || 'default'
  }

  const getDifficultyColor = (level?: string) => {
    const colorMap: Record<string, string> = {
      easy: 'green',
      medium: 'orange',
      hard: 'red',
    }
    return colorMap[level || ''] || 'default'
  }

  const getDifficultyText = (level?: string) => {
    const textMap: Record<string, string> = {
      easy: '简单',
      medium: '中等',
      hard: '复杂',
    }
    return textMap[level || ''] || '未知'
  }

  const actions = showActions ? [
    <Button
      key="view"
      type="text"
      icon={<EyeOutlined />}
      onClick={() => onView?.(template.id)}
    >
      预览
    </Button>,
    <Button
      key="use"
      type="text"
      icon={<CopyOutlined />}
      onClick={() => onUse?.(template.id)}
    >
      使用
    </Button>,
    <Button
      key="download"
      type="text"
      icon={<DownloadOutlined />}
      onClick={() => onDownload?.(template.id)}
    >
      下载
    </Button>,
  ] : undefined

  return (
    <Card
      hoverable={hoverable}
      actions={actions}
      style={{ marginBottom: 16 }}
    >
      <div>
        {/* 模板标题 */}
        <div style={{ marginBottom: 12 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 4 }}>
            {template.name}
            {template.is_premium && (
              <Tag color="gold" style={{ marginLeft: 8 }}>
                高级
              </Tag>
            )}
          </Title>
        </div>

        {/* 标签区域 */}
        <div style={{ marginBottom: 12 }}>
          <Space wrap>
            <Tag color={getCategoryColor(template.category)}>
              {template.category}
            </Tag>
            {template.difficulty_level && (
              <Tag color={getDifficultyColor(template.difficulty_level)}>
                {getDifficultyText(template.difficulty_level)}
              </Tag>
            )}
            <Tag>
              使用次数：{template.usage_count}
            </Tag>
          </Space>
        </div>

        {/* 模板描述 */}
        {template.description && (
          <div style={{ marginBottom: 12 }}>
            <Paragraph
              ellipsis={{ rows: 2, expandable: true, symbol: '展开' }}
              style={{ margin: 0, color: 'rgba(0, 0, 0, 0.65)' }}
            >
              {template.description}
            </Paragraph>
          </div>
        )}

        {/* 标签 */}
        {template.tags && template.tags.length > 0 && (
          <div style={{ marginBottom: 12 }}>
            <Text style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
              标签：
            </Text>
            <Space wrap size={[4, 4]}>
              {template.tags.map((tag, index) => (
                <Tag key={index} size="small">
                  {tag}
                </Tag>
              ))}
            </Space>
          </div>
        )}

        {/* 时间信息 */}
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            创建时间：{dayjs(template.created_at).format('YYYY-MM-DD')}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            更新时间：{dayjs(template.updated_at).format('YYYY-MM-DD')}
          </Text>
        </div>
      </div>
    </Card>
  )
}

export default TemplateCard
