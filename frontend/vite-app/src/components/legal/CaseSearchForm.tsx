import React from 'react'
import { Form, Input, Select, DatePicker, Button, Space, Card, Row, Col } from 'antd'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { Option } = Select

interface CaseSearchFormProps {
  onSearch?: (values: any) => void
  onReset?: () => void
  loading?: boolean
  initialValues?: any
}

const CaseSearchForm: React.FC<CaseSearchFormProps> = ({
  onSearch,
  onReset,
  loading = false,
  initialValues,
}) => {
  const [form] = Form.useForm()

  const handleSearch = (values: any) => {
    // 处理日期范围
    if (values.dateRange) {
      values.startDate = values.dateRange[0]?.format('YYYY-MM-DD')
      values.endDate = values.dateRange[1]?.format('YYYY-MM-DD')
      delete values.dateRange
    }

    // 过滤空值
    const filteredValues = Object.fromEntries(
      Object.entries(values).filter(([_, value]) => value !== undefined && value !== '')
    )

    onSearch?.(filteredValues)
  }

  const handleReset = () => {
    form.resetFields()
    onReset?.()
  }

  return (
    <Card title="案例搜索" style={{ marginBottom: 16 }}>
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={handleSearch}
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="关键词"
              name="keyword"
            >
              <Input
                placeholder="请输入案件标题、关键词等"
                allowClear
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="案件编号"
              name="caseNumber"
            >
              <Input
                placeholder="请输入案件编号"
                allowClear
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="案件类型"
              name="caseType"
            >
              <Select
                placeholder="请选择案件类型"
                allowClear
              >
                <Option value="civil">民事案件</Option>
                <Option value="criminal">刑事案件</Option>
                <Option value="administrative">行政案件</Option>
                <Option value="commercial">商事案件</Option>
                <Option value="labor">劳动争议</Option>
                <Option value="intellectual_property">知识产权</Option>
                <Option value="environmental">环境保护</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="审理法院"
              name="courtName"
            >
              <Input
                placeholder="请输入法院名称"
                allowClear
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="判例价值"
              name="precedentValue"
            >
              <Select
                placeholder="请选择判例价值"
                allowClear
              >
                <Option value="高">高</Option>
                <Option value="中">中</Option>
                <Option value="低">低</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="判决日期"
              name="dateRange"
            >
              <RangePicker
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
                format="YYYY-MM-DD"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={24}>
            <Form.Item style={{ marginBottom: 0 }}>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                >
                  搜索
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleReset}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  )
}

export default CaseSearchForm
