import React from 'react'
import { Card, Tag, Space, Typography, Divider, Button } from 'antd'
import { EyeOutlined, EditOutlined, DeleteOutlined, CalendarOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text, Paragraph } = Typography

interface CaseCardProps {
  case: {
    id: string
    case_number: string
    title: string
    court_name: string
    case_type: string
    judgment_date?: string
    case_summary?: string
    keywords?: string[]
    precedent_value?: string
    citation_count: string
    created_at: string
  }
  onView?: (caseId: string) => void
  onEdit?: (caseId: string) => void
  onDelete?: (caseId: string) => void
  showActions?: boolean
  hoverable?: boolean
}

const CaseCard: React.FC<CaseCardProps> = ({
  case: caseData,
  onView,
  onEdit,
  onDelete,
  showActions = true,
  hoverable = true,
}) => {
  const getCaseTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      civil: 'blue',
      criminal: 'red',
      administrative: 'green',
      commercial: 'orange',
      labor: 'purple',
      intellectual_property: 'cyan',
      environmental: 'lime',
      other: 'default',
    }
    return colorMap[type] || 'default'
  }

  const getPrecedentValueColor = (value?: string) => {
    const colorMap: Record<string, string> = {
      高: 'red',
      中: 'orange',
      低: 'default',
    }
    return colorMap[value || ''] || 'default'
  }

  const actions = showActions ? [
    <Button
      key="view"
      type="text"
      icon={<EyeOutlined />}
      onClick={() => onView?.(caseData.id)}
    >
      查看
    </Button>,
    <Button
      key="edit"
      type="text"
      icon={<EditOutlined />}
      onClick={() => onEdit?.(caseData.id)}
    >
      编辑
    </Button>,
    <Button
      key="delete"
      type="text"
      danger
      icon={<DeleteOutlined />}
      onClick={() => onDelete?.(caseData.id)}
    >
      删除
    </Button>,
  ] : undefined

  return (
    <Card
      hoverable={hoverable}
      actions={actions}
      style={{ marginBottom: 16 }}
    >
      <div>
        {/* 案件标题和编号 */}
        <div style={{ marginBottom: 12 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 4 }}>
            {caseData.title}
          </Title>
          <Text type="secondary" style={{ fontSize: 12 }}>
            案件编号：{caseData.case_number}
          </Text>
        </div>

        {/* 标签区域 */}
        <div style={{ marginBottom: 12 }}>
          <Space wrap>
            <Tag color={getCaseTypeColor(caseData.case_type)}>
              {caseData.case_type}
            </Tag>
            {caseData.precedent_value && (
              <Tag color={getPrecedentValueColor(caseData.precedent_value)}>
                判例价值：{caseData.precedent_value}
              </Tag>
            )}
            <Tag>
              引用次数：{caseData.citation_count}
            </Tag>
          </Space>
        </div>

        {/* 法院和日期信息 */}
        <div style={{ marginBottom: 12 }}>
          <Space split={<Divider type="vertical" />}>
            <Text>
              <strong>审理法院：</strong>{caseData.court_name}
            </Text>
            {caseData.judgment_date && (
              <Text>
                <CalendarOutlined style={{ marginRight: 4 }} />
                {dayjs(caseData.judgment_date).format('YYYY-MM-DD')}
              </Text>
            )}
          </Space>
        </div>

        {/* 案件摘要 */}
        {caseData.case_summary && (
          <div style={{ marginBottom: 12 }}>
            <Paragraph
              ellipsis={{ rows: 2, expandable: true, symbol: '展开' }}
              style={{ margin: 0, color: 'rgba(0, 0, 0, 0.65)' }}
            >
              {caseData.case_summary}
            </Paragraph>
          </div>
        )}

        {/* 关键词 */}
        {caseData.keywords && caseData.keywords.length > 0 && (
          <div style={{ marginBottom: 8 }}>
            <Text style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
              关键词：
            </Text>
            <Space wrap size={[4, 4]}>
              {caseData.keywords.map((keyword, index) => (
                <Tag key={index} size="small">
                  {keyword}
                </Tag>
              ))}
            </Space>
          </div>
        )}

        {/* 创建时间 */}
        <div style={{ textAlign: 'right' }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            创建时间：{dayjs(caseData.created_at).format('YYYY-MM-DD HH:mm')}
          </Text>
        </div>
      </div>
    </Card>
  )
}

export default CaseCard
