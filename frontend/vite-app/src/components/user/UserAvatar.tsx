import React from 'react'
import { <PERSON><PERSON>, Badge, Tooltip } from 'antd'
import { UserOutlined } from '@ant-design/icons'

interface UserAvatarProps {
  user?: {
    id: string
    username: string
    full_name?: string
    avatar_url?: string
    status?: 'active' | 'inactive' | 'suspended'
    user_type?: 'individual' | 'enterprise' | 'lawyer'
    is_online?: boolean
  }
  size?: number | 'small' | 'default' | 'large'
  showStatus?: boolean
  showTooltip?: boolean
  onClick?: () => void
  style?: React.CSSProperties
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'default',
  showStatus = false,
  showTooltip = true,
  onClick,
  style,
}) => {
  if (!user) {
    return (
      <Avatar
        size={size}
        icon={<UserOutlined />}
        style={style}
        onClick={onClick}
      />
    )
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active':
        return '#52c41a'
      case 'inactive':
        return '#d9d9d9'
      case 'suspended':
        return '#ff4d4f'
      default:
        return '#d9d9d9'
    }
  }

  const getUserTypeText = (userType?: string) => {
    switch (userType) {
      case 'individual':
        return '个人用户'
      case 'enterprise':
        return '企业用户'
      case 'lawyer':
        return '律师用户'
      default:
        return '未知类型'
    }
  }

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'active':
        return '正常'
      case 'inactive':
        return '未激活'
      case 'suspended':
        return '已暂停'
      default:
        return '未知状态'
    }
  }

  const avatar = (
    <Avatar
      size={size}
      src={user.avatar_url}
      style={{
        backgroundColor: user.avatar_url ? undefined : '#1890ff',
        cursor: onClick ? 'pointer' : 'default',
        ...style,
      }}
      onClick={onClick}
    >
      {!user.avatar_url && (user.full_name || user.username).charAt(0).toUpperCase()}
    </Avatar>
  )

  const avatarWithStatus = showStatus ? (
    <Badge
      dot
      color={user.is_online ? '#52c41a' : getStatusColor(user.status)}
      offset={[-8, 8]}
    >
      {avatar}
    </Badge>
  ) : avatar

  if (showTooltip) {
    return (
      <Tooltip
        title={
          <div>
            <div><strong>{user.full_name || user.username}</strong></div>
            <div style={{ fontSize: 12, opacity: 0.8 }}>
              {getUserTypeText(user.user_type)}
            </div>
            {showStatus && (
              <div style={{ fontSize: 12, opacity: 0.8 }}>
                状态：{getStatusText(user.status)}
                {user.is_online && ' (在线)'}
              </div>
            )}
          </div>
        }
      >
        {avatarWithStatus}
      </Tooltip>
    )
  }

  return avatarWithStatus
}

export default UserAvatar
