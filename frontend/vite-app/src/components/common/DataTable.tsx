import React from 'react'
import { Table, TableProps, Card, Space, Button, Tooltip } from 'antd'
import { ReloadOutlined, SettingOutlined } from '@ant-design/icons'
import LoadingSpinner from './LoadingSpinner'

interface DataTableProps<T = any> extends Omit<TableProps<T>, 'loading'> {
  title?: string
  loading?: boolean
  error?: string | null
  showReload?: boolean
  showSettings?: boolean
  onReload?: () => void
  onSettings?: () => void
  extra?: React.ReactNode
  cardProps?: any
  emptyText?: string
}

function DataTable<T extends Record<string, any>>({
  title,
  loading = false,
  error,
  showReload = true,
  showSettings = false,
  onReload,
  onSettings,
  extra,
  cardProps,
  emptyText = '暂无数据',
  ...tableProps
}: DataTableProps<T>) {
  const headerExtra = (
    <Space>
      {extra}
      {showReload && (
        <Tooltip title="刷新">
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={onReload}
            loading={loading}
          />
        </Tooltip>
      )}
      {showSettings && (
        <Tooltip title="设置">
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={onSettings}
          />
        </Tooltip>
      )}
    </Space>
  )

  if (error) {
    return (
      <Card
        title={title}
        extra={headerExtra}
        {...cardProps}
      >
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#ff4d4f' }}>
          <div style={{ marginBottom: 16 }}>数据加载失败</div>
          <div style={{ fontSize: 14, color: 'rgba(0, 0, 0, 0.45)' }}>
            {error}
          </div>
          {showReload && (
            <Button
              type="primary"
              onClick={onReload}
              style={{ marginTop: 16 }}
            >
              重新加载
            </Button>
          )}
        </div>
      </Card>
    )
  }

  return (
    <Card
      title={title}
      extra={headerExtra}
      {...cardProps}
    >
      <Table
        {...tableProps}
        loading={{
          spinning: loading,
          indicator: <LoadingSpinner />,
        }}
        locale={{
          emptyText: emptyText,
        }}
        scroll={{
          x: 'max-content',
          ...tableProps.scroll,
        }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          ...tableProps.pagination,
        }}
      />
    </Card>
  )
}

export default DataTable
