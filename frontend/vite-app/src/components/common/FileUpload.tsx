import React, { useState } from 'react'
import { Upload, Button, message, Progress, Space } from 'antd'
import { UploadOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons'
import type { UploadProps, UploadFile } from 'antd'

interface FileUploadProps {
  value?: UploadFile[]
  onChange?: (fileList: UploadFile[]) => void
  maxCount?: number
  maxSize?: number // MB
  accept?: string
  multiple?: boolean
  disabled?: boolean
  showUploadList?: boolean
  listType?: 'text' | 'picture' | 'picture-card'
  action?: string
  headers?: Record<string, string>
  beforeUpload?: (file: File) => boolean | Promise<boolean>
  onPreview?: (file: UploadFile) => void
  onRemove?: (file: UploadFile) => boolean | Promise<boolean>
  customRequest?: (options: any) => void
}

const FileUpload: React.FC<FileUploadProps> = ({
  value = [],
  onChange,
  maxCount = 1,
  maxSize = 10, // 10MB
  accept,
  multiple = false,
  disabled = false,
  showUploadList = true,
  listType = 'text',
  action = '/api/v1/upload',
  headers,
  beforeUpload,
  onPreview,
  onRemove,
  customRequest,
}) => {
  const [uploading, setUploading] = useState(false)

  const handleChange: UploadProps['onChange'] = (info) => {
    let fileList = [...info.fileList]

    // 限制文件数量
    if (maxCount > 0) {
      fileList = fileList.slice(-maxCount)
    }

    // 更新文件状态
    fileList = fileList.map(file => {
      if (file.response) {
        // 处理上传成功的响应
        file.url = file.response.url
      }
      return file
    })

    onChange?.(fileList)

    // 处理上传状态
    if (info.file.status === 'uploading') {
      setUploading(true)
    } else {
      setUploading(false)
    }

    if (info.file.status === 'done') {
      message.success(`${info.file.name} 文件上传成功`)
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 文件上传失败`)
    }
  }

  const handleBeforeUpload = (file: File) => {
    // 检查文件大小
    if (maxSize > 0 && file.size / 1024 / 1024 > maxSize) {
      message.error(`文件大小不能超过 ${maxSize}MB`)
      return false
    }

    // 检查文件类型
    if (accept) {
      const acceptTypes = accept.split(',').map(type => type.trim())
      const fileType = file.type
      const fileName = file.name
      const fileExtension = fileName.substring(fileName.lastIndexOf('.'))

      const isValidType = acceptTypes.some(type => {
        if (type.startsWith('.')) {
          return fileExtension.toLowerCase() === type.toLowerCase()
        } else {
          return fileType.includes(type.replace('*', ''))
        }
      })

      if (!isValidType) {
        message.error(`不支持的文件类型，请上传 ${accept} 格式的文件`)
        return false
      }
    }

    // 自定义验证
    if (beforeUpload) {
      return beforeUpload(file)
    }

    return true
  }

  const handlePreview = (file: UploadFile) => {
    if (onPreview) {
      onPreview(file)
    } else if (file.url || file.preview) {
      window.open(file.url || file.preview)
    }
  }

  const handleRemove = (file: UploadFile) => {
    if (onRemove) {
      return onRemove(file)
    }
    return true
  }

  const uploadProps: UploadProps = {
    action,
    headers: {
      authorization: `Bearer ${localStorage.getItem('auth_token')}`,
      ...headers,
    },
    fileList: value,
    onChange: handleChange,
    beforeUpload: handleBeforeUpload,
    onPreview: handlePreview,
    onRemove: handleRemove,
    multiple: multiple && maxCount !== 1,
    disabled,
    showUploadList: showUploadList ? {
      showPreviewIcon: true,
      showRemoveIcon: !disabled,
      showDownloadIcon: false,
    } : false,
    listType,
    customRequest,
  }

  const canUpload = !disabled && (maxCount === 0 || value.length < maxCount)

  if (listType === 'picture-card') {
    return (
      <Upload {...uploadProps}>
        {canUpload && (
          <div>
            <UploadOutlined />
            <div style={{ marginTop: 8 }}>上传文件</div>
          </div>
        )}
      </Upload>
    )
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Upload {...uploadProps}>
        {canUpload && (
          <Button icon={<UploadOutlined />} loading={uploading} disabled={disabled}>
            {uploading ? '上传中...' : '选择文件'}
          </Button>
        )}
      </Upload>
      
      {maxSize > 0 && (
        <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>
          文件大小不超过 {maxSize}MB
          {accept && `，支持格式：${accept}`}
        </div>
      )}
    </Space>
  )
}

export default FileUpload
