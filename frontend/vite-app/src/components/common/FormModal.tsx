import React, { useEffect } from 'react'
import { Modal, Form, Button, Space } from 'antd'
import { FormInstance } from 'antd/es/form'

interface FormModalProps {
  open: boolean
  title: string
  onOk?: (values: any) => void | Promise<void>
  onCancel?: () => void
  loading?: boolean
  width?: number
  centered?: boolean
  children: React.ReactNode
  form?: FormInstance
  initialValues?: any
  okText?: string
  cancelText?: string
  destroyOnClose?: boolean
  maskClosable?: boolean
  layout?: 'horizontal' | 'vertical' | 'inline'
  labelCol?: any
  wrapperCol?: any
}

const FormModal: React.FC<FormModalProps> = ({
  open,
  title,
  onOk,
  onCancel,
  loading = false,
  width = 520,
  centered = true,
  children,
  form: externalForm,
  initialValues,
  okText = '确定',
  cancelText = '取消',
  destroyOnClose = true,
  maskClosable = false,
  layout = 'vertical',
  labelCol,
  wrapperCol,
}) => {
  const [form] = Form.useForm()
  const formInstance = externalForm || form

  useEffect(() => {
    if (open && initialValues) {
      formInstance.setFieldsValue(initialValues)
    }
  }, [open, initialValues, formInstance])

  const handleOk = async () => {
    try {
      const values = await formInstance.validateFields()
      await onOk?.(values)
    } catch (error) {
      console.error('Form validation failed:', error)
    }
  }

  const handleCancel = () => {
    if (!loading) {
      formInstance.resetFields()
      onCancel?.()
    }
  }

  return (
    <Modal
      open={open}
      title={title}
      onCancel={handleCancel}
      width={width}
      centered={centered}
      destroyOnClose={destroyOnClose}
      maskClosable={!loading && maskClosable}
      footer={
        <Space>
          <Button onClick={handleCancel} disabled={loading}>
            {cancelText}
          </Button>
          <Button type="primary" onClick={handleOk} loading={loading}>
            {okText}
          </Button>
        </Space>
      }
    >
      <Form
        form={formInstance}
        layout={layout}
        labelCol={labelCol}
        wrapperCol={wrapperCol}
        initialValues={initialValues}
        preserve={false}
      >
        {children}
      </Form>
    </Modal>
  )
}

export default FormModal
