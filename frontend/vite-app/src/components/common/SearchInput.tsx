import React, { useState, useCallback } from 'react'
import { Input, Button, Space } from 'antd'
import { SearchOutlined, ClearOutlined } from '@ant-design/icons'
import { debounce } from 'lodash-es'

interface SearchInputProps {
  placeholder?: string
  onSearch?: (value: string) => void
  onClear?: () => void
  loading?: boolean
  allowClear?: boolean
  debounceMs?: number
  size?: 'small' | 'middle' | 'large'
  style?: React.CSSProperties
  className?: string
  defaultValue?: string
  showSearchButton?: boolean
}

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = '请输入搜索关键词',
  onSearch,
  onClear,
  loading = false,
  allowClear = true,
  debounceMs = 300,
  size = 'middle',
  style,
  className,
  defaultValue = '',
  showSearchButton = false,
}) => {
  const [value, setValue] = useState(defaultValue)

  // 防抖搜索
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      onSearch?.(searchValue)
    }, debounceMs),
    [onSearch, debounceMs]
  )

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)
    
    if (debounceMs > 0) {
      debouncedSearch(newValue)
    } else {
      onSearch?.(newValue)
    }
  }

  const handleSearch = () => {
    onSearch?.(value)
  }

  const handleClear = () => {
    setValue('')
    onClear?.()
    onSearch?.('')
  }

  const handlePressEnter = () => {
    onSearch?.(value)
  }

  if (showSearchButton) {
    return (
      <Space.Compact style={style} className={className}>
        <Input
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onPressEnter={handlePressEnter}
          size={size}
          allowClear={allowClear}
          suffix={
            allowClear && value ? (
              <ClearOutlined
                onClick={handleClear}
                style={{ cursor: 'pointer', color: '#bfbfbf' }}
              />
            ) : null
          }
        />
        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={handleSearch}
          loading={loading}
          size={size}
        >
          搜索
        </Button>
      </Space.Compact>
    )
  }

  return (
    <Input
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      onPressEnter={handlePressEnter}
      size={size}
      style={style}
      className={className}
      prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
      suffix={
        allowClear && value ? (
          <ClearOutlined
            onClick={handleClear}
            style={{ cursor: 'pointer', color: '#bfbfbf' }}
          />
        ) : null
      }
      loading={loading}
    />
  )
}

export default SearchInput
