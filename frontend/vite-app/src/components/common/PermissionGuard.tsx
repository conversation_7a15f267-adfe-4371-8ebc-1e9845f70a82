import React from 'react'
import { Result, Button } from 'antd'
import { useAuthStore } from '@/store/auth'

interface PermissionGuardProps {
  permissions?: string[]
  roles?: string[]
  requireAll?: boolean
  fallback?: React.ReactNode
  children: React.ReactNode
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permissions = [],
  roles = [],
  requireAll = false,
  fallback,
  children,
}) => {
  const { user } = useAuthStore()

  // 如果没有用户信息，显示未授权
  if (!user) {
    return (
      fallback || (
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您没有权限访问此页面。"
          extra={
            <Button type="primary" onClick={() => window.location.href = '/login'}>
              去登录
            </Button>
          }
        />
      )
    )
  }

  // TODO: 实现实际的权限检查逻辑
  // 这里需要根据后端返回的用户权限信息进行检查
  const userPermissions: string[] = [] // 从用户信息中获取权限列表
  const userRoles: string[] = [] // 从用户信息中获取角色列表

  // 检查权限
  if (permissions.length > 0) {
    const hasPermission = requireAll
      ? permissions.every(permission => userPermissions.includes(permission))
      : permissions.some(permission => userPermissions.includes(permission))

    if (!hasPermission) {
      return (
        fallback || (
          <Result
            status="403"
            title="权限不足"
            subTitle="抱歉，您没有足够的权限访问此功能。"
            extra={
              <Button type="primary" onClick={() => window.history.back()}>
                返回
              </Button>
            }
          />
        )
      )
    }
  }

  // 检查角色
  if (roles.length > 0) {
    const hasRole = requireAll
      ? roles.every(role => userRoles.includes(role))
      : roles.some(role => userRoles.includes(role))

    if (!hasRole) {
      return (
        fallback || (
          <Result
            status="403"
            title="角色不足"
            subTitle="抱歉，您的角色无法访问此功能。"
            extra={
              <Button type="primary" onClick={() => window.history.back()}>
                返回
              </Button>
            }
          />
        )
      )
    }
  }

  return <>{children}</>
}

// 权限检查 Hook
export const usePermission = () => {
  const { user } = useAuthStore()

  const hasPermission = (permissions: string[], requireAll = false): boolean => {
    if (!user) return false
    
    // TODO: 实现实际的权限检查逻辑
    const userPermissions: string[] = [] // 从用户信息中获取权限列表
    
    return requireAll
      ? permissions.every(permission => userPermissions.includes(permission))
      : permissions.some(permission => userPermissions.includes(permission))
  }

  const hasRole = (roles: string[], requireAll = false): boolean => {
    if (!user) return false
    
    // TODO: 实现实际的角色检查逻辑
    const userRoles: string[] = [] // 从用户信息中获取角色列表
    
    return requireAll
      ? roles.every(role => userRoles.includes(role))
      : roles.some(role => userRoles.includes(role))
  }

  const isAdmin = (): boolean => {
    return hasRole(['admin', 'super_admin'])
  }

  const isLawyer = (): boolean => {
    return hasRole(['lawyer'])
  }

  return {
    hasPermission,
    hasRole,
    isAdmin,
    isLawyer,
    user,
  }
}

export default PermissionGuard
