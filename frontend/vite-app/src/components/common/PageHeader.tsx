import React from 'react'
import { <PERSON>Header as Antd<PERSON>age<PERSON><PERSON><PERSON>, Breadcrumb, Space, Button } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

interface BreadcrumbItem {
  title: string
  path?: string
}

interface PageHeaderProps {
  title: string
  subTitle?: string
  breadcrumbs?: BreadcrumbItem[]
  showBack?: boolean
  backPath?: string
  extra?: React.ReactNode
  children?: React.ReactNode
  className?: string
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subTitle,
  breadcrumbs,
  showBack = false,
  backPath,
  extra,
  children,
  className,
}) => {
  const navigate = useNavigate()

  const handleBack = () => {
    if (backPath) {
      navigate(backPath)
    } else {
      navigate(-1)
    }
  }

  const breadcrumbItems = breadcrumbs?.map((item, index) => ({
    title: item.path ? (
      <a onClick={() => navigate(item.path!)}>{item.title}</a>
    ) : (
      item.title
    ),
  }))

  return (
    <div className={`page-header ${className || ''}`}>
      {breadcrumbs && breadcrumbs.length > 0 && (
        <Breadcrumb
          items={breadcrumbItems}
          style={{ marginBottom: 16 }}
        />
      )}
      
      <div className="page-header-content">
        <div className="page-header-main">
          <div className="page-header-row">
            {showBack && (
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={handleBack}
                style={{ marginRight: 16 }}
              >
                返回
              </Button>
            )}
            
            <div className="page-header-heading">
              <div className="page-header-title">
                <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 600 }}>
                  {title}
                </h1>
              </div>
              {subTitle && (
                <div className="page-header-sub-title">
                  <span style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: '14px' }}>
                    {subTitle}
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {extra && (
            <div className="page-header-extra">
              <Space>{extra}</Space>
            </div>
          )}
        </div>
        
        {children && (
          <div className="page-header-content-body" style={{ marginTop: 16 }}>
            {children}
          </div>
        )}
      </div>
    </div>
  )
}

export default PageHeader
