import React from 'react'
import { Modal, Button, Space } from 'antd'
import { ExclamationCircleOutlined, QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons'

interface ConfirmModalProps {
  open: boolean
  title?: string
  content?: React.ReactNode
  type?: 'warning' | 'info' | 'error' | 'success'
  okText?: string
  cancelText?: string
  onOk?: () => void | Promise<void>
  onCancel?: () => void
  loading?: boolean
  danger?: boolean
  width?: number
  centered?: boolean
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  open,
  title = '确认操作',
  content,
  type = 'warning',
  okText = '确定',
  cancelText = '取消',
  onOk,
  onCancel,
  loading = false,
  danger = false,
  width = 416,
  centered = true,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
      case 'success':
        return <QuestionCircleOutlined style={{ color: '#52c41a' }} />
      default:
        return <QuestionCircleOutlined style={{ color: '#1890ff' }} />
    }
  }

  return (
    <Modal
      open={open}
      title={null}
      footer={null}
      onCancel={onCancel}
      width={width}
      centered={centered}
      closable={false}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start' }}>
        <div style={{ marginRight: 16, fontSize: 22 }}>
          {getIcon()}
        </div>
        <div style={{ flex: 1 }}>
          <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 8 }}>
            {title}
          </div>
          {content && (
            <div style={{ color: 'rgba(0, 0, 0, 0.65)', marginBottom: 24 }}>
              {content}
            </div>
          )}
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={onCancel} disabled={loading}>
                {cancelText}
              </Button>
              <Button
                type="primary"
                danger={danger}
                onClick={onOk}
                loading={loading}
              >
                {okText}
              </Button>
            </Space>
          </div>
        </div>
      </div>
    </Modal>
  )
}

// 便捷方法
export const showConfirm = (props: Omit<ConfirmModalProps, 'open'>) => {
  return new Promise<boolean>((resolve) => {
    const modal = Modal.confirm({
      title: props.title || '确认操作',
      content: props.content,
      okText: props.okText || '确定',
      cancelText: props.cancelText || '取消',
      centered: props.centered !== false,
      width: props.width || 416,
      icon: props.type === 'warning' ? <ExclamationCircleOutlined /> : <QuestionCircleOutlined />,
      okButtonProps: {
        danger: props.danger,
        loading: props.loading,
      },
      onOk: async () => {
        try {
          await props.onOk?.()
          resolve(true)
        } catch (error) {
          console.error('Confirm modal onOk error:', error)
          resolve(false)
        }
      },
      onCancel: () => {
        props.onCancel?.()
        resolve(false)
      },
    })

    return modal
  })
}

export default ConfirmModal
