import React from 'react'
import { Spin, SpinProps } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

interface LoadingSpinnerProps extends SpinProps {
  text?: string
  fullScreen?: boolean
  overlay?: boolean
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  text = '加载中...',
  fullScreen = false,
  overlay = false,
  size = 'default',
  ...props
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />

  const spinner = (
    <Spin
      indicator={antIcon}
      tip={text}
      size={size}
      {...props}
    />
  )

  if (fullScreen) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: overlay ? 'rgba(255, 255, 255, 0.8)' : 'transparent',
          zIndex: 9999,
        }}
      >
        {spinner}
      </div>
    )
  }

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
      }}
    >
      {spinner}
    </div>
  )
}

export default LoadingSpinner
