import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'
import Cookies from 'js-cookie'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1'
const TOKEN_KEY = 'auth_token'
const REFRESH_TOKEN_KEY = 'refresh_token'

// 创建axios实例
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const token = Cookies.get(TOKEN_KEY)
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求ID用于追踪
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    if (config.headers) {
      config.headers['X-Request-ID'] = requestId
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 处理成功响应
    const { data } = response
    
    // 如果后端返回的是标准格式 {success: true, data: ..., message: ...}
    if (data && typeof data === 'object' && 'success' in data) {
      if (data.success) {
        return { ...response, data: data.data }
      } else {
        // 后端返回业务错误
        const errorMessage = data.message || '操作失败'
        message.error(errorMessage)
        return Promise.reject(new Error(errorMessage))
      }
    }
    
    return response
  },
  async (error) => {
    const { response, config } = error
    
    if (!response) {
      // 网络错误
      message.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }
    
    const { status, data } = response
    
    switch (status) {
      case 401:
        // 未授权，尝试刷新token
        if (!config._retry) {
          config._retry = true
          
          const refreshToken = Cookies.get(REFRESH_TOKEN_KEY)
          if (refreshToken) {
            try {
              const refreshResponse = await axios.post(
                `${API_BASE_URL}/auth/refresh`,
                { refresh_token: refreshToken }
              )
              
              const { access_token } = refreshResponse.data
              Cookies.set(TOKEN_KEY, access_token, { expires: 7 })
              
              // 重新发送原请求
              config.headers.Authorization = `Bearer ${access_token}`
              return apiClient(config)
            } catch (refreshError) {
              // 刷新失败，清除token并跳转到登录页
              Cookies.remove(TOKEN_KEY)
              Cookies.remove(REFRESH_TOKEN_KEY)
              window.location.href = '/login'
              return Promise.reject(refreshError)
            }
          } else {
            // 没有refresh token，跳转到登录页
            window.location.href = '/login'
          }
        }
        break
        
      case 403:
        message.error('权限不足，无法访问该资源')
        break
        
      case 404:
        message.error('请求的资源不存在')
        break
        
      case 422:
        // 表单验证错误
        if (data && data.detail && Array.isArray(data.detail)) {
          const errorMessages = data.detail.map((err: any) => err.msg).join(', ')
          message.error(`输入验证失败: ${errorMessages}`)
        } else {
          message.error(data?.message || '输入数据格式错误')
        }
        break
        
      case 429:
        message.error('请求过于频繁，请稍后再试')
        break
        
      case 500:
        message.error('服务器内部错误，请稍后再试')
        break
        
      case 502:
      case 503:
      case 504:
        message.error('服务暂时不可用，请稍后再试')
        break
        
      default:
        const errorMessage = data?.message || `请求失败 (${status})`
        message.error(errorMessage)
    }
    
    return Promise.reject(error)
  }
)

// 通用API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: number
  request_id?: string
}

// 分页响应接口
export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 错误响应接口
export interface ApiError {
  message: string
  code?: number
  details?: any
  request_id?: string
}

// 通用请求方法封装
export const api = {
  // GET请求
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.get(url, config).then(res => res.data),
    
  // POST请求
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.post(url, data, config).then(res => res.data),
    
  // PUT请求
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.put(url, data, config).then(res => res.data),
    
  // PATCH请求
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.patch(url, data, config).then(res => res.data),
    
  // DELETE请求
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.delete(url, config).then(res => res.data),
}

export default apiClient
