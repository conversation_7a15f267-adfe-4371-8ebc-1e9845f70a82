import { apiClient } from './api'
import {
  User,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RefreshTokenRequest,
  RefreshTokenResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  EmailVerificationRequest,
  UserProfileUpdate,
} from '@/types/auth'

export const authService = {
  // 用户登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await apiClient.post('/auth/login/json', credentials)
    return response.data
  },

  // 用户注册
  async register(userData: RegisterRequest): Promise<User> {
    const response = await apiClient.post('/auth/register', userData)
    return response.data
  },

  // 用户登出
  async logout(): Promise<void> {
    await apiClient.post('/auth/logout')
  },

  // 刷新token
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    const response = await apiClient.post('/auth/refresh', {
      refresh_token: refreshToken,
    })
    return response.data
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get('/auth/me')
    return response.data
  },

  // 发送邮箱验证
  async sendEmailVerification(data: EmailVerificationRequest): Promise<void> {
    await apiClient.post('/auth/verify-email', data)
  },

  // 确认邮箱验证
  async confirmEmailVerification(token: string): Promise<void> {
    await apiClient.post('/auth/confirm-email', { token })
  },

  // 发送密码重置邮件
  async sendPasswordReset(data: PasswordResetRequest): Promise<void> {
    await apiClient.post('/auth/password-reset', data)
  },

  // 确认密码重置
  async confirmPasswordReset(data: PasswordResetConfirm): Promise<void> {
    await apiClient.post('/auth/password-reset/confirm', data)
  },

  // 更新用户资料
  async updateProfile(data: UserProfileUpdate): Promise<User> {
    const response = await apiClient.put('/users/profile', data)
    return response.data
  },

  // 修改密码
  async changePassword(data: {
    current_password: string
    new_password: string
  }): Promise<void> {
    await apiClient.post('/users/change-password', data)
  },
}
