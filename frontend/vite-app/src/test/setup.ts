/**
 * 测试环境设置文件
 * 配置测试所需的全局设置和模拟
 */

import '@testing-library/jest-dom'
import { vi } from 'vitest'
import { configure } from '@testing-library/react'

// 配置Testing Library
configure({
  testIdAttribute: 'data-testid',
  asyncUtilTimeout: 5000,
})

// 模拟环境变量
Object.defineProperty(window, 'import.meta', {
  value: {
    env: {
      VITE_API_BASE_URL: 'http://localhost:8000/api/v1',
      VITE_APP_TITLE: 'AI法律助手',
      VITE_APP_VERSION: '1.0.0',
      VITE_DEBUG: 'true',
      MODE: 'test',
      DEV: true,
      PROD: false,
    },
  },
})

// 模拟localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// 模拟sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
})

// 模拟window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: '',
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
  },
  writable: true,
})

// 模拟window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// 模拟ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟fetch API
global.fetch = vi.fn()

// 模拟console方法（避免测试时输出过多日志）
const originalConsole = { ...console }

beforeEach(() => {
  // 重置所有模拟
  vi.clearAllMocks()
  
  // 重置localStorage和sessionStorage
  localStorageMock.getItem.mockClear()
  localStorageMock.setItem.mockClear()
  localStorageMock.removeItem.mockClear()
  localStorageMock.clear.mockClear()
  
  sessionStorageMock.getItem.mockClear()
  sessionStorageMock.setItem.mockClear()
  sessionStorageMock.removeItem.mockClear()
  sessionStorageMock.clear.mockClear()
  
  // 静默console.log和console.warn（保留error）
  console.log = vi.fn()
  console.warn = vi.fn()
  console.info = vi.fn()
  console.debug = vi.fn()
})

afterEach(() => {
  // 恢复console
  console.log = originalConsole.log
  console.warn = originalConsole.warn
  console.info = originalConsole.info
  console.debug = originalConsole.debug
})

// 全局测试工具函数
declare global {
  var testUtils: {
    mockLocalStorage: typeof localStorageMock
    mockSessionStorage: typeof sessionStorageMock
    createMockUser: () => any
    createMockApiResponse: (data: any, status?: number) => any
    waitForNextTick: () => Promise<void>
  }
}

// 导出测试工具
global.testUtils = {
  mockLocalStorage: localStorageMock,
  mockSessionStorage: sessionStorageMock,
  
  // 创建模拟用户数据
  createMockUser: () => ({
    id: 'test-user-id',
    username: 'testuser',
    email: '<EMAIL>',
    fullName: '测试用户',
    userType: 'individual',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
  }),
  
  // 创建模拟API响应
  createMockApiResponse: (data: any, status = 200) => ({
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    json: vi.fn().mockResolvedValue(data),
    text: vi.fn().mockResolvedValue(JSON.stringify(data)),
    headers: new Headers(),
  }),
  
  // 等待下一个事件循环
  waitForNextTick: () => new Promise((resolve) => setTimeout(resolve, 0)),
}

// 模拟Ant Design的message组件
vi.mock('antd', async () => {
  const actual = await vi.importActual('antd')
  return {
    ...actual,
    message: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
      loading: vi.fn(),
      destroy: vi.fn(),
    },
    notification: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
      open: vi.fn(),
      destroy: vi.fn(),
    },
  }
})

// 模拟react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/',
      search: '',
      hash: '',
      state: null,
    }),
    useParams: () => ({}),
    useSearchParams: () => [new URLSearchParams(), vi.fn()],
  }
})

// 模拟axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
      patch: vi.fn(),
      interceptors: {
        request: {
          use: vi.fn(),
          eject: vi.fn(),
        },
        response: {
          use: vi.fn(),
          eject: vi.fn(),
        },
      },
    })),
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  },
}))

// 设置测试超时
vi.setConfig({
  testTimeout: 10000,
  hookTimeout: 10000,
})

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
})

// 导出常用的测试工具
export { vi, screen, render, fireEvent, waitFor, userEvent } from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'
