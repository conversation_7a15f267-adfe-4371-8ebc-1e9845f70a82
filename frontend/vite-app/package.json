{"name": "ai-legal-assistant-frontend", "version": "1.0.0", "description": "AI法律助手前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\""}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@ant-design/colors": "^7.0.2", "axios": "^1.6.2", "@tanstack/react-query": "^5.8.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "classnames": "^2.3.2", "js-cookie": "^3.0.5", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "framer-motion": "^10.16.16", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash-es": "^4.17.12", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "typescript": "^5.2.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "@vitest/coverage-v8": "^1.0.4", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}