# AI法律助手 🤖⚖️

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.11+-green.svg)](https://python.org)
[![React](https://img.shields.io/badge/react-18+-blue.svg)](https://reactjs.org)
[![FastAPI](https://img.shields.io/badge/fastapi-0.104+-green.svg)](https://fastapi.tiangolo.com)

> 专业的AI法律助手应用，提供智能问答、案例检索、合同分析、文书生成等法律服务

## 🌟 项目概述

AI法律助手是一个综合性的智能法律服务平台，旨在为个人用户、企业和法律从业者提供便捷、准确的法律服务支持。通过集成人工智能技术和丰富的法律数据资源，为用户提供智能问答、案例检索、合同工具、文书生成和纠纷解决指引等核心功能。

## 核心功能

### 🤖 AI法律答疑
- 基于自然语言处理的智能问答系统
- 支持多轮对话和上下文理解
- 覆盖民法、刑法、商法、劳动法等主要法律领域
- 提供法条依据和相关案例引用

### 🔍 案例检索
- 智能案例搜索和分析功能
- 支持多维度搜索（关键词、法院、时间等）
- 相似案例推荐和对比分析
- 案例要点提取和争议焦点分析

### 📄 合同工具
- 50+常用合同模板库
- 智能合同生成和自定义编辑
- 合同风险识别和评估
- 条款优化建议和修改指导

### 📝 文书工具
- 30+常用法律文书模板
- 自动化文书生成和格式校验
- 支持多种格式导出（Word、PDF）
- 文书内容完整性检查

### ⚖️ 纠纷解决指引
- 纠纷类型智能识别
- 解决流程图和步骤指导
- 成本评估和风险分析
- 法律服务资源推荐

## 技术架构

### 前端技术栈
- **框架**: React 18 + TypeScript 5
- **构建工具**: Vite
- **UI库**: Ant Design 5
- **状态管理**: Zustand
- **路由**: React Router 6
- **HTTP客户端**: Axios
- **代码规范**: ESLint + Prettier

### 后端技术栈
- **框架**: FastAPI + Python 3.11
- **数据库**: PostgreSQL 15 + Redis 7
- **搜索引擎**: Elasticsearch 8
- **ORM**: SQLAlchemy 2.0
- **异步任务**: Celery
- **API文档**: OpenAPI/Swagger

### AI/ML技术栈
- **深度学习**: PyTorch + Transformers
- **中文NLP**: jieba + BERT
- **知识图谱**: Neo4j
- **机器学习**: scikit-learn
- **文本处理**: spaCy + NLTK

### 基础设施
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitLab CI/CD

## 项目结构

```
ai-legal-assistant/
├── docs/                    # 项目文档
│   ├── 需求文档.md
│   ├── 系统设计文档.md
│   ├── 开发计划.md
│   └── 功能清单TODO.md
├── backend/                 # 后端服务
│   ├── services/           # 微服务
│   │   ├── user/          # 用户服务
│   │   ├── qa/            # 问答服务
│   │   ├── case/          # 案例服务
│   │   ├── contract/      # 合同服务
│   │   └── document/      # 文书服务
│   ├── shared/            # 共享模块
│   └── gateway/           # API网关
├── frontend/               # 前端应用
│   ├── web/               # Web端
│   ├── mobile/            # 移动端
│   └── admin/             # 管理后台
├── data/                   # 数据文件
│   ├── legal_cases/       # 法律案例数据
│   ├── regulations/       # 法律法规数据
│   └── templates/         # 模板数据
├── deployment/             # 部署配置
│   ├── docker/            # Docker配置
│   ├── kubernetes/        # K8s配置
│   └── scripts/           # 部署脚本
└── tests/                  # 测试文件
    ├── unit/              # 单元测试
    ├── integration/       # 集成测试
    └── e2e/               # 端到端测试
```

## 当前开发状态

### ✅ 已完成功能 (第一阶段基础设施)

#### 开发环境配置
- ✅ Docker开发环境配置 (PostgreSQL + Redis + Elasticsearch)
- ✅ 环境变量配置模板 (.env.example)
- ✅ 开发工具配置 (ESLint, Prettier, TypeScript)

#### 后端基础架构
- ✅ FastAPI项目结构搭建
- ✅ 异步PostgreSQL数据库连接 (SQLAlchemy 2.0)
- ✅ Redis缓存配置
- ✅ Elasticsearch搜索引擎集成
- ✅ JWT用户认证系统
- ✅ API路由结构设计
- ✅ 数据库迁移脚本 (Alembic)

#### 数据模型设计
- ✅ 用户管理模型 (User, UserProfile, UserSession)
- ✅ 法律案例模型 (LegalCase, CaseSimilarity, LegalArticle)
- ✅ 问答记录模型 (QARecord)
- ✅ 合同审查模型 (ContractReview)
- ✅ 文书生成模型 (DocumentGeneration)

#### 前端基础框架
- ✅ React + TypeScript + Vite项目搭建
- ✅ Ant Design UI组件库集成
- ✅ 路由配置 (React Router 6)
- ✅ 状态管理 (Zustand)
- ✅ API客户端配置 (Axios + 拦截器)
- ✅ 用户认证界面 (登录/注册)
- ✅ 响应式布局设计

#### API接口
- ✅ 用户认证API (/auth/*)
- ✅ 用户管理API (/users/*)
- ✅ 法律案例管理API (/legal-cases/*)
- ✅ OpenAPI文档自动生成

#### 数据初始化
- ✅ 示例用户数据
- ✅ 示例法律案例数据
- ✅ 示例法条数据
- ✅ 数据库初始化脚本

### 🔄 进行中功能

#### 第二阶段：安全和用户系统
- 🔄 数据安全基础设施
- 🔄 权限控制系统
- 🔄 前端基础组件完善

### 📋 待开发功能

#### 第三阶段：核心业务功能
- ⏳ AI问答系统
- ⏳ 案例检索功能
- ⏳ 合同审查工具
- ⏳ 文书生成工具

#### 第四阶段：集成和优化
- ⏳ 前后端集成测试
- ⏳ 性能优化
- ⏳ 部署配置
- ⏳ 文档完善

## 开发计划

### 第一阶段 - MVP基础版 (8周)
- **数据安全与合规基础** - 数据加密、隐私保护、审计日志
- **基础设施搭建** - 微服务架构、数据库设计、CI/CD流水线
- **用户管理系统** - 认证授权、权限控制、个人中心
- **简单问答功能** - 基于规则的问答系统
- **基础案例检索** - 关键词搜索、基础筛选
- **简单合同模板** - 静态模板、基础填充

### 第二阶段 - 核心AI功能 (12周)
- **完整NLP处理能力** - 中文分词、语义理解、意图识别
- **法律知识图谱构建** - 实体抽取、关系建模、知识推理
- **智能问答系统** - 语义匹配、多轮对话、答案生成
- **高级案例检索** - 相似度分析、智能推荐
- **合同风险识别** - 风险条款识别、智能分析
- **系统监控完善** - 性能监控、日志分析、告警机制

### 第三阶段 - 功能完善 (8周)
- **文书工具系统** - 文书模板、自动生成、格式校验
- **纠纷解决指引** - 类型识别、流程指导、成本评估
- **用户体验优化** - 界面优化、交互改进、性能提升
- **移动端适配** - 响应式设计、PWA功能
- **性能优化** - 数据库优化、缓存策略、并发处理

### 第四阶段 - 高级功能 (4周)
- **高级分析功能** - 数据统计、趋势分析、报表生成
- **管理后台** - 系统管理、用户管理、数据管理
- **第三方集成** - 支付系统、通知服务、外部API
- **最终优化** - 安全加固、性能调优、文档完善

### 工作量评估
- **总工作量**: 280-320人天
- **建议团队**: 10-12人，8个月完成
- **关键里程碑**: 每2周一个迭代，每4周一个阶段评审

## 数据源

### 法律法规数据
- 国家法律法规数据库
- 地方性法规和规章制度
- 司法解释和指导案例

### 案例数据
- 中国裁判文书网公开数据
- 各级法院典型案例
- 仲裁机构案例信息

### 第三方服务
- 自然语言处理API
- 法律知识图谱服务
- 地理位置和通知服务

## 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- Docker 20.10+
- PostgreSQL 15+
- Redis 7.0+
- Elasticsearch 8.0+

### 本地开发环境搭建

1. **克隆项目**
```bash
git clone https://git.atjog.com/aier/ai-legal-assistant.git
cd ai-legal-assistant
```

2. **启动基础服务**
```bash
docker-compose up -d postgres redis elasticsearch mongodb
```

3. **后端服务启动**
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

4. **前端应用启动**
```bash
cd frontend/web
npm install
npm run dev
```

### 部署说明

详细的部署说明请参考 [部署文档](docs/deployment.md)

## 贡献指南

1. Fork 项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

- 项目维护者：AI法律助手开发团队
- 邮箱：<EMAIL>
- 项目地址：https://git.atjog.com/aier/ai-legal-assistant

## 致谢

感谢所有为本项目做出贡献的开发者和法律专家，以及提供数据支持的相关机构。
