# 部署指南

本文档详细说明了AI法律助手系统的部署流程和配置要求。

## 🏗️ 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Docker

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 100Mbps带宽

## 🐳 Docker部署 (推荐)

### 1. 环境准备

```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 项目部署

```bash
# 克隆项目
git clone <repository-url>
cd ai-legal-assistant

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量

# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 3. 初始化数据

```bash
# 运行数据库迁移
docker-compose exec backend alembic upgrade head

# 初始化权限数据
docker-compose exec backend python scripts/init_permissions.py

# 创建管理员用户
docker-compose exec backend python scripts/create_admin.py
```

### 4. 验证部署

```bash
# 检查服务健康状态
curl http://localhost:8000/api/v1/monitoring/health

# 访问前端应用
# 浏览器打开: http://localhost:3000
```

## 🖥️ 传统部署

### 后端部署

#### 1. 环境准备

```bash
# 安装Python 3.11+
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev

# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib

# 安装Redis
sudo apt install redis-server
```

#### 2. 数据库配置

```bash
# 创建数据库用户和数据库
sudo -u postgres psql
CREATE USER legal_assistant WITH PASSWORD 'your_password';
CREATE DATABASE legal_assistant OWNER legal_assistant;
GRANT ALL PRIVILEGES ON DATABASE legal_assistant TO legal_assistant;
\q
```

#### 3. 后端服务部署

```bash
cd backend

# 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 运行数据库迁移
alembic upgrade head

# 初始化数据
python scripts/init_permissions.py

# 启动服务 (开发模式)
uvicorn main:app --host 0.0.0.0 --port 8000

# 生产模式启动
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### 4. 系统服务配置

创建systemd服务文件 `/etc/systemd/system/legal-assistant-backend.service`:

```ini
[Unit]
Description=Legal Assistant Backend
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/ai-legal-assistant/backend
Environment=PATH=/path/to/ai-legal-assistant/backend/venv/bin
ExecStart=/path/to/ai-legal-assistant/backend/venv/bin/gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务:
```bash
sudo systemctl daemon-reload
sudo systemctl enable legal-assistant-backend
sudo systemctl start legal-assistant-backend
```

### 前端部署

#### 1. 环境准备

```bash
# 安装Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装Nginx
sudo apt install nginx
```

#### 2. 构建前端

```bash
cd frontend/vite-app

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.production
# 编辑 .env.production 文件

# 构建生产版本
npm run build
```

#### 3. Nginx配置

创建Nginx配置文件 `/etc/nginx/sites-available/legal-assistant`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/ai-legal-assistant/frontend/vite-app/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket支持 (如果需要)
    location /ws/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

启用站点:
```bash
sudo ln -s /etc/nginx/sites-available/legal-assistant /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 SSL证书配置

### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### 1. 日志配置

```bash
# 创建日志目录
sudo mkdir -p /var/log/legal-assistant
sudo chown www-data:www-data /var/log/legal-assistant

# 配置日志轮转
sudo tee /etc/logrotate.d/legal-assistant << EOF
/var/log/legal-assistant/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload legal-assistant-backend
    endscript
}
EOF
```

### 2. 系统监控

安装监控工具:
```bash
# 安装htop和iotop
sudo apt install htop iotop

# 安装Prometheus Node Exporter (可选)
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.6.1.linux-amd64.tar.gz
sudo cp node_exporter-1.6.1.linux-amd64/node_exporter /usr/local/bin/
```

## 🔧 性能优化

### 数据库优化

PostgreSQL配置优化 (`/etc/postgresql/15/main/postgresql.conf`):

```ini
# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# 连接配置
max_connections = 100

# 日志配置
log_statement = 'all'
log_duration = on
log_min_duration_statement = 1000
```

### Redis优化

Redis配置优化 (`/etc/redis/redis.conf`):

```ini
# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
```

### Nginx优化

```nginx
# 工作进程数
worker_processes auto;

# 连接数配置
events {
    worker_connections 1024;
    use epoll;
}

# Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 缓冲区配置
client_body_buffer_size 128k;
client_max_body_size 10m;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查PostgreSQL状态
   sudo systemctl status postgresql
   
   # 检查连接配置
   sudo -u postgres psql -c "SELECT version();"
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   sudo systemctl status redis-server
   
   # 测试连接
   redis-cli ping
   ```

3. **前端无法访问API**
   ```bash
   # 检查后端服务状态
   sudo systemctl status legal-assistant-backend
   
   # 检查端口占用
   sudo netstat -tlnp | grep :8000
   ```

4. **权限问题**
   ```bash
   # 检查文件权限
   ls -la /path/to/ai-legal-assistant/
   
   # 修复权限
   sudo chown -R www-data:www-data /path/to/ai-legal-assistant/
   ```

### 日志查看

```bash
# 查看后端日志
sudo journalctl -u legal-assistant-backend -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 查看应用日志
sudo tail -f /var/log/legal-assistant/app.log
```

## 📋 维护清单

### 日常维护

- [ ] 检查系统资源使用情况
- [ ] 查看应用日志是否有异常
- [ ] 检查数据库性能指标
- [ ] 验证备份是否正常

### 周期性维护

- [ ] 更新系统安全补丁
- [ ] 清理过期日志文件
- [ ] 检查SSL证书有效期
- [ ] 数据库性能调优
- [ ] 监控磁盘空间使用

### 备份策略

```bash
# 数据库备份脚本
#!/bin/bash
BACKUP_DIR="/backup/legal-assistant"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump -h localhost -U legal_assistant legal_assistant > $BACKUP_DIR/db_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

## 📞 技术支持

如遇到部署问题，请提供以下信息：

1. 操作系统版本
2. 错误日志内容
3. 系统资源使用情况
4. 网络配置信息

联系方式：
- GitHub Issues: [项目Issues页面]
- 邮件: <EMAIL>
