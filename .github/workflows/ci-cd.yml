# AI法律助手 - CI/CD流水线配置
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

    - name: 代码格式检查
      run: |
        black --check backend/
        isort --check-only backend/

    - name: 代码风格检查
      run: |
        flake8 backend/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 backend/ --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: 类型检查
      run: |
        mypy backend/ --ignore-missing-imports

  # 单元测试
  test:
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio

    - name: 运行单元测试
      run: |
        cd backend
        python -m pytest test_nlp_simple.py -v
        python -m pytest test_semantic_standalone.py -v
        python -m pytest test_intent_standalone.py -v
        python -m pytest test_kg_standalone.py -v
        python -m pytest test_qa_standalone.py -v
        python -m pytest test_contract_standalone.py -v
        python -m pytest test_contract_risk_standalone.py -v
        python -m pytest test_clause_analyzer_standalone.py -v
        python -m pytest test_suggestion_generator_standalone.py -v
        python -m pytest test_template_manager_standalone.py -v
        python -m pytest test_document_generator_standalone.py -v
        python -m pytest test_compliance_checker_standalone.py -v
        python -m pytest test_performance_monitoring.py -v

    - name: 生成测试覆盖率报告
      run: |
        cd backend
        python -m pytest --cov=app --cov-report=xml --cov-report=html

    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 安全扫描
  security:
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 运行安全扫描
      uses: github/super-linter@v4
      env:
        DEFAULT_BRANCH: main
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        VALIDATE_PYTHON_FLAKE8: true
        VALIDATE_PYTHON_BLACK: true

    - name: 依赖安全检查
      run: |
        python -m pip install --upgrade pip
        pip install safety
        safety check -r requirements.txt

  # 构建Docker镜像
  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name == 'push'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 登录容器注册表
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: 构建并推送Docker镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 部署到测试环境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 部署到测试环境
      run: |
        echo "部署到测试环境..."
        # 这里可以添加实际的部署脚本
        # 例如：kubectl apply -f k8s/staging/
        # 或者：docker-compose -f docker-compose.staging.yml up -d

    - name: 运行集成测试
      run: |
        echo "运行集成测试..."
        # 这里可以添加集成测试脚本
        # 例如：pytest tests/integration/

    - name: 通知部署结果
      if: always()
      run: |
        echo "测试环境部署完成"

  # 部署到生产环境
  deploy-production:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 部署到生产环境
      run: |
        echo "部署到生产环境..."
        # 这里可以添加实际的生产部署脚本
        # 例如：kubectl apply -f k8s/production/
        # 或者：docker-compose -f docker-compose.prod.yml up -d

    - name: 健康检查
      run: |
        echo "执行健康检查..."
        # 这里可以添加健康检查脚本
        # 例如：curl -f http://your-domain.com/health

    - name: 通知部署结果
      if: always()
      run: |
        echo "生产环境部署完成"

  # 性能测试
  performance-test:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 运行性能测试
      run: |
        echo "运行性能测试..."
        # 这里可以添加性能测试脚本
        # 例如：artillery run performance-tests.yml

    - name: 生成性能报告
      run: |
        echo "生成性能报告..."
        # 这里可以添加性能报告生成脚本
