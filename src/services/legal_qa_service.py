#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI法律问答核心服务

功能描述：
- 实现法律问题的智能问答处理
- 集成意图识别、相似度计算和答案生成
- 支持多轮对话和上下文管理
- 提供问答质量评估和优化

技术栈：
- scikit-learn (TF-IDF向量化)
- jieba (中文分词)
- Redis (会话管理)
- PostgreSQL (数据存储)

作者：AI法律助手开发团队
创建时间：2024年8月26日
"""

import jieba
import jieba.posseg as pseg
import re
import json
import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, Counter
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.naive_bayes import MultinomialNB
import redis.asyncio as redis
import psycopg2
from psycopg2.extras import RealDictCursor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class IntentResult:
    """意图识别结果"""
    intent: str                 # 识别的意图类型
    confidence: float           # 置信度
    category: str               # 法律分类
    keywords: List[str]         # 关键词

@dataclass
class AnswerResult:
    """问答结果"""
    answer: str                 # 生成的答案
    confidence: float           # 答案置信度
    sources: List[Dict]         # 答案来源
    related_questions: List[str] # 相关问题推荐
    processing_time: float      # 处理时间

@dataclass
class HistoryResult:
    """历史记录结果"""
    total: int                  # 总记录数
    records: List[Dict]         # 记录列表

class LegalQAService:
    """
    法律问答核心服务类
    
    提供完整的法律问答处理流程，包括：
    - 问题预处理和意图识别
    - 相似问题检索和答案生成
    - 多轮对话管理和历史记录
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化法律问答服务
        
        Args:
            config: 服务配置参数
        """
        self.config = config or self._get_default_config()
        
        # 初始化组件
        self._init_tokenizer()
        self._init_vectorizer()
        self._init_intent_classifier()
        self._init_redis_client()
        self._init_database()
        
        # 加载知识库
        self.knowledge_base = self._load_knowledge_base()
        self.qa_pairs = self._load_qa_pairs()
        
        # 统计信息
        self.stats = {
            'total_questions': 0,
            'successful_answers': 0,
            'failed_answers': 0,
            'average_confidence': 0.0
        }
        
        logger.info("法律问答服务初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'redis': {
                'host': 'localhost',
                'port': 6379,
                'db': 0
            },
            'database': {
                'host': 'localhost',
                'port': 5432,
                'database': 'legal_assistant',
                'user': 'postgres',
                'password': 'password'
            },
            'qa': {
                'min_confidence': 0.6,
                'max_context_length': 2000,
                'session_timeout': 3600
            }
        }
    
    def _init_tokenizer(self):
        """初始化中文分词器"""
        try:
            # 加载法律专业词典
            jieba.load_userdict('data/dictionaries/legal_terms.txt')
            
            # 设置分词模式
            jieba.enable_paddle()
            
            logger.info("中文分词器初始化完成")
            
        except Exception as e:
            logger.warning(f"分词器初始化失败: {e}")
    
    def _init_vectorizer(self):
        """初始化TF-IDF向量化器"""
        self.vectorizer = TfidfVectorizer(
            max_features=5000,          # 最大特征数
            ngram_range=(1, 2),         # 1-2元语法
            min_df=2,                   # 最小文档频率
            max_df=0.8,                 # 最大文档频率
            stop_words=self._get_stop_words(),
            tokenizer=self._jieba_tokenizer
        )
        
        logger.info("TF-IDF向量化器初始化完成")
    
    def _init_intent_classifier(self):
        """初始化意图分类器"""
        self.intent_classifier = MultinomialNB(alpha=1.0)
        self.intent_labels = [
            'consultation',    # 法律咨询
            'contract',        # 合同相关
            'dispute',         # 纠纷解决
            'procedure',       # 法律程序
            'regulation',      # 法规查询
            'rights',          # 权利义务
            'penalty',         # 法律责任
            'other'            # 其他
        ]
        
        # 训练意图分类器（使用预定义的训练数据）
        self._train_intent_classifier()
        
        logger.info("意图分类器初始化完成")
    
    async def _init_redis_client(self):
        """初始化Redis客户端"""
        try:
            self.redis_client = redis.Redis(
                host=self.config['redis']['host'],
                port=self.config['redis']['port'],
                db=self.config['redis']['db'],
                decode_responses=True
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis客户端初始化完成")
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.redis_client = None
    
    def _init_database(self):
        """初始化数据库连接"""
        self.db_config = self.config['database']
        logger.info("数据库配置初始化完成")
    
    def _get_stop_words(self) -> List[str]:
        """获取中文停用词列表"""
        return [
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', 
            '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', 
            '着', '没有', '看', '好', '自己', '这', '那', '里', '就是', '还是'
        ]
    
    def _jieba_tokenizer(self, text: str) -> List[str]:
        """jieba分词器"""
        words = jieba.cut(text)
        return [word for word in words if len(word) >= 2]
    
    def _train_intent_classifier(self):
        """训练意图分类器"""
        # 预定义的训练数据（实际项目中应从数据库加载）
        training_data = [
            ("合同违约怎么办", "contract"),
            ("劳动合同可以随时解除吗", "contract"),
            ("交通事故赔偿标准", "dispute"),
            ("如何起诉欠款人", "procedure"),
            ("民法典关于婚姻的规定", "regulation"),
            ("员工有哪些权利", "rights"),
            ("违反劳动法的后果", "penalty"),
            ("法律咨询收费标准", "consultation"),
            # 更多训练数据...
        ]
        
        if training_data:
            texts = [item[0] for item in training_data]
            labels = [item[1] for item in training_data]
            
            # 向量化文本
            X = self.vectorizer.fit_transform(texts)
            
            # 训练分类器
            self.intent_classifier.fit(X, labels)
            
            logger.info(f"意图分类器训练完成，使用 {len(training_data)} 条训练数据")
    
    def _load_knowledge_base(self) -> Dict:
        """加载法律知识库"""
        knowledge_base = {
            'laws': {},      # 法律条文
            'cases': {},     # 案例库
            'concepts': {}   # 法律概念
        }
        
        try:
            # 从数据库加载知识库（简化版本）
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # 加载法律条文
            cursor.execute("""
                SELECT title, content, category 
                FROM cleaned_legal_documents 
                WHERE quality_score >= 0.8 
                LIMIT 1000
            """)
            
            laws = cursor.fetchall()
            for law in laws:
                knowledge_base['laws'][law['title']] = {
                    'content': law['content'],
                    'category': law['category']
                }
            
            cursor.close()
            conn.close()
            
            logger.info(f"知识库加载完成，包含 {len(knowledge_base['laws'])} 条法律条文")
            
        except Exception as e:
            logger.warning(f"知识库加载失败: {e}")
        
        return knowledge_base
    
    def _load_qa_pairs(self) -> List[Dict]:
        """加载问答对数据"""
        qa_pairs = []
        
        try:
            # 从数据库加载问答对（简化版本）
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            cursor.execute("""
                SELECT question, answer, intent, confidence 
                FROM qa_records 
                WHERE confidence >= 0.8 
                ORDER BY created_at DESC 
                LIMIT 5000
            """)
            
            records = cursor.fetchall()
            qa_pairs = [dict(record) for record in records]
            
            cursor.close()
            conn.close()
            
            logger.info(f"问答对加载完成，包含 {len(qa_pairs)} 条记录")
            
        except Exception as e:
            logger.warning(f"问答对加载失败: {e}")
        
        return qa_pairs
    
    async def preprocess_question(self, question: str, context: Optional[str] = None) -> str:
        """
        问题预处理
        
        Args:
            question: 原始问题
            context: 上下文信息
            
        Returns:
            str: 预处理后的问题
        """
        try:
            # 1. 基本清洗
            processed = question.strip()
            
            # 2. 去除多余空白字符
            processed = re.sub(r'\s+', ' ', processed)
            
            # 3. 标准化标点符号
            processed = processed.replace('？', '?').replace('！', '!')
            
            # 4. 如果有上下文，进行上下文融合
            if context:
                # 简单的上下文融合策略
                context_keywords = self._extract_keywords(context)
                if context_keywords:
                    processed = f"{' '.join(context_keywords[:3])} {processed}"
            
            logger.debug(f"问题预处理完成: {question} -> {processed}")
            return processed
            
        except Exception as e:
            logger.error(f"问题预处理失败: {e}")
            return question
    
    async def classify_intent(self, question: str, category: Optional[str] = None) -> IntentResult:
        """
        问题意图识别
        
        Args:
            question: 预处理后的问题
            category: 用户指定的分类
            
        Returns:
            IntentResult: 意图识别结果
        """
        try:
            # 1. 向量化问题
            question_vector = self.vectorizer.transform([question])
            
            # 2. 预测意图
            intent_probs = self.intent_classifier.predict_proba(question_vector)[0]
            intent_idx = np.argmax(intent_probs)
            intent = self.intent_labels[intent_idx]
            confidence = float(intent_probs[intent_idx])
            
            # 3. 提取关键词
            keywords = self._extract_keywords(question)
            
            # 4. 推断法律分类
            inferred_category = category or self._infer_legal_category(question, keywords)
            
            result = IntentResult(
                intent=intent,
                confidence=confidence,
                category=inferred_category,
                keywords=keywords
            )
            
            logger.debug(f"意图识别完成: {intent} (置信度: {confidence:.2f})")
            return result
            
        except Exception as e:
            logger.error(f"意图识别失败: {e}")
            return IntentResult(
                intent='other',
                confidence=0.5,
                category='其他',
                keywords=[]
            )
    
    async def generate_answer(
        self, 
        question: str, 
        intent: str, 
        context: Optional[str] = None,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> AnswerResult:
        """
        生成法律建议答案
        
        Args:
            question: 预处理后的问题
            intent: 识别的意图
            context: 上下文信息
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            AnswerResult: 答案生成结果
        """
        start_time = datetime.now()
        
        try:
            self.stats['total_questions'] += 1
            
            # 1. 检索相似问答对
            similar_qa = await self._find_similar_qa(question, top_k=5)
            
            # 2. 检索相关法条
            related_laws = await self._find_related_laws(question, intent)
            
            # 3. 生成答案
            answer = await self._generate_answer_text(
                question=question,
                intent=intent,
                similar_qa=similar_qa,
                related_laws=related_laws,
                context=context
            )
            
            # 4. 计算答案置信度
            confidence = self._calculate_answer_confidence(
                question, answer, similar_qa, related_laws
            )
            
            # 5. 生成相关问题推荐
            related_questions = await self._generate_related_questions(question, intent)
            
            # 6. 准备答案来源
            sources = self._prepare_sources(similar_qa, related_laws)
            
            # 7. 更新会话上下文
            if session_id:
                await self._update_session_context(session_id, question, answer)
            
            # 8. 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = AnswerResult(
                answer=answer,
                confidence=confidence,
                sources=sources,
                related_questions=related_questions,
                processing_time=processing_time
            )
            
            # 9. 更新统计信息
            if confidence >= self.config['qa']['min_confidence']:
                self.stats['successful_answers'] += 1
            else:
                self.stats['failed_answers'] += 1
            
            self.stats['average_confidence'] = (
                (self.stats['average_confidence'] * (self.stats['total_questions'] - 1) + confidence) 
                / self.stats['total_questions']
            )
            
            logger.info(f"答案生成完成，置信度: {confidence:.2f}, 耗时: {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"答案生成失败: {e}")
            self.stats['failed_answers'] += 1
            
            return AnswerResult(
                answer="抱歉，我暂时无法回答这个问题。建议您咨询专业律师获取准确的法律建议。",
                confidence=0.0,
                sources=[],
                related_questions=[],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """提取文本关键词"""
        try:
            words = jieba.cut(text)
            word_freq = Counter(word for word in words if len(word) >= 2)
            return [word for word, freq in word_freq.most_common(top_k)]
        except:
            return []
    
    def _infer_legal_category(self, question: str, keywords: List[str]) -> str:
        """推断法律分类"""
        category_keywords = {
            '民法': ['合同', '婚姻', '财产', '继承', '侵权'],
            '刑法': ['犯罪', '刑事', '判刑', '坐牢', '罚金'],
            '劳动法': ['劳动', '工作', '工资', '加班', '辞职'],
            '商法': ['公司', '股东', '投资', '商业', '经营'],
            '行政法': ['行政', '政府', '公务员', '行政处罚']
        }
        
        for category, cat_keywords in category_keywords.items():
            if any(keyword in question or keyword in keywords for keyword in cat_keywords):
                return category
        
        return '其他'
    
    async def _find_similar_qa(self, question: str, top_k: int = 5) -> List[Dict]:
        """查找相似问答对"""
        if not self.qa_pairs:
            return []
        
        try:
            # 计算问题相似度
            questions = [qa['question'] for qa in self.qa_pairs]
            question_vectors = self.vectorizer.transform(questions)
            query_vector = self.vectorizer.transform([question])
            
            similarities = cosine_similarity(query_vector, question_vectors)[0]
            
            # 获取最相似的问答对
            top_indices = np.argsort(similarities)[-top_k:][::-1]
            
            similar_qa = []
            for idx in top_indices:
                if similarities[idx] > 0.3:  # 相似度阈值
                    qa = self.qa_pairs[idx].copy()
                    qa['similarity'] = float(similarities[idx])
                    similar_qa.append(qa)
            
            return similar_qa
            
        except Exception as e:
            logger.error(f"相似问答检索失败: {e}")
            return []
    
    async def _find_related_laws(self, question: str, intent: str) -> List[Dict]:
        """查找相关法条"""
        related_laws = []
        
        try:
            # 简化的法条检索逻辑
            keywords = self._extract_keywords(question)
            
            for title, law_info in self.knowledge_base['laws'].items():
                content = law_info['content']
                
                # 计算关键词匹配度
                match_score = sum(1 for keyword in keywords if keyword in content)
                
                if match_score > 0:
                    related_laws.append({
                        'title': title,
                        'content': content[:200] + '...',  # 截取前200字符
                        'category': law_info['category'],
                        'relevance': match_score / len(keywords) if keywords else 0
                    })
            
            # 按相关度排序
            related_laws.sort(key=lambda x: x['relevance'], reverse=True)
            return related_laws[:3]  # 返回前3个最相关的法条
            
        except Exception as e:
            logger.error(f"相关法条检索失败: {e}")
            return []
    
    async def _generate_answer_text(
        self, 
        question: str, 
        intent: str, 
        similar_qa: List[Dict],
        related_laws: List[Dict],
        context: Optional[str] = None
    ) -> str:
        """生成答案文本"""
        try:
            # 基于相似问答对生成答案
            if similar_qa and similar_qa[0]['similarity'] > 0.8:
                base_answer = similar_qa[0]['answer']
            else:
                # 基于意图生成通用答案模板
                base_answer = self._get_intent_template(intent)
            
            # 结合相关法条丰富答案
            if related_laws:
                law_info = f"\n\n相关法律依据：\n"
                for law in related_laws[:2]:
                    law_info += f"• {law['title']}: {law['content']}\n"
                base_answer += law_info
            
            # 添加建议和免责声明
            base_answer += "\n\n建议您根据具体情况咨询专业律师，以获得更准确的法律建议。"
            
            return base_answer
            
        except Exception as e:
            logger.error(f"答案文本生成失败: {e}")
            return "抱歉，我无法为您提供准确的法律建议。建议您咨询专业律师。"
    
    def _get_intent_template(self, intent: str) -> str:
        """获取意图对应的答案模板"""
        templates = {
            'consultation': "根据您的法律咨询问题，我为您提供以下参考信息：",
            'contract': "关于合同相关问题，通常需要考虑以下几个方面：",
            'dispute': "对于法律纠纷，建议您采取以下解决方案：",
            'procedure': "关于法律程序问题，一般流程如下：",
            'regulation': "根据相关法律法规，具体规定如下：",
            'rights': "关于权利义务问题，您需要了解：",
            'penalty': "关于法律责任，可能面临的后果包括：",
            'other': "关于您的法律问题，我提供以下信息供参考："
        }
        
        return templates.get(intent, templates['other'])
    
    def _calculate_answer_confidence(
        self, 
        question: str, 
        answer: str, 
        similar_qa: List[Dict],
        related_laws: List[Dict]
    ) -> float:
        """计算答案置信度"""
        confidence = 0.5  # 基础置信度
        
        # 基于相似问答对的置信度
        if similar_qa:
            max_similarity = max(qa['similarity'] for qa in similar_qa)
            confidence += max_similarity * 0.3
        
        # 基于相关法条的置信度
        if related_laws:
            max_relevance = max(law['relevance'] for law in related_laws)
            confidence += max_relevance * 0.2
        
        # 基于答案长度的置信度
        if len(answer) > 100:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    async def _generate_related_questions(self, question: str, intent: str) -> List[str]:
        """生成相关问题推荐"""
        related_questions = []
        
        try:
            # 基于意图生成相关问题
            intent_questions = {
                'contract': [
                    "合同违约如何赔偿？",
                    "如何解除合同？",
                    "合同无效的情形有哪些？"
                ],
                'dispute': [
                    "如何申请法律援助？",
                    "调解和诉讼的区别？",
                    "证据不足怎么办？"
                ],
                'procedure': [
                    "起诉需要准备什么材料？",
                    "法院审理期限是多久？",
                    "如何申请强制执行？"
                ]
            }
            
            if intent in intent_questions:
                related_questions = intent_questions[intent][:3]
            
        except Exception as e:
            logger.error(f"相关问题生成失败: {e}")
        
        return related_questions
    
    def _prepare_sources(self, similar_qa: List[Dict], related_laws: List[Dict]) -> List[Dict]:
        """准备答案来源信息"""
        sources = []
        
        # 添加相似问答来源
        for qa in similar_qa[:2]:
            sources.append({
                'type': 'qa',
                'title': f"相似问题 (相似度: {qa['similarity']:.2f})",
                'content': qa['question'],
                'confidence': qa['similarity']
            })
        
        # 添加法条来源
        for law in related_laws[:2]:
            sources.append({
                'type': 'law',
                'title': law['title'],
                'content': law['content'],
                'category': law['category'],
                'relevance': law['relevance']
            })
        
        return sources
    
    async def _update_session_context(self, session_id: str, question: str, answer: str):
        """更新会话上下文"""
        if not self.redis_client:
            return
        
        try:
            # 获取现有上下文
            context_key = f"session:{session_id}:context"
            existing_context = await self.redis_client.get(context_key)
            
            if existing_context:
                context = json.loads(existing_context)
            else:
                context = {'history': []}
            
            # 添加新的问答对
            context['history'].append({
                'question': question,
                'answer': answer,
                'timestamp': datetime.now().isoformat()
            })
            
            # 保持最近10轮对话
            if len(context['history']) > 10:
                context['history'] = context['history'][-10:]
            
            # 保存到Redis，设置过期时间
            await self.redis_client.setex(
                context_key,
                self.config['qa']['session_timeout'],
                json.dumps(context, ensure_ascii=False)
            )
            
        except Exception as e:
            logger.error(f"更新会话上下文失败: {e}")
    
    async def get_qa_history(self, user_id: str, **filters) -> HistoryResult:
        """获取用户问答历史"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # 构建查询条件
            where_conditions = ["user_id = %s"]
            params = [user_id]
            
            if filters.get('session_id'):
                where_conditions.append("session_id = %s")
                params.append(filters['session_id'])
            
            if filters.get('start_date'):
                where_conditions.append("created_at >= %s")
                params.append(filters['start_date'])
            
            if filters.get('end_date'):
                where_conditions.append("created_at <= %s")
                params.append(filters['end_date'])
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) FROM qa_records WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()[0]
            
            # 查询记录
            page = filters.get('page', 1)
            page_size = filters.get('page_size', 10)
            offset = (page - 1) * page_size
            
            query_sql = f"""
                SELECT question, answer, intent, confidence, session_id, created_at
                FROM qa_records 
                WHERE {where_clause}
                ORDER BY created_at DESC 
                LIMIT %s OFFSET %s
            """
            
            cursor.execute(query_sql, params + [page_size, offset])
            records = [dict(record) for record in cursor.fetchall()]
            
            cursor.close()
            conn.close()
            
            return HistoryResult(total=total, records=records)
            
        except Exception as e:
            logger.error(f"获取问答历史失败: {e}")
            return HistoryResult(total=0, records=[])
    
    async def warmup(self):
        """服务预热"""
        logger.info("法律问答服务预热中...")
        
        # 预热向量化器
        if self.qa_pairs:
            sample_questions = [qa['question'] for qa in self.qa_pairs[:100]]
            self.vectorizer.transform(sample_questions)
        
        logger.info("法律问答服务预热完成")
    
    async def cleanup(self):
        """服务清理"""
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("法律问答服务清理完成")
    
    async def check_redis_connection(self) -> bool:
        """检查Redis连接"""
        try:
            if self.redis_client:
                await self.redis_client.ping()
                return True
            return False
        except:
            return False
