#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多轮对话管理系统

功能描述：
- 实现多轮对话的上下文管理和状态跟踪
- 对话历史记录和上下文理解
- 澄清问题机制和意图追踪
- 会话状态持久化和恢复

技术栈：
- Redis (会话存储)
- asyncio (异步处理)
- JSON (数据序列化)

作者：AI法律助手开发团队
创建时间：2024年8月26日
"""

import json
import logging
import asyncio
import uuid
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import redis.asyncio as redis

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConversationState(Enum):
    """对话状态枚举"""
    ACTIVE = "active"           # 活跃状态
    WAITING = "waiting"         # 等待用户回复
    CLARIFYING = "clarifying"   # 澄清问题中
    COMPLETED = "completed"     # 对话完成
    EXPIRED = "expired"         # 会话过期

class MessageType(Enum):
    """消息类型枚举"""
    USER_QUESTION = "user_question"         # 用户问题
    ASSISTANT_ANSWER = "assistant_answer"   # 助手回答
    CLARIFICATION = "clarification"         # 澄清问题
    SYSTEM_MESSAGE = "system_message"       # 系统消息

@dataclass
class ConversationMessage:
    """对话消息数据结构"""
    id: str                     # 消息ID
    session_id: str             # 会话ID
    message_type: MessageType   # 消息类型
    content: str                # 消息内容
    intent: Optional[str]       # 意图类型
    confidence: float           # 置信度
    context_used: List[str]     # 使用的上下文
    timestamp: str              # 时间戳
    metadata: Dict[str, Any]    # 元数据

@dataclass
class ConversationContext:
    """对话上下文数据结构"""
    session_id: str             # 会话ID
    user_id: str                # 用户ID
    state: ConversationState    # 对话状态
    current_topic: str          # 当前话题
    legal_category: str         # 法律分类
    entities: Dict[str, Any]    # 实体信息
    history: List[ConversationMessage]  # 对话历史
    context_summary: str        # 上下文摘要
    last_activity: str          # 最后活动时间
    metadata: Dict[str, Any]    # 元数据

class ConversationManager:
    """
    多轮对话管理器
    
    负责管理用户与AI助手的多轮对话，包括：
    - 会话状态管理和持久化
    - 上下文理解和维护
    - 对话历史记录
    - 澄清问题机制
    """
    
    def __init__(self, redis_config: Dict[str, Any]):
        """
        初始化对话管理器
        
        Args:
            redis_config: Redis配置
        """
        self.redis_config = redis_config
        self.redis_client = None
        
        # 配置参数
        self.config = {
            'session_timeout': 3600,        # 会话超时时间（秒）
            'max_history_length': 20,       # 最大历史记录长度
            'context_window_size': 5,       # 上下文窗口大小
            'clarification_threshold': 0.6, # 澄清问题阈值
            'max_clarification_attempts': 3 # 最大澄清尝试次数
        }
        
        # 统计信息
        self.stats = {
            'active_sessions': 0,
            'total_messages': 0,
            'clarification_requests': 0,
            'context_hits': 0
        }
        
        logger.info("多轮对话管理器初始化完成")
    
    async def initialize(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.Redis(
                host=self.redis_config['host'],
                port=self.redis_config['port'],
                db=self.redis_config.get('db', 0),
                decode_responses=True
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis连接初始化成功")
            
        except Exception as e:
            logger.error(f"Redis连接初始化失败: {e}")
            raise
    
    async def create_session(self, user_id: str, initial_message: str = "") -> str:
        """
        创建新的对话会话
        
        Args:
            user_id: 用户ID
            initial_message: 初始消息
            
        Returns:
            str: 会话ID
        """
        try:
            session_id = str(uuid.uuid4())
            
            # 创建对话上下文
            context = ConversationContext(
                session_id=session_id,
                user_id=user_id,
                state=ConversationState.ACTIVE,
                current_topic="",
                legal_category="",
                entities={},
                history=[],
                context_summary="",
                last_activity=datetime.now().isoformat(),
                metadata={}
            )
            
            # 如果有初始消息，添加到历史记录
            if initial_message:
                initial_msg = ConversationMessage(
                    id=str(uuid.uuid4()),
                    session_id=session_id,
                    message_type=MessageType.USER_QUESTION,
                    content=initial_message,
                    intent=None,
                    confidence=1.0,
                    context_used=[],
                    timestamp=datetime.now().isoformat(),
                    metadata={}
                )
                context.history.append(initial_msg)
            
            # 保存到Redis
            await self._save_context(context)
            
            self.stats['active_sessions'] += 1
            logger.info(f"创建新会话: {session_id}")
            
            return session_id
            
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise
    
    async def add_message(
        self, 
        session_id: str, 
        message_type: MessageType,
        content: str,
        intent: Optional[str] = None,
        confidence: float = 1.0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        添加消息到对话历史
        
        Args:
            session_id: 会话ID
            message_type: 消息类型
            content: 消息内容
            intent: 意图类型
            confidence: 置信度
            metadata: 元数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取对话上下文
            context = await self._get_context(session_id)
            if not context:
                logger.warning(f"会话不存在: {session_id}")
                return False
            
            # 创建新消息
            message = ConversationMessage(
                id=str(uuid.uuid4()),
                session_id=session_id,
                message_type=message_type,
                content=content,
                intent=intent,
                confidence=confidence,
                context_used=self._get_relevant_context(context, content),
                timestamp=datetime.now().isoformat(),
                metadata=metadata or {}
            )
            
            # 添加到历史记录
            context.history.append(message)
            
            # 限制历史记录长度
            if len(context.history) > self.config['max_history_length']:
                context.history = context.history[-self.config['max_history_length']:]
            
            # 更新上下文信息
            await self._update_context_info(context, message)
            
            # 更新最后活动时间
            context.last_activity = datetime.now().isoformat()
            
            # 保存上下文
            await self._save_context(context)
            
            self.stats['total_messages'] += 1
            logger.debug(f"添加消息到会话 {session_id}: {message_type.value}")
            
            return True
            
        except Exception as e:
            logger.error(f"添加消息失败: {e}")
            return False
    
    async def get_conversation_context(self, session_id: str) -> Optional[ConversationContext]:
        """
        获取对话上下文
        
        Args:
            session_id: 会话ID
            
        Returns:
            Optional[ConversationContext]: 对话上下文
        """
        return await self._get_context(session_id)
    
    async def get_context_for_question(self, session_id: str, question: str) -> Dict[str, Any]:
        """
        获取问题相关的上下文信息
        
        Args:
            session_id: 会话ID
            question: 当前问题
            
        Returns:
            Dict[str, Any]: 上下文信息
        """
        try:
            context = await self._get_context(session_id)
            if not context:
                return {}
            
            # 获取相关的历史消息
            relevant_history = self._get_relevant_history(context, question)
            
            # 提取实体信息
            entities = context.entities
            
            # 获取当前话题
            current_topic = context.current_topic
            
            # 获取法律分类
            legal_category = context.legal_category
            
            # 生成上下文摘要
            context_summary = self._generate_context_summary(context, question)
            
            self.stats['context_hits'] += 1
            
            return {
                'session_id': session_id,
                'relevant_history': relevant_history,
                'entities': entities,
                'current_topic': current_topic,
                'legal_category': legal_category,
                'context_summary': context_summary,
                'conversation_state': context.state.value
            }
            
        except Exception as e:
            logger.error(f"获取上下文失败: {e}")
            return {}
    
    async def should_clarify(self, session_id: str, question: str, confidence: float) -> bool:
        """
        判断是否需要澄清问题
        
        Args:
            session_id: 会话ID
            question: 问题内容
            confidence: 理解置信度
            
        Returns:
            bool: 是否需要澄清
        """
        try:
            # 置信度低于阈值
            if confidence < self.config['clarification_threshold']:
                return True
            
            # 获取上下文
            context = await self._get_context(session_id)
            if not context:
                return False
            
            # 检查澄清尝试次数
            clarification_count = sum(
                1 for msg in context.history 
                if msg.message_type == MessageType.CLARIFICATION
            )
            
            if clarification_count >= self.config['max_clarification_attempts']:
                return False
            
            # 检查问题是否过于模糊
            if self._is_question_ambiguous(question):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"判断澄清需求失败: {e}")
            return False
    
    async def generate_clarification(self, session_id: str, question: str) -> str:
        """
        生成澄清问题
        
        Args:
            session_id: 会话ID
            question: 原始问题
            
        Returns:
            str: 澄清问题
        """
        try:
            context = await self._get_context(session_id)
            if not context:
                return "请您详细描述一下您的问题。"
            
            # 根据上下文生成澄清问题
            clarification_templates = [
                "您是想了解关于{topic}的哪个具体方面？",
                "请问您遇到的具体情况是什么？",
                "您希望了解{category}方面的什么问题？",
                "能否提供更多详细信息？"
            ]
            
            # 选择合适的模板
            if context.current_topic:
                clarification = clarification_templates[0].format(topic=context.current_topic)
            elif context.legal_category:
                clarification = clarification_templates[2].format(category=context.legal_category)
            else:
                clarification = clarification_templates[1]
            
            # 添加澄清消息到历史
            await self.add_message(
                session_id=session_id,
                message_type=MessageType.CLARIFICATION,
                content=clarification,
                metadata={'original_question': question}
            )
            
            self.stats['clarification_requests'] += 1
            
            return clarification
            
        except Exception as e:
            logger.error(f"生成澄清问题失败: {e}")
            return "请您详细描述一下您的问题。"
    
    async def update_session_state(self, session_id: str, state: ConversationState) -> bool:
        """
        更新会话状态
        
        Args:
            session_id: 会话ID
            state: 新状态
            
        Returns:
            bool: 是否成功
        """
        try:
            context = await self._get_context(session_id)
            if not context:
                return False
            
            context.state = state
            context.last_activity = datetime.now().isoformat()
            
            await self._save_context(context)
            
            logger.debug(f"更新会话状态 {session_id}: {state.value}")
            return True
            
        except Exception as e:
            logger.error(f"更新会话状态失败: {e}")
            return False
    
    async def cleanup_expired_sessions(self) -> int:
        """
        清理过期会话
        
        Returns:
            int: 清理的会话数量
        """
        try:
            # 获取所有会话键
            pattern = "conversation:*"
            keys = await self.redis_client.keys(pattern)
            
            expired_count = 0
            current_time = datetime.now()
            
            for key in keys:
                try:
                    context_data = await self.redis_client.get(key)
                    if context_data:
                        context_dict = json.loads(context_data)
                        last_activity = datetime.fromisoformat(context_dict['last_activity'])
                        
                        # 检查是否过期
                        if (current_time - last_activity).total_seconds() > self.config['session_timeout']:
                            await self.redis_client.delete(key)
                            expired_count += 1
                            
                except Exception as e:
                    logger.warning(f"清理会话 {key} 失败: {e}")
            
            if expired_count > 0:
                self.stats['active_sessions'] -= expired_count
                logger.info(f"清理了 {expired_count} 个过期会话")
            
            return expired_count
            
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            return 0
    
    def _get_relevant_context(self, context: ConversationContext, content: str) -> List[str]:
        """获取相关上下文"""
        relevant_context = []
        
        # 获取最近的几条消息
        recent_messages = context.history[-self.config['context_window_size']:]
        
        for msg in recent_messages:
            if msg.message_type in [MessageType.USER_QUESTION, MessageType.ASSISTANT_ANSWER]:
                relevant_context.append(msg.content[:100])  # 截取前100字符
        
        return relevant_context
    
    async def _update_context_info(self, context: ConversationContext, message: ConversationMessage):
        """更新上下文信息"""
        try:
            # 更新当前话题
            if message.message_type == MessageType.USER_QUESTION:
                topic = self._extract_topic(message.content)
                if topic:
                    context.current_topic = topic
            
            # 更新法律分类
            if message.intent:
                category = self._map_intent_to_category(message.intent)
                if category:
                    context.legal_category = category
            
            # 提取和更新实体
            entities = self._extract_entities(message.content)
            context.entities.update(entities)
            
        except Exception as e:
            logger.warning(f"更新上下文信息失败: {e}")
    
    def _get_relevant_history(self, context: ConversationContext, question: str) -> List[Dict[str, Any]]:
        """获取相关历史记录"""
        relevant_history = []
        
        # 获取最近的对话记录
        recent_messages = context.history[-self.config['context_window_size']:]
        
        for msg in recent_messages:
            relevant_history.append({
                'type': msg.message_type.value,
                'content': msg.content,
                'timestamp': msg.timestamp,
                'intent': msg.intent
            })
        
        return relevant_history
    
    def _generate_context_summary(self, context: ConversationContext, current_question: str) -> str:
        """生成上下文摘要"""
        summary_parts = []
        
        if context.current_topic:
            summary_parts.append(f"当前讨论话题：{context.current_topic}")
        
        if context.legal_category:
            summary_parts.append(f"法律分类：{context.legal_category}")
        
        if context.entities:
            entities_str = ", ".join(f"{k}: {v}" for k, v in list(context.entities.items())[:3])
            summary_parts.append(f"相关实体：{entities_str}")
        
        return "; ".join(summary_parts) if summary_parts else "新对话"
    
    def _is_question_ambiguous(self, question: str) -> bool:
        """判断问题是否模糊"""
        # 简单的模糊性检测
        ambiguous_indicators = [
            len(question) < 10,  # 问题太短
            question.count('？') == 0 and question.count('?') == 0,  # 没有问号
            '这个' in question or '那个' in question,  # 指代不明
            question in ['是的', '不是', '对', '好的']  # 简单回复
        ]
        
        return any(ambiguous_indicators)
    
    def _extract_topic(self, text: str) -> str:
        """提取话题"""
        # 简单的话题提取
        topics = ['合同', '劳动', '婚姻', '继承', '侵权', '公司', '刑事', '行政']
        
        for topic in topics:
            if topic in text:
                return topic
        
        return ""
    
    def _map_intent_to_category(self, intent: str) -> str:
        """将意图映射到法律分类"""
        intent_category_map = {
            'contract': '合同法',
            'labor': '劳动法',
            'marriage': '婚姻法',
            'criminal': '刑法',
            'administrative': '行政法',
            'commercial': '商法'
        }
        
        return intent_category_map.get(intent, '')
    
    def _extract_entities(self, text: str) -> Dict[str, Any]:
        """提取实体信息"""
        entities = {}
        
        # 简单的实体提取
        if '公司' in text:
            entities['entity_type'] = 'company'
        if '合同' in text:
            entities['document_type'] = 'contract'
        if '工资' in text or '薪水' in text:
            entities['topic'] = 'salary'
        
        return entities
    
    async def _save_context(self, context: ConversationContext):
        """保存对话上下文到Redis"""
        try:
            key = f"conversation:{context.session_id}"
            
            # 转换为字典并序列化
            context_dict = asdict(context)
            
            # 处理枚举类型
            context_dict['state'] = context.state.value
            
            # 处理消息列表
            context_dict['history'] = [
                {
                    **asdict(msg),
                    'message_type': msg.message_type.value
                }
                for msg in context.history
            ]
            
            # 保存到Redis，设置过期时间
            await self.redis_client.setex(
                key,
                self.config['session_timeout'],
                json.dumps(context_dict, ensure_ascii=False)
            )
            
        except Exception as e:
            logger.error(f"保存上下文失败: {e}")
            raise
    
    async def _get_context(self, session_id: str) -> Optional[ConversationContext]:
        """从Redis获取对话上下文"""
        try:
            key = f"conversation:{session_id}"
            context_data = await self.redis_client.get(key)
            
            if not context_data:
                return None
            
            context_dict = json.loads(context_data)
            
            # 重构消息对象
            messages = []
            for msg_dict in context_dict['history']:
                msg_dict['message_type'] = MessageType(msg_dict['message_type'])
                messages.append(ConversationMessage(**msg_dict))
            
            # 重构上下文对象
            context_dict['state'] = ConversationState(context_dict['state'])
            context_dict['history'] = messages
            
            return ConversationContext(**context_dict)
            
        except Exception as e:
            logger.error(f"获取上下文失败: {e}")
            return None
    
    async def close(self):
        """关闭连接"""
        if self.redis_client:
            await self.redis_client.close()
        logger.info("对话管理器已关闭")

# 使用示例
async def main():
    """示例用法"""
    redis_config = {
        'host': 'localhost',
        'port': 6379,
        'db': 0
    }
    
    manager = ConversationManager(redis_config)
    await manager.initialize()
    
    try:
        # 创建会话
        session_id = await manager.create_session("user123", "我想了解劳动合同的问题")
        
        # 添加消息
        await manager.add_message(
            session_id=session_id,
            message_type=MessageType.ASSISTANT_ANSWER,
            content="我可以帮您解答劳动合同相关问题。请问您具体想了解什么？",
            intent="consultation"
        )
        
        # 获取上下文
        context_info = await manager.get_context_for_question(session_id, "合同可以随时解除吗？")
        print(f"上下文信息: {context_info}")
        
    finally:
        await manager.close()

if __name__ == "__main__":
    asyncio.run(main())
