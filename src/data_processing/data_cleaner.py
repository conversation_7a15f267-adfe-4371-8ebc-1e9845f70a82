#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
法条数据清洗和标准化处理模块

功能描述：
- 对爬取的法条数据进行清洗、去重和标准化处理
- 数据格式统一和层次结构解析
- 建立法律术语词典和分词优化
- 实现数据质量评估和验证

技术栈：
- pandas 1.5+
- jieba 0.42+
- scikit-learn 1.1+
- PostgreSQL

作者：AI法律助手开发团队
创建时间：2024年8月26日
"""

import pandas as pd
import jieba
import jieba.posseg as pseg
import re
import json
import logging
import hashlib
from typing import Dict, List, Set, Tuple, Optional, Any
from datetime import datetime
from dataclasses import dataclass, asdict
from collections import Counter, defaultdict
import psycopg2
from psycopg2.extras import RealDictCursor
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_cleaner.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class CleanedDocument:
    """清洗后的法律文档数据结构"""
    id: str                     # 文档唯一标识
    title: str                  # 标准化标题
    content: str                # 清洗后内容
    structured_content: Dict    # 结构化内容（条款层次）
    document_type: str          # 标准化文档类型
    category: str               # 标准化分类
    authority: str              # 标准化发布机关
    document_number: str        # 标准化文号
    effective_date: str         # 标准化生效日期
    keywords: List[str]         # 提取的关键词
    legal_terms: List[str]      # 法律术语
    quality_score: float        # 数据质量评分
    processing_time: str        # 处理时间

class LegalDataCleaner:
    """
    法律数据清洗器
    
    负责对原始爬取数据进行清洗、标准化和质量控制
    """
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化数据清洗器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        
        # 初始化jieba分词器
        self._init_jieba()
        
        # 加载法律术语词典
        self.legal_terms = self._load_legal_terms()
        
        # 初始化数据标准化规则
        self.standardization_rules = self._init_standardization_rules()
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'successfully_cleaned': 0,
            'failed_cleaning': 0,
            'duplicate_removed': 0,
            'quality_improved': 0
        }
        
        logger.info("法律数据清洗器初始化完成")
    
    def _init_jieba(self):
        """初始化jieba分词器，加载法律领域词典"""
        try:
            # 加载法律专业词典
            legal_dict_path = 'data/dictionaries/legal_terms.txt'
            jieba.load_userdict(legal_dict_path)
            
            # 设置分词模式
            jieba.enable_paddle()  # 启用paddle模式，提高准确率
            
            logger.info("jieba分词器初始化完成，已加载法律专业词典")
            
        except Exception as e:
            logger.warning(f"加载法律词典失败，使用默认词典: {e}")
    
    def _load_legal_terms(self) -> Set[str]:
        """
        加载法律术语词典
        
        Returns:
            Set[str]: 法律术语集合
        """
        legal_terms = set()
        
        # 基础法律术语
        basic_terms = [
            '法律', '法规', '条例', '规定', '办法', '细则', '通知', '公告',
            '民法', '刑法', '行政法', '商法', '经济法', '劳动法', '婚姻法',
            '合同', '协议', '违约', '赔偿', '责任', '义务', '权利', '权益',
            '诉讼', '仲裁', '调解', '执行', '判决', '裁定', '决定',
            '当事人', '原告', '被告', '第三人', '代理人', '律师',
            '证据', '事实', '程序', '期限', '费用', '罚款', '处罚'
        ]
        
        legal_terms.update(basic_terms)
        
        # 从文件加载更多术语
        try:
            with open('data/dictionaries/legal_terms.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    term = line.strip()
                    if term and len(term) >= 2:
                        legal_terms.add(term)
            
            logger.info(f"成功加载 {len(legal_terms)} 个法律术语")
            
        except FileNotFoundError:
            logger.warning("法律术语词典文件不存在，使用基础术语")
        
        return legal_terms
    
    def _init_standardization_rules(self) -> Dict[str, Dict]:
        """
        初始化数据标准化规则
        
        Returns:
            Dict: 标准化规则字典
        """
        return {
            'document_types': {
                '法律': ['法', '法律'],
                '行政法规': ['行政法规', '条例', '规定'],
                '部门规章': ['部门规章', '办法', '细则'],
                '司法解释': ['司法解释', '解释', '批复'],
                '地方性法规': ['地方性法规', '地方法规'],
                '规范性文件': ['通知', '意见', '公告']
            },
            'categories': {
                '民商法': ['民法', '商法', '合同法', '公司法', '婚姻法'],
                '刑法': ['刑法', '刑事', '犯罪'],
                '行政法': ['行政法', '行政处罚', '行政许可'],
                '经济法': ['经济法', '税法', '金融法'],
                '劳动法': ['劳动法', '劳动合同', '社会保险'],
                '环境法': ['环境法', '环保', '污染'],
                '知识产权法': ['专利法', '商标法', '著作权法']
            },
            'authorities': {
                '全国人大': ['全国人民代表大会', '全国人大'],
                '国务院': ['国务院', '中华人民共和国国务院'],
                '最高法院': ['最高人民法院', '最高法'],
                '最高检察院': ['最高人民检察院', '最高检'],
                '部委': ['部', '委员会', '总局', '总署']
            }
        }
    
    def process_batch(self, batch_size: int = 100) -> bool:
        """
        批量处理原始数据
        
        Args:
            batch_size: 批处理大小
            
        Returns:
            bool: 处理是否成功
        """
        try:
            logger.info(f"开始批量处理数据，批大小: {batch_size}")
            
            # 从数据库获取未处理的原始数据
            raw_documents = self._fetch_raw_documents(batch_size)
            
            if not raw_documents:
                logger.info("没有待处理的数据")
                return True
            
            logger.info(f"获取到 {len(raw_documents)} 条待处理数据")
            
            # 逐条处理数据
            for doc in raw_documents:
                try:
                    self.stats['total_processed'] += 1
                    
                    # 数据清洗和标准化
                    cleaned_doc = self._clean_document(doc)
                    
                    if cleaned_doc and cleaned_doc.quality_score >= 0.7:
                        # 保存清洗后的数据
                        if self._save_cleaned_document(cleaned_doc):
                            self.stats['successfully_cleaned'] += 1
                            logger.debug(f"成功处理文档: {cleaned_doc.title}")
                        else:
                            self.stats['failed_cleaning'] += 1
                    else:
                        self.stats['failed_cleaning'] += 1
                        logger.warning(f"文档质量不达标，跳过: {doc.get('title', 'Unknown')}")
                
                except Exception as e:
                    self.stats['failed_cleaning'] += 1
                    logger.error(f"处理文档失败: {e}")
            
            # 输出处理统计
            logger.info(f"批处理完成: {json.dumps(self.stats, ensure_ascii=False)}")
            return True
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return False
    
    def _fetch_raw_documents(self, limit: int) -> List[Dict]:
        """
        从数据库获取原始文档数据
        
        Args:
            limit: 获取数量限制
            
        Returns:
            List[Dict]: 原始文档列表
        """
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # 查询未处理的原始数据
            query = """
                SELECT * FROM legal_documents 
                WHERE processed = FALSE 
                ORDER BY crawl_time DESC 
                LIMIT %s
            """
            
            cursor.execute(query, (limit,))
            documents = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            return [dict(doc) for doc in documents]
            
        except Exception as e:
            logger.error(f"获取原始文档失败: {e}")
            return []
    
    def _clean_document(self, raw_doc: Dict) -> Optional[CleanedDocument]:
        """
        清洗单个文档
        
        Args:
            raw_doc: 原始文档数据
            
        Returns:
            Optional[CleanedDocument]: 清洗后的文档，失败返回None
        """
        try:
            # 1. 标题清洗
            cleaned_title = self._clean_title(raw_doc.get('title', ''))
            
            # 2. 内容清洗
            cleaned_content = self._clean_content(raw_doc.get('content', ''))
            
            # 3. 结构化解析
            structured_content = self._parse_structure(cleaned_content)
            
            # 4. 数据标准化
            standardized_type = self._standardize_document_type(raw_doc.get('document_type', ''))
            standardized_category = self._standardize_category(cleaned_content)
            standardized_authority = self._standardize_authority(raw_doc.get('authority', ''))
            
            # 5. 关键词提取
            keywords = self._extract_keywords(cleaned_content)
            
            # 6. 法律术语识别
            legal_terms = self._extract_legal_terms(cleaned_content)
            
            # 7. 质量评估
            quality_score = self._calculate_quality_score(
                cleaned_title, cleaned_content, structured_content, keywords
            )
            
            # 8. 创建清洗后的文档对象
            cleaned_doc = CleanedDocument(
                id=str(raw_doc.get('id', '')),
                title=cleaned_title,
                content=cleaned_content,
                structured_content=structured_content,
                document_type=standardized_type,
                category=standardized_category,
                authority=standardized_authority,
                document_number=self._clean_document_number(raw_doc.get('document_number', '')),
                effective_date=self._clean_date(raw_doc.get('effective_date', '')),
                keywords=keywords,
                legal_terms=legal_terms,
                quality_score=quality_score,
                processing_time=datetime.now().isoformat()
            )
            
            return cleaned_doc
            
        except Exception as e:
            logger.error(f"清洗文档失败: {e}")
            return None
    
    def _clean_title(self, title: str) -> str:
        """清洗文档标题"""
        if not title:
            return ""
        
        # 去除多余空白字符
        title = re.sub(r'\s+', ' ', title.strip())
        
        # 去除特殊字符
        title = re.sub(r'[^\w\s\u4e00-\u9fff（）()【】\[\]《》<>]', '', title)
        
        # 标准化括号
        title = title.replace('（', '(').replace('）', ')')
        title = title.replace('【', '[').replace('】', ']')
        title = title.replace('《', '<').replace('》', '>')
        
        return title
    
    def _clean_content(self, content: str) -> str:
        """清洗文档内容"""
        if not content:
            return ""
        
        # 去除HTML标签
        content = re.sub(r'<[^>]+>', '', content)
        
        # 去除多余空白字符
        content = re.sub(r'\s+', ' ', content)
        
        # 去除特殊字符，保留中文、数字、基本标点
        content = re.sub(r'[^\w\s\u4e00-\u9fff，。；：！？（）()【】\[\]《》<>""\'\-\.]', '', content)
        
        # 标准化段落分隔
        content = re.sub(r'\n\s*\n', '\n\n', content)
        
        return content.strip()
    
    def _parse_structure(self, content: str) -> Dict:
        """
        解析文档结构（章节条款）
        
        Args:
            content: 文档内容
            
        Returns:
            Dict: 结构化内容
        """
        structure = {
            'chapters': [],
            'articles': [],
            'sections': []
        }
        
        try:
            # 提取章节
            chapter_pattern = r'第([一二三四五六七八九十百千万\d]+)章\s*([^\n]+)'
            chapters = re.findall(chapter_pattern, content)
            structure['chapters'] = [{'number': ch[0], 'title': ch[1]} for ch in chapters]
            
            # 提取条款
            article_pattern = r'第([一二三四五六七八九十百千万\d]+)条\s*([^\n]+)'
            articles = re.findall(article_pattern, content)
            structure['articles'] = [{'number': art[0], 'content': art[1]} for art in articles]
            
            # 提取款项
            section_pattern = r'（([一二三四五六七八九十\d]+)）([^（）]+)'
            sections = re.findall(section_pattern, content)
            structure['sections'] = [{'number': sec[0], 'content': sec[1]} for sec in sections]
            
        except Exception as e:
            logger.warning(f"结构解析失败: {e}")
        
        return structure
    
    def _standardize_document_type(self, doc_type: str) -> str:
        """标准化文档类型"""
        if not doc_type:
            return "未知类型"
        
        doc_type = doc_type.strip()
        
        for standard_type, variants in self.standardization_rules['document_types'].items():
            for variant in variants:
                if variant in doc_type:
                    return standard_type
        
        return doc_type
    
    def _standardize_category(self, content: str) -> str:
        """根据内容推断并标准化文档分类"""
        category_scores = defaultdict(int)
        
        for category, keywords in self.standardization_rules['categories'].items():
            for keyword in keywords:
                if keyword in content:
                    category_scores[category] += content.count(keyword)
        
        if category_scores:
            return max(category_scores, key=category_scores.get)
        
        return "未分类"
    
    def _standardize_authority(self, authority: str) -> str:
        """标准化发布机关"""
        if not authority:
            return "未知机关"
        
        authority = authority.strip()
        
        for standard_auth, variants in self.standardization_rules['authorities'].items():
            for variant in variants:
                if variant in authority:
                    return standard_auth
        
        return authority
    
    def _extract_keywords(self, content: str, top_k: int = 20) -> List[str]:
        """
        提取文档关键词
        
        Args:
            content: 文档内容
            top_k: 返回前k个关键词
            
        Returns:
            List[str]: 关键词列表
        """
        try:
            # 使用jieba分词
            words = jieba.cut(content)
            
            # 过滤停用词和短词
            stopwords = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
            filtered_words = [word for word in words if len(word) >= 2 and word not in stopwords]
            
            # 统计词频
            word_freq = Counter(filtered_words)
            
            # 返回高频词作为关键词
            keywords = [word for word, freq in word_freq.most_common(top_k)]
            
            return keywords
            
        except Exception as e:
            logger.warning(f"关键词提取失败: {e}")
            return []
    
    def _extract_legal_terms(self, content: str) -> List[str]:
        """
        提取法律术语
        
        Args:
            content: 文档内容
            
        Returns:
            List[str]: 法律术语列表
        """
        found_terms = []
        
        for term in self.legal_terms:
            if term in content:
                found_terms.append(term)
        
        return found_terms
    
    def _calculate_quality_score(self, title: str, content: str, 
                                structure: Dict, keywords: List[str]) -> float:
        """
        计算文档质量评分
        
        Args:
            title: 文档标题
            content: 文档内容
            structure: 结构化内容
            keywords: 关键词列表
            
        Returns:
            float: 质量评分 (0-1)
        """
        score = 0.0
        
        # 标题质量 (20%)
        if title and len(title) >= 5:
            score += 0.2
        elif title and len(title) >= 3:
            score += 0.1
        
        # 内容长度 (30%)
        if len(content) >= 1000:
            score += 0.3
        elif len(content) >= 500:
            score += 0.2
        elif len(content) >= 200:
            score += 0.1
        
        # 结构化程度 (25%)
        structure_score = 0
        if structure['chapters']:
            structure_score += 0.1
        if structure['articles']:
            structure_score += 0.1
        if structure['sections']:
            structure_score += 0.05
        score += structure_score
        
        # 关键词丰富度 (15%)
        if len(keywords) >= 15:
            score += 0.15
        elif len(keywords) >= 10:
            score += 0.1
        elif len(keywords) >= 5:
            score += 0.05
        
        # 法律术语密度 (10%)
        legal_term_count = sum(1 for term in self.legal_terms if term in content)
        if legal_term_count >= 10:
            score += 0.1
        elif legal_term_count >= 5:
            score += 0.05
        
        return min(score, 1.0)
    
    def _clean_document_number(self, doc_number: str) -> str:
        """清洗文号"""
        if not doc_number:
            return ""
        
        # 去除多余空白
        doc_number = re.sub(r'\s+', '', doc_number.strip())
        
        # 标准化格式
        doc_number = re.sub(r'[（）]', '', doc_number)
        doc_number = re.sub(r'第(\d+)号', r'[\1]', doc_number)
        
        return doc_number
    
    def _clean_date(self, date_str: str) -> str:
        """清洗日期格式"""
        if not date_str:
            return ""
        
        # 提取日期模式
        date_patterns = [
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',
            r'(\d{4})-(\d{1,2})-(\d{1,2})',
            r'(\d{4})\.(\d{1,2})\.(\d{1,2})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                year, month, day = match.groups()
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        return date_str.strip()
    
    def _save_cleaned_document(self, doc: CleanedDocument) -> bool:
        """
        保存清洗后的文档
        
        Args:
            doc: 清洗后的文档
            
        Returns:
            bool: 保存是否成功
        """
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 插入清洗后的数据
            insert_sql = """
                INSERT INTO cleaned_legal_documents (
                    original_id, title, content, structured_content, document_type,
                    category, authority, document_number, effective_date, keywords,
                    legal_terms, quality_score, processing_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (original_id) DO UPDATE SET
                    title = EXCLUDED.title,
                    content = EXCLUDED.content,
                    quality_score = EXCLUDED.quality_score,
                    processing_time = EXCLUDED.processing_time
            """
            
            cursor.execute(insert_sql, (
                doc.id, doc.title, doc.content, json.dumps(doc.structured_content, ensure_ascii=False),
                doc.document_type, doc.category, doc.authority, doc.document_number,
                doc.effective_date, doc.keywords, doc.legal_terms, doc.quality_score,
                doc.processing_time
            ))
            
            # 标记原始数据为已处理
            update_sql = "UPDATE legal_documents SET processed = TRUE WHERE id = %s"
            cursor.execute(update_sql, (doc.id,))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"保存清洗后文档失败: {e}")
            return False
    
    def generate_legal_dictionary(self) -> bool:
        """
        生成法律术语词典
        
        Returns:
            bool: 生成是否成功
        """
        try:
            logger.info("开始生成法律术语词典")
            
            # 从清洗后的数据中提取术语
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            cursor.execute("SELECT content FROM cleaned_legal_documents WHERE quality_score >= 0.8")
            documents = cursor.fetchall()
            
            # 统计所有术语
            all_terms = Counter()
            
            for doc in documents:
                content = doc[0]
                words = jieba.cut(content)
                
                # 使用词性标注筛选名词
                for word, flag in pseg.cut(content):
                    if (len(word) >= 2 and 
                        flag in ['n', 'nr', 'ns', 'nt', 'nz'] and  # 名词类
                        any(char in word for char in '法律条款规定责任权利义务')):
                        all_terms[word] += 1
            
            # 筛选高频术语
            legal_dictionary = []
            for term, freq in all_terms.most_common(500):
                if freq >= 3:  # 至少出现3次
                    legal_dictionary.append(term)
            
            # 保存词典
            with open('data/dictionaries/generated_legal_terms.txt', 'w', encoding='utf-8') as f:
                for term in legal_dictionary:
                    f.write(f"{term}\n")
            
            logger.info(f"成功生成法律术语词典，包含 {len(legal_dictionary)} 个术语")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"生成法律术语词典失败: {e}")
            return False

if __name__ == "__main__":
    """
    数据清洗脚本启动入口
    """
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'legal_assistant',
        'user': 'postgres',
        'password': 'password'
    }
    
    # 创建数据清洗器
    cleaner = LegalDataCleaner(db_config)
    
    # 执行批量清洗
    success = cleaner.process_batch(batch_size=100)
    
    if success:
        # 生成法律术语词典
        cleaner.generate_legal_dictionary()
        logger.info("数据清洗任务完成")
    else:
        logger.error("数据清洗任务失败")
