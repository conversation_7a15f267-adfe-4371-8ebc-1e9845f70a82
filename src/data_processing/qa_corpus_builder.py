#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
法律问答语料库构建系统

功能描述：
- 构建高质量的法律问答数据集
- 问题分类、答案标注和质量评估
- 专家审核和数据增强
- 建立问答质量评估标准

技术栈：
- pandas (数据处理)
- jieba (中文分词)
- scikit-learn (文本分类)
- PostgreSQL (数据存储)

作者：AI法律助手开发团队
创建时间：2024年8月26日
"""

import pandas as pd
import jieba
import jieba.posseg as pseg
import json
import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass, asdict
from collections import Counter, defaultdict
import psycopg2
from psycopg2.extras import RealDictCursor
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/qa_corpus_builder.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class QAPair:
    """问答对数据结构"""
    id: str                     # 唯一标识
    question: str               # 问题内容
    answer: str                 # 答案内容
    category: str               # 法律分类
    intent: str                 # 问题意图
    difficulty: str             # 难度等级
    keywords: List[str]         # 关键词
    sources: List[str]          # 答案来源
    quality_score: float        # 质量评分
    expert_reviewed: bool       # 专家审核状态
    created_at: str            # 创建时间
    updated_at: str            # 更新时间

@dataclass
class QualityMetrics:
    """质量评估指标"""
    completeness: float         # 完整性评分
    accuracy: float            # 准确性评分
    relevance: float           # 相关性评分
    clarity: float             # 清晰度评分
    overall_score: float       # 综合评分

class QACorpusBuilder:
    """
    法律问答语料库构建器
    
    负责构建高质量的法律问答数据集，包括：
    - 问答对收集和预处理
    - 质量评估和专家审核
    - 数据增强和标准化
    """
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化语料库构建器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        
        # 初始化分词器
        self._init_tokenizer()
        
        # 加载质量评估标准
        self.quality_standards = self._load_quality_standards()
        
        # 初始化分类器
        self.category_classifier = self._init_category_classifier()
        
        # 统计信息
        self.stats = {
            'total_collected': 0,
            'high_quality_pairs': 0,
            'expert_reviewed': 0,
            'categories_covered': 0,
            'average_quality_score': 0.0
        }
        
        logger.info("法律问答语料库构建器初始化完成")
    
    def _init_tokenizer(self):
        """初始化中文分词器"""
        try:
            # 加载法律专业词典
            jieba.load_userdict('data/dictionaries/legal_terms.txt')
            jieba.enable_paddle()
            
            logger.info("中文分词器初始化完成")
            
        except Exception as e:
            logger.warning(f"分词器初始化失败: {e}")
    
    def _load_quality_standards(self) -> Dict[str, Any]:
        """加载问答质量评估标准"""
        return {
            'min_question_length': 5,      # 问题最小长度
            'max_question_length': 500,    # 问题最大长度
            'min_answer_length': 20,       # 答案最小长度
            'max_answer_length': 2000,     # 答案最大长度
            'required_keywords': [         # 必需关键词
                '法律', '法规', '条例', '规定', '权利', '义务', '责任'
            ],
            'quality_thresholds': {        # 质量阈值
                'high': 0.8,
                'medium': 0.6,
                'low': 0.4
            },
            'legal_categories': [          # 法律分类
                '民法', '刑法', '商法', '行政法', '劳动法',
                '知识产权法', '环境法', '税法', '金融法', '其他'
            ],
            'intent_types': [              # 意图类型
                'consultation', 'contract', 'dispute', 'procedure',
                'regulation', 'rights', 'penalty', 'other'
            ]
        }
    
    def _init_category_classifier(self):
        """初始化分类器"""
        # 这里使用简单的关键词分类，实际项目中可以使用机器学习模型
        self.category_keywords = {
            '民法': ['合同', '婚姻', '财产', '继承', '侵权', '人格权'],
            '刑法': ['犯罪', '刑事', '判刑', '坐牢', '罚金', '刑罚'],
            '劳动法': ['劳动', '工作', '工资', '加班', '辞职', '社保'],
            '商法': ['公司', '股东', '投资', '商业', '经营', '破产'],
            '行政法': ['行政', '政府', '公务员', '行政处罚', '许可'],
            '知识产权法': ['专利', '商标', '著作权', '版权', '知识产权'],
            '环境法': ['环境', '污染', '环保', '生态', '排放'],
            '税法': ['税收', '纳税', '税务', '发票', '税率'],
            '金融法': ['银行', '贷款', '证券', '保险', '金融']
        }
        
        logger.info("分类器初始化完成")
    
    def collect_qa_pairs_from_sources(self, sources: List[str]) -> List[QAPair]:
        """
        从多个数据源收集问答对
        
        Args:
            sources: 数据源列表
            
        Returns:
            List[QAPair]: 收集的问答对列表
        """
        collected_pairs = []
        
        for source in sources:
            try:
                logger.info(f"开始从数据源收集问答对: {source}")
                
                if source == 'database':
                    pairs = self._collect_from_database()
                elif source == 'legal_websites':
                    pairs = self._collect_from_legal_websites()
                elif source == 'expert_annotations':
                    pairs = self._collect_from_expert_annotations()
                elif source == 'synthetic_generation':
                    pairs = self._generate_synthetic_qa_pairs()
                else:
                    logger.warning(f"未知数据源: {source}")
                    continue
                
                collected_pairs.extend(pairs)
                logger.info(f"从 {source} 收集到 {len(pairs)} 个问答对")
                
            except Exception as e:
                logger.error(f"从数据源 {source} 收集问答对失败: {e}")
        
        self.stats['total_collected'] = len(collected_pairs)
        logger.info(f"总共收集到 {len(collected_pairs)} 个问答对")
        
        return collected_pairs
    
    def _collect_from_database(self) -> List[QAPair]:
        """从数据库收集现有问答对"""
        qa_pairs = []
        
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # 查询现有的问答记录
            query = """
                SELECT question, answer, intent, confidence, created_at
                FROM qa_records 
                WHERE confidence >= 0.7 
                ORDER BY created_at DESC 
                LIMIT 1000
            """
            
            cursor.execute(query)
            records = cursor.fetchall()
            
            for record in records:
                qa_pair = QAPair(
                    id=f"db_{hash(record['question'])}",
                    question=record['question'],
                    answer=record['answer'],
                    category=self._classify_category(record['question']),
                    intent=record['intent'],
                    difficulty='medium',
                    keywords=self._extract_keywords(record['question']),
                    sources=['database'],
                    quality_score=float(record['confidence']),
                    expert_reviewed=False,
                    created_at=record['created_at'].isoformat(),
                    updated_at=datetime.now().isoformat()
                )
                qa_pairs.append(qa_pair)
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"从数据库收集问答对失败: {e}")
        
        return qa_pairs
    
    def _collect_from_legal_websites(self) -> List[QAPair]:
        """从法律网站收集问答对"""
        # 模拟从法律咨询网站收集问答对
        sample_qa_pairs = [
            {
                'question': '劳动合同可以随时解除吗？',
                'answer': '劳动合同的解除需要符合法定条件。用人单位不能随意解除劳动合同，需要有正当理由...',
                'category': '劳动法',
                'intent': 'contract'
            },
            {
                'question': '交通事故责任如何认定？',
                'answer': '交通事故责任认定由交警部门根据当事人的行为对发生道路交通事故所起的作用以及过错的严重程度确定...',
                'category': '民法',
                'intent': 'dispute'
            },
            # 更多示例数据...
        ]
        
        qa_pairs = []
        for i, data in enumerate(sample_qa_pairs):
            qa_pair = QAPair(
                id=f"web_{i}",
                question=data['question'],
                answer=data['answer'],
                category=data['category'],
                intent=data['intent'],
                difficulty='medium',
                keywords=self._extract_keywords(data['question']),
                sources=['legal_websites'],
                quality_score=0.8,
                expert_reviewed=False,
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            qa_pairs.append(qa_pair)
        
        return qa_pairs
    
    def _collect_from_expert_annotations(self) -> List[QAPair]:
        """从专家标注数据收集问答对"""
        # 模拟专家标注的高质量问答对
        expert_qa_pairs = [
            {
                'question': '公司法人代表的法律责任有哪些？',
                'answer': '公司法人代表的法律责任主要包括：1.民事责任：对公司债务承担连带责任的情形；2.行政责任：违反行政法规的处罚；3.刑事责任：构成犯罪的刑事处罚...',
                'category': '商法',
                'intent': 'rights'
            }
        ]
        
        qa_pairs = []
        for i, data in enumerate(expert_qa_pairs):
            qa_pair = QAPair(
                id=f"expert_{i}",
                question=data['question'],
                answer=data['answer'],
                category=data['category'],
                intent=data['intent'],
                difficulty='high',
                keywords=self._extract_keywords(data['question']),
                sources=['expert_annotations'],
                quality_score=0.95,
                expert_reviewed=True,
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            qa_pairs.append(qa_pair)
        
        return qa_pairs
    
    def _generate_synthetic_qa_pairs(self) -> List[QAPair]:
        """生成合成问答对"""
        # 基于模板生成问答对
        templates = [
            {
                'question_template': '{category}中关于{topic}的规定是什么？',
                'answer_template': '根据{category}相关法律规定，关于{topic}的具体规定如下：...',
                'variables': {
                    'category': ['民法典', '劳动法', '公司法'],
                    'topic': ['合同履行', '工资支付', '股东权利']
                }
            }
        ]
        
        synthetic_pairs = []
        for template in templates:
            for category in template['variables']['category']:
                for topic in template['variables']['topic']:
                    question = template['question_template'].format(
                        category=category, topic=topic
                    )
                    answer = template['answer_template'].format(
                        category=category, topic=topic
                    )
                    
                    qa_pair = QAPair(
                        id=f"synthetic_{len(synthetic_pairs)}",
                        question=question,
                        answer=answer,
                        category=self._classify_category(question),
                        intent='regulation',
                        difficulty='easy',
                        keywords=self._extract_keywords(question),
                        sources=['synthetic_generation'],
                        quality_score=0.7,
                        expert_reviewed=False,
                        created_at=datetime.now().isoformat(),
                        updated_at=datetime.now().isoformat()
                    )
                    synthetic_pairs.append(qa_pair)
        
        return synthetic_pairs
    
    def evaluate_qa_quality(self, qa_pair: QAPair) -> QualityMetrics:
        """
        评估问答对质量
        
        Args:
            qa_pair: 问答对
            
        Returns:
            QualityMetrics: 质量评估指标
        """
        # 1. 完整性评估
        completeness = self._evaluate_completeness(qa_pair)
        
        # 2. 准确性评估
        accuracy = self._evaluate_accuracy(qa_pair)
        
        # 3. 相关性评估
        relevance = self._evaluate_relevance(qa_pair)
        
        # 4. 清晰度评估
        clarity = self._evaluate_clarity(qa_pair)
        
        # 5. 综合评分
        overall_score = (completeness + accuracy + relevance + clarity) / 4
        
        return QualityMetrics(
            completeness=completeness,
            accuracy=accuracy,
            relevance=relevance,
            clarity=clarity,
            overall_score=overall_score
        )
    
    def _evaluate_completeness(self, qa_pair: QAPair) -> float:
        """评估完整性"""
        score = 0.0
        
        # 问题长度检查
        if (self.quality_standards['min_question_length'] <= 
            len(qa_pair.question) <= 
            self.quality_standards['max_question_length']):
            score += 0.25
        
        # 答案长度检查
        if (self.quality_standards['min_answer_length'] <= 
            len(qa_pair.answer) <= 
            self.quality_standards['max_answer_length']):
            score += 0.25
        
        # 必需字段检查
        if qa_pair.category and qa_pair.intent:
            score += 0.25
        
        # 关键词检查
        if qa_pair.keywords and len(qa_pair.keywords) >= 3:
            score += 0.25
        
        return score
    
    def _evaluate_accuracy(self, qa_pair: QAPair) -> float:
        """评估准确性"""
        score = 0.8  # 基础分数
        
        # 检查是否包含法律关键词
        legal_keywords = self.quality_standards['required_keywords']
        found_keywords = sum(1 for keyword in legal_keywords 
                           if keyword in qa_pair.question or keyword in qa_pair.answer)
        
        if found_keywords >= 2:
            score += 0.2
        
        return min(score, 1.0)
    
    def _evaluate_relevance(self, qa_pair: QAPair) -> float:
        """评估相关性"""
        score = 0.0
        
        # 问题与答案的相关性
        question_keywords = set(self._extract_keywords(qa_pair.question))
        answer_keywords = set(self._extract_keywords(qa_pair.answer))
        
        if question_keywords and answer_keywords:
            overlap = len(question_keywords & answer_keywords)
            total = len(question_keywords | answer_keywords)
            score = overlap / total if total > 0 else 0
        
        return score
    
    def _evaluate_clarity(self, qa_pair: QAPair) -> float:
        """评估清晰度"""
        score = 0.8  # 基础分数
        
        # 检查问题是否以问号结尾
        if qa_pair.question.endswith('？') or qa_pair.question.endswith('?'):
            score += 0.1
        
        # 检查答案结构
        if '。' in qa_pair.answer and len(qa_pair.answer.split('。')) >= 2:
            score += 0.1
        
        return min(score, 1.0)
    
    def _classify_category(self, text: str) -> str:
        """分类法律类别"""
        for category, keywords in self.category_keywords.items():
            if any(keyword in text for keyword in keywords):
                return category
        return '其他'
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        words = jieba.cut(text)
        keywords = [word for word in words if len(word) >= 2]
        return list(set(keywords))[:10]  # 返回前10个去重关键词
    
    def filter_high_quality_pairs(self, qa_pairs: List[QAPair]) -> List[QAPair]:
        """筛选高质量问答对"""
        high_quality_pairs = []
        
        for qa_pair in qa_pairs:
            quality_metrics = self.evaluate_qa_quality(qa_pair)
            qa_pair.quality_score = quality_metrics.overall_score
            
            # 只保留高质量的问答对
            if quality_metrics.overall_score >= self.quality_standards['quality_thresholds']['high']:
                high_quality_pairs.append(qa_pair)
        
        self.stats['high_quality_pairs'] = len(high_quality_pairs)
        logger.info(f"筛选出 {len(high_quality_pairs)} 个高质量问答对")
        
        return high_quality_pairs
    
    def augment_qa_data(self, qa_pairs: List[QAPair]) -> List[QAPair]:
        """数据增强"""
        augmented_pairs = qa_pairs.copy()
        
        # 1. 同义词替换
        for qa_pair in qa_pairs[:10]:  # 限制数量
            augmented_question = self._synonym_replacement(qa_pair.question)
            if augmented_question != qa_pair.question:
                new_pair = QAPair(
                    id=f"{qa_pair.id}_aug",
                    question=augmented_question,
                    answer=qa_pair.answer,
                    category=qa_pair.category,
                    intent=qa_pair.intent,
                    difficulty=qa_pair.difficulty,
                    keywords=self._extract_keywords(augmented_question),
                    sources=qa_pair.sources + ['augmentation'],
                    quality_score=qa_pair.quality_score * 0.9,  # 略微降低质量分
                    expert_reviewed=False,
                    created_at=datetime.now().isoformat(),
                    updated_at=datetime.now().isoformat()
                )
                augmented_pairs.append(new_pair)
        
        logger.info(f"数据增强后共有 {len(augmented_pairs)} 个问答对")
        return augmented_pairs
    
    def _synonym_replacement(self, text: str) -> str:
        """同义词替换"""
        synonyms = {
            '合同': '协议',
            '法律': '法规',
            '责任': '义务',
            '权利': '权益'
        }
        
        for original, synonym in synonyms.items():
            if original in text:
                return text.replace(original, synonym, 1)
        
        return text
    
    def save_qa_corpus(self, qa_pairs: List[QAPair]) -> bool:
        """保存问答语料库"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 创建表（如果不存在）
            create_table_sql = """
                CREATE TABLE IF NOT EXISTS qa_corpus (
                    id VARCHAR(255) PRIMARY KEY,
                    question TEXT NOT NULL,
                    answer TEXT NOT NULL,
                    category VARCHAR(100),
                    intent VARCHAR(100),
                    difficulty VARCHAR(50),
                    keywords TEXT[],
                    sources TEXT[],
                    quality_score FLOAT,
                    expert_reviewed BOOLEAN,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            """
            cursor.execute(create_table_sql)
            
            # 批量插入数据
            insert_sql = """
                INSERT INTO qa_corpus (
                    id, question, answer, category, intent, difficulty,
                    keywords, sources, quality_score, expert_reviewed,
                    created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    question = EXCLUDED.question,
                    answer = EXCLUDED.answer,
                    quality_score = EXCLUDED.quality_score,
                    updated_at = EXCLUDED.updated_at
            """
            
            for qa_pair in qa_pairs:
                cursor.execute(insert_sql, (
                    qa_pair.id, qa_pair.question, qa_pair.answer,
                    qa_pair.category, qa_pair.intent, qa_pair.difficulty,
                    qa_pair.keywords, qa_pair.sources, qa_pair.quality_score,
                    qa_pair.expert_reviewed, qa_pair.created_at, qa_pair.updated_at
                ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info(f"成功保存 {len(qa_pairs)} 个问答对到数据库")
            return True
            
        except Exception as e:
            logger.error(f"保存问答语料库失败: {e}")
            return False
    
    def generate_corpus_report(self, qa_pairs: List[QAPair]) -> Dict[str, Any]:
        """生成语料库报告"""
        # 统计各类别分布
        category_dist = Counter(qa.category for qa in qa_pairs)
        intent_dist = Counter(qa.intent for qa in qa_pairs)
        difficulty_dist = Counter(qa.difficulty for qa in qa_pairs)
        
        # 计算平均质量分
        avg_quality = sum(qa.quality_score for qa in qa_pairs) / len(qa_pairs) if qa_pairs else 0
        
        # 专家审核统计
        expert_reviewed_count = sum(1 for qa in qa_pairs if qa.expert_reviewed)
        
        report = {
            'total_qa_pairs': len(qa_pairs),
            'high_quality_pairs': len([qa for qa in qa_pairs if qa.quality_score >= 0.8]),
            'expert_reviewed_pairs': expert_reviewed_count,
            'average_quality_score': avg_quality,
            'category_distribution': dict(category_dist),
            'intent_distribution': dict(intent_dist),
            'difficulty_distribution': dict(difficulty_dist),
            'coverage_metrics': {
                'categories_covered': len(category_dist),
                'intents_covered': len(intent_dist),
                'expert_review_rate': expert_reviewed_count / len(qa_pairs) if qa_pairs else 0
            }
        }
        
        return report

def main():
    """主函数"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'legal_assistant',
        'user': 'postgres',
        'password': 'password'
    }
    
    # 创建语料库构建器
    builder = QACorpusBuilder(db_config)
    
    # 收集问答对
    sources = ['database', 'legal_websites', 'expert_annotations', 'synthetic_generation']
    qa_pairs = builder.collect_qa_pairs_from_sources(sources)
    
    # 筛选高质量问答对
    high_quality_pairs = builder.filter_high_quality_pairs(qa_pairs)
    
    # 数据增强
    augmented_pairs = builder.augment_qa_data(high_quality_pairs)
    
    # 保存语料库
    success = builder.save_qa_corpus(augmented_pairs)
    
    if success:
        # 生成报告
        report = builder.generate_corpus_report(augmented_pairs)
        
        # 保存报告
        with open('data/reports/qa_corpus_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("法律问答语料库构建完成")
        logger.info(f"语料库报告: {json.dumps(report, ensure_ascii=False, indent=2)}")
    else:
        logger.error("法律问答语料库构建失败")

if __name__ == "__main__":
    main()
