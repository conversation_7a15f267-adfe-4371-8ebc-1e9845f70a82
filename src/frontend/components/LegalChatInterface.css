/**
 * AI法律助手聊天界面样式
 * 
 * 功能描述：
 * - 响应式聊天界面设计
 * - 美观的消息气泡样式
 * - 流畅的动画效果
 * - 多设备适配
 * 
 * 作者：AI法律助手开发团队
 * 创建时间：2024年8月26日
 */

/* 主容器样式 */
.legal-chat-interface {
  max-width: 800px;
  margin: 0 auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 聊天卡片样式 */
.chat-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
}

.chat-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  height: 100%;
}

/* 聊天头部样式 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 消息容器样式 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

/* 自定义滚动条 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 欢迎消息样式 */
.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 400px;
  padding: 40px 20px;
}

.quick-questions {
  margin-top: 24px;
  text-align: left;
}

/* 消息列表样式 */
.messages-list {
  padding: 0;
}

.messages-list .ant-list-item {
  border: none;
  padding: 0;
  margin-bottom: 16px;
}

/* 消息项样式 */
.message-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  animation: messageSlideIn 0.3s ease-out;
}

.user-message {
  flex-direction: row-reverse;
}

.system-message {
  justify-content: center;
}

.system-message .message-content {
  background: #f6f6f6;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 8px 12px;
  max-width: 300px;
  text-align: center;
}

/* 消息头像样式 */
.message-avatar {
  flex-shrink: 0;
}

/* 消息内容样式 */
.message-content {
  flex: 1;
  max-width: 70%;
  position: relative;
}

.user-message .message-content {
  text-align: right;
}

/* 消息头部样式 */
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.message-time {
  font-size: 12px;
  margin: 0 8px;
}

/* 消息主体样式 */
.message-body {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
}

.user-message .message-body {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.user-message .message-body .ant-typography {
  color: white;
}

/* 消息气泡尾巴 */
.message-body::before {
  content: '';
  position: absolute;
  top: 12px;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #ffffff;
}

.user-message .message-body::before {
  left: auto;
  right: -8px;
  border-right: none;
  border-left: 8px solid #1890ff;
}

.system-message .message-body::before {
  display: none;
}

/* 消息文本样式 */
.message-text {
  margin: 0;
  word-wrap: break-word;
  line-height: 1.6;
}

/* 置信度显示样式 */
.message-confidence {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.user-message .message-confidence {
  border-top-color: rgba(255, 255, 255, 0.3);
}

/* 消息来源样式 */
.message-sources {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.user-message .message-sources {
  border-top-color: rgba(255, 255, 255, 0.3);
}

.message-sources .ant-tag {
  margin-bottom: 4px;
}

/* 相关问题样式 */
.related-questions {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.user-message .related-questions {
  border-top-color: rgba(255, 255, 255, 0.3);
}

.related-questions .ant-btn-link {
  color: #1890ff;
  text-decoration: none;
  display: block;
  white-space: normal;
  margin-bottom: 4px;
}

.user-message .related-questions .ant-btn-link {
  color: rgba(255, 255, 255, 0.9);
}

.related-questions .ant-btn-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 正在输入指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator .ant-spin {
  margin-right: 8px;
}

/* 消息操作按钮 */
.message-actions {
  position: absolute;
  top: -8px;
  right: 8px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 4px;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.user-message .message-actions {
  right: auto;
  left: 8px;
}

/* 输入区域样式 */
.input-area {
  background: #fafafa;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e8e8e8;
}

.input-area .ant-input {
  border: none;
  box-shadow: none;
  background: transparent;
  resize: none;
}

.input-area .ant-input:focus {
  border: none;
  box-shadow: none;
}

/* 动画效果 */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .legal-chat-interface {
    height: 100vh;
    margin: 0;
    max-width: 100%;
  }
  
  .chat-card {
    height: 100vh;
    border-radius: 0;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .welcome-content {
    padding: 20px 16px;
  }
  
  .input-area {
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-bottom: none;
  }
}

@media (max-width: 480px) {
  .message-content {
    max-width: 90%;
  }
  
  .message-body {
    padding: 8px 12px;
  }
  
  .chat-header .ant-typography {
    font-size: 16px;
  }
  
  .input-area .ant-row {
    gap: 8px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .message-body {
    background: #1f1f1f;
    border-color: #434343;
    color: #ffffff;
  }
  
  .message-body::before {
    border-right-color: #1f1f1f;
  }
  
  .user-message .message-body::before {
    border-left-color: #1890ff;
  }
  
  .message-confidence,
  .message-sources,
  .related-questions {
    border-top-color: #434343;
  }
  
  .input-area {
    background: #1f1f1f;
    border-color: #434343;
  }
  
  .message-actions {
    background: #1f1f1f;
    border: 1px solid #434343;
  }
}

/* 打印样式 */
@media print {
  .chat-header,
  .input-area,
  .message-actions {
    display: none;
  }
  
  .messages-container {
    overflow: visible;
    height: auto;
  }
  
  .message-body {
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .message-body::before {
    display: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .message-body {
    border-width: 2px;
  }
  
  .user-message .message-body {
    background: #0066cc;
  }
  
  .ant-tag {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .message-item {
    animation: none;
  }
  
  .message-actions {
    transition: none;
  }
  
  .messages-container {
    scroll-behavior: auto;
  }
}
