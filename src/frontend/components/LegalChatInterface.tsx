/**
 * AI法律助手聊天界面组件
 * 
 * 功能描述：
 * - 提供用户友好的聊天式法律咨询界面
 * - 支持多轮对话、历史记录、文件上传等功能
 * - 实现响应式设计，支持多设备适配
 * - 集成实时消息更新和状态管理
 * 
 * 技术栈：
 * - React 18+ with TypeScript
 * - Ant Design 5.0+
 * - Socket.IO (实时通信)
 * - React Query (状态管理)
 * 
 * 作者：AI法律助手开发团队
 * 创建时间：2024年8月26日
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Input,
  Button,
  Avatar,
  List,
  Typography,
  Space,
  Spin,
  Alert,
  Tag,
  Tooltip,
  Upload,
  Dropdown,
  Modal,
  Progress,
  Divider,
  Row,
  Col
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  PaperClipOutlined,
  HistoryOutlined,
  QuestionCircleOutlined,
  StarOutlined,
  CopyOutlined,
  DownloadOutlined,
  MoreOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { io, Socket } from 'socket.io-client';
import dayjs from 'dayjs';
import './LegalChatInterface.css';

const { TextArea } = Input;
const { Text, Title, Paragraph } = Typography;

// 类型定义
interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  confidence?: number;
  sources?: Array<{
    title: string;
    content: string;
    type: 'law' | 'case' | 'qa';
  }>;
  relatedQuestions?: string[];
  isTyping?: boolean;
  metadata?: Record<string, any>;
}

interface ChatSession {
  sessionId: string;
  userId: string;
  title: string;
  lastMessage: string;
  lastActivity: string;
  messageCount: number;
}

interface LegalChatProps {
  userId: string;
  initialQuestion?: string;
  onQuestionSubmit?: (question: string) => void;
  className?: string;
}

// API服务函数
const chatAPI = {
  // 发送问题到后端
  sendQuestion: async (data: {
    question: string;
    sessionId?: string;
    context?: string;
  }) => {
    const response = await fetch('/api/v1/legal/question', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error('发送问题失败');
    }
    
    return response.json();
  },

  // 获取聊天历史
  getChatHistory: async (sessionId: string) => {
    const response = await fetch(`/api/v1/legal/history?session_id=${sessionId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!response.ok) {
      throw new Error('获取历史记录失败');
    }
    
    return response.json();
  },

  // 获取会话列表
  getSessions: async () => {
    const response = await fetch('/api/v1/legal/sessions', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!response.ok) {
      throw new Error('获取会话列表失败');
    }
    
    return response.json();
  }
};

const LegalChatInterface: React.FC<LegalChatProps> = ({
  userId,
  initialQuestion,
  onQuestionSubmit,
  className
}) => {
  // 状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState<string>(initialQuestion || '');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [socket, setSocket] = useState<Socket | null>(null);
  const [showHistory, setShowHistory] = useState<boolean>(false);
  const [selectedSources, setSelectedSources] = useState<any[]>([]);
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<any>(null);
  
  // React Query
  const queryClient = useQueryClient();
  
  // 发送问题的mutation
  const sendQuestionMutation = useMutation({
    mutationFn: chatAPI.sendQuestion,
    onSuccess: (data) => {
      // 添加AI回答到消息列表
      const aiMessage: ChatMessage = {
        id: `ai_${Date.now()}`,
        type: 'assistant',
        content: data.answer,
        timestamp: new Date().toISOString(),
        confidence: data.confidence,
        sources: data.sources,
        relatedQuestions: data.related_questions
      };
      
      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
      
      // 滚动到底部
      scrollToBottom();
    },
    onError: (error) => {
      console.error('发送问题失败:', error);
      
      // 添加错误消息
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: 'system',
        content: '抱歉，系统暂时无法处理您的问题，请稍后重试。',
        timestamp: new Date().toISOString()
      };
      
      setMessages(prev => [...prev, errorMessage]);
      setIsLoading(false);
    }
  });

  // 初始化Socket连接
  useEffect(() => {
    const newSocket = io('/legal-chat', {
      auth: {
        token: localStorage.getItem('token')
      }
    });

    newSocket.on('connect', () => {
      console.log('Socket连接成功');
    });

    newSocket.on('typing', (data) => {
      // 显示AI正在输入的状态
      if (data.isTyping) {
        const typingMessage: ChatMessage = {
          id: 'typing',
          type: 'assistant',
          content: 'AI法律助手正在思考中...',
          timestamp: new Date().toISOString(),
          isTyping: true
        };
        setMessages(prev => [...prev.filter(m => m.id !== 'typing'), typingMessage]);
      } else {
        setMessages(prev => prev.filter(m => m.id !== 'typing'));
      }
    });

    newSocket.on('message', (data) => {
      // 接收实时消息
      const newMessage: ChatMessage = {
        id: data.id,
        type: data.type,
        content: data.content,
        timestamp: data.timestamp,
        confidence: data.confidence,
        sources: data.sources
      };
      
      setMessages(prev => [...prev.filter(m => m.id !== 'typing'), newMessage]);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, []);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // 监听消息变化，自动滚动
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 处理问题提交
  const handleQuestionSubmit = async () => {
    if (!currentQuestion.trim() || isLoading) {
      return;
    }

    const question = currentQuestion.trim();
    
    // 添加用户消息到列表
    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: question,
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setCurrentQuestion('');
    setIsLoading(true);

    // 触发外部回调
    onQuestionSubmit?.(question);

    // 发送到后端
    try {
      await sendQuestionMutation.mutateAsync({
        question,
        sessionId: sessionId || undefined,
        context: messages.slice(-5).map(m => m.content).join('\n')
      });
    } catch (error) {
      console.error('发送问题失败:', error);
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleQuestionSubmit();
    }
  };

  // 复制消息内容
  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    // 可以添加成功提示
  };

  // 收藏问答
  const handleStarMessage = (messageId: string) => {
    // 实现收藏功能
    console.log('收藏消息:', messageId);
  };

  // 查看消息来源
  const handleViewSources = (sources: any[]) => {
    setSelectedSources(sources);
    // 可以打开模态框显示来源详情
  };

  // 渲染消息项
  const renderMessage = (message: ChatMessage) => {
    const isUser = message.type === 'user';
    const isSystem = message.type === 'system';
    
    return (
      <div
        key={message.id}
        className={`message-item ${isUser ? 'user-message' : 'assistant-message'} ${
          isSystem ? 'system-message' : ''
        }`}
      >
        <div className="message-avatar">
          <Avatar
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            style={{
              backgroundColor: isUser ? '#1890ff' : '#52c41a',
            }}
          />
        </div>
        
        <div className="message-content">
          <div className="message-header">
            <Text strong>{isUser ? '您' : 'AI法律助手'}</Text>
            <Text type="secondary" className="message-time">
              {dayjs(message.timestamp).format('HH:mm')}
            </Text>
          </div>
          
          <div className="message-body">
            {message.isTyping ? (
              <div className="typing-indicator">
                <Spin size="small" />
                <Text type="secondary" style={{ marginLeft: 8 }}>
                  {message.content}
                </Text>
              </div>
            ) : (
              <Paragraph className="message-text">
                {message.content}
              </Paragraph>
            )}
            
            {/* 置信度显示 */}
            {message.confidence && (
              <div className="message-confidence">
                <Text type="secondary">
                  置信度: 
                </Text>
                <Progress
                  percent={Math.round(message.confidence * 100)}
                  size="small"
                  style={{ width: 100, marginLeft: 8 }}
                />
              </div>
            )}
            
            {/* 答案来源 */}
            {message.sources && message.sources.length > 0 && (
              <div className="message-sources">
                <Text type="secondary">参考来源：</Text>
                <Space wrap>
                  {message.sources.map((source, index) => (
                    <Tag
                      key={index}
                      color={source.type === 'law' ? 'blue' : source.type === 'case' ? 'green' : 'orange'}
                      style={{ cursor: 'pointer' }}
                      onClick={() => handleViewSources([source])}
                    >
                      {source.title}
                    </Tag>
                  ))}
                </Space>
              </div>
            )}
            
            {/* 相关问题推荐 */}
            {message.relatedQuestions && message.relatedQuestions.length > 0 && (
              <div className="related-questions">
                <Text type="secondary">相关问题：</Text>
                <div style={{ marginTop: 8 }}>
                  {message.relatedQuestions.map((question, index) => (
                    <Button
                      key={index}
                      type="link"
                      size="small"
                      onClick={() => setCurrentQuestion(question)}
                      style={{ padding: '4px 8px', height: 'auto', textAlign: 'left' }}
                    >
                      {question}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          {/* 消息操作按钮 */}
          {!message.isTyping && !isSystem && (
            <div className="message-actions">
              <Space>
                <Tooltip title="复制">
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => handleCopyMessage(message.content)}
                  />
                </Tooltip>
                
                {!isUser && (
                  <Tooltip title="收藏">
                    <Button
                      type="text"
                      size="small"
                      icon={<StarOutlined />}
                      onClick={() => handleStarMessage(message.id)}
                    />
                  </Tooltip>
                )}
                
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'report',
                        label: '举报问题',
                        icon: <QuestionCircleOutlined />
                      },
                      {
                        key: 'export',
                        label: '导出对话',
                        icon: <DownloadOutlined />
                      }
                    ]
                  }}
                  trigger={['click']}
                >
                  <Button type="text" size="small" icon={<MoreOutlined />} />
                </Dropdown>
              </Space>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`legal-chat-interface ${className}`}>
      <Card
        title={
          <div className="chat-header">
            <Space>
              <RobotOutlined style={{ color: '#52c41a' }} />
              <Title level={4} style={{ margin: 0 }}>
                AI法律助手
              </Title>
            </Space>
            
            <Space>
              <Tooltip title="查看历史">
                <Button
                  type="text"
                  icon={<HistoryOutlined />}
                  onClick={() => setShowHistory(true)}
                />
              </Tooltip>
            </Space>
          </div>
        }
        className="chat-card"
      >
        {/* 消息列表区域 */}
        <div className="messages-container">
          {messages.length === 0 ? (
            <div className="welcome-message">
              <div className="welcome-content">
                <RobotOutlined style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }} />
                <Title level={3}>欢迎使用AI法律助手</Title>
                <Paragraph type="secondary">
                  我可以为您提供专业的法律咨询服务，请描述您遇到的法律问题。
                </Paragraph>
                
                {/* 快捷问题模板 */}
                <div className="quick-questions">
                  <Text strong>常见问题：</Text>
                  <div style={{ marginTop: 8 }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {[
                        '劳动合同可以随时解除吗？',
                        '交通事故责任如何认定？',
                        '房屋买卖合同注意事项',
                        '公司法人代表的责任'
                      ].map((question, index) => (
                        <Button
                          key={index}
                          type="dashed"
                          block
                          onClick={() => setCurrentQuestion(question)}
                        >
                          {question}
                        </Button>
                      ))}
                    </Space>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <List
              className="messages-list"
              dataSource={messages}
              renderItem={renderMessage}
              split={false}
            />
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        {/* 输入区域 */}
        <div className="input-area">
          <Row gutter={8} align="middle">
            <Col flex="auto">
              <TextArea
                ref={inputRef}
                value={currentQuestion}
                onChange={(e) => setCurrentQuestion(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="请输入您的法律问题..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                disabled={isLoading}
              />
            </Col>
            
            <Col>
              <Space direction="vertical">
                <Upload
                  showUploadList={false}
                  beforeUpload={() => false}
                  accept=".pdf,.doc,.docx,.txt"
                >
                  <Tooltip title="上传文件">
                    <Button
                      type="text"
                      icon={<PaperClipOutlined />}
                      disabled={isLoading}
                    />
                  </Tooltip>
                </Upload>
                
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleQuestionSubmit}
                  loading={isLoading}
                  disabled={!currentQuestion.trim()}
                >
                  发送
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      </Card>
      
      {/* 免责声明 */}
      <Alert
        message="免责声明"
        description="本AI助手提供的法律建议仅供参考，不构成正式法律意见。如需专业法律服务，请咨询执业律师。"
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </div>
  );
};

export default LegalChatInterface;
