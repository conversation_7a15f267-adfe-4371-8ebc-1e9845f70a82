#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI法律问答API接口

功能描述：
- 提供法律问答的核心API服务
- 支持问题接收、预处理、答案生成和后处理
- 实现多轮对话和历史记录管理
- 集成意图识别和答案质量评估

技术栈：
- FastAPI 0.104+
- Pydantic 2.0+
- SQLAlchemy 2.0+
- Redis 5.0+

作者：AI法律助手开发团队
创建时间：2024年8月26日
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import json
import uuid
import time
import asyncio
from contextlib import asynccontextmanager

# 内部模块导入（实际项目中的相对导入）
# from ..services.legal_qa_service import LegalQAService
# from ..services.intent_classifier import IntentClassifier
# from ..services.answer_generator import AnswerGenerator
# from ..models.database import get_db_session
# from ..models.user import User
# from ..models.qa_record import QARecord
# from ..utils.auth import verify_token, get_current_user
# from ..utils.rate_limiter import RateLimiter
# from ..utils.logger import setup_logger

# 临时导入（用于演示）
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# 配置日志
logger = setup_logger(__name__)

# 安全认证
security = HTTPBearer()

# 请求模型定义
class QuestionRequest(BaseModel):
    """法律问题请求模型"""
    question: str = Field(
        ..., 
        min_length=5, 
        max_length=1000, 
        description="法律问题内容，长度5-1000字符"
    )
    category: Optional[str] = Field(
        None, 
        description="问题分类：民法/刑法/商法/行政法/劳动法/知识产权法等"
    )
    context: Optional[str] = Field(
        None, 
        max_length=2000,
        description="问题上下文，用于多轮对话"
    )
    session_id: Optional[str] = Field(
        None, 
        description="会话ID，用于多轮对话管理"
    )
    priority: Optional[str] = Field(
        "normal", 
        description="问题优先级：low/normal/high/urgent"
    )
    
    @validator('question')
    def validate_question(cls, v):
        """验证问题内容"""
        if not v or not v.strip():
            raise ValueError('问题内容不能为空')
        
        # 检查是否包含敏感内容
        sensitive_words = ['暴力', '违法', '犯罪具体操作']
        if any(word in v for word in sensitive_words):
            raise ValueError('问题内容包含敏感词汇')
        
        return v.strip()
    
    @validator('category')
    def validate_category(cls, v):
        """验证问题分类"""
        if v is not None:
            valid_categories = [
                '民法', '刑法', '商法', '行政法', '劳动法', 
                '知识产权法', '环境法', '税法', '金融法', '其他'
            ]
            if v not in valid_categories:
                raise ValueError(f'无效的问题分类，支持的分类: {", ".join(valid_categories)}')
        return v

class QuestionResponse(BaseModel):
    """法律问题回答模型"""
    answer: str = Field(..., description="AI生成的法律建议")
    confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="回答置信度，范围0-1"
    )
    intent: str = Field(..., description="识别的问题意图")
    sources: List[Dict[str, Any]] = Field(
        default=[], 
        description="答案来源的法条或案例"
    )
    related_questions: List[str] = Field(
        default=[], 
        description="相关问题推荐"
    )
    session_id: str = Field(..., description="会话ID")
    response_time: float = Field(..., description="响应时间（秒）")
    disclaimer: str = Field(
        default="本回答仅供参考，不构成正式法律意见。如需专业法律服务，请咨询执业律师。",
        description="免责声明"
    )

class QAHistoryResponse(BaseModel):
    """问答历史响应模型"""
    total: int = Field(..., description="历史记录总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    records: List[Dict[str, Any]] = Field(..., description="问答记录列表")

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用启动和关闭时的生命周期管理"""
    # 启动时初始化
    logger.info("AI法律问答API服务启动中...")
    
    # 初始化服务组件
    app.state.qa_service = LegalQAService()
    app.state.intent_classifier = IntentClassifier()
    app.state.answer_generator = AnswerGenerator()
    app.state.rate_limiter = RateLimiter()
    
    # 预热模型
    await app.state.qa_service.warmup()
    
    logger.info("AI法律问答API服务启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("AI法律问答API服务关闭中...")
    await app.state.qa_service.cleanup()
    logger.info("AI法律问答API服务已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="AI法律助手问答API",
    description="提供智能法律问答服务的RESTful API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 中间件：请求日志记录
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录所有API请求"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(
        f"收到请求: {request.method} {request.url.path}",
        extra={
            "method": request.method,
            "path": request.url.path,
            "client_ip": request.client.host,
            "user_agent": request.headers.get("user-agent", "")
        }
    )
    
    # 处理请求
    response = await call_next(request)
    
    # 记录响应信息
    process_time = time.time() - start_time
    logger.info(
        f"请求完成: {request.method} {request.url.path} - "
        f"状态码: {response.status_code} - 耗时: {process_time:.3f}s"
    )
    
    return response

# 依赖注入：获取服务实例
async def get_qa_service() -> LegalQAService:
    """获取法律问答服务实例"""
    return app.state.qa_service

async def get_rate_limiter() -> RateLimiter:
    """获取限流器实例"""
    return app.state.rate_limiter

# API路由定义

@app.get("/", tags=["健康检查"])
async def root():
    """API根路径，返回服务状态"""
    return {
        "service": "AI法律助手问答API",
        "version": "1.0.0",
        "status": "运行中",
        "timestamp": datetime.now().isoformat(),
        "docs": "/docs"
    }

@app.get("/health", tags=["健康检查"])
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        async with get_db_session() as db:
            await db.execute("SELECT 1")
        
        # 检查Redis连接
        qa_service = await get_qa_service()
        await qa_service.check_redis_connection()
        
        return {
            "status": "健康",
            "database": "正常",
            "redis": "正常",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")

@app.post("/api/v1/legal/question", response_model=QuestionResponse, tags=["法律问答"])
async def ask_legal_question(
    request: QuestionRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    qa_service: LegalQAService = Depends(get_qa_service),
    rate_limiter: RateLimiter = Depends(get_rate_limiter)
) -> QuestionResponse:
    """
    法律问答核心接口
    
    接收用户的法律问题，返回AI生成的法律建议
    支持多轮对话和上下文理解
    
    Args:
        request: 问题请求对象
        background_tasks: 后台任务
        current_user: 当前登录用户
        qa_service: 法律问答服务
        rate_limiter: 限流器
        
    Returns:
        QuestionResponse: 包含答案、置信度、来源等信息的响应
        
    Raises:
        HTTPException: 当问题处理失败时抛出HTTP异常
    """
    start_time = time.time()
    
    try:
        # 1. 限流检查
        if not await rate_limiter.check_rate_limit(
            user_id=str(current_user.id),
            limit=10,  # 每分钟最多10次请求
            window=60
        ):
            raise HTTPException(
                status_code=429, 
                detail="请求过于频繁，请稍后再试"
            )
        
        # 2. 记录用户问题到审计日志
        logger.info(
            f"用户 {current_user.username} 提出法律问题",
            extra={
                "user_id": str(current_user.id),
                "question_length": len(request.question),
                "category": request.category,
                "session_id": request.session_id
            }
        )
        
        # 3. 生成或使用现有会话ID
        session_id = request.session_id or str(uuid.uuid4())
        
        # 4. 问题预处理和验证
        processed_question = await qa_service.preprocess_question(
            question=request.question,
            context=request.context
        )
        
        # 5. 意图识别和分类
        intent_result = await qa_service.classify_intent(
            question=processed_question,
            category=request.category
        )
        
        # 6. 生成法律建议
        answer_result = await qa_service.generate_answer(
            question=processed_question,
            intent=intent_result.intent,
            context=request.context,
            session_id=session_id,
            user_id=str(current_user.id)
        )
        
        # 7. 计算响应时间
        response_time = time.time() - start_time
        
        # 8. 构建响应
        response = QuestionResponse(
            answer=answer_result.answer,
            confidence=answer_result.confidence,
            intent=intent_result.intent,
            sources=answer_result.sources,
            related_questions=answer_result.related_questions,
            session_id=session_id,
            response_time=response_time
        )
        
        # 9. 后台任务：保存问答记录
        background_tasks.add_task(
            save_qa_record,
            user_id=str(current_user.id),
            question=request.question,
            answer=answer_result.answer,
            intent=intent_result.intent,
            confidence=answer_result.confidence,
            session_id=session_id,
            response_time=response_time
        )
        
        # 10. 记录成功日志
        logger.info(
            f"法律问答处理成功 - 用户: {current_user.username}, "
            f"置信度: {answer_result.confidence:.2f}, "
            f"响应时间: {response_time:.3f}s"
        )
        
        return response
        
    except ValueError as e:
        logger.warning(f"请求参数验证失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"法律问答处理失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, 
            detail="系统内部错误，请稍后重试"
        )

@app.get("/api/v1/legal/history", response_model=QAHistoryResponse, tags=["问答历史"])
async def get_qa_history(
    page: int = Field(1, ge=1, description="页码，从1开始"),
    page_size: int = Field(10, ge=1, le=50, description="每页大小，最大50"),
    session_id: Optional[str] = Field(None, description="会话ID筛选"),
    start_date: Optional[str] = Field(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Field(None, description="结束日期 YYYY-MM-DD"),
    current_user: User = Depends(get_current_user),
    qa_service: LegalQAService = Depends(get_qa_service)
) -> QAHistoryResponse:
    """
    获取用户问答历史记录
    
    支持分页查询和条件筛选
    """
    try:
        # 构建查询条件
        filters = {
            "user_id": str(current_user.id),
            "page": page,
            "page_size": page_size
        }
        
        if session_id:
            filters["session_id"] = session_id
        
        if start_date:
            filters["start_date"] = start_date
        
        if end_date:
            filters["end_date"] = end_date
        
        # 查询历史记录
        history_result = await qa_service.get_qa_history(**filters)
        
        return QAHistoryResponse(
            total=history_result.total,
            page=page,
            page_size=page_size,
            records=history_result.records
        )
        
    except Exception as e:
        logger.error(f"获取问答历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取历史记录失败")

@app.get("/api/v1/legal/intents", tags=["问题分类"])
async def get_supported_intents():
    """获取支持的问题意图类型"""
    return {
        "intents": [
            {"code": "consultation", "name": "法律咨询", "description": "一般性法律问题咨询"},
            {"code": "contract", "name": "合同相关", "description": "合同条款、违约等问题"},
            {"code": "dispute", "name": "纠纷解决", "description": "法律纠纷解决方案"},
            {"code": "procedure", "name": "法律程序", "description": "诉讼、仲裁等程序问题"},
            {"code": "regulation", "name": "法规查询", "description": "法律法规条文查询"},
            {"code": "rights", "name": "权利义务", "description": "权利义务相关问题"},
            {"code": "penalty", "name": "法律责任", "description": "违法后果和法律责任"},
            {"code": "other", "name": "其他", "description": "其他法律相关问题"}
        ]
    }

@app.get("/api/v1/legal/categories", tags=["问题分类"])
async def get_legal_categories():
    """获取支持的法律分类"""
    return {
        "categories": [
            {"code": "civil", "name": "民法", "description": "民事法律关系"},
            {"code": "criminal", "name": "刑法", "description": "刑事法律问题"},
            {"code": "commercial", "name": "商法", "description": "商事法律关系"},
            {"code": "administrative", "name": "行政法", "description": "行政法律关系"},
            {"code": "labor", "name": "劳动法", "description": "劳动关系法律问题"},
            {"code": "intellectual", "name": "知识产权法", "description": "知识产权相关"},
            {"code": "environmental", "name": "环境法", "description": "环境保护法律"},
            {"code": "tax", "name": "税法", "description": "税务法律问题"},
            {"code": "financial", "name": "金融法", "description": "金融法律关系"},
            {"code": "other", "name": "其他", "description": "其他法律领域"}
        ]
    }

# 后台任务函数
async def save_qa_record(
    user_id: str,
    question: str,
    answer: str,
    intent: str,
    confidence: float,
    session_id: str,
    response_time: float
):
    """
    保存问答记录到数据库
    
    这是一个后台任务，不会阻塞API响应
    """
    try:
        async with get_db_session() as db:
            qa_record = QARecord(
                id=uuid.uuid4(),
                user_id=uuid.UUID(user_id),
                question=question,
                answer=answer,
                intent=intent,
                confidence=confidence,
                session_id=session_id,
                response_time=response_time,
                created_at=datetime.now()
            )
            
            db.add(qa_record)
            await db.commit()
            
        logger.debug(f"问答记录保存成功: {session_id}")
        
    except Exception as e:
        logger.error(f"保存问答记录失败: {e}")

# 异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.warning(
        f"HTTP异常: {exc.status_code} - {exc.detail}",
        extra={
            "path": request.url.path,
            "method": request.method,
            "status_code": exc.status_code
        }
    )
    
    return {
        "error": True,
        "status_code": exc.status_code,
        "message": exc.detail,
        "timestamp": datetime.now().isoformat()
    }

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(
        f"未处理的异常: {str(exc)}",
        extra={
            "path": request.url.path,
            "method": request.method,
            "exception_type": type(exc).__name__
        },
        exc_info=True
    )
    
    return {
        "error": True,
        "status_code": 500,
        "message": "系统内部错误",
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    
    # 启动API服务
    uvicorn.run(
        "legal_qa_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )
