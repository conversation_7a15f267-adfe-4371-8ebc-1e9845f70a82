#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合同模板数据模型

功能描述：
- 设计合同模板的数据模型和存储结构
- 支持变量和占位符系统
- 实现模板版本管理
- 支持复杂的合同结构和字段类型

技术栈：
- SQLAlchemy 2.0+ (ORM)
- PostgreSQL (数据库)
- Pydantic (数据验证)
- JSON (复杂数据存储)

作者：AI法律助手开发团队
创建时间：2024年8月26日
"""

from sqlalchemy import (
    Column, String, Text, Integer, Float, Boolean, DateTime, 
    ForeignKey, Index, CheckConstraint, JSON, Enum as SQLEnum
)
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, validates
from sqlalchemy.sql import func
from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid
import json

Base = declarative_base()

class ContractCategory(Enum):
    """合同分类枚举"""
    LABOR = "labor"                    # 劳动合同
    SALES = "sales"                    # 销售合同
    SERVICE = "service"                # 服务合同
    LEASE = "lease"                    # 租赁合同
    PARTNERSHIP = "partnership"        # 合作协议
    EMPLOYMENT = "employment"          # 雇佣合同
    CONFIDENTIALITY = "confidentiality"  # 保密协议
    LICENSING = "licensing"            # 许可协议
    PURCHASE = "purchase"              # 采购合同
    CONSTRUCTION = "construction"      # 建设工程合同
    OTHER = "other"                    # 其他

class TemplateStatus(Enum):
    """模板状态枚举"""
    DRAFT = "draft"                    # 草稿
    ACTIVE = "active"                  # 激活
    ARCHIVED = "archived"              # 归档
    DEPRECATED = "deprecated"          # 已废弃

class FieldType(Enum):
    """字段类型枚举"""
    TEXT = "text"                      # 文本
    NUMBER = "number"                  # 数字
    DATE = "date"                      # 日期
    DATETIME = "datetime"              # 日期时间
    EMAIL = "email"                    # 邮箱
    PHONE = "phone"                    # 电话
    URL = "url"                        # 网址
    CURRENCY = "currency"              # 货币
    PERCENTAGE = "percentage"          # 百分比
    BOOLEAN = "boolean"                # 布尔值
    SELECT = "select"                  # 单选
    MULTISELECT = "multiselect"        # 多选
    TEXTAREA = "textarea"              # 多行文本
    FILE = "file"                      # 文件
    IMAGE = "image"                    # 图片
    SIGNATURE = "signature"            # 签名
    ADDRESS = "address"                # 地址
    PERSON = "person"                  # 人员信息
    COMPANY = "company"                # 公司信息
    LEGAL_ENTITY = "legal_entity"      # 法人实体

class ContractTemplate(Base):
    """
    合同模板主表
    
    存储合同模板的基本信息和元数据
    """
    __tablename__ = 'contract_templates'
    
    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(200), nullable=False, comment='模板名称')
    title = Column(String(500), nullable=False, comment='合同标题')
    description = Column(Text, comment='模板描述')
    category = Column(SQLEnum(ContractCategory), nullable=False, comment='合同分类')
    
    # 版本管理
    version = Column(String(20), nullable=False, default='1.0.0', comment='版本号')
    parent_template_id = Column(UUID(as_uuid=True), ForeignKey('contract_templates.id'), comment='父模板ID')
    is_latest_version = Column(Boolean, default=True, comment='是否最新版本')
    
    # 状态管理
    status = Column(SQLEnum(TemplateStatus), default=TemplateStatus.DRAFT, comment='模板状态')
    is_public = Column(Boolean, default=False, comment='是否公开模板')
    is_system_template = Column(Boolean, default=False, comment='是否系统模板')
    
    # 内容字段
    content = Column(Text, nullable=False, comment='模板内容')
    variables = Column(JSON, comment='变量定义')
    sections = Column(JSON, comment='章节结构')
    clauses = Column(JSON, comment='条款定义')
    
    # 配置字段
    form_schema = Column(JSON, comment='表单结构定义')
    validation_rules = Column(JSON, comment='验证规则')
    conditional_logic = Column(JSON, comment='条件逻辑')
    styling_config = Column(JSON, comment='样式配置')
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment='使用次数')
    download_count = Column(Integer, default=0, comment='下载次数')
    rating_average = Column(Float, default=0.0, comment='平均评分')
    rating_count = Column(Integer, default=0, comment='评分次数')
    
    # 标签和关键词
    tags = Column(ARRAY(String), comment='标签数组')
    keywords = Column(ARRAY(String), comment='关键词数组')
    
    # 审计字段
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), comment='创建人')
    updated_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), comment='更新人')
    reviewed_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), comment='审核人')
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment='创建时间')
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment='更新时间')
    reviewed_at = Column(DateTime(timezone=True), comment='审核时间')
    
    # 关系定义
    parent_template = relationship("ContractTemplate", remote_side=[id])
    child_templates = relationship("ContractTemplate", back_populates="parent_template")
    template_fields = relationship("TemplateField", back_populates="template", cascade="all, delete-orphan")
    contracts = relationship("Contract", back_populates="template")
    
    # 索引定义
    __table_args__ = (
        Index('idx_contract_templates_category', 'category'),
        Index('idx_contract_templates_status', 'status'),
        Index('idx_contract_templates_version', 'version'),
        Index('idx_contract_templates_created_at', 'created_at'),
        Index('idx_contract_templates_tags', 'tags', postgresql_using='gin'),
        Index('idx_contract_templates_keywords', 'keywords', postgresql_using='gin'),
        CheckConstraint('rating_average >= 0 AND rating_average <= 5', name='check_rating_range'),
        CheckConstraint('usage_count >= 0', name='check_usage_count_positive'),
    )
    
    @validates('version')
    def validate_version(self, key, version):
        """验证版本号格式"""
        import re
        if not re.match(r'^\d+\.\d+\.\d+$', version):
            raise ValueError('版本号格式必须为 x.y.z')
        return version
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': str(self.id),
            'name': self.name,
            'title': self.title,
            'description': self.description,
            'category': self.category.value if self.category else None,
            'version': self.version,
            'status': self.status.value if self.status else None,
            'is_public': self.is_public,
            'usage_count': self.usage_count,
            'rating_average': self.rating_average,
            'tags': self.tags,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class TemplateField(Base):
    """
    模板字段表
    
    定义合同模板中的可变字段和占位符
    """
    __tablename__ = 'template_fields'
    
    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_id = Column(UUID(as_uuid=True), ForeignKey('contract_templates.id'), nullable=False)
    
    # 字段定义
    field_name = Column(String(100), nullable=False, comment='字段名称')
    field_label = Column(String(200), nullable=False, comment='字段标签')
    field_type = Column(SQLEnum(FieldType), nullable=False, comment='字段类型')
    field_description = Column(Text, comment='字段描述')
    
    # 字段配置
    is_required = Column(Boolean, default=False, comment='是否必填')
    is_readonly = Column(Boolean, default=False, comment='是否只读')
    is_hidden = Column(Boolean, default=False, comment='是否隐藏')
    default_value = Column(Text, comment='默认值')
    placeholder = Column(String(200), comment='占位符文本')
    
    # 验证规则
    validation_rules = Column(JSON, comment='验证规则')
    min_length = Column(Integer, comment='最小长度')
    max_length = Column(Integer, comment='最大长度')
    min_value = Column(Float, comment='最小值')
    max_value = Column(Float, comment='最大值')
    pattern = Column(String(500), comment='正则表达式')
    
    # 选项配置（用于select类型）
    options = Column(JSON, comment='选项列表')
    options_source = Column(String(200), comment='选项数据源')
    
    # 显示配置
    display_order = Column(Integer, default=0, comment='显示顺序')
    group_name = Column(String(100), comment='分组名称')
    section_name = Column(String(100), comment='章节名称')
    
    # 条件逻辑
    conditional_logic = Column(JSON, comment='条件显示逻辑')
    dependent_fields = Column(ARRAY(String), comment='依赖字段')
    
    # 样式配置
    css_class = Column(String(200), comment='CSS类名')
    inline_style = Column(Text, comment='内联样式')
    width = Column(String(20), comment='字段宽度')
    
    # 审计字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系定义
    template = relationship("ContractTemplate", back_populates="template_fields")
    
    # 索引定义
    __table_args__ = (
        Index('idx_template_fields_template_id', 'template_id'),
        Index('idx_template_fields_field_name', 'field_name'),
        Index('idx_template_fields_field_type', 'field_type'),
        Index('idx_template_fields_display_order', 'display_order'),
        CheckConstraint('display_order >= 0', name='check_display_order_positive'),
    )

class Contract(Base):
    """
    合同实例表
    
    存储基于模板生成的具体合同
    """
    __tablename__ = 'contracts'
    
    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_id = Column(UUID(as_uuid=True), ForeignKey('contract_templates.id'), nullable=False)
    
    # 合同信息
    contract_number = Column(String(100), unique=True, comment='合同编号')
    title = Column(String(500), nullable=False, comment='合同标题')
    description = Column(Text, comment='合同描述')
    
    # 当事人信息
    party_a_info = Column(JSON, comment='甲方信息')
    party_b_info = Column(JSON, comment='乙方信息')
    other_parties = Column(JSON, comment='其他当事人信息')
    
    # 合同内容
    content = Column(Text, nullable=False, comment='合同内容')
    field_values = Column(JSON, comment='字段值')
    generated_clauses = Column(JSON, comment='生成的条款')
    
    # 状态管理
    status = Column(String(50), default='draft', comment='合同状态')
    is_signed = Column(Boolean, default=False, comment='是否已签署')
    is_executed = Column(Boolean, default=False, comment='是否已执行')
    
    # 重要日期
    effective_date = Column(DateTime(timezone=True), comment='生效日期')
    expiry_date = Column(DateTime(timezone=True), comment='到期日期')
    signed_date = Column(DateTime(timezone=True), comment='签署日期')
    
    # 文件信息
    file_path = Column(String(500), comment='文件路径')
    file_format = Column(String(20), comment='文件格式')
    file_size = Column(Integer, comment='文件大小')
    file_hash = Column(String(64), comment='文件哈希')
    
    # 审计字段
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), comment='创建人')
    updated_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), comment='更新人')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系定义
    template = relationship("ContractTemplate", back_populates="contracts")
    
    # 索引定义
    __table_args__ = (
        Index('idx_contracts_template_id', 'template_id'),
        Index('idx_contracts_contract_number', 'contract_number'),
        Index('idx_contracts_status', 'status'),
        Index('idx_contracts_created_at', 'created_at'),
        Index('idx_contracts_effective_date', 'effective_date'),
    )

class TemplateVersion(Base):
    """
    模板版本历史表
    
    记录模板的版本变更历史
    """
    __tablename__ = 'template_versions'
    
    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_id = Column(UUID(as_uuid=True), ForeignKey('contract_templates.id'), nullable=False)
    
    # 版本信息
    version_number = Column(String(20), nullable=False, comment='版本号')
    version_name = Column(String(200), comment='版本名称')
    change_log = Column(Text, comment='变更日志')
    
    # 版本内容快照
    content_snapshot = Column(JSON, comment='内容快照')
    schema_snapshot = Column(JSON, comment='结构快照')
    
    # 版本状态
    is_major_version = Column(Boolean, default=False, comment='是否主要版本')
    is_published = Column(Boolean, default=False, comment='是否已发布')
    
    # 审计字段
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), comment='创建人')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引定义
    __table_args__ = (
        Index('idx_template_versions_template_id', 'template_id'),
        Index('idx_template_versions_version_number', 'version_number'),
        Index('idx_template_versions_created_at', 'created_at'),
    )

class TemplateCategory(Base):
    """
    模板分类表
    
    管理合同模板的分类体系
    """
    __tablename__ = 'template_categories'
    
    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, comment='分类名称')
    code = Column(String(50), unique=True, nullable=False, comment='分类代码')
    description = Column(Text, comment='分类描述')
    
    # 层级结构
    parent_id = Column(UUID(as_uuid=True), ForeignKey('template_categories.id'), comment='父分类ID')
    level = Column(Integer, default=1, comment='层级')
    sort_order = Column(Integer, default=0, comment='排序')
    
    # 配置信息
    icon = Column(String(100), comment='图标')
    color = Column(String(20), comment='颜色')
    is_active = Column(Boolean, default=True, comment='是否激活')
    
    # 统计信息
    template_count = Column(Integer, default=0, comment='模板数量')
    
    # 审计字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系定义
    parent = relationship("TemplateCategory", remote_side=[id])
    children = relationship("TemplateCategory", back_populates="parent")
    
    # 索引定义
    __table_args__ = (
        Index('idx_template_categories_parent_id', 'parent_id'),
        Index('idx_template_categories_level', 'level'),
        Index('idx_template_categories_sort_order', 'sort_order'),
    )

# 数据库初始化函数
def create_tables(engine):
    """创建所有表"""
    Base.metadata.create_all(engine)

def get_field_type_options():
    """获取字段类型选项"""
    return [
        {'value': ft.value, 'label': ft.value, 'description': ''}
        for ft in FieldType
    ]

def get_contract_category_options():
    """获取合同分类选项"""
    return [
        {'value': cc.value, 'label': cc.value, 'description': ''}
        for cc in ContractCategory
    ]

# 示例数据创建函数
def create_sample_template():
    """创建示例模板"""
    template = ContractTemplate(
        name="劳动合同模板",
        title="劳动合同",
        description="标准劳动合同模板，适用于一般企业员工雇佣",
        category=ContractCategory.LABOR,
        content="""
        劳动合同
        
        甲方（用人单位）：{{company_name}}
        法定代表人：{{legal_representative}}
        地址：{{company_address}}
        
        乙方（劳动者）：{{employee_name}}
        身份证号：{{employee_id}}
        地址：{{employee_address}}
        
        根据《中华人民共和国劳动法》等相关法律法规，甲乙双方在平等自愿、协商一致的基础上，签订本劳动合同。
        
        第一条 工作内容和工作地点
        1.1 乙方同意根据甲方工作需要，担任{{position}}职务。
        1.2 工作地点：{{work_location}}
        
        第二条 合同期限
        本合同为{{contract_type}}，期限从{{start_date}}至{{end_date}}。
        
        第三条 工作时间和休息休假
        甲方安排乙方执行{{work_schedule}}工作制。
        
        第四条 劳动报酬
        4.1 乙方月工资为人民币{{monthly_salary}}元。
        4.2 工资发放时间：每月{{salary_date}}日前发放。
        
        第五条 社会保险
        甲方依法为乙方缴纳社会保险费。
        
        第六条 劳动纪律
        乙方应遵守甲方依法制定的规章制度。
        
        第七条 合同的变更、解除和终止
        按照《劳动法》相关规定执行。
        
        第八条 争议解决
        因履行本合同发生争议，双方应协商解决；协商不成的，可向劳动争议仲裁委员会申请仲裁。
        
        第九条 其他约定
        {{other_terms}}
        
        本合同一式两份，甲乙双方各执一份。
        
        甲方（盖章）：                    乙方（签字）：
        
        日期：{{signature_date}}          日期：{{signature_date}}
        """,
        variables={
            "company_name": {"type": "text", "required": True, "label": "公司名称"},
            "legal_representative": {"type": "text", "required": True, "label": "法定代表人"},
            "company_address": {"type": "address", "required": True, "label": "公司地址"},
            "employee_name": {"type": "text", "required": True, "label": "员工姓名"},
            "employee_id": {"type": "text", "required": True, "label": "身份证号"},
            "employee_address": {"type": "address", "required": True, "label": "员工地址"},
            "position": {"type": "text", "required": True, "label": "职位"},
            "work_location": {"type": "text", "required": True, "label": "工作地点"},
            "contract_type": {"type": "select", "required": True, "label": "合同类型", 
                            "options": ["固定期限", "无固定期限", "以完成一定工作任务为期限"]},
            "start_date": {"type": "date", "required": True, "label": "开始日期"},
            "end_date": {"type": "date", "required": False, "label": "结束日期"},
            "work_schedule": {"type": "select", "required": True, "label": "工作制度",
                            "options": ["标准工时", "综合计算工时", "不定时工时"]},
            "monthly_salary": {"type": "currency", "required": True, "label": "月工资"},
            "salary_date": {"type": "number", "required": True, "label": "发薪日", "min": 1, "max": 31},
            "other_terms": {"type": "textarea", "required": False, "label": "其他约定"},
            "signature_date": {"type": "date", "required": True, "label": "签署日期"}
        },
        form_schema={
            "sections": [
                {
                    "title": "甲方信息",
                    "fields": ["company_name", "legal_representative", "company_address"]
                },
                {
                    "title": "乙方信息", 
                    "fields": ["employee_name", "employee_id", "employee_address"]
                },
                {
                    "title": "工作信息",
                    "fields": ["position", "work_location", "work_schedule"]
                },
                {
                    "title": "合同期限",
                    "fields": ["contract_type", "start_date", "end_date"]
                },
                {
                    "title": "薪酬信息",
                    "fields": ["monthly_salary", "salary_date"]
                },
                {
                    "title": "其他条款",
                    "fields": ["other_terms", "signature_date"]
                }
            ]
        },
        tags=["劳动合同", "雇佣", "标准模板"],
        keywords=["劳动", "合同", "雇佣", "工资", "社保"],
        status=TemplateStatus.ACTIVE,
        is_public=True
    )
    
    return template
