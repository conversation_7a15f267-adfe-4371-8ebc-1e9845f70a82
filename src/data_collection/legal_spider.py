#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
法条数据采集爬虫系统

功能描述：
- 从权威法律数据源采集最新法条数据
- 支持反爬虫策略和增量更新
- 实现数据验证和质量控制
- 遵循robots.txt协议

技术栈：
- Python 3.9+
- Scrapy 2.5+
- Redis (用于去重和缓存)
- PostgreSQL (数据存储)

作者：AI法律助手开发团队
创建时间：2024年8月26日
"""

import scrapy
import requests
import time
import random
import hashlib
import json
import logging
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse
from datetime import datetime, timedelta
from dataclasses import dataclass
from scrapy.http import Response
from scrapy.utils.project import get_project_settings
import redis
import psycopg2
from psycopg2.extras import RealDictCursor

# 配置日志输出为中文
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/legal_spider.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class LegalDocument:
    """法律文档数据结构"""
    title: str              # 文档标题
    content: str            # 文档内容
    document_type: str      # 文档类型：法律/行政法规/部门规章等
    category: str           # 分类：民法/刑法/商法等
    authority: str          # 发布机关
    document_number: str    # 文号
    effective_date: str     # 生效日期
    source_url: str         # 来源URL
    crawl_time: str         # 爬取时间
    content_hash: str       # 内容哈希值（用于去重）

class LegalDataSpider(scrapy.Spider):
    """
    法律数据爬虫主类
    
    负责从国家法律法规数据库等权威源采集法条数据
    实现智能反爬虫策略和数据质量控制
    """
    
    name = 'legal_data_spider'
    allowed_domains = ['flk.npc.gov.cn', 'www.gov.cn']  # 权威法律数据源
    
    # 爬虫配置
    custom_settings = {
        'DOWNLOAD_DELAY': 2,                    # 下载延迟2秒，避免过于频繁请求
        'RANDOMIZE_DOWNLOAD_DELAY': 0.5,        # 随机延迟因子
        'CONCURRENT_REQUESTS': 1,               # 并发请求数限制为1
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,    # 每个域名并发请求数
        'AUTOTHROTTLE_ENABLED': True,           # 启用自动限速
        'AUTOTHROTTLE_START_DELAY': 1,          # 初始下载延迟
        'AUTOTHROTTLE_MAX_DELAY': 10,           # 最大下载延迟
        'AUTOTHROTTLE_TARGET_CONCURRENCY': 1.0, # 平均并发数
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    def __init__(self, *args, **kwargs):
        """初始化爬虫"""
        super().__init__(*args, **kwargs)
        
        # 初始化Redis连接（用于去重和缓存）
        self.redis_client = redis.Redis(
            host='localhost',
            port=6379,
            db=0,
            decode_responses=True
        )
        
        # 初始化数据库连接
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'legal_assistant',
            'user': 'postgres',
            'password': 'password'
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_crawls': 0,
            'duplicate_items': 0,
            'failed_requests': 0
        }
        
        logger.info("法律数据爬虫初始化完成")
    
    def start_requests(self):
        """
        生成初始请求
        
        从多个权威法律数据源开始爬取
        """
        start_urls = [
            'http://flk.npc.gov.cn/index.html',  # 国家法律法规数据库
            'http://www.gov.cn/zhengce/',         # 中国政府网政策文件
        ]
        
        for url in start_urls:
            # 检查robots.txt协议
            if self._check_robots_txt(url):
                logger.info(f"开始爬取URL: {url}")
                yield scrapy.Request(
                    url=url,
                    callback=self.parse_index,
                    meta={'source': 'start_url'}
                )
            else:
                logger.warning(f"robots.txt禁止访问: {url}")
    
    def _check_robots_txt(self, url: str) -> bool:
        """
        检查robots.txt协议
        
        Args:
            url: 待检查的URL
            
        Returns:
            bool: 是否允许访问
        """
        try:
            domain = urlparse(url).netloc
            robots_url = f"http://{domain}/robots.txt"
            
            response = requests.get(robots_url, timeout=10)
            if response.status_code == 200:
                # 简单的robots.txt解析（实际项目中应使用专业库）
                robots_content = response.text.lower()
                if 'disallow: /' in robots_content and 'user-agent: *' in robots_content:
                    return False
            
            return True
            
        except Exception as e:
            logger.warning(f"检查robots.txt失败: {e}")
            return True  # 默认允许访问
    
    def parse_index(self, response: Response):
        """
        解析首页，提取法律文档链接
        
        Args:
            response: Scrapy响应对象
        """
        try:
            self.stats['total_requests'] += 1
            
            # 提取法律文档链接
            document_links = response.css('a[href*="law"], a[href*="regulation"]::attr(href)').getall()
            
            for link in document_links[:50]:  # 限制每页处理50个链接
                full_url = urljoin(response.url, link)
                
                # 检查是否已经爬取过
                if not self._is_duplicate_url(full_url):
                    logger.info(f"发现新的法律文档链接: {full_url}")
                    yield scrapy.Request(
                        url=full_url,
                        callback=self.parse_document,
                        meta={'source_url': response.url}
                    )
                else:
                    self.stats['duplicate_items'] += 1
                    logger.debug(f"跳过重复链接: {full_url}")
            
            # 查找下一页链接
            next_page = response.css('a[href*="next"], a[href*="下一页"]::attr(href)').get()
            if next_page:
                next_url = urljoin(response.url, next_page)
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse_index,
                    meta={'source': 'pagination'}
                )
                
        except Exception as e:
            logger.error(f"解析首页失败: {e}")
            self.stats['failed_requests'] += 1
    
    def parse_document(self, response: Response):
        """
        解析具体的法律文档页面
        
        Args:
            response: Scrapy响应对象
        """
        try:
            # 提取文档基本信息
            title = self._extract_title(response)
            content = self._extract_content(response)
            document_info = self._extract_document_info(response)
            
            if title and content:
                # 创建法律文档对象
                legal_doc = LegalDocument(
                    title=title,
                    content=content,
                    document_type=document_info.get('type', '未知'),
                    category=document_info.get('category', '未分类'),
                    authority=document_info.get('authority', '未知'),
                    document_number=document_info.get('number', ''),
                    effective_date=document_info.get('effective_date', ''),
                    source_url=response.url,
                    crawl_time=datetime.now().isoformat(),
                    content_hash=self._calculate_content_hash(content)
                )
                
                # 数据验证
                if self._validate_document(legal_doc):
                    # 保存到数据库
                    if self._save_document(legal_doc):
                        self.stats['successful_crawls'] += 1
                        logger.info(f"成功爬取法律文档: {title}")
                        yield legal_doc.__dict__
                    else:
                        logger.error(f"保存文档失败: {title}")
                else:
                    logger.warning(f"文档验证失败: {title}")
            else:
                logger.warning(f"文档内容提取失败: {response.url}")
                
        except Exception as e:
            logger.error(f"解析文档失败: {e}")
            self.stats['failed_requests'] += 1
    
    def _extract_title(self, response: Response) -> str:
        """提取文档标题"""
        title_selectors = [
            'h1::text',
            '.title::text',
            '.document-title::text',
            'title::text'
        ]
        
        for selector in title_selectors:
            title = response.css(selector).get()
            if title:
                return title.strip()
        
        return ""
    
    def _extract_content(self, response: Response) -> str:
        """提取文档内容"""
        content_selectors = [
            '.content',
            '.document-content',
            '.law-content',
            '.main-content'
        ]
        
        for selector in content_selectors:
            content_elements = response.css(selector)
            if content_elements:
                # 提取纯文本内容
                content = ' '.join(content_elements.css('::text').getall())
                return content.strip()
        
        # 如果没有找到特定的内容区域，提取body中的文本
        body_text = ' '.join(response.css('body ::text').getall())
        return body_text.strip()
    
    def _extract_document_info(self, response: Response) -> Dict[str, str]:
        """提取文档元信息"""
        info = {}
        
        # 提取文档类型
        type_text = response.css('.document-type::text, .law-type::text').get()
        if type_text:
            info['type'] = type_text.strip()
        
        # 提取发布机关
        authority_text = response.css('.authority::text, .publisher::text').get()
        if authority_text:
            info['authority'] = authority_text.strip()
        
        # 提取文号
        number_text = response.css('.document-number::text, .law-number::text').get()
        if number_text:
            info['number'] = number_text.strip()
        
        # 提取生效日期
        date_text = response.css('.effective-date::text, .publish-date::text').get()
        if date_text:
            info['effective_date'] = date_text.strip()
        
        return info
    
    def _calculate_content_hash(self, content: str) -> str:
        """计算内容哈希值，用于去重"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _is_duplicate_url(self, url: str) -> bool:
        """检查URL是否已经爬取过"""
        url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()
        return self.redis_client.exists(f"crawled_url:{url_hash}")
    
    def _mark_url_crawled(self, url: str):
        """标记URL为已爬取"""
        url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()
        self.redis_client.setex(f"crawled_url:{url_hash}", 86400 * 7, "1")  # 7天过期
    
    def _validate_document(self, doc: LegalDocument) -> bool:
        """
        验证文档数据质量
        
        Args:
            doc: 法律文档对象
            
        Returns:
            bool: 验证是否通过
        """
        # 基本字段验证
        if not doc.title or len(doc.title) < 5:
            return False
        
        if not doc.content or len(doc.content) < 100:
            return False
        
        # 内容质量验证
        if self._calculate_content_quality_score(doc.content) < 0.7:
            return False
        
        return True
    
    def _calculate_content_quality_score(self, content: str) -> float:
        """
        计算内容质量评分
        
        Args:
            content: 文档内容
            
        Returns:
            float: 质量评分 (0-1)
        """
        score = 0.0
        
        # 长度评分
        if len(content) > 500:
            score += 0.3
        elif len(content) > 200:
            score += 0.2
        
        # 法律关键词评分
        legal_keywords = ['法', '条', '款', '项', '规定', '应当', '不得', '违反', '责任']
        keyword_count = sum(1 for keyword in legal_keywords if keyword in content)
        score += min(keyword_count * 0.1, 0.5)
        
        # 结构化程度评分
        if '第' in content and '条' in content:
            score += 0.2
        
        return min(score, 1.0)
    
    def _save_document(self, doc: LegalDocument) -> bool:
        """
        保存文档到数据库
        
        Args:
            doc: 法律文档对象
            
        Returns:
            bool: 保存是否成功
        """
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 检查是否已存在相同内容的文档
            cursor.execute(
                "SELECT id FROM legal_documents WHERE content_hash = %s",
                (doc.content_hash,)
            )
            
            if cursor.fetchone():
                logger.debug(f"文档已存在，跳过保存: {doc.title}")
                return False
            
            # 插入新文档
            insert_sql = """
                INSERT INTO legal_documents (
                    title, content, document_type, category, authority,
                    document_number, effective_date, source_url, crawl_time, content_hash
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_sql, (
                doc.title, doc.content, doc.document_type, doc.category,
                doc.authority, doc.document_number, doc.effective_date,
                doc.source_url, doc.crawl_time, doc.content_hash
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            # 标记URL为已爬取
            self._mark_url_crawled(doc.source_url)
            
            return True
            
        except Exception as e:
            logger.error(f"保存文档到数据库失败: {e}")
            return False
    
    def closed(self, reason):
        """爬虫结束时的清理工作"""
        logger.info("法律数据爬虫执行完成")
        logger.info(f"统计信息: {json.dumps(self.stats, ensure_ascii=False, indent=2)}")
        
        # 关闭Redis连接
        if hasattr(self, 'redis_client'):
            self.redis_client.close()

if __name__ == "__main__":
    """
    爬虫启动脚本
    
    使用方法:
    python legal_spider.py
    """
    from scrapy.crawler import CrawlerProcess
    
    # 配置爬虫设置
    settings = {
        'BOT_NAME': 'legal_assistant_spider',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 2,
        'CONCURRENT_REQUESTS': 1,
        'ITEM_PIPELINES': {
            'scrapy.pipelines.files.FilesPipeline': 1,
        },
        'FILES_STORE': 'data/downloads',
        'LOG_LEVEL': 'INFO',
    }
    
    # 启动爬虫
    process = CrawlerProcess(settings)
    process.crawl(LegalDataSpider)
    process.start()
