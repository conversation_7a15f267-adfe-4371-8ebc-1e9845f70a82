# AI法律助手 - 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# =============================================================================
# 应用基础配置
# =============================================================================
APP_NAME=AI法律助手
APP_VERSION=1.0.0
APP_DESCRIPTION=智能法律服务平台
DEBUG=true
ENVIRONMENT=development

# 服务端口配置
BACKEND_PORT=8000
FRONTEND_PORT=3000
NGINX_PORT=80

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL 主数据库
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/legal_assistant
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=legal_assistant
DATABASE_USER=postgres
DATABASE_PASSWORD=password

# 数据库连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# =============================================================================
# Redis 缓存配置
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=100

# =============================================================================
# Elasticsearch 搜索引擎配置
# =============================================================================
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_INDEX_PREFIX=legal_assistant

# =============================================================================
# 安全配置
# =============================================================================
# JWT 令牌配置
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 数据加密密钥
ENCRYPTION_KEY=your-encryption-key-32-bytes-long

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_ALLOW_CREDENTIALS=true

# =============================================================================
# 文件存储配置
# =============================================================================
# 本地文件存储
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# =============================================================================
# AI/ML 配置
# =============================================================================
# 模型配置
MODEL_PATH=./models
BERT_MODEL_NAME=bert-base-chinese
EMBEDDING_DIMENSION=768

# NLP 处理配置
JIEBA_DICT_PATH=./data/jieba_dict.txt
STOPWORDS_PATH=./data/stopwords.txt

# =============================================================================
# 开发工具配置
# =============================================================================
# 代码质量检查
ENABLE_LINTING=true
ENABLE_TYPE_CHECKING=true

# 测试配置
TEST_DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/legal_assistant_test
PYTEST_WORKERS=4

# 热重载配置
RELOAD_ON_CHANGE=true
RELOAD_DIRS=./backend/app,./frontend/src