# AI法律助手开发规范

## 1. 代码规范

### 1.1 Python后端代码规范

#### 代码风格
- 遵循 PEP 8 标准
- 使用 Black 进行代码格式化
- 使用 isort 进行导入排序
- 使用 flake8 进行代码检查

```python
# 良好的代码示例
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User
from app.services.auth import get_current_user


class UserCreateRequest(BaseModel):
    """用户创建请求模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: str = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=8, description="密码")
    full_name: Optional[str] = Field(None, max_length=100, description="真实姓名")


class UserResponse(BaseModel):
    """用户响应模型"""
    id: str
    username: str
    email: str
    full_name: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True


router = APIRouter(prefix="/api/v1/users", tags=["用户管理"])


@router.post("/", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_data: UserCreateRequest,
    db: Session = Depends(get_db)
) -> UserResponse:
    """
    创建新用户
    
    Args:
        user_data: 用户创建数据
        db: 数据库会话
        
    Returns:
        创建的用户信息
        
    Raises:
        HTTPException: 用户名或邮箱已存在时抛出409错误
    """
    # 检查用户名是否已存在
    existing_user = db.query(User).filter(
        User.username == user_data.username
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="用户名已存在"
        )
    
    # 创建用户逻辑
    new_user = User(**user_data.dict())
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    return UserResponse.from_orm(new_user)
```

#### 命名规范
```python
# 变量和函数名：使用snake_case
user_name = "张三"
legal_case_id = "12345"

def get_user_by_id(user_id: str) -> Optional[User]:
    """根据ID获取用户"""
    pass

def calculate_case_similarity(case1: dict, case2: dict) -> float:
    """计算案例相似度"""
    pass

# 类名：使用PascalCase
class LegalCaseService:
    """法律案例服务类"""
    pass

class QAMatchingEngine:
    """问答匹配引擎"""
    pass

# 常量：使用UPPER_CASE
MAX_QUERY_LENGTH = 1000
DEFAULT_PAGE_SIZE = 20
LEGAL_CATEGORIES = ["民事", "刑事", "行政", "商事"]

# 私有方法：使用下划线前缀
class UserService:
    def _validate_password(self, password: str) -> bool:
        """验证密码强度（私有方法）"""
        pass
    
    def create_user(self, user_data: dict) -> User:
        """创建用户（公共方法）"""
        if not self._validate_password(user_data['password']):
            raise ValueError("密码强度不足")
        # 创建用户逻辑
```

### 1.2 TypeScript前端代码规范

#### 代码风格
```typescript
// 使用ESLint + Prettier进行代码格式化
// 接口定义：使用PascalCase，以I开头
interface IUser {
  id: string;
  username: string;
  email: string;
  fullName?: string;
  createdAt: Date;
}

// 类型定义：使用PascalCase
type UserRole = 'admin' | 'user' | 'guest';

type ApiResponse<T> = {
  data: T;
  message: string;
  success: boolean;
};

// 组件定义：使用PascalCase
interface UserListProps {
  users: IUser[];
  onUserSelect: (user: IUser) => void;
  loading?: boolean;
}

const UserList: React.FC<UserListProps> = ({ 
  users, 
  onUserSelect, 
  loading = false 
}) => {
  // 使用camelCase命名变量和函数
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  
  const handleUserClick = useCallback((user: IUser) => {
    setSelectedUserId(user.id);
    onUserSelect(user);
  }, [onUserSelect]);
  
  // 早期返回模式
  if (loading) {
    return <Spin size="large" />;
  }
  
  if (users.length === 0) {
    return <Empty description="暂无用户数据" />;
  }
  
  return (
    <List
      dataSource={users}
      renderItem={(user) => (
        <List.Item
          key={user.id}
          onClick={() => handleUserClick(user)}
          className={selectedUserId === user.id ? 'selected' : ''}
        >
          <List.Item.Meta
            title={user.username}
            description={user.email}
          />
        </List.Item>
      )}
    />
  );
};

export default UserList;
```

#### 自定义Hook规范
```typescript
// 自定义Hook：以use开头，使用camelCase
interface UseApiOptions<T> {
  initialData?: T;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

function useApi<T>(
  apiCall: () => Promise<T>,
  options: UseApiOptions<T> = {}
) {
  const [data, setData] = useState<T | undefined>(options.initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const execute = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      setData(result);
      options.onSuccess?.(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      options.onError?.(error);
    } finally {
      setLoading(false);
    }
  }, [apiCall, options]);
  
  return {
    data,
    loading,
    error,
    execute,
    refetch: execute
  };
}

// 使用示例
const UserProfile: React.FC<{ userId: string }> = ({ userId }) => {
  const { data: user, loading, error, execute } = useApi(
    () => userService.getUserById(userId),
    {
      onError: (error) => {
        message.error(`获取用户信息失败: ${error.message}`);
      }
    }
  );
  
  useEffect(() => {
    execute();
  }, [userId, execute]);
  
  // 组件渲染逻辑...
};
```

## 2. 数据库规范

### 2.1 表设计规范
```sql
-- 表名：使用复数形式，snake_case命名
-- 字段名：使用snake_case命名
-- 主键：统一使用UUID类型，字段名为id
-- 时间字段：created_at, updated_at, deleted_at

CREATE TABLE legal_cases (
    -- 主键：UUID类型
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 业务字段：使用描述性名称
    case_number VARCHAR(100) UNIQUE NOT NULL COMMENT '案件编号',
    title VARCHAR(500) NOT NULL COMMENT '案件标题',
    court_name VARCHAR(200) NOT NULL COMMENT '审理法院',
    case_type VARCHAR(100) NOT NULL COMMENT '案件类型',
    judgment_date DATE COMMENT '判决日期',
    
    -- JSON字段：存储复杂数据结构
    parties JSONB COMMENT '当事人信息',
    case_summary TEXT COMMENT '案件摘要',
    judgment_result TEXT COMMENT '判决结果',
    
    -- 关联字段：使用外键约束
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    
    -- 状态字段：使用枚举值
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    
    -- 审计字段：必须包含
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL -- 软删除标记
);

-- 索引规范：为常用查询字段创建索引
CREATE INDEX idx_legal_cases_case_number ON legal_cases(case_number);
CREATE INDEX idx_legal_cases_court_name ON legal_cases(court_name);
CREATE INDEX idx_legal_cases_case_type ON legal_cases(case_type);
CREATE INDEX idx_legal_cases_judgment_date ON legal_cases(judgment_date);
CREATE INDEX idx_legal_cases_status ON legal_cases(status) WHERE status != 'deleted';

-- 全文搜索索引
CREATE INDEX idx_legal_cases_title_gin ON legal_cases USING gin(to_tsvector('chinese', title));
CREATE INDEX idx_legal_cases_summary_gin ON legal_cases USING gin(to_tsvector('chinese', case_summary));
```

### 2.2 查询规范
```python
# 使用SQLAlchemy进行数据库操作
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session, selectinload

class LegalCaseRepository:
    def __init__(self, db: Session):
        self.db = db
    
    def get_by_id(self, case_id: str) -> Optional[LegalCase]:
        """根据ID获取案例"""
        return self.db.query(LegalCase).filter(
            and_(
                LegalCase.id == case_id,
                LegalCase.deleted_at.is_(None)  # 排除软删除记录
            )
        ).first()
    
    def search_cases(
        self,
        keyword: Optional[str] = None,
        case_type: Optional[str] = None,
        court_name: Optional[str] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[LegalCase], int]:
        """搜索案例"""
        query = self.db.query(LegalCase).filter(
            LegalCase.deleted_at.is_(None)
        )
        
        # 动态构建查询条件
        if keyword:
            query = query.filter(
                or_(
                    LegalCase.title.contains(keyword),
                    LegalCase.case_summary.contains(keyword)
                )
            )
        
        if case_type:
            query = query.filter(LegalCase.case_type == case_type)
        
        if court_name:
            query = query.filter(LegalCase.court_name == court_name)
        
        if date_from:
            query = query.filter(LegalCase.judgment_date >= date_from)
        
        if date_to:
            query = query.filter(LegalCase.judgment_date <= date_to)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        cases = query.order_by(LegalCase.judgment_date.desc()).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        return cases, total
    
    def create_case(self, case_data: dict) -> LegalCase:
        """创建案例"""
        new_case = LegalCase(**case_data)
        self.db.add(new_case)
        self.db.commit()
        self.db.refresh(new_case)
        return new_case
    
    def soft_delete(self, case_id: str) -> bool:
        """软删除案例"""
        case = self.get_by_id(case_id)
        if case:
            case.deleted_at = datetime.utcnow()
            self.db.commit()
            return True
        return False
```

## 3. API设计规范

### 3.1 RESTful API规范
```python
# URL设计规范
# GET    /api/v1/cases           - 获取案例列表
# GET    /api/v1/cases/{id}      - 获取特定案例
# POST   /api/v1/cases           - 创建案例
# PUT    /api/v1/cases/{id}      - 更新案例
# DELETE /api/v1/cases/{id}      - 删除案例

# 响应格式规范
class ApiResponse(BaseModel):
    """统一API响应格式"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    error_code: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class PaginatedResponse(BaseModel):
    """分页响应格式"""
    items: List[Any]
    total: int
    page: int
    page_size: int
    total_pages: int

# 错误处理规范
class APIException(HTTPException):
    """自定义API异常"""
    def __init__(
        self,
        status_code: int,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[dict] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details
        super().__init__(status_code=status_code, detail=message)

# 异常处理器
@app.exception_handler(APIException)
async def api_exception_handler(request: Request, exc: APIException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.message,
            "error_code": exc.error_code,
            "details": exc.details,
            "timestamp": datetime.utcnow().isoformat()
        }
    )
```

### 3.2 请求验证规范
```python
from pydantic import BaseModel, Field, validator
from typing import List, Optional
from datetime import date

class CaseSearchRequest(BaseModel):
    """案例搜索请求"""
    keyword: Optional[str] = Field(None, max_length=200, description="搜索关键词")
    case_type: Optional[str] = Field(None, description="案件类型")
    court_name: Optional[str] = Field(None, description="法院名称")
    date_from: Optional[date] = Field(None, description="开始日期")
    date_to: Optional[date] = Field(None, description="结束日期")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    
    @validator('date_to')
    def validate_date_range(cls, v, values):
        """验证日期范围"""
        if v and values.get('date_from') and v < values['date_from']:
            raise ValueError('结束日期不能早于开始日期')
        return v
    
    @validator('keyword')
    def validate_keyword(cls, v):
        """验证关键词"""
        if v and len(v.strip()) < 2:
            raise ValueError('搜索关键词至少需要2个字符')
        return v.strip() if v else None

class CaseCreateRequest(BaseModel):
    """案例创建请求"""
    case_number: str = Field(..., max_length=100, description="案件编号")
    title: str = Field(..., max_length=500, description="案件标题")
    court_name: str = Field(..., max_length=200, description="审理法院")
    case_type: str = Field(..., description="案件类型")
    judgment_date: Optional[date] = Field(None, description="判决日期")
    parties: Optional[List[dict]] = Field(None, description="当事人信息")
    case_summary: Optional[str] = Field(None, description="案件摘要")
    
    @validator('case_number')
    def validate_case_number(cls, v):
        """验证案件编号格式"""
        import re
        if not re.match(r'^[\w\-\(\)]+$', v):
            raise ValueError('案件编号格式不正确')
        return v
```

## 4. 测试规范

### 4.1 单元测试规范
```python
import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from app.main import app
from app.services.case_service import CaseService

client = TestClient(app)

class TestCaseService:
    """案例服务测试类"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return Mock()
    
    @pytest.fixture
    def case_service(self, mock_db):
        """案例服务实例"""
        return CaseService(mock_db)
    
    def test_get_case_by_id_success(self, case_service, mock_db):
        """测试根据ID获取案例 - 成功场景"""
        # Arrange
        case_id = "test-case-id"
        expected_case = Mock()
        expected_case.id = case_id
        mock_db.query.return_value.filter.return_value.first.return_value = expected_case
        
        # Act
        result = case_service.get_case_by_id(case_id)
        
        # Assert
        assert result == expected_case
        mock_db.query.assert_called_once()
    
    def test_get_case_by_id_not_found(self, case_service, mock_db):
        """测试根据ID获取案例 - 未找到场景"""
        # Arrange
        case_id = "non-existent-id"
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Act
        result = case_service.get_case_by_id(case_id)
        
        # Assert
        assert result is None
    
    @patch('app.services.case_service.datetime')
    def test_create_case_success(self, mock_datetime, case_service, mock_db):
        """测试创建案例 - 成功场景"""
        # Arrange
        case_data = {
            "case_number": "TEST001",
            "title": "测试案例",
            "court_name": "测试法院"
        }
        mock_datetime.utcnow.return_value = "2023-01-01T00:00:00"
        
        # Act
        result = case_service.create_case(case_data)
        
        # Assert
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
```

### 4.2 集成测试规范
```python
def test_create_case_api():
    """测试创建案例API"""
    case_data = {
        "case_number": "TEST001",
        "title": "测试案例标题",
        "court_name": "北京市第一中级人民法院",
        "case_type": "民事",
        "judgment_date": "2023-01-01"
    }
    
    response = client.post("/api/v1/cases", json=case_data)
    
    assert response.status_code == 201
    data = response.json()
    assert data["success"] is True
    assert data["data"]["case_number"] == case_data["case_number"]

def test_search_cases_api():
    """测试搜索案例API"""
    params = {
        "keyword": "合同纠纷",
        "case_type": "民事",
        "page": 1,
        "page_size": 10
    }
    
    response = client.get("/api/v1/cases", params=params)
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "items" in data["data"]
    assert "total" in data["data"]
```

## 5. 文档规范

### 5.1 代码注释规范
```python
def calculate_case_similarity(case1: dict, case2: dict) -> float:
    """
    计算两个案例的相似度
    
    使用TF-IDF和语义向量相结合的方法计算案例相似度。
    相似度范围为0-1，值越大表示越相似。
    
    Args:
        case1 (dict): 第一个案例，包含title、summary等字段
        case2 (dict): 第二个案例，包含title、summary等字段
        
    Returns:
        float: 相似度分数，范围[0, 1]
        
    Raises:
        ValueError: 当案例数据格式不正确时抛出
        
    Example:
        >>> case1 = {"title": "合同纠纷案", "summary": "..."}
        >>> case2 = {"title": "买卖合同纠纷", "summary": "..."}
        >>> similarity = calculate_case_similarity(case1, case2)
        >>> print(f"相似度: {similarity:.2f}")
        相似度: 0.85
    """
    if not isinstance(case1, dict) or not isinstance(case2, dict):
        raise ValueError("案例数据必须是字典格式")
    
    # 提取文本特征
    text1 = f"{case1.get('title', '')} {case1.get('summary', '')}"
    text2 = f"{case2.get('title', '')} {case2.get('summary', '')}"
    
    # 计算相似度逻辑...
    return similarity_score
```

### 5.2 API文档规范
```python
@router.post(
    "/cases",
    response_model=ApiResponse[CaseResponse],
    status_code=status.HTTP_201_CREATED,
    summary="创建法律案例",
    description="创建一个新的法律案例记录",
    responses={
        201: {"description": "案例创建成功"},
        400: {"description": "请求参数错误"},
        409: {"description": "案例编号已存在"},
        422: {"description": "数据验证失败"}
    },
    tags=["案例管理"]
)
async def create_case(
    case_data: CaseCreateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ApiResponse[CaseResponse]:
    """
    创建法律案例
    
    - **case_number**: 案例编号，必须唯一
    - **title**: 案例标题，最大500字符
    - **court_name**: 审理法院名称
    - **case_type**: 案例类型（民事、刑事、行政等）
    - **judgment_date**: 判决日期，可选
    - **parties**: 当事人信息列表，可选
    - **case_summary**: 案例摘要，可选
    """
    # 实现逻辑...
```

## 6. Git提交规范

### 6.1 提交信息格式
```bash
# 提交信息格式：<type>(<scope>): <subject>
# 
# type: 提交类型
# - feat: 新功能
# - fix: 修复bug
# - docs: 文档更新
# - style: 代码格式调整
# - refactor: 代码重构
# - test: 测试相关
# - chore: 构建过程或辅助工具的变动
#
# scope: 影响范围（可选）
# - api: API相关
# - ui: 用户界面
# - db: 数据库
# - auth: 认证授权
# - qa: 问答功能
# - case: 案例功能
#
# subject: 简短描述

# 示例：
feat(qa): 添加智能问答接口
fix(auth): 修复token过期处理逻辑
docs(api): 更新API文档
refactor(db): 优化数据库查询性能
test(case): 添加案例搜索单元测试
```

### 6.2 分支管理规范
```bash
# 分支命名规范
main                    # 主分支，生产环境代码
develop                 # 开发分支，集成最新功能
feature/qa-system      # 功能分支：问答系统
feature/case-search    # 功能分支：案例搜索
hotfix/auth-bug        # 热修复分支：认证bug修复
release/v1.0.0         # 发布分支：版本发布

# 工作流程
1. 从develop创建feature分支
2. 在feature分支开发功能
3. 功能完成后提交PR到develop
4. 代码审查通过后合并到develop
5. 发布时从develop创建release分支
6. 测试通过后合并到main并打tag
```
