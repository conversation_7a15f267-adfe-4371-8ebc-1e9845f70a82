# AI法律助手开发规范和最佳实践

## 概述

本文档基于Context7最新开发规范，结合AI法律助手项目的特殊需求，制定了详细的开发规范和最佳实践指南。

## 代码规范

### 1. 中文注释规范

#### 后端Python代码注释
```python
class LegalDocumentProcessor:
    """
    法律文档处理器
    
    负责处理各种法律文档的解析、分析和信息提取
    支持多种文档格式：PDF、Word、HTML等
    """
    
    def __init__(self, config: ProcessorConfig):
        """
        初始化法律文档处理器
        
        Args:
            config: 处理器配置对象，包含分词器设置、模型参数等
        """
        self.config = config
        self.tokenizer = self._init_tokenizer()  # 初始化中文分词器
        self.nlp_model = self._load_nlp_model()  # 加载NLP模型
    
    def extract_legal_entities(self, document: str) -> List[LegalEntity]:
        """
        从法律文档中提取法律实体
        
        Args:
            document: 待处理的法律文档文本
            
        Returns:
            List[LegalEntity]: 提取的法律实体列表，包括法条、案例引用、当事人等
            
        Raises:
            DocumentProcessingError: 当文档格式不支持或处理失败时抛出
        """
        try:
            # 1. 文档预处理：清理格式、标准化文本
            cleaned_text = self._preprocess_document(document)
            
            # 2. 分词处理：使用法律领域专用词典
            tokens = self.tokenizer.tokenize(cleaned_text)
            
            # 3. 实体识别：识别法律相关实体
            entities = self._recognize_entities(tokens)
            
            # 4. 后处理：验证实体有效性，过滤噪声
            validated_entities = self._validate_entities(entities)
            
            return validated_entities
            
        except Exception as e:
            self.logger.error(f"法律实体提取失败: {str(e)}")
            raise DocumentProcessingError(f"文档处理失败: {str(e)}")
```

#### 前端TypeScript代码注释
```typescript
/**
 * 法律问答聊天组件
 * 
 * 提供用户友好的聊天式法律咨询界面
 * 支持多轮对话、历史记录、文件上传等功能
 */
interface LegalChatProps {
  userId: string;           // 用户ID，用于会话管理
  initialQuestion?: string; // 初始问题，支持外部传入
  onQuestionSubmit?: (question: string) => void; // 问题提交回调
}

const LegalChatComponent: React.FC<LegalChatProps> = ({
  userId,
  initialQuestion,
  onQuestionSubmit
}) => {
  // 聊天消息状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  
  // 当前输入的问题
  const [currentQuestion, setCurrentQuestion] = useState<string>('');
  
  // 加载状态：用于显示AI思考中的状态
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  /**
   * 提交法律问题到后端API
   * 
   * @param question 用户输入的法律问题
   * @returns Promise<void>
   */
  const handleQuestionSubmit = async (question: string): Promise<void> => {
    try {
      setIsLoading(true);
      
      // 1. 添加用户消息到聊天记录
      const userMessage: ChatMessage = {
        id: generateMessageId(),
        type: 'user',
        content: question,
        timestamp: new Date(),
        sender: '用户'
      };
      setMessages(prev => [...prev, userMessage]);
      
      // 2. 调用法律问答API
      const response = await legalQAService.askQuestion({
        question,
        userId,
        sessionId: getCurrentSessionId()
      });
      
      // 3. 添加AI回答到聊天记录
      const aiMessage: ChatMessage = {
        id: generateMessageId(),
        type: 'assistant',
        content: response.answer,
        timestamp: new Date(),
        sender: 'AI法律助手',
        confidence: response.confidence, // AI回答的置信度
        sources: response.sources        // 答案来源的法条或案例
      };
      setMessages(prev => [...prev, aiMessage]);
      
      // 4. 触发外部回调
      onQuestionSubmit?.(question);
      
    } catch (error) {
      console.error('法律问答请求失败:', error);
      
      // 显示错误消息
      const errorMessage: ChatMessage = {
        id: generateMessageId(),
        type: 'error',
        content: '抱歉，系统暂时无法处理您的问题，请稍后重试。',
        timestamp: new Date(),
        sender: '系统'
      };
      setMessages(prev => [...prev, errorMessage]);
      
    } finally {
      setIsLoading(false);
      setCurrentQuestion(''); // 清空输入框
    }
  };
  
  return (
    <div className="legal-chat-container">
      {/* 聊天消息列表 */}
      <ChatMessageList 
        messages={messages}
        isLoading={isLoading}
      />
      
      {/* 问题输入区域 */}
      <QuestionInput
        value={currentQuestion}
        onChange={setCurrentQuestion}
        onSubmit={handleQuestionSubmit}
        disabled={isLoading}
        placeholder="请输入您的法律问题..."
      />
    </div>
  );
};
```

### 2. 数据库设计规范

#### 表结构设计
```python
class LegalDocument(Base):
    """
    法律文档表
    
    存储各类法律文档的基本信息和内容
    支持法条、案例、合同模板等多种文档类型
    """
    __tablename__ = 'legal_documents'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 文档基本信息
    title = Column(String(500), nullable=False, comment='文档标题')
    document_type = Column(Enum(DocumentType), nullable=False, comment='文档类型：法条/案例/合同模板')
    category = Column(String(100), nullable=False, comment='文档分类：民法/刑法/商法等')
    
    # 文档内容（加密存储）
    content = Column(EncryptedType(Text, secret_key), nullable=False, comment='文档内容（加密）')
    summary = Column(Text, comment='文档摘要')
    keywords = Column(ARRAY(String), comment='关键词数组')
    
    # 法律相关字段
    law_level = Column(String(50), comment='法律层级：法律/行政法规/部门规章等')
    effective_date = Column(Date, comment='生效日期')
    expiry_date = Column(Date, comment='失效日期')
    
    # 元数据
    source_url = Column(String(1000), comment='来源URL')
    authority = Column(String(200), comment='发布机关')
    document_number = Column(String(100), comment='文号')
    
    # 审计字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment='创建时间')
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment='更新时间')
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), comment='创建人')
    
    # 数据质量字段
    quality_score = Column(Float, default=0.0, comment='数据质量评分')
    verification_status = Column(Enum(VerificationStatus), default=VerificationStatus.PENDING, comment='验证状态')
    
    # 索引定义
    __table_args__ = (
        Index('idx_legal_documents_type_category', 'document_type', 'category'),
        Index('idx_legal_documents_effective_date', 'effective_date'),
        Index('idx_legal_documents_keywords', 'keywords', postgresql_using='gin'),
    )
```

### 3. API设计规范

#### RESTful API设计
```python
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from pydantic import BaseModel, Field

router = APIRouter(prefix="/api/v1/legal", tags=["法律服务"])

class QuestionRequest(BaseModel):
    """法律问题请求模型"""
    question: str = Field(..., min_length=5, max_length=1000, description="法律问题内容")
    category: Optional[str] = Field(None, description="问题分类：民法/刑法/商法等")
    context: Optional[str] = Field(None, description="问题上下文")
    session_id: Optional[str] = Field(None, description="会话ID，用于多轮对话")

class QuestionResponse(BaseModel):
    """法律问题回答模型"""
    answer: str = Field(..., description="AI生成的法律建议")
    confidence: float = Field(..., ge=0.0, le=1.0, description="回答置信度")
    sources: List[str] = Field(default=[], description="答案来源的法条或案例")
    related_questions: List[str] = Field(default=[], description="相关问题推荐")
    disclaimer: str = Field(..., description="免责声明")

@router.post("/question", response_model=QuestionResponse)
async def ask_legal_question(
    request: QuestionRequest,
    current_user: User = Depends(get_current_user),
    qa_service: LegalQAService = Depends(get_qa_service)
) -> QuestionResponse:
    """
    法律问答接口
    
    接收用户的法律问题，返回AI生成的法律建议
    支持多轮对话和上下文理解
    
    Args:
        request: 问题请求对象
        current_user: 当前登录用户
        qa_service: 法律问答服务
        
    Returns:
        QuestionResponse: 包含答案、置信度、来源等信息的响应
        
    Raises:
        HTTPException: 当问题处理失败时抛出HTTP异常
    """
    try:
        # 1. 记录用户问题到审计日志
        await audit_service.log_user_action(
            user_id=current_user.id,
            action="legal_question",
            details={"question": request.question[:100]}  # 只记录前100字符
        )
        
        # 2. 问题预处理和验证
        processed_question = await qa_service.preprocess_question(request.question)
        
        # 3. 意图识别和分类
        intent = await qa_service.classify_intent(processed_question)
        
        # 4. 生成法律建议
        answer_result = await qa_service.generate_answer(
            question=processed_question,
            intent=intent,
            context=request.context,
            session_id=request.session_id
        )
        
        # 5. 保存问答记录
        await qa_service.save_qa_record(
            user_id=current_user.id,
            question=request.question,
            answer=answer_result.answer,
            confidence=answer_result.confidence
        )
        
        # 6. 构建响应
        response = QuestionResponse(
            answer=answer_result.answer,
            confidence=answer_result.confidence,
            sources=answer_result.sources,
            related_questions=answer_result.related_questions,
            disclaimer="本回答仅供参考，不构成正式法律意见。如需专业法律服务，请咨询执业律师。"
        )
        
        return response
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=f"请求参数验证失败: {str(e)}")
    except ServiceUnavailableError as e:
        raise HTTPException(status_code=503, detail=f"服务暂时不可用: {str(e)}")
    except Exception as e:
        logger.error(f"法律问答处理失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="系统内部错误，请稍后重试")
```

## 测试规范

### 1. 单元测试
```python
import pytest
from unittest.mock import Mock, patch
from app.services.legal_qa_service import LegalQAService

class TestLegalQAService:
    """法律问答服务测试类"""
    
    @pytest.fixture
    def qa_service(self):
        """创建测试用的问答服务实例"""
        return LegalQAService(
            nlp_model=Mock(),
            knowledge_base=Mock(),
            config=Mock()
        )
    
    @pytest.mark.asyncio
    async def test_process_simple_question(self, qa_service):
        """测试简单法律问题处理"""
        # 准备测试数据
        question = "合同违约的法律后果是什么？"
        expected_answer = "合同违约的法律后果主要包括..."
        
        # 模拟依赖服务的返回值
        qa_service.nlp_model.classify_intent.return_value = "contract_law"
        qa_service.knowledge_base.search_similar.return_value = [
            {"content": "相关法条内容", "similarity": 0.95}
        ]
        
        # 执行测试
        result = await qa_service.process_question(question)
        
        # 验证结果
        assert result.confidence > 0.8
        assert "合同" in result.answer
        assert "违约" in result.answer
        assert len(result.sources) > 0
    
    @pytest.mark.asyncio
    async def test_process_complex_question_with_context(self, qa_service):
        """测试带上下文的复杂法律问题处理"""
        question = "那如果对方拒绝赔偿怎么办？"
        context = "之前问过合同违约的问题"
        
        # 执行测试
        result = await qa_service.process_question(question, context=context)
        
        # 验证结果
        assert result.confidence > 0.7
        assert "诉讼" in result.answer or "仲裁" in result.answer
```

### 2. 集成测试
```python
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_legal_qa_api_integration():
    """测试法律问答API集成"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        # 1. 用户登录获取token
        login_response = await client.post("/api/v1/auth/login", json={
            "username": "test_user",
            "password": "test_password"
        })
        token = login_response.json()["access_token"]
        
        # 2. 发送法律问题
        qa_response = await client.post(
            "/api/v1/legal/question",
            json={"question": "劳动合同可以随时解除吗？"},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        # 3. 验证响应
        assert qa_response.status_code == 200
        data = qa_response.json()
        assert "answer" in data
        assert data["confidence"] > 0.0
        assert len(data["sources"]) > 0
```

## 安全规范

### 1. 数据加密
```python
from cryptography.fernet import Fernet
from sqlalchemy_utils import EncryptedType
from sqlalchemy_utils.types.encrypted.encrypted_type import AesEngine

# 配置数据加密
SECRET_KEY = os.getenv("ENCRYPTION_SECRET_KEY")
encrypted_type = EncryptedType(String, SECRET_KEY, AesEngine, 'pkcs5')

class SensitiveData(Base):
    """敏感数据表"""
    __tablename__ = 'sensitive_data'
    
    id = Column(UUID(as_uuid=True), primary_key=True)
    
    # 加密存储的敏感字段
    personal_info = Column(encrypted_type, comment='个人信息（加密）')
    legal_document = Column(encrypted_type, comment='法律文档（加密）')
    
    # 脱敏处理的字段
    phone_masked = Column(String(20), comment='手机号（脱敏）')
    id_card_masked = Column(String(30), comment='身份证号（脱敏）')
```

### 2. 权限控制
```python
from enum import Enum
from functools import wraps

class Permission(Enum):
    """权限枚举"""
    READ_LEGAL_DOCS = "read_legal_docs"
    WRITE_LEGAL_DOCS = "write_legal_docs"
    ADMIN_USERS = "admin_users"
    VIEW_AUDIT_LOGS = "view_audit_logs"

def require_permission(permission: Permission):
    """权限检查装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(status_code=401, detail="未登录")
            
            if not await check_user_permission(current_user.id, permission):
                raise HTTPException(status_code=403, detail="权限不足")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

@router.get("/admin/users")
@require_permission(Permission.ADMIN_USERS)
async def get_users(current_user: User = Depends(get_current_user)):
    """获取用户列表（需要管理员权限）"""
    pass
```

## 性能优化规范

### 1. 数据库优化
```python
# 使用连接池
from sqlalchemy.pool import QueuePool

engine = create_async_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,          # 连接池大小
    max_overflow=30,       # 最大溢出连接数
    pool_pre_ping=True,    # 连接前检查
    pool_recycle=3600,     # 连接回收时间
)

# 查询优化示例
async def get_legal_documents_optimized(
    category: str,
    limit: int = 10,
    offset: int = 0
) -> List[LegalDocument]:
    """优化的法律文档查询"""
    query = (
        select(LegalDocument)
        .where(LegalDocument.category == category)
        .options(
            selectinload(LegalDocument.creator),  # 预加载关联数据
            load_only(                            # 只加载需要的字段
                LegalDocument.id,
                LegalDocument.title,
                LegalDocument.summary
            )
        )
        .order_by(LegalDocument.created_at.desc())
        .limit(limit)
        .offset(offset)
    )
    
    result = await session.execute(query)
    return result.scalars().all()
```

### 2. 缓存策略
```python
import redis.asyncio as redis
from functools import wraps

# Redis缓存配置
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    password=REDIS_PASSWORD,
    decode_responses=True
)

def cache_result(expire_time: int = 3600):
    """结果缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await redis_client.setex(
                cache_key,
                expire_time,
                json.dumps(result, default=str)
            )
            
            return result
        return wrapper
    return decorator

@cache_result(expire_time=1800)  # 缓存30分钟
async def get_legal_qa_answer(question: str) -> dict:
    """获取法律问答结果（带缓存）"""
    # 复杂的AI处理逻辑
    pass
```

## 监控和日志规范

### 1. 结构化日志
```python
import structlog
from datetime import datetime

# 配置结构化日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer(ensure_ascii=False)
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# 使用示例
async def process_legal_question(question: str, user_id: str):
    """处理法律问题"""
    logger.info(
        "开始处理法律问题",
        user_id=user_id,
        question_length=len(question),
        timestamp=datetime.now().isoformat()
    )
    
    try:
        result = await qa_service.process(question)
        
        logger.info(
            "法律问题处理成功",
            user_id=user_id,
            confidence=result.confidence,
            processing_time=result.processing_time,
            answer_length=len(result.answer)
        )
        
        return result
        
    except Exception as e:
        logger.error(
            "法律问题处理失败",
            user_id=user_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise
```

### 2. 性能监控
```python
import time
from prometheus_client import Counter, Histogram, Gauge

# 定义监控指标
REQUEST_COUNT = Counter('legal_qa_requests_total', '法律问答请求总数', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('legal_qa_request_duration_seconds', '法律问答请求耗时')
ACTIVE_USERS = Gauge('legal_qa_active_users', '活跃用户数')

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = await func(*args, **kwargs)
            REQUEST_COUNT.labels(method='POST', endpoint='/legal/question').inc()
            return result
            
        finally:
            REQUEST_DURATION.observe(time.time() - start_time)
            
    return wrapper
```

---

**文档版本**: v1.0  
**最后更新**: 2024年8月26日  
**适用项目**: AI法律助手系统  
**维护团队**: 开发团队
