# AI法律助手技术实现指南

## 1. 技术架构概览

### 1.1 整体架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API网关       │    │   微服务集群    │
│ React+TypeScript│────│ Nginx+Kong      │────│ FastAPI Services│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   消息队列      │    │   缓存层        │    │   数据存储      │
│ Redis+Celery    │    │ Redis Cluster   │    │ PostgreSQL+ES   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI/ML服务     │    │   监控系统      │    │   知识图谱      │
│ PyTorch+HuggingF│    │ Prometheus+Graf │    │ Neo4j+GraphQL   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 核心技术栈
- **前端**: React 18 + TypeScript 5 + Vite + Ant Design 5
- **后端**: FastAPI + Python 3.11 + SQLAlchemy 2.0
- **数据库**: PostgreSQL 15 + Redis 7 + Elasticsearch 8
- **AI/ML**: PyTorch + Transformers + scikit-learn + jieba
- **部署**: Docker + Kubernetes + Nginx
- **监控**: Prometheus + Grafana + ELK Stack

## 2. 数据库设计

### 2.1 核心数据模型

#### 用户相关表
```sql
-- 用户基础信息表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户会话表
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 法律知识相关表
```sql
-- 法条表
CREATE TABLE legal_articles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    article_number VARCHAR(50),
    law_name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    effective_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 案例表
CREATE TABLE legal_cases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    case_number VARCHAR(100) UNIQUE,
    title VARCHAR(500) NOT NULL,
    court_name VARCHAR(200),
    case_type VARCHAR(100),
    judgment_date DATE,
    parties JSONB, -- 当事人信息
    case_summary TEXT,
    judgment_result TEXT,
    key_points JSONB, -- 争议焦点
    related_articles JSONB, -- 相关法条ID数组
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 问答对表
CREATE TABLE qa_pairs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    confidence_score FLOAT DEFAULT 0.0,
    source_type VARCHAR(50), -- 'manual', 'generated', 'extracted'
    source_id UUID, -- 来源ID（案例或法条）
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 索引优化策略
```sql
-- 用户查询优化
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);

-- 法条搜索优化
CREATE INDEX idx_legal_articles_law_name ON legal_articles(law_name);
CREATE INDEX idx_legal_articles_category ON legal_articles(category);
CREATE INDEX idx_legal_articles_content_gin ON legal_articles USING gin(to_tsvector('chinese', content));

-- 案例检索优化
CREATE INDEX idx_legal_cases_case_type ON legal_cases(case_type);
CREATE INDEX idx_legal_cases_court_name ON legal_cases(court_name);
CREATE INDEX idx_legal_cases_judgment_date ON legal_cases(judgment_date);

-- 问答匹配优化
CREATE INDEX idx_qa_pairs_category ON qa_pairs(category);
CREATE INDEX idx_qa_pairs_question_gin ON qa_pairs USING gin(to_tsvector('chinese', question));
```

## 3. API设计规范

### 3.1 RESTful API设计原则
```python
# FastAPI路由结构示例
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional

# 用户管理API
user_router = APIRouter(prefix="/api/v1/users", tags=["users"])

class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    full_name: Optional[str] = None

class UserResponse(BaseModel):
    id: str
    username: str
    email: str
    full_name: Optional[str]
    role: str
    is_active: bool
    created_at: datetime

@user_router.post("/", response_model=UserResponse)
async def create_user(user_data: UserCreate):
    """创建新用户"""
    # 实现用户创建逻辑
    pass

@user_router.get("/{user_id}", response_model=UserResponse)
async def get_user(user_id: str, current_user: User = Depends(get_current_user)):
    """获取用户信息"""
    # 实现用户查询逻辑
    pass
```

### 3.2 问答API设计
```python
# 问答服务API
qa_router = APIRouter(prefix="/api/v1/qa", tags=["qa"])

class QuestionRequest(BaseModel):
    question: str
    context: Optional[str] = None
    category: Optional[str] = None
    session_id: Optional[str] = None

class AnswerResponse(BaseModel):
    answer: str
    confidence: float
    sources: List[dict]
    related_cases: List[dict]
    related_articles: List[dict]
    session_id: str

@qa_router.post("/ask", response_model=AnswerResponse)
async def ask_question(
    request: QuestionRequest,
    current_user: User = Depends(get_current_user)
):
    """智能问答接口"""
    # 1. 问题预处理和意图识别
    # 2. 知识检索和匹配
    # 3. 答案生成和后处理
    # 4. 相关资源推荐
    pass
```

## 4. AI/ML模块实现

### 4.1 中文法律文本处理
```python
import jieba
import jieba.posseg as pseg
from transformers import BertTokenizer, BertModel
import torch

class LegalTextProcessor:
    def __init__(self):
        # 加载法律专业词典
        jieba.load_userdict("legal_dict.txt")
        
        # 加载BERT模型
        self.tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')
        self.model = BertModel.from_pretrained('bert-base-chinese')
        
    def segment_text(self, text: str) -> List[str]:
        """法律文本分词"""
        words = jieba.lcut(text)
        return [word for word in words if len(word.strip()) > 0]
    
    def extract_entities(self, text: str) -> List[dict]:
        """提取法律实体"""
        words = pseg.lcut(text)
        entities = []
        
        for word, flag in words:
            if flag in ['nr', 'ns', 'nt']:  # 人名、地名、机构名
                entities.append({
                    'text': word,
                    'type': flag,
                    'category': 'entity'
                })
        
        return entities
    
    def get_text_embedding(self, text: str) -> torch.Tensor:
        """获取文本语义向量"""
        inputs = self.tokenizer(text, return_tensors='pt', 
                               max_length=512, truncation=True)
        
        with torch.no_grad():
            outputs = self.model(**inputs)
            # 使用[CLS]标记的向量作为文本表示
            embedding = outputs.last_hidden_state[:, 0, :]
            
        return embedding
```

### 4.2 问答匹配算法
```python
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

class QAMatcher:
    def __init__(self, text_processor: LegalTextProcessor):
        self.text_processor = text_processor
        self.qa_embeddings = {}  # 缓存问答对向量
        
    def build_qa_index(self, qa_pairs: List[dict]):
        """构建问答对索引"""
        for qa in qa_pairs:
            question_embedding = self.text_processor.get_text_embedding(
                qa['question']
            )
            self.qa_embeddings[qa['id']] = {
                'embedding': question_embedding,
                'qa_data': qa
            }
    
    def find_similar_questions(self, query: str, top_k: int = 5) -> List[dict]:
        """查找相似问题"""
        query_embedding = self.text_processor.get_text_embedding(query)
        
        similarities = []
        for qa_id, qa_info in self.qa_embeddings.items():
            similarity = cosine_similarity(
                query_embedding.numpy(),
                qa_info['embedding'].numpy()
            )[0][0]
            
            similarities.append({
                'qa_id': qa_id,
                'similarity': similarity,
                'qa_data': qa_info['qa_data']
            })
        
        # 按相似度排序
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        return similarities[:top_k]
```

## 5. Elasticsearch配置

### 5.1 中文分析器配置
```json
{
  "settings": {
    "analysis": {
      "analyzer": {
        "legal_chinese": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": [
            "lowercase",
            "stop_filter",
            "synonym_filter"
          ]
        }
      },
      "filter": {
        "stop_filter": {
          "type": "stop",
          "stopwords": ["的", "了", "在", "是", "我", "有", "和"]
        },
        "synonym_filter": {
          "type": "synonym",
          "synonyms": [
            "合同,契约",
            "法院,法庭",
            "判决,裁决"
          ]
        }
      }
    }
  }
}
```

### 5.2 案例索引映射
```json
{
  "mappings": {
    "properties": {
      "case_number": {
        "type": "keyword"
      },
      "title": {
        "type": "text",
        "analyzer": "legal_chinese",
        "fields": {
          "keyword": {
            "type": "keyword"
          }
        }
      },
      "content": {
        "type": "text",
        "analyzer": "legal_chinese"
      },
      "court_name": {
        "type": "keyword"
      },
      "case_type": {
        "type": "keyword"
      },
      "judgment_date": {
        "type": "date"
      },
      "parties": {
        "type": "nested",
        "properties": {
          "name": {
            "type": "text",
            "analyzer": "legal_chinese"
          },
          "type": {
            "type": "keyword"
          }
        }
      },
      "key_points": {
        "type": "text",
        "analyzer": "legal_chinese"
      }
    }
  }
}
```

## 6. 前端架构设计

### 6.1 项目结构
```
src/
├── components/          # 通用组件
│   ├── common/         # 基础组件
│   ├── forms/          # 表单组件
│   └── layout/         # 布局组件
├── pages/              # 页面组件
│   ├── auth/           # 认证相关
│   ├── qa/             # 问答功能
│   ├── cases/          # 案例检索
│   └── contracts/      # 合同工具
├── hooks/              # 自定义Hooks
├── services/           # API服务
├── stores/             # 状态管理
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
└── styles/             # 样式文件
```

### 6.2 状态管理设计
```typescript
// stores/useAuthStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  username: string;
  email: string;
  role: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      
      login: async (credentials) => {
        try {
          const response = await authService.login(credentials);
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true
          });
        } catch (error) {
          throw error;
        }
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false
        });
      },
      
      refreshToken: async () => {
        // 实现token刷新逻辑
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user
      })
    }
  )
);
```

## 7. 部署与运维

### 7.1 Docker配置
```dockerfile
# 后端Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 7.2 Kubernetes部署配置
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: legal-assistant-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: legal-assistant-api
  template:
    metadata:
      labels:
        app: legal-assistant-api
    spec:
      containers:
      - name: api
        image: legal-assistant-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

## 8. 安全与合规

### 8.1 数据加密
```python
from cryptography.fernet import Fernet
import os

class DataEncryption:
    def __init__(self):
        # 从环境变量获取加密密钥
        key = os.getenv('ENCRYPTION_KEY')
        if not key:
            key = Fernet.generate_key()
        self.cipher = Fernet(key)
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
```

### 8.2 访问控制
```python
from functools import wraps
from fastapi import HTTPException, status

def require_permission(permission: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user or not has_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions"
                )
            return await func(*args, **kwargs)
        return wrapper
    return decorator

@require_permission('qa:read')
async def get_qa_history(current_user: User = Depends(get_current_user)):
    """获取问答历史 - 需要qa:read权限"""
    pass
```
