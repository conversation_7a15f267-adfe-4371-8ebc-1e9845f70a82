# AI法律助手技术文档

## 目录
1. [系统架构](#系统架构)
2. [技术栈](#技术栈)
3. [API文档](#api文档)
4. [部署指南](#部署指南)
5. [开发指南](#开发指南)
6. [维护手册](#维护手册)

## 系统架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   API网关       │    │   核心服务      │
│   (React/Vue)   │◄──►│   (Nginx)       │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   缓存层        │◄────────────┤
                       │   (Redis)       │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   数据库        │◄────────────┤
                       │ (PostgreSQL)    │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   搜索引擎      │◄────────────┤
                       │ (Elasticsearch) │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   监控系统      │◄────────────┘
                       │ (Prometheus)    │
                       └─────────────────┘
```

### 核心模块架构
```
AI法律助手核心服务
├── NLP处理层
│   ├── 中文分词 (jieba)
│   ├── 语义分析 (TF-IDF + 词向量)
│   ├── 意图识别 (朴素贝叶斯)
│   └── 实体抽取 (正则 + 规则)
├── 知识处理层
│   ├── 知识图谱 (内存图数据库)
│   ├── 问答引擎 (语义匹配)
│   └── 推理引擎 (规则推理)
├── 合同分析层
│   ├── 文档解析 (多格式支持)
│   ├── 风险识别 (规则 + ML)
│   ├── 条款分析 (多维度评估)
│   └── 建议生成 (智能推荐)
├── 文书工具层
│   ├── 模板管理 (CRUD + 搜索)
│   ├── 文书生成 (参数化渲染)
│   └── 合规检查 (多维度验证)
└── 基础设施层
    ├── 缓存管理 (LRU + 文件缓存)
    ├── 性能监控 (指标收集)
    ├── 日志系统 (结构化日志)
    └── 配置管理 (环境配置)
```

## 技术栈

### 后端技术
- **编程语言**: Python 3.9+
- **Web框架**: FastAPI / Flask
- **数据库**: PostgreSQL 15+
- **缓存**: Redis 7+
- **搜索**: Elasticsearch 8+
- **消息队列**: Celery + Redis
- **任务调度**: APScheduler

### AI/ML技术
- **中文NLP**: jieba (分词)
- **机器学习**: scikit-learn
- **数值计算**: numpy, scipy
- **文本处理**: TF-IDF, 词向量
- **深度学习**: 预留接口支持

### 基础设施
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (可选)
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (可选)
- **CI/CD**: GitHub Actions

### 开发工具
- **代码质量**: flake8, black, isort
- **测试框架**: pytest
- **文档生成**: Sphinx
- **版本控制**: Git
- **包管理**: pip + requirements.txt

## API文档

### 认证方式
所有API请求需要在Header中包含认证信息：
```
Authorization: Bearer <access_token>
```

### 基础响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-08-26T10:30:00Z"
}
```

### 核心API接口

#### 1. 智能问答API
```
POST /api/v1/qa/ask
Content-Type: application/json

{
  "question": "什么是合同违约？",
  "context": "商业合同纠纷",
  "session_id": "optional_session_id"
}

Response:
{
  "success": true,
  "data": {
    "answer": "合同违约是指...",
    "confidence": 0.95,
    "sources": ["法条1", "案例1"],
    "suggestions": ["相关问题1", "相关问题2"]
  }
}
```

#### 2. 合同分析API
```
POST /api/v1/contract/analyze
Content-Type: multipart/form-data

file: <contract_file>
contract_type: "service_contract"

Response:
{
  "success": true,
  "data": {
    "analysis_id": "contract_123",
    "contract_type": "服务合同",
    "risk_score": 75,
    "risks": [
      {
        "type": "legal_risk",
        "severity": "high",
        "description": "条款可能违反相关法律",
        "suggestion": "建议修改为..."
      }
    ],
    "clauses": [
      {
        "type": "payment_terms",
        "content": "付款条款内容",
        "score": 80
      }
    ]
  }
}
```

#### 3. 文书生成API
```
POST /api/v1/document/generate
Content-Type: application/json

{
  "template_id": "contract_service_001",
  "variables": {
    "甲方名称": "北京科技有限公司",
    "乙方名称": "上海服务有限公司",
    "服务内容": "软件开发服务"
  },
  "customizations": {
    "字体": "宋体",
    "字号": "小四"
  }
}

Response:
{
  "success": true,
  "data": {
    "document_id": "doc_20250826_001",
    "content": "生成的文书内容",
    "compliance_score": 95,
    "export_formats": ["txt", "docx", "pdf"]
  }
}
```

#### 4. 语义搜索API
```
GET /api/v1/search?q=合同违约&type=law&limit=10

Response:
{
  "success": true,
  "data": {
    "total": 156,
    "results": [
      {
        "id": "law_001",
        "title": "合同法第107条",
        "content": "当事人一方不履行合同义务...",
        "relevance": 0.95,
        "type": "law"
      }
    ]
  }
}
```

### 错误处理
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": "question字段不能为空"
  },
  "timestamp": "2025-08-26T10:30:00Z"
}
```

## 部署指南

### 环境要求
- **操作系统**: Linux (Ubuntu 20.04+ 推荐)
- **内存**: 最低4GB，推荐8GB+
- **存储**: 最低50GB，推荐100GB+
- **CPU**: 最低2核，推荐4核+
- **网络**: 稳定的互联网连接

### Docker部署

#### 1. 开发环境
```bash
# 克隆代码
git clone https://github.com/your-org/ai-legal-assistant.git
cd ai-legal-assistant

# 启动开发环境
docker-compose up -d

# 查看服务状态
docker-compose ps
```

#### 2. 生产环境
```bash
# 设置环境变量
cp .env.example .env.prod
vim .env.prod  # 配置生产环境变量

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 执行健康检查
curl -f http://localhost/health
```

### Kubernetes部署
```bash
# 创建命名空间
kubectl create namespace ai-legal

# 应用配置
kubectl apply -f k8s/

# 检查部署状态
kubectl get pods -n ai-legal
kubectl get services -n ai-legal
```

### 环境变量配置
```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/ai_legal_db
REDIS_URL=redis://localhost:6379/0

# 应用配置
ENVIRONMENT=production
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key

# 第三方服务
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
```

## 开发指南

### 开发环境搭建
```bash
# 1. 克隆代码
git clone https://github.com/your-org/ai-legal-assistant.git
cd ai-legal-assistant

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 启动数据库服务
docker-compose up -d postgres redis

# 5. 运行应用
cd backend
python -m app.main
```

### 代码规范
```bash
# 代码格式化
black backend/
isort backend/

# 代码检查
flake8 backend/
mypy backend/

# 运行测试
pytest backend/tests/
```

### 添加新功能
1. **创建功能分支**: `git checkout -b feature/new-feature`
2. **编写代码**: 遵循现有代码结构和规范
3. **添加测试**: 确保测试覆盖率
4. **更新文档**: 更新相关文档
5. **提交代码**: 创建Pull Request

### 测试指南
```bash
# 运行所有测试
cd backend
python -m pytest

# 运行特定测试
python -m pytest test_nlp_simple.py -v

# 生成覆盖率报告
python -m pytest --cov=app --cov-report=html
```

## 维护手册

### 日常维护

#### 1. 系统监控
- 检查服务状态: `docker-compose ps`
- 查看系统资源: `htop`, `df -h`
- 监控日志: `docker-compose logs -f`
- 检查数据库: 连接数、慢查询、存储空间

#### 2. 性能优化
- 定期清理日志文件
- 优化数据库索引
- 清理缓存数据
- 监控内存使用

#### 3. 安全维护
- 定期更新系统补丁
- 检查安全漏洞
- 更新SSL证书
- 审查访问日志

### 备份策略

#### 1. 数据库备份
```bash
# 每日备份
docker-compose exec postgres pg_dump -U ai_legal_user ai_legal_db > backup_$(date +%Y%m%d).sql

# 恢复备份
docker-compose exec -T postgres psql -U ai_legal_user ai_legal_db < backup_20250826.sql
```

#### 2. 文件备份
```bash
# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz *.yml *.env
```

### 故障排除

#### 1. 常见问题
- **服务无法启动**: 检查端口占用、配置文件
- **数据库连接失败**: 检查数据库服务、连接字符串
- **内存不足**: 检查内存使用、优化配置
- **磁盘空间不足**: 清理日志、临时文件

#### 2. 日志分析
```bash
# 查看应用日志
docker-compose logs ai-legal-assistant

# 查看错误日志
grep -i error logs/app.log

# 查看访问日志
tail -f logs/access.log
```

#### 3. 性能分析
```bash
# 查看系统资源
docker stats

# 分析慢查询
docker-compose exec postgres psql -U ai_legal_user -c "SELECT * FROM pg_stat_activity WHERE state = 'active';"
```

### 版本升级
1. **备份数据**: 完整备份数据库和文件
2. **停止服务**: `docker-compose down`
3. **更新代码**: `git pull origin main`
4. **更新镜像**: `docker-compose pull`
5. **启动服务**: `docker-compose up -d`
6. **验证功能**: 执行健康检查和功能测试

---

**版本**: v1.0.0  
**更新日期**: 2025年8月26日  
**文档维护**: AI法律助手技术团队

如需技术支持，请联系：<EMAIL>
