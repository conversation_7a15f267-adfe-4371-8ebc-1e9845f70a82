#!/usr/bin/env python3
"""
集成测试脚本
测试前后端集成功能，验证系统完整性
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, Any, Optional
import httpx
import pytest
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent / "backend"))

from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class IntegrationTester:
    """集成测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        self.client = httpx.AsyncClient(timeout=30.0)
        self.access_token: Optional[str] = None
        self.test_user = {
            "username": "integration_test_user",
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "full_name": "集成测试用户",
            "user_type": "individual"
        }
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def test_health_check(self) -> bool:
        """测试健康检查"""
        logger.info("测试健康检查...")
        
        try:
            response = await self.client.get(f"{self.api_base}/monitoring/health")
            if response.status_code == 200:
                data = response.json()
                logger.info(f"健康检查通过: {data}")
                return True
            else:
                logger.error(f"健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return False
    
    async def test_user_registration(self) -> bool:
        """测试用户注册"""
        logger.info("测试用户注册...")
        
        try:
            # 先尝试删除可能存在的测试用户
            await self._cleanup_test_user()
            
            response = await self.client.post(
                f"{self.api_base}/auth/register",
                json=self.test_user
            )
            
            if response.status_code == 201:
                data = response.json()
                logger.info(f"用户注册成功: {data['username']}")
                return True
            else:
                logger.error(f"用户注册失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"用户注册异常: {e}")
            return False
    
    async def test_user_login(self) -> bool:
        """测试用户登录"""
        logger.info("测试用户登录...")
        
        try:
            response = await self.client.post(
                f"{self.api_base}/auth/login",
                data={
                    "username": self.test_user["username"],
                    "password": self.test_user["password"]
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data["access_token"]
                logger.info("用户登录成功")
                return True
            else:
                logger.error(f"用户登录失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"用户登录异常: {e}")
            return False
    
    async def test_qa_system(self) -> bool:
        """测试问答系统"""
        logger.info("测试问答系统...")
        
        if not self.access_token:
            logger.error("需要先登录")
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            # 提交问题
            question_data = {
                "question": "合同违约的法律责任有哪些？",
                "category": "合同法"
            }
            
            response = await self.client.post(
                f"{self.api_base}/qa/ask",
                json=question_data,
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"问答成功: 置信度 {data.get('confidence_score', 0)}")
                
                # 测试获取历史记录
                history_response = await self.client.get(
                    f"{self.api_base}/qa/history",
                    headers=headers
                )
                
                if history_response.status_code == 200:
                    logger.info("获取问答历史成功")
                    return True
                else:
                    logger.error("获取问答历史失败")
                    return False
            else:
                logger.error(f"问答失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"问答系统异常: {e}")
            return False
    
    async def test_case_search(self) -> bool:
        """测试案例搜索"""
        logger.info("测试案例搜索...")
        
        if not self.access_token:
            logger.error("需要先登录")
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            # 搜索案例
            response = await self.client.get(
                f"{self.api_base}/case-search/search",
                params={"q": "合同纠纷", "page": 1, "page_size": 10},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"案例搜索成功: 找到 {data.get('total', 0)} 个结果")
                
                # 测试统计信息
                stats_response = await self.client.get(
                    f"{self.api_base}/case-search/categories/stats",
                    headers=headers
                )
                
                if stats_response.status_code == 200:
                    logger.info("获取案例统计成功")
                    return True
                else:
                    logger.error("获取案例统计失败")
                    return False
            else:
                logger.error(f"案例搜索失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"案例搜索异常: {e}")
            return False
    
    async def test_contract_templates(self) -> bool:
        """测试合同模板"""
        logger.info("测试合同模板...")
        
        if not self.access_token:
            logger.error("需要先登录")
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            # 获取模板列表
            response = await self.client.get(
                f"{self.api_base}/contract-templates/templates",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                templates = data.get("templates", [])
                logger.info(f"获取模板列表成功: {len(templates)} 个模板")
                
                if templates:
                    # 测试生成合同
                    template_id = templates[0]["id"]
                    contract_data = {
                        "template_id": template_id,
                        "variables": {
                            "employer_name": "测试公司",
                            "employee_name": "张三",
                            "position": "软件工程师",
                            "salary": "10000",
                            "start_date": "2024-01-01"
                        }
                    }
                    
                    generate_response = await self.client.post(
                        f"{self.api_base}/contract-templates/generate",
                        json=contract_data,
                        headers=headers
                    )
                    
                    if generate_response.status_code == 200:
                        logger.info("合同生成成功")
                        return True
                    else:
                        logger.error(f"合同生成失败: {generate_response.status_code}")
                        return False
                else:
                    logger.warning("没有可用的模板")
                    return True
            else:
                logger.error(f"获取模板列表失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"合同模板异常: {e}")
            return False
    
    async def test_user_profile(self) -> bool:
        """测试用户资料"""
        logger.info("测试用户资料...")
        
        if not self.access_token:
            logger.error("需要先登录")
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            # 获取用户信息
            response = await self.client.get(
                f"{self.api_base}/users/me",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"获取用户信息成功: {data['username']}")
                
                # 更新用户信息
                update_data = {
                    "full_name": "更新后的测试用户"
                }
                
                update_response = await self.client.put(
                    f"{self.api_base}/users/me",
                    json=update_data,
                    headers=headers
                )
                
                if update_response.status_code == 200:
                    logger.info("更新用户信息成功")
                    return True
                else:
                    logger.error(f"更新用户信息失败: {update_response.status_code}")
                    return False
            else:
                logger.error(f"获取用户信息失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"用户资料异常: {e}")
            return False
    
    async def test_system_monitoring(self) -> bool:
        """测试系统监控"""
        logger.info("测试系统监控...")
        
        try:
            # 健康检查
            health_response = await self.client.get(f"{self.api_base}/monitoring/health")
            
            if health_response.status_code == 200:
                logger.info("系统监控健康检查通过")
                return True
            else:
                logger.error(f"系统监控健康检查失败: {health_response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"系统监控异常: {e}")
            return False
    
    async def _cleanup_test_user(self):
        """清理测试用户"""
        try:
            # 这里可以添加清理测试用户的逻辑
            # 由于没有删除用户的API，暂时跳过
            pass
        except Exception as e:
            logger.warning(f"清理测试用户失败: {e}")
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有集成测试"""
        logger.info("开始运行集成测试...")
        
        tests = [
            ("健康检查", self.test_health_check),
            ("用户注册", self.test_user_registration),
            ("用户登录", self.test_user_login),
            ("问答系统", self.test_qa_system),
            ("案例搜索", self.test_case_search),
            ("合同模板", self.test_contract_templates),
            ("用户资料", self.test_user_profile),
            ("系统监控", self.test_system_monitoring),
        ]
        
        results = {}
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results[test_name] = result
                if result:
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
                results[test_name] = False
            
            # 测试间隔
            await asyncio.sleep(1)
        
        # 清理
        await self._cleanup_test_user()
        
        logger.info(f"\n集成测试完成: {passed}/{total} 通过")
        return results


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI法律助手集成测试")
    parser.add_argument(
        "--base-url",
        default="http://localhost:8000",
        help="后端服务基础URL"
    )
    parser.add_argument(
        "--wait-for-service",
        action="store_true",
        help="等待服务启动"
    )
    
    args = parser.parse_args()
    
    # 等待服务启动
    if args.wait_for_service:
        logger.info("等待服务启动...")
        for i in range(30):
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{args.base_url}/api/v1/monitoring/health")
                    if response.status_code == 200:
                        logger.info("服务已启动")
                        break
            except:
                pass
            
            logger.info(f"等待中... ({i+1}/30)")
            await asyncio.sleep(2)
        else:
            logger.error("服务启动超时")
            return 1
    
    # 运行集成测试
    async with IntegrationTester(args.base_url) as tester:
        results = await tester.run_all_tests()
        
        # 输出结果
        failed_tests = [name for name, result in results.items() if not result]
        
        if failed_tests:
            logger.error(f"失败的测试: {', '.join(failed_tests)}")
            return 1
        else:
            logger.info("所有集成测试通过！")
            return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
