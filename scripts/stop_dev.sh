#!/bin/bash

# AI法律助手开发环境停止脚本
# 用于停止开发环境的所有服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止进程
stop_process() {
    local process_name=$1
    local port=$2
    
    log_info "停止 $process_name..."
    
    # 通过端口查找进程
    if [ ! -z "$port" ]; then
        local pid=$(lsof -ti:$port 2>/dev/null || true)
        if [ ! -z "$pid" ]; then
            kill -TERM $pid 2>/dev/null || true
            sleep 2
            
            # 如果进程仍在运行，强制杀死
            if kill -0 $pid 2>/dev/null; then
                kill -KILL $pid 2>/dev/null || true
                log_warning "$process_name 被强制停止"
            else
                log_success "$process_name 已停止"
            fi
        else
            log_info "$process_name 未在运行"
        fi
    fi
}

# 停止Docker容器
stop_docker_container() {
    local container_name=$1
    
    if command -v docker &> /dev/null; then
        if docker ps -q -f name=$container_name | grep -q .; then
            log_info "停止Docker容器: $container_name"
            docker stop $container_name 2>/dev/null || true
            docker rm $container_name 2>/dev/null || true
            log_success "Docker容器 $container_name 已停止"
        else
            log_info "Docker容器 $container_name 未在运行"
        fi
    fi
}

# 停止所有相关进程
stop_all_processes() {
    log_info "停止所有AI法律助手相关进程..."
    
    # 停止前端服务 (端口3000)
    stop_process "前端服务" 3000
    
    # 停止后端服务 (端口8000)
    stop_process "后端服务" 8000
    
    # 停止可能的其他端口
    stop_process "其他服务" 8001
    stop_process "其他服务" 3001
    
    # 通过进程名停止
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "vite" 2>/dev/null || true
    
    log_success "所有应用进程已停止"
}

# 停止Docker服务
stop_docker_services() {
    log_info "停止Docker服务..."
    
    # 停止开发环境容器
    stop_docker_container "legal-assistant-postgres-dev"
    stop_docker_container "legal-assistant-redis-dev"
    stop_docker_container "legal-assistant-elasticsearch-dev"
    
    # 停止可能的其他容器
    stop_docker_container "legal-assistant-backend"
    stop_docker_container "legal-assistant-frontend"
    stop_docker_container "legal-assistant-nginx"
    
    log_success "所有Docker服务已停止"
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 清理后端临时文件
    if [ -d "backend" ]; then
        rm -f backend/.deps_installed 2>/dev/null || true
        rm -rf backend/__pycache__ 2>/dev/null || true
        rm -rf backend/app/__pycache__ 2>/dev/null || true
        find backend -name "*.pyc" -delete 2>/dev/null || true
    fi
    
    # 清理前端临时文件
    if [ -d "frontend/vite-app" ]; then
        rm -rf frontend/vite-app/.vite 2>/dev/null || true
        rm -rf frontend/vite-app/dist 2>/dev/null || true
    fi
    
    # 清理日志文件 (可选)
    if [ "$CLEAN_LOGS" = true ]; then
        rm -rf logs/*.log 2>/dev/null || true
        rm -rf backend/logs/*.log 2>/dev/null || true
    fi
    
    log_success "临时文件已清理"
}

# 显示状态
show_status() {
    echo
    log_success "=== AI法律助手开发环境已停止 ==="
    echo
    
    # 检查端口状态
    local ports=(3000 8000 5432 6379 9200)
    local running_services=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            running_services+=($port)
        fi
    done
    
    if [ ${#running_services[@]} -eq 0 ]; then
        echo "✅ 所有服务端口已释放"
    else
        echo "⚠️  以下端口仍在使用: ${running_services[*]}"
        echo "   可能有其他服务在使用这些端口"
    fi
    
    # 检查Docker容器状态
    if command -v docker &> /dev/null; then
        local running_containers=$(docker ps -q -f name=legal-assistant 2>/dev/null | wc -l)
        if [ $running_containers -eq 0 ]; then
            echo "✅ 所有Docker容器已停止"
        else
            echo "⚠️  仍有 $running_containers 个相关Docker容器在运行"
        fi
    fi
    
    echo
    echo "🔄 重新启动: ./scripts/start_dev.sh"
    echo "🧹 完全清理: ./scripts/stop_dev.sh --clean-all"
    echo
}

# 主函数
main() {
    log_info "停止AI法律助手开发环境..."
    
    # 解析命令行参数
    CLEAN_LOGS=false
    CLEAN_ALL=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean-logs)
                CLEAN_LOGS=true
                shift
                ;;
            --clean-all)
                CLEAN_LOGS=true
                CLEAN_ALL=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --clean-logs   清理日志文件"
                echo "  --clean-all    完全清理（包括日志和缓存）"
                echo "  -h, --help     显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 停止所有进程
    stop_all_processes
    
    # 停止Docker服务
    stop_docker_services
    
    # 清理临时文件
    if [ "$CLEAN_ALL" = true ]; then
        cleanup_temp_files
    fi
    
    # 显示状态
    show_status
    
    log_success "开发环境停止完成"
}

# 运行主函数
main "$@"
