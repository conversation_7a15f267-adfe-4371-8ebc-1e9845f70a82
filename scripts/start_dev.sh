#!/bin/bash

# AI法律助手开发环境启动脚本
# 用于快速启动开发环境的所有服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 $1 已被占用"
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    log_info "等待 $service_name 启动..."

    while [ $attempt -le $max_attempts ]; do
        if curl -s $url > /dev/null 2>&1; then
            log_success "$service_name 已启动"
            return 0
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "$service_name 启动超时"
    return 1
}

# 启动PostgreSQL
start_postgres() {
    log_info "启动PostgreSQL..."

    if check_port 5432; then
        if command -v docker &> /dev/null; then
            docker run -d \
                --name legal-assistant-postgres-dev \
                -e POSTGRES_DB=legal_assistant_dev \
                -e POSTGRES_USER=legal_assistant \
                -e POSTGRES_PASSWORD=legal_assistant_password \
                -p 5432:5432 \
                postgres:15-alpine

            # 等待PostgreSQL启动
            sleep 5
            log_success "PostgreSQL 已启动"
        else
            log_warning "Docker未安装，请手动启动PostgreSQL"
        fi
    else
        log_info "PostgreSQL 已在运行"
    fi
}

# 启动Redis
start_redis() {
    log_info "启动Redis..."

    if check_port 6379; then
        if command -v docker &> /dev/null; then
            docker run -d \
                --name legal-assistant-redis-dev \
                -p 6379:6379 \
                redis:7-alpine \
                redis-server --requirepass redis_password

            log_success "Redis 已启动"
        else
            log_warning "Docker未安装，请手动启动Redis"
        fi
    else
        log_info "Redis 已在运行"
    fi
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."

    cd backend

    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python -m venv venv
    fi

    # 激活虚拟环境
    source venv/bin/activate

    # 安装依赖
    if [ ! -f ".deps_installed" ]; then
        log_info "安装Python依赖..."
        pip install -r requirements.txt
        touch .deps_installed
    fi

    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        log_info "创建环境变量文件..."
        cp .env.example .env
        log_warning "请编辑 backend/.env 文件配置数据库连接"
    fi

    # 运行数据库迁移
    log_info "运行数据库迁移..."
    alembic upgrade head

    # 初始化权限数据
    if [ -f "scripts/init_permissions.py" ]; then
        log_info "初始化权限数据..."
        python scripts/init_permissions.py
    fi

    # 启动后端服务
    log_info "启动后端API服务..."
    uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!

    cd ..

    # 等待后端服务启动
    if wait_for_service "http://localhost:8000/api/v1/monitoring/health" "后端API服务"; then
        log_success "后端服务已启动 (PID: $BACKEND_PID)"
    else
        log_error "后端服务启动失败"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."

    cd frontend/vite-app

    # 安装依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装Node.js依赖..."
        npm install
    fi

    # 检查环境变量文件
    if [ ! -f ".env.local" ]; then
        log_info "创建前端环境变量文件..."
        cp .env.example .env.local
    fi

    # 启动前端服务
    log_info "启动前端开发服务器..."
    npm run dev &
    FRONTEND_PID=$!

    cd ../..

    # 等待前端服务启动
    if wait_for_service "http://localhost:3000" "前端开发服务器"; then
        log_success "前端服务已启动 (PID: $FRONTEND_PID)"
    else
        log_error "前端服务启动失败"
        kill $FRONTEND_PID 2>/dev/null || true
        exit 1
    fi
}

# 运行集成测试
run_tests() {
    log_info "运行集成测试..."

    if [ -f "scripts/integration_test.py" ]; then
        cd backend
        source venv/bin/activate
        python ../scripts/integration_test.py --wait-for-service
        cd ..
    else
        log_warning "集成测试脚本不存在，跳过测试"
    fi
}

# 显示服务状态
show_status() {
    echo
    log_success "=== AI法律助手开发环境已启动 ==="
    echo
    echo "🌐 前端应用: http://localhost:3000"
    echo "🔧 后端API: http://localhost:8000"
    echo "📚 API文档: http://localhost:8000/docs"
    echo "🔍 ReDoc文档: http://localhost:8000/redoc"
    echo
    echo "📊 数据库:"
    echo "  - PostgreSQL: localhost:5432"
    echo "  - Redis: localhost:6379"
    echo
    echo "🛠️  开发工具:"
    echo "  - 后端日志: tail -f backend/logs/app.log"
    echo "  - 前端热重载: 自动启用"
    echo
    echo "⚠️  停止服务: Ctrl+C 或运行 scripts/stop_dev.sh"
    echo
}

# 清理函数
cleanup() {
    log_info "正在停止服务..."

    # 停止后端服务
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        log_info "后端服务已停止"
    fi

    # 停止前端服务
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        log_info "前端服务已停止"
    fi

    # 停止Docker容器
    if command -v docker &> /dev/null; then
        docker stop legal-assistant-postgres-dev 2>/dev/null || true
        docker rm legal-assistant-postgres-dev 2>/dev/null || true
        docker stop legal-assistant-redis-dev 2>/dev/null || true
        docker rm legal-assistant-redis-dev 2>/dev/null || true
    fi

    log_success "开发环境已停止"
    exit 0
}

# 主函数
main() {
    log_info "启动AI法律助手开发环境..."

    # 检查必要的命令
    check_command "python"
    check_command "node"
    check_command "npm"

    # 设置信号处理
    trap cleanup SIGINT SIGTERM

    # 解析命令行参数
    SKIP_DEPS=false
    RUN_TESTS=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --run-tests)
                RUN_TESTS=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-deps    跳过依赖服务启动"
                echo "  --run-tests    启动后运行集成测试"
                echo "  -h, --help     显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done

    # 启动依赖服务
    if [ "$SKIP_DEPS" = false ]; then
        start_postgres
        start_redis
    fi

    # 启动应用服务
    start_backend
    start_frontend

    # 运行测试
    if [ "$RUN_TESTS" = true ]; then
        run_tests
    fi

    # 显示状态
    show_status

    # 保持脚本运行
    log_info "按 Ctrl+C 停止所有服务"
    wait
}

# 运行主函数
main "$@"