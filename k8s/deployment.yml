# AI法律助手 - Kubernetes部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-legal-assistant
  namespace: ai-legal
  labels:
    app: ai-legal-assistant
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-legal-assistant
  template:
    metadata:
      labels:
        app: ai-legal-assistant
        version: v1.0.0
    spec:
      containers:
      - name: ai-legal-assistant
        image: ghcr.io/your-org/ai-legal-assistant:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: ai-legal-secrets
              key: redis-url
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-legal-secrets
              key: database-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: cache
          mountPath: /app/cache
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: ai-legal-logs-pvc
      - name: cache
        emptyDir: {}
      - name: config
        configMap:
          name: ai-legal-config
      imagePullSecrets:
      - name: ghcr-secret
      restartPolicy: Always
      terminationGracePeriodSeconds: 30

---
# Service配置
apiVersion: v1
kind: Service
metadata:
  name: ai-legal-assistant-service
  namespace: ai-legal
  labels:
    app: ai-legal-assistant
spec:
  selector:
    app: ai-legal-assistant
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  type: ClusterIP

---
# Ingress配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-legal-assistant-ingress
  namespace: ai-legal
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
spec:
  tls:
  - hosts:
    - api.ai-legal-assistant.com
    secretName: ai-legal-tls
  rules:
  - host: api.ai-legal-assistant.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-legal-assistant-service
            port:
              number: 80

---
# ConfigMap配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-legal-config
  namespace: ai-legal
data:
  app.conf: |
    [app]
    name = ai-legal-assistant
    version = 1.0.0
    debug = false
    
    [logging]
    level = INFO
    format = json
    
    [cache]
    enabled = true
    ttl = 3600
    
    [monitoring]
    enabled = true
    metrics_port = 9090

---
# Secret配置
apiVersion: v1
kind: Secret
metadata:
  name: ai-legal-secrets
  namespace: ai-legal
type: Opaque
data:
  # 这些值需要base64编码
  redis-url: cmVkaXM6Ly9yZWRpcy1zZXJ2aWNlOjYzNzk=  # redis://redis-service:6379
  database-url: ****************************************************************  # ********************************************/db

---
# PersistentVolumeClaim配置
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-legal-logs-pvc
  namespace: ai-legal
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
# HorizontalPodAutoscaler配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-legal-assistant-hpa
  namespace: ai-legal
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-legal-assistant
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# NetworkPolicy配置
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ai-legal-assistant-netpol
  namespace: ai-legal
spec:
  podSelector:
    matchLabels:
      app: ai-legal-assistant
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
