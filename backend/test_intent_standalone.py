#!/usr/bin/env python3
"""
独立的意图分类器测试
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, Counter
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import Pipeline
import jieba


class SimpleIntentClassifier:
    """简化的意图分类器"""
    
    def __init__(self):
        """初始化意图分类器"""
        self.intent_patterns = {
            "咨询": [
                r"请问|咨询|想了解|想知道|如何|怎么|什么是|能否|可以吗",
                r"帮我|告诉我|解释|说明|介绍",
                r"法律规定|相关法律|法条|条款"
            ],
            "投诉": [
                r"投诉|举报|控告|告发",
                r"违法|违规|不合理|不公平|欺骗|诈骗",
                r"要求赔偿|索赔|维权"
            ],
            "申请": [
                r"申请|办理|提交|递交",
                r"如何申请|申请流程|申请条件|申请材料",
                r"仲裁申请|诉讼申请|复议申请"
            ],
            "查询": [
                r"查询|查看|查找|搜索",
                r"案件进度|处理结果|审理情况",
                r"法条查询|判例查询|案例查询"
            ],
            "求助": [
                r"求助|帮助|救助|援助",
                r"紧急|急需|马上|立即",
                r"不知道怎么办|该怎么办|怎么处理"
            ]
        }
        
        self.intent_keywords = {
            "咨询": ["咨询", "请教", "了解", "知道", "解释", "说明", "法律", "法规"],
            "投诉": ["投诉", "举报", "违法", "违规", "欺骗", "维权", "赔偿"],
            "申请": ["申请", "办理", "提交", "流程", "条件", "材料", "仲裁"],
            "查询": ["查询", "查看", "搜索", "进度", "结果", "法条", "案例"],
            "求助": ["求助", "帮助", "紧急", "急需", "怎么办", "如何处理"]
        }
    
    def classify_intent(self, text: str) -> Dict[str, Any]:
        """分类意图"""
        # 规则基础分类
        rule_scores = self._rule_based_classification(text)
        
        # 关键词基础分类
        keyword_scores = self._keyword_based_classification(text)
        
        # 综合评分
        final_scores = self._combine_scores(rule_scores, keyword_scores)
        
        # 确定最终意图
        if not final_scores:
            intent = "未知"
            confidence = 0.0
        else:
            intent = max(final_scores, key=final_scores.get)
            confidence = final_scores[intent]
        
        return {
            "intent": intent,
            "confidence": confidence,
            "scores": final_scores,
            "rule_scores": rule_scores,
            "keyword_scores": keyword_scores
        }
    
    def _rule_based_classification(self, text: str) -> Dict[str, float]:
        """基于规则的分类"""
        scores = defaultdict(float)
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    pattern_score = len(matches) * (len(pattern) / 100.0)
                    scores[intent] += pattern_score
        
        # 归一化分数
        if scores:
            max_score = max(scores.values())
            scores = {k: v / max_score for k, v in scores.items()}
        
        return dict(scores)
    
    def _keyword_based_classification(self, text: str) -> Dict[str, float]:
        """基于关键词的分类"""
        scores = defaultdict(float)
        
        # 使用jieba分词
        tokens = list(jieba.cut(text))
        
        for intent, keywords in self.intent_keywords.items():
            for keyword in keywords:
                if keyword in tokens:
                    weight = len(keyword) / 5.0 + 0.5
                    scores[intent] += weight
        
        # 归一化分数
        if scores:
            max_score = max(scores.values())
            scores = {k: v / max_score for k, v in scores.items()}
        
        return dict(scores)
    
    def _combine_scores(self, rule_scores: Dict[str, float], 
                       keyword_scores: Dict[str, float]) -> Dict[str, float]:
        """综合评分"""
        all_intents = set(rule_scores.keys()) | set(keyword_scores.keys())
        combined_scores = {}
        
        for intent in all_intents:
            rule_score = rule_scores.get(intent, 0.0)
            keyword_score = keyword_scores.get(intent, 0.0)
            combined_score = 0.6 * rule_score + 0.4 * keyword_score
            combined_scores[intent] = combined_score
        
        return combined_scores
    
    def get_intent_explanation(self, intent: str) -> str:
        """获取意图解释"""
        explanations = {
            "咨询": "用户想要了解法律知识、法规条文或寻求法律建议",
            "投诉": "用户要举报违法行为或投诉不当处理",
            "申请": "用户想要申请某项法律服务或办理相关手续",
            "查询": "用户要查询案件进度、法条内容或相关信息",
            "求助": "用户遇到紧急情况需要立即帮助或指导",
            "未知": "无法确定用户的具体意图，需要进一步澄清"
        }
        return explanations.get(intent, f"关于{intent}的意图")


def test_basic_intent_classification():
    """测试基本意图分类"""
    classifier = SimpleIntentClassifier()
    
    test_cases = [
        ("请问劳动合同法是怎么规定的？", "咨询"),
        ("我要投诉这家公司违法用工", "投诉"),
        ("如何申请劳动仲裁？", "申请"),
        ("查询我的案件处理进度", "查询"),
        ("紧急求助，不知道该怎么办", "求助")
    ]
    
    correct_predictions = 0
    total_predictions = len(test_cases)
    
    print("✓ 基本意图分类测试:")
    for text, expected_intent in test_cases:
        result = classifier.classify_intent(text)
        predicted_intent = result["intent"]
        confidence = result["confidence"]
        
        is_correct = predicted_intent == expected_intent
        if is_correct:
            correct_predictions += 1
        
        status = "✓" if is_correct else "✗"
        print(f"  {status} '{text[:25]}...' -> {predicted_intent} (置信度: {confidence:.3f})")
    
    accuracy = correct_predictions / total_predictions
    print(f"  准确率: {accuracy:.2%} ({correct_predictions}/{total_predictions})")
    
    return accuracy >= 0.6


def test_rule_based_classification():
    """测试基于规则的分类"""
    classifier = SimpleIntentClassifier()
    
    test_text = "请问如何申请劳动仲裁？"
    rule_scores = classifier._rule_based_classification(test_text)
    
    print(f"✓ 规则分类测试: {rule_scores}")
    
    # 应该能识别出"咨询"和"申请"意图
    expected_intents = ["咨询", "申请"]
    found_intents = [intent for intent in expected_intents if intent in rule_scores and rule_scores[intent] > 0]
    
    return len(found_intents) > 0


def test_keyword_based_classification():
    """测试基于关键词的分类"""
    classifier = SimpleIntentClassifier()
    
    test_text = "我要投诉公司违法行为，要求赔偿"
    keyword_scores = classifier._keyword_based_classification(test_text)
    
    print(f"✓ 关键词分类测试: {keyword_scores}")
    
    # 应该能识别出"投诉"意图
    return "投诉" in keyword_scores and keyword_scores["投诉"] > 0


def test_intent_explanation():
    """测试意图解释"""
    classifier = SimpleIntentClassifier()
    
    intents = ["咨询", "投诉", "申请", "查询", "求助"]
    
    print("✓ 意图解释测试:")
    for intent in intents:
        explanation = classifier.get_intent_explanation(intent)
        print(f"  {intent}: {explanation[:40]}...")
    
    return True


def test_combined_classification():
    """测试综合分类"""
    classifier = SimpleIntentClassifier()
    
    test_text = "请问我可以投诉这家公司的违法行为吗？"
    result = classifier.classify_intent(test_text)
    
    print(f"✓ 综合分类测试:")
    print(f"  文本: '{test_text}'")
    print(f"  意图: {result['intent']}")
    print(f"  置信度: {result['confidence']:.3f}")
    print(f"  规则分数: {result['rule_scores']}")
    print(f"  关键词分数: {result['keyword_scores']}")
    
    return result['intent'] in ["咨询", "投诉"]


def test_edge_cases():
    """测试边界情况"""
    classifier = SimpleIntentClassifier()
    
    edge_cases = [
        "",  # 空字符串
        "你好",  # 简单问候
        "今天天气不错",  # 无关内容
        "法律法律法律",  # 重复词汇
    ]
    
    print("✓ 边界情况测试:")
    for text in edge_cases:
        result = classifier.classify_intent(text)
        print(f"  '{text}' -> {result['intent']} (置信度: {result['confidence']:.3f})")
    
    return True


def test_jieba_integration():
    """测试jieba集成"""
    try:
        text = "我想咨询劳动合同法的相关问题"
        tokens = list(jieba.cut(text))
        
        print(f"✓ jieba集成测试: {tokens}")
        
        # 检查是否正确分词
        expected_tokens = ["劳动合同法", "咨询", "问题"]
        found_tokens = [token for token in expected_tokens if token in tokens]
        
        return len(found_tokens) > 0
        
    except Exception as e:
        print(f"✗ jieba集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始独立意图分类器测试...")
    print("=" * 50)
    
    tests = [
        ("基本意图分类测试", test_basic_intent_classification),
        ("规则分类测试", test_rule_based_classification),
        ("关键词分类测试", test_keyword_based_classification),
        ("意图解释测试", test_intent_explanation),
        ("综合分类测试", test_combined_classification),
        ("边界情况测试", test_edge_cases),
        ("jieba集成测试", test_jieba_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
