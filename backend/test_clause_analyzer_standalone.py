#!/usr/bin/env python3
"""
独立的合同条款分析器测试
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from datetime import datetime


class SimpleClauseAnalysisResult:
    """简化的条款分析结果类"""
    
    def __init__(self, clause_id: str, clause_title: str):
        self.clause_id = clause_id
        self.clause_title = clause_title
        self.legality_score = 0.0
        self.completeness_score = 0.0
        self.reasonableness_score = 0.0
        self.overall_score = 0.0
        
        self.legality_issues = []
        self.completeness_issues = []
        self.reasonableness_issues = []
        
        self.suggestions = []
        self.legal_references = []
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "clause_id": self.clause_id,
            "clause_title": self.clause_title,
            "scores": {
                "legality": self.legality_score,
                "completeness": self.completeness_score,
                "reasonableness": self.reasonableness_score,
                "overall": self.overall_score
            },
            "issues": {
                "legality": self.legality_issues,
                "completeness": self.completeness_issues,
                "reasonableness": self.reasonableness_issues
            },
            "suggestions": self.suggestions,
            "legal_references": self.legal_references
        }


class SimpleContractClauseAnalyzer:
    """简化的合同条款分析器"""
    
    def __init__(self):
        self.legality_rules = {
            "违法内容": [
                {
                    "pattern": r"规避.*税收|避税|逃税",
                    "severity": "critical",
                    "description": "涉嫌税收规避"
                },
                {
                    "pattern": r"垄断.*市场|限制.*竞争",
                    "severity": "high",
                    "description": "可能违反反垄断法"
                }
            ],
            "无效条款": [
                {
                    "pattern": r"排除.*法律适用|不受.*法律约束",
                    "severity": "high",
                    "description": "试图排除法律适用"
                }
            ]
        }
        
        self.completeness_rules = {
            "必要要素": [
                {
                    "element": "当事人信息",
                    "patterns": [r"甲方[:：]", r"乙方[:：]", r"当事人"],
                    "required": True
                },
                {
                    "element": "标的物",
                    "patterns": [r"标的", r"服务内容", r"商品"],
                    "required": True
                },
                {
                    "element": "价款或报酬",
                    "patterns": [r"价格", r"费用", r"报酬", r"金额", r"\d+元"],
                    "required": True
                }
            ]
        }
        
        self.reasonableness_rules = {
            "经济条款": [
                {
                    "pattern": r"违约金.*(\d+)%",
                    "check_type": "percentage",
                    "max_reasonable": 30,
                    "description": "违约金比例检查"
                }
            ],
            "权利义务": [
                {
                    "pattern": r"单方.*决定|单方.*变更",
                    "severity": "medium",
                    "description": "存在单方决定权，可能不够合理"
                }
            ]
        }
    
    def analyze_clause(self, clause_content: str, clause_type: str = "", clause_title: str = "") -> SimpleClauseAnalysisResult:
        """分析单个条款"""
        result = SimpleClauseAnalysisResult(
            clause_id=f"clause_{hash(clause_content) % 10000}",
            clause_title=clause_title or clause_content[:30] + "..."
        )
        
        try:
            # 1. 合法性分析
            result.legality_score, result.legality_issues = self._analyze_legality(clause_content)
            
            # 2. 完整性分析
            result.completeness_score, result.completeness_issues = self._analyze_completeness(clause_content)
            
            # 3. 合理性分析
            result.reasonableness_score, result.reasonableness_issues = self._analyze_reasonableness(clause_content)
            
            # 4. 计算综合得分
            result.overall_score = (
                result.legality_score * 0.4 +
                result.completeness_score * 0.3 +
                result.reasonableness_score * 0.3
            )
            
            # 5. 生成改进建议
            result.suggestions = self._generate_suggestions(result)
            
            return result
            
        except Exception as e:
            result.overall_score = 0.0
            result.legality_issues.append(f"分析过程出错: {str(e)}")
            return result
    
    def _analyze_legality(self, content: str) -> Tuple[float, List[str]]:
        """分析合法性"""
        issues = []
        base_score = 100.0
        
        for category, rules in self.legality_rules.items():
            for rule in rules:
                pattern = rule["pattern"]
                if re.search(pattern, content, re.IGNORECASE):
                    severity = rule["severity"]
                    description = rule["description"]
                    
                    if severity == "critical":
                        base_score -= 40
                    elif severity == "high":
                        base_score -= 25
                    elif severity == "medium":
                        base_score -= 15
                    else:
                        base_score -= 5
                    
                    issues.append(f"[{severity.upper()}] {description}")
        
        return max(0.0, base_score), issues
    
    def _analyze_completeness(self, content: str) -> Tuple[float, List[str]]:
        """分析完整性"""
        issues = []
        total_elements = 0
        present_elements = 0
        
        for category, elements in self.completeness_rules.items():
            for element in elements:
                total_elements += 1
                element_name = element["element"]
                patterns = element["patterns"]
                required = element["required"]
                
                found = any(re.search(pattern, content, re.IGNORECASE) for pattern in patterns)
                
                if found:
                    present_elements += 1
                elif required:
                    issues.append(f"缺少必要要素: {element_name}")
        
        if total_elements > 0:
            completeness_score = (present_elements / total_elements) * 100
        else:
            completeness_score = 100.0
        
        return completeness_score, issues
    
    def _analyze_reasonableness(self, content: str) -> Tuple[float, List[str]]:
        """分析合理性"""
        issues = []
        base_score = 100.0
        
        for category, rules in self.reasonableness_rules.items():
            for rule in rules:
                pattern = rule["pattern"]
                check_type = rule.get("check_type", "pattern")
                
                if check_type == "percentage":
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        try:
                            percentage = float(match)
                            max_reasonable = rule.get("max_reasonable", 100)
                            if percentage > max_reasonable:
                                base_score -= 20
                                issues.append(f"{rule['description']}: {percentage}%超过合理范围")
                        except ValueError:
                            continue
                else:
                    if re.search(pattern, content, re.IGNORECASE):
                        severity = rule.get("severity", "low")
                        description = rule["description"]
                        
                        if severity == "high":
                            base_score -= 20
                        elif severity == "medium":
                            base_score -= 10
                        else:
                            base_score -= 5
                        
                        issues.append(f"合理性问题: {description}")
        
        return max(0.0, base_score), issues
    
    def _generate_suggestions(self, result: SimpleClauseAnalysisResult) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if result.legality_score < 80:
            suggestions.append("建议审查条款的合法性")
        
        if result.completeness_score < 70:
            suggestions.append("建议补充缺失的必要条款要素")
        
        if result.reasonableness_score < 70:
            suggestions.append("建议调整条款内容，确保合理性")
        
        if result.overall_score < 60:
            suggestions.append("条款存在较多问题，建议重新起草")
        
        return suggestions
    
    def analyze_contract_clauses(self, clauses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析整个合同的所有条款"""
        try:
            clause_results = []
            
            for clause in clauses:
                content = clause.get("content", "")
                clause_type = clause.get("clause_type", "")
                title = clause.get("title", "")
                
                if content:
                    result = self.analyze_clause(content, clause_type, title)
                    clause_results.append(result)
            
            overall_stats = self._calculate_overall_stats(clause_results)
            overall_suggestions = self._generate_overall_suggestions(clause_results, overall_stats)
            
            return {
                "clause_analyses": [result.to_dict() for result in clause_results],
                "overall_stats": overall_stats,
                "overall_suggestions": overall_suggestions,
                "analysis_timestamp": datetime.now().isoformat(),
                "total_clauses": len(clause_results)
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    def _calculate_overall_stats(self, results: List[SimpleClauseAnalysisResult]) -> Dict[str, Any]:
        """计算整体统计信息"""
        if not results:
            return {
                "average_scores": {"legality": 0, "completeness": 0, "reasonableness": 0, "overall": 0},
                "issue_counts": {"legality": 0, "completeness": 0, "reasonableness": 0},
                "quality_level": "unknown"
            }
        
        avg_legality = sum(r.legality_score for r in results) / len(results)
        avg_completeness = sum(r.completeness_score for r in results) / len(results)
        avg_reasonableness = sum(r.reasonableness_score for r in results) / len(results)
        avg_overall = sum(r.overall_score for r in results) / len(results)
        
        legality_issues = sum(len(r.legality_issues) for r in results)
        completeness_issues = sum(len(r.completeness_issues) for r in results)
        reasonableness_issues = sum(len(r.reasonableness_issues) for r in results)
        
        if avg_overall >= 90:
            quality_level = "excellent"
        elif avg_overall >= 80:
            quality_level = "good"
        elif avg_overall >= 70:
            quality_level = "fair"
        else:
            quality_level = "poor"
        
        return {
            "average_scores": {
                "legality": round(avg_legality, 2),
                "completeness": round(avg_completeness, 2),
                "reasonableness": round(avg_reasonableness, 2),
                "overall": round(avg_overall, 2)
            },
            "issue_counts": {
                "legality": legality_issues,
                "completeness": completeness_issues,
                "reasonableness": reasonableness_issues
            },
            "quality_level": quality_level
        }
    
    def _generate_overall_suggestions(self, results: List[SimpleClauseAnalysisResult], 
                                    stats: Dict[str, Any]) -> List[str]:
        """生成整体建议"""
        suggestions = []
        avg_scores = stats["average_scores"]
        
        if avg_scores["legality"] < 80:
            suggestions.append("合同存在合法性问题，建议法律专业人士审查")
        
        if avg_scores["completeness"] < 70:
            suggestions.append("合同条款不够完整，建议补充必要条款")
        
        if avg_scores["reasonableness"] < 70:
            suggestions.append("合同条款合理性有待提高")
        
        if avg_scores["overall"] < 60:
            suggestions.append("合同整体质量较低，建议重新起草")
        else:
            suggestions.append("合同质量可接受")
        
        return suggestions


def test_single_clause_analysis():
    """测试单个条款分析"""
    analyzer = SimpleContractClauseAnalyzer()
    
    # 测试正常条款
    normal_clause = "甲方：北京科技有限公司，乙方：上海贸易有限公司。服务费用为10万元。"
    result = analyzer.analyze_clause(normal_clause, "基本条款", "当事人和费用条款")
    
    print("✓ 单个条款分析测试:")
    print(f"  条款标题: {result.clause_title}")
    print(f"  合法性得分: {result.legality_score}")
    print(f"  完整性得分: {result.completeness_score}")
    print(f"  合理性得分: {result.reasonableness_score}")
    print(f"  综合得分: {result.overall_score}")
    print(f"  建议数量: {len(result.suggestions)}")
    
    return result.overall_score > 0


def test_legality_analysis():
    """测试合法性分析"""
    analyzer = SimpleContractClauseAnalyzer()
    
    test_cases = [
        ("正常条款：甲方提供服务，乙方支付费用", True),
        ("违法条款：本合同帮助甲方规避税收", False),
        ("无效条款：本合同不受法律约束", False),
        ("垄断条款：甲乙双方联合限制市场竞争", False)
    ]
    
    print("✓ 合法性分析测试:")
    for clause_text, should_be_legal in test_cases:
        result = analyzer.analyze_clause(clause_text)
        is_legal = result.legality_score >= 80
        
        status = "✓" if (is_legal == should_be_legal) else "✗"
        print(f"  {status} '{clause_text[:20]}...' -> 合法性得分: {result.legality_score}")
        
        if result.legality_issues:
            print(f"    问题: {result.legality_issues}")
    
    return True


def test_completeness_analysis():
    """测试完整性分析"""
    analyzer = SimpleContractClauseAnalyzer()
    
    test_cases = [
        ("完整条款：甲方：公司A，乙方：公司B，服务内容：软件开发，费用：10万元", True),
        ("不完整条款：甲方提供服务", False),
        ("缺少当事人：服务费用为5万元", False)
    ]
    
    print("✓ 完整性分析测试:")
    for clause_text, should_be_complete in test_cases:
        result = analyzer.analyze_clause(clause_text)
        is_complete = result.completeness_score >= 70
        
        status = "✓" if (is_complete == should_be_complete) else "✗"
        print(f"  {status} '{clause_text[:20]}...' -> 完整性得分: {result.completeness_score}")
        
        if result.completeness_issues:
            print(f"    问题: {result.completeness_issues}")
    
    return True


def test_reasonableness_analysis():
    """测试合理性分析"""
    analyzer = SimpleContractClauseAnalyzer()
    
    test_cases = [
        ("合理条款：违约金为合同金额的10%", True),
        ("不合理条款：违约金为合同金额的50%", False),
        ("单方决定：甲方有权单方决定价格调整", False)
    ]
    
    print("✓ 合理性分析测试:")
    for clause_text, should_be_reasonable in test_cases:
        result = analyzer.analyze_clause(clause_text)
        is_reasonable = result.reasonableness_score >= 70
        
        status = "✓" if (is_reasonable == should_be_reasonable) else "✗"
        print(f"  {status} '{clause_text[:20]}...' -> 合理性得分: {result.reasonableness_score}")
        
        if result.reasonableness_issues:
            print(f"    问题: {result.reasonableness_issues}")
    
    return True


def test_contract_clauses_analysis():
    """测试整个合同条款分析"""
    analyzer = SimpleContractClauseAnalyzer()
    
    # 模拟合同条款
    clauses = [
        {
            "title": "第一条 当事人",
            "content": "甲方：北京科技有限公司，乙方：上海贸易有限公司",
            "clause_type": "当事人条款"
        },
        {
            "title": "第二条 服务内容",
            "content": "甲方为乙方提供软件开发服务",
            "clause_type": "标的条款"
        },
        {
            "title": "第三条 费用",
            "content": "服务费用为20万元，分两期支付",
            "clause_type": "价款条款"
        },
        {
            "title": "第四条 违约责任",
            "content": "如一方违约，应支付违约金为合同金额的15%",
            "clause_type": "违约责任"
        }
    ]
    
    result = analyzer.analyze_contract_clauses(clauses)
    
    print("✓ 整个合同条款分析测试:")
    print(f"  分析条款数: {result['total_clauses']}")
    print(f"  平均综合得分: {result['overall_stats']['average_scores']['overall']}")
    print(f"  质量等级: {result['overall_stats']['quality_level']}")
    print(f"  整体建议数: {len(result['overall_suggestions'])}")
    
    return result['total_clauses'] == 4


def test_suggestions_generation():
    """测试建议生成"""
    analyzer = SimpleContractClauseAnalyzer()
    
    # 有问题的条款
    problematic_clause = "本合同规避相关税收，甲方单方决定所有事项，违约金为合同金额的60%"
    result = analyzer.analyze_clause(problematic_clause)
    
    print("✓ 建议生成测试:")
    print(f"  综合得分: {result.overall_score}")
    print(f"  生成建议数: {len(result.suggestions)}")
    for i, suggestion in enumerate(result.suggestions, 1):
        print(f"    {i}. {suggestion}")
    
    return len(result.suggestions) > 0


def main():
    """主测试函数"""
    print("开始独立合同条款分析器测试...")
    print("=" * 50)
    
    tests = [
        ("单个条款分析测试", test_single_clause_analysis),
        ("合法性分析测试", test_legality_analysis),
        ("完整性分析测试", test_completeness_analysis),
        ("合理性分析测试", test_reasonableness_analysis),
        ("整个合同条款分析测试", test_contract_clauses_analysis),
        ("建议生成测试", test_suggestions_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
