#!/usr/bin/env python3
"""
合同解析器测试
"""

import sys
import os

# 添加app目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_contract_parser_import():
    """测试合同解析器导入"""
    try:
        from services.contract_parser import ContractParser, contract_parser, parse_contract
        print("✓ 合同解析器导入成功")
        return True
    except ImportError as e:
        print(f"✗ 合同解析器导入失败: {e}")
        return False

def test_basic_contract_parsing():
    """测试基本合同解析"""
    try:
        from services.contract_parser import parse_contract
        
        # 模拟合同文本
        contract_text = """
        房屋租赁合同
        
        甲方：张三（出租方）
        乙方：李四（承租方）
        
        根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿的基础上，就房屋租赁事宜达成如下协议：
        
        第一条 租赁房屋基本情况
        甲方将位于北京市朝阳区的房屋出租给乙方使用。房屋面积为80平方米。
        
        第二条 租赁期限
        租赁期限为2年，自2023年1月1日起至2024年12月31日止。
        
        第三条 租金及支付方式
        月租金为5000元，乙方应于每月1日前支付当月租金。
        
        第四条 违约责任
        如乙方逾期支付租金超过10日，甲方有权解除合同，乙方应支付违约金2000元。
        
        第五条 争议解决
        因本合同发生争议，双方应协商解决；协商不成的，提交北京仲裁委员会仲裁。
        
        本合同自双方签字之日起生效。
        
        甲方签字：_______    乙方签字：_______
        日期：2023年1月1日
        """
        
        result = parse_contract(contract_text)
        
        print("✓ 基本合同解析测试:")
        print(f"  合同类型: {result['contract_type']['primary_type']}")
        print(f"  条款数量: {result['clause_count']}")
        print(f"  当事人数量: {len(result['basic_info']['parties'])}")
        print(f"  实体类型数: {len(result['entities'])}")
        print(f"  结构质量: {result['structure_analysis']['structure_quality']}")
        
        # 验证基本解析结果
        assert result['contract_type']['primary_type'] == "租赁合同"
        assert result['clause_count'] > 0
        assert len(result['basic_info']['parties']) >= 2
        assert "合同金额" in result['entities'] or "租金" in str(result)
        
        return True
        
    except Exception as e:
        print(f"✗ 基本合同解析测试失败: {e}")
        return False

def test_contract_type_identification():
    """测试合同类型识别"""
    try:
        from services.contract_parser import contract_parser
        
        test_cases = [
            ("这是一份房屋买卖合同，甲方出售房屋给乙方", "买卖合同"),
            ("劳动合同：甲方聘用乙方为员工，月薪8000元", "劳动合同"),
            ("借款合同：甲方向乙方借款10万元，年利率5%", "借款合同"),
            ("技术服务合同：甲方为乙方提供软件开发服务", "服务合同"),
            ("保密协议：双方就商业秘密保护达成协议", "保密协议")
        ]
        
        print("✓ 合同类型识别测试:")
        correct = 0
        for text, expected_type in test_cases:
            result = contract_parser._identify_contract_type(text)
            predicted_type = result["primary_type"]
            confidence = result["confidence"]
            
            is_correct = predicted_type == expected_type
            if is_correct:
                correct += 1
            
            status = "✓" if is_correct else "✗"
            print(f"  {status} '{text[:20]}...' -> {predicted_type} (置信度: {confidence:.2f})")
        
        accuracy = correct / len(test_cases)
        print(f"  准确率: {accuracy:.2%}")
        
        return accuracy >= 0.6
        
    except Exception as e:
        print(f"✗ 合同类型识别测试失败: {e}")
        return False

def test_entity_extraction():
    """测试实体提取"""
    try:
        from services.contract_parser import contract_parser
        
        test_text = """
        合同金额为100万元，租赁期限为3年，违约金为合同金额的10%。
        利率为年利率5.5%，保证金为5万元。
        交付时间为2023年6月30日，管辖法院为北京市朝阳区人民法院。
        甲方：北京科技有限公司
        乙方：上海贸易有限公司
        """
        
        entities = contract_parser._extract_entities(test_text)
        
        print("✓ 实体提取测试:")
        for entity_type, values in entities.items():
            print(f"  {entity_type}: {values}")
        
        # 验证关键实体是否被提取
        expected_entities = ["合同金额", "合同期限", "违约金", "利率", "当事人"]
        found_entities = list(entities.keys())
        
        return len(found_entities) > 0
        
    except Exception as e:
        print(f"✗ 实体提取测试失败: {e}")
        return False

def test_clause_classification():
    """测试条款分类"""
    try:
        from services.contract_parser import contract_parser
        
        test_clauses = [
            ("第一条 定义：本合同所称房屋是指...", "定义条款"),
            ("第二条 甲方权利：甲方有权...", "权利义务"),
            ("第三条 违约责任：如乙方违约...", "违约责任"),
            ("第四条 争议解决：双方发生争议时...", "争议解决"),
            ("第五条 合同生效：本合同自签署之日起生效", "生效条件")
        ]
        
        print("✓ 条款分类测试:")
        correct = 0
        for clause_text, expected_type in test_clauses:
            predicted_type = contract_parser._classify_clause(clause_text)
            
            is_correct = predicted_type == expected_type
            if is_correct:
                correct += 1
            
            status = "✓" if is_correct else "✗"
            print(f"  {status} '{clause_text[:20]}...' -> {predicted_type}")
        
        accuracy = correct / len(test_clauses)
        print(f"  准确率: {accuracy:.2%}")
        
        return accuracy >= 0.6
        
    except Exception as e:
        print(f"✗ 条款分类测试失败: {e}")
        return False

def test_key_terms_extraction():
    """测试关键术语提取"""
    try:
        from services.contract_parser import contract_parser
        
        test_text = """
        根据合同法规定，当事人应当履行合同义务。
        如发生违约行为，违约方应承担违约责任，支付违约金。
        双方可通过仲裁或诉讼方式解决争议。
        """
        
        key_terms = contract_parser._extract_key_terms(test_text)
        
        print("✓ 关键术语提取测试:")
        for term in key_terms[:5]:  # 显示前5个
            print(f"  {term['term']} (频率: {term['frequency']})")
        
        # 验证是否提取到法律术语
        term_names = [term['term'] for term in key_terms]
        legal_terms = ["合同", "当事人", "义务", "违约", "责任"]
        found_legal_terms = [term for term in legal_terms if term in term_names]
        
        return len(found_legal_terms) > 0
        
    except Exception as e:
        print(f"✗ 关键术语提取测试失败: {e}")
        return False

def test_structure_analysis():
    """测试结构分析"""
    try:
        from services.contract_parser import ContractClause, contract_parser
        
        # 创建模拟条款
        clauses = [
            ContractClause("1", "定义条款", "本合同所称...", "定义条款"),
            ContractClause("2", "权利义务", "甲方权利...", "权利义务"),
            ContractClause("3", "违约责任", "违约责任...", "违约责任"),
            ContractClause("4", "争议解决", "争议解决...", "争议解决"),
            ContractClause("5", "生效条件", "合同生效...", "生效条件")
        ]
        
        structure = contract_parser._analyze_structure(clauses)
        
        print("✓ 结构分析测试:")
        print(f"  总条款数: {structure['total_clauses']}")
        print(f"  完整性得分: {structure['completeness_score']:.2f}")
        print(f"  结构质量: {structure['structure_quality']}")
        print(f"  缺失条款: {structure['missing_clauses']}")
        
        # 验证结构分析结果
        assert structure['total_clauses'] == 5
        assert structure['completeness_score'] == 1.0  # 所有必要条款都存在
        assert structure['structure_quality'] == "excellent"
        
        return True
        
    except Exception as e:
        print(f"✗ 结构分析测试失败: {e}")
        return False

def test_comprehensive_parsing():
    """测试综合解析功能"""
    try:
        from services.contract_parser import parse_contract
        
        # 复杂合同文本
        complex_contract = """
        软件开发服务合同
        
        甲方：北京科技有限公司（委托方）
        乙方：上海软件开发有限公司（承接方）
        
        第一条 项目概述
        甲方委托乙方开发一套企业管理系统，开发周期为6个月。
        
        第二条 合同金额
        项目总金额为50万元，分三期支付：
        1. 签约后支付30%，即15万元
        2. 项目完成50%后支付40%，即20万元  
        3. 项目验收通过后支付30%，即15万元
        
        第三条 交付时间
        乙方应于2023年12月31日前完成项目交付。
        
        第四条 违约责任
        如乙方延期交付，每延期一日支付合同金额1%的违约金。
        
        第五条 知识产权
        项目开发的所有知识产权归甲方所有。
        
        第六条 保密条款
        双方应对项目相关信息严格保密。
        
        第七条 争议解决
        争议提交北京仲裁委员会仲裁解决。
        
        甲方：_______    乙方：_______
        2023年6月1日
        """
        
        result = parse_contract(complex_contract)
        
        print("✓ 综合解析测试:")
        print(f"  合同标题: {result['basic_info']['title']}")
        print(f"  合同类型: {result['contract_type']['primary_type']}")
        print(f"  当事人: {len(result['basic_info']['parties'])}方")
        print(f"  条款数量: {result['clause_count']}")
        print(f"  实体类型: {len(result['entities'])}种")
        print(f"  关键术语: {len(result['key_terms'])}个")
        print(f"  完整性得分: {result['structure_analysis']['completeness_score']:.2f}")
        
        # 验证解析质量
        assert result['contract_type']['primary_type'] == "服务合同"
        assert result['clause_count'] >= 5
        assert len(result['basic_info']['parties']) == 2
        assert result['structure_analysis']['completeness_score'] > 0.5
        
        return True
        
    except Exception as e:
        print(f"✗ 综合解析测试失败: {e}")
        return False

def test_jieba_integration():
    """测试jieba集成"""
    try:
        import jieba
        
        text = "根据合同法规定，当事人应当履行合同义务"
        tokens = list(jieba.cut(text))
        
        print(f"✓ jieba分词测试: {tokens}")
        
        # 检查关键法律术语是否正确分词
        expected_terms = ["合同法", "当事人", "合同", "义务"]
        found_terms = [term for term in expected_terms if term in tokens]
        
        return len(found_terms) > 0
        
    except Exception as e:
        print(f"✗ jieba集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始合同解析器测试...")
    print("=" * 50)
    
    tests = [
        ("合同解析器导入测试", test_contract_parser_import),
        ("基本合同解析测试", test_basic_contract_parsing),
        ("合同类型识别测试", test_contract_type_identification),
        ("实体提取测试", test_entity_extraction),
        ("条款分类测试", test_clause_classification),
        ("关键术语提取测试", test_key_terms_extraction),
        ("结构分析测试", test_structure_analysis),
        ("综合解析测试", test_comprehensive_parsing),
        ("jieba集成测试", test_jieba_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
