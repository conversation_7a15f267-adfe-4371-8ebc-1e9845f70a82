apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql
  namespace: ai-legal-prod
  labels:
    app: postgresql
    component: database
spec:
  serviceName: postgresql-headless
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
        component: database
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: postgresql
        image: postgres:15-alpine
        imagePullPolicy: IfNotPresent
        env:
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-secret
              key: database-name
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-secret
              key: database-user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-secret
              key: database-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        ports:
        - containerPort: 5432
          name: postgresql
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U $POSTGRES_USER -d $POSTGRES_DB
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U $POSTGRES_USER -d $POSTGRES_DB
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: postgresql-data
          mountPath: /var/lib/postgresql/data
        - name: postgresql-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        - name: postgresql-ssl
          mountPath: /var/lib/postgresql/ssl
          readOnly: true
      volumes:
      - name: postgresql-config
        configMap:
          name: postgresql-config
      - name: postgresql-ssl
        secret:
          secretName: postgresql-ssl
          defaultMode: 0600
  volumeClaimTemplates:
  - metadata:
      name: postgresql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql
  namespace: ai-legal-prod
  labels:
    app: postgresql
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgresql
  selector:
    app: postgresql

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-headless
  namespace: ai-legal-prod
  labels:
    app: postgresql
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgresql
  selector:
    app: postgresql

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-config
  namespace: ai-legal-prod
data:
  postgresql.conf: |
    # 连接和认证设置
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    
    # SSL设置
    ssl = on
    ssl_cert_file = '/var/lib/postgresql/ssl/server.crt'
    ssl_key_file = '/var/lib/postgresql/ssl/server.key'
    ssl_ca_file = '/var/lib/postgresql/ssl/ca.crt'
    
    # 内存设置
    shared_buffers = 256MB
    effective_cache_size = 1GB
    work_mem = 4MB
    maintenance_work_mem = 64MB
    
    # 检查点设置
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    
    # 日志设置
    logging_collector = on
    log_directory = 'pg_log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
    log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
    
    # 性能设置
    random_page_cost = 1.1
    effective_io_concurrency = 200
    
    # 安全设置
    password_encryption = scram-sha-256
    
    # 备份和恢复设置
    archive_mode = on
    archive_command = 'cp %p /var/lib/postgresql/archive/%f'
    max_wal_senders = 3
    wal_level = replica

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ai-legal-prod
  labels:
    app: redis
    component: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        component: cache
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: redis
        image: redis:7-alpine
        imagePullPolicy: IfNotPresent
        command:
        - redis-server
        - /etc/redis/redis.conf
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        ports:
        - containerPort: 6379
          name: redis
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-data-pvc
      - name: redis-config
        configMap:
          name: redis-config

---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: ai-legal-prod
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: ai-legal-prod
data:
  redis.conf: |
    # 网络设置
    bind 0.0.0.0
    port 6379
    
    # 安全设置
    requirepass ${REDIS_PASSWORD}
    
    # 持久化设置
    appendonly yes
    appendfsync everysec
    save 900 1
    save 300 10
    save 60 10000
    
    # 内存设置
    maxmemory 256mb
    maxmemory-policy allkeys-lru
    
    # 日志设置
    loglevel notice
    logfile /data/redis.log
    
    # 性能设置
    tcp-keepalive 300
    timeout 0

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-data-pvc
  namespace: ai-legal-prod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgresql-backup
  namespace: ai-legal-prod
spec:
  schedule: "0 2 * * *"  # 每天凌晨2点
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: database-password
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: database-user
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: database-name
            command:
            - /bin/bash
            - -c
            - |
              DATE=$(date +%Y%m%d_%H%M%S)
              BACKUP_FILE="/backups/db_backup_${DATE}.sql"
              pg_dump -h postgresql -U $POSTGRES_USER -d $POSTGRES_DB > $BACKUP_FILE
              
              # 压缩备份文件
              gzip $BACKUP_FILE
              
              # 清理30天前的备份
              find /backups -name "db_backup_*.sql.gz" -mtime +30 -delete
              
              echo "备份完成: ${BACKUP_FILE}.gz"
            volumeMounts:
            - name: backup-storage
              mountPath: /backups
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          restartPolicy: OnFailure

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-storage-pvc
  namespace: ai-legal-prod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: standard
