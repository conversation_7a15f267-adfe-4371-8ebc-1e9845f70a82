apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-legal-assistant
  namespace: ai-legal-prod
  labels:
    app: ai-legal-assistant
    version: v1.0.0
    environment: production
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ai-legal-assistant
  template:
    metadata:
      labels:
        app: ai-legal-assistant
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: ai-legal-assistant
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: app
        image: ghcr.io/yourorg/ai-legal-assistant:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-legal-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: ai-legal-secrets
              key: redis-url
        - name: ENCRYPTION_MASTER_KEY
          valueFrom:
            secretKeyRef:
              name: ai-legal-secrets
              key: encryption-master-key
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: ai-legal-secrets
              key: secret-key
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-legal-secrets
              key: openai-api-key
        envFrom:
        - configMapRef:
            name: ai-legal-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
        - name: ssl-certs
          mountPath: /app/certs
          readOnly: true
        - name: config-volume
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: app-logs
        persistentVolumeClaim:
          claimName: ai-legal-logs-pvc
      - name: ssl-certs
        secret:
          secretName: ai-legal-tls
      - name: config-volume
        configMap:
          name: ai-legal-config
      imagePullSecrets:
      - name: ghcr-secret
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ai-legal-assistant
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: ai-legal-assistant-service
  namespace: ai-legal-prod
  labels:
    app: ai-legal-assistant
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: ai-legal-assistant

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-legal-assistant-ingress
  namespace: ai-legal-prod
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options DENY always;
      add_header X-Content-Type-Options nosniff always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;" always;
spec:
  tls:
  - hosts:
    - yourdomain.com
    - www.yourdomain.com
    secretName: ai-legal-tls
  rules:
  - host: yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-legal-assistant-service
            port:
              number: 80
  - host: www.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-legal-assistant-service
            port:
              number: 80

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-legal-config
  namespace: ai-legal-prod
data:
  # 应用配置
  APP_NAME: "AI法律助手"
  APP_VERSION: "1.0.0"
  DEBUG: "false"
  LOG_LEVEL: "INFO"
  
  # 安全配置
  FORCE_HTTPS: "true"
  TRUSTED_HOSTS: "yourdomain.com,*.yourdomain.com"
  CORS_ORIGINS: "https://yourdomain.com,https://www.yourdomain.com"
  CORS_ALLOW_CREDENTIALS: "true"
  
  # 速率限制
  RATE_LIMIT_ENABLED: "true"
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"
  
  # 审计日志
  AUDIT_LOG_ENABLED: "true"
  AUDIT_LOG_LEVEL: "INFO"
  
  # 数据脱敏
  DATA_MASKING_ENABLED: "true"
  MASK_EMAIL: "true"
  MASK_PHONE: "true"
  MASK_ID_CARD: "true"
  
  # 合规配置
  GDPR_COMPLIANCE: "true"
  CCPA_COMPLIANCE: "true"
  
  # 监控配置
  SECURITY_MONITORING_ENABLED: "true"
  FAILED_LOGIN_THRESHOLD: "5"
  FAILED_LOGIN_WINDOW: "300"
  
  # 数据保留
  DATA_RETENTION_DAYS: "2555"
  LOG_RETENTION_DAYS: "365"
  SESSION_RETENTION_DAYS: "30"
  
  # 性能配置
  WORKERS: "4"
  MAX_CONNECTIONS: "1000"
  KEEPALIVE_TIMEOUT: "65"
  CLIENT_TIMEOUT: "60"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-legal-logs-pvc
  namespace: ai-legal-prod
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: nfs-client

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ai-legal-assistant-pdb
  namespace: ai-legal-prod
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: ai-legal-assistant

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-legal-assistant-hpa
  namespace: ai-legal-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-legal-assistant
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ai-legal-assistant
  namespace: ai-legal-prod
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT:role/ai-legal-assistant-role

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: ai-legal-prod
  name: ai-legal-assistant-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ai-legal-assistant-rolebinding
  namespace: ai-legal-prod
subjects:
- kind: ServiceAccount
  name: ai-legal-assistant
  namespace: ai-legal-prod
roleRef:
  kind: Role
  name: ai-legal-assistant-role
  apiGroup: rbac.authorization.k8s.io
