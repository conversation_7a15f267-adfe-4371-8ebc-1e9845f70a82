"""
免责声明系统测试
"""

import pytest
import json
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session
from fastapi import Request, Response
from starlette.responses import Response as StarletteResponse

from app.core.disclaimer import (
    DisclaimerManager, DisclaimerType, AgreementType,
    get_disclaimer_manager, DEFAULT_DISCLAIMERS, DEFAULT_AGREEMENTS
)
from app.middleware.disclaimer_middleware import DisclaimerMiddleware


class TestDisclaimerManager:
    """测试免责声明管理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.mock_db = Mock(spec=Session)
        self.disclaimer_manager = DisclaimerManager(self.mock_db)
    
    def test_create_disclaimer_template(self):
        """测试创建免责声明模板"""
        template_name = "测试AI免责声明"
        disclaimer_type = DisclaimerType.AI_RESPONSE
        title = "AI回答免责声明"
        content = "这是一个测试免责声明内容"
        
        # 模拟数据库操作
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        # 调用方法
        result = self.disclaimer_manager.create_disclaimer_template(
            template_name=template_name,
            disclaimer_type=disclaimer_type,
            title=title,
            content=content
        )
        
        # 验证结果
        assert result.template_name == template_name
        assert result.disclaimer_type == disclaimer_type.value
        assert result.title == title
        assert result.content == content
        assert result.is_mandatory == True
        assert result.display_order == 0
        
        # 验证数据库操作
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
    
    def test_create_user_agreement(self):
        """测试创建用户协议"""
        agreement_name = "测试服务协议"
        agreement_type = AgreementType.TERMS_OF_SERVICE
        title = "用户服务协议"
        content = "这是一个测试用户协议内容"
        
        # 模拟数据库操作
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        # 调用方法
        result = self.disclaimer_manager.create_user_agreement(
            agreement_name=agreement_name,
            agreement_type=agreement_type,
            title=title,
            content=content
        )
        
        # 验证结果
        assert result.agreement_name == agreement_name
        assert result.agreement_type == agreement_type.value
        assert result.title == title
        assert result.content == content
        assert result.requires_acceptance == True
        assert result.effective_date is not None
        
        # 验证数据库操作
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
    
    def test_get_active_disclaimers(self):
        """测试获取激活的免责声明"""
        # 创建模拟免责声明
        mock_disclaimers = [
            Mock(
                id="disclaimer-1",
                template_name="AI免责声明",
                disclaimer_type=DisclaimerType.AI_RESPONSE.value,
                title="AI回答免责声明",
                content="AI回答仅供参考",
                is_active=True,
                display_order=1
            ),
            Mock(
                id="disclaimer-2",
                template_name="法律建议免责声明",
                disclaimer_type=DisclaimerType.LEGAL_ADVICE.value,
                title="法律建议免责声明",
                content="不构成法律建议",
                is_active=True,
                display_order=2
            )
        ]
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = mock_disclaimers
        
        self.mock_db.query.return_value = mock_query
        
        # 调用方法
        result = self.disclaimer_manager.get_active_disclaimers()
        
        # 验证结果
        assert len(result) == 2
        assert all(disclaimer.is_active for disclaimer in result)
    
    def test_get_ai_response_disclaimer(self):
        """测试获取AI回答免责声明"""
        # 模拟有免责声明的情况
        mock_disclaimer = Mock()
        mock_disclaimer.content = "AI回答仅供参考，不构成法律建议"
        
        with patch.object(self.disclaimer_manager, 'get_active_disclaimers') as mock_get:
            mock_get.return_value = [mock_disclaimer]
            
            result = self.disclaimer_manager.get_ai_response_disclaimer()
            assert result == mock_disclaimer.content
        
        # 模拟没有免责声明的情况
        with patch.object(self.disclaimer_manager, 'get_active_disclaimers') as mock_get:
            mock_get.return_value = []
            
            result = self.disclaimer_manager.get_ai_response_disclaimer()
            assert result is None
    
    def test_get_legal_risk_warning(self):
        """测试获取法律风险提示"""
        # 模拟有法律建议免责声明的情况
        mock_disclaimer = Mock()
        mock_disclaimer.content = "请咨询专业律师获取法律建议"
        
        with patch.object(self.disclaimer_manager, 'get_active_disclaimers') as mock_get:
            mock_get.return_value = [mock_disclaimer]
            
            result = self.disclaimer_manager.get_legal_risk_warning()
            assert result == mock_disclaimer.content
        
        # 模拟没有法律建议免责声明的情况
        with patch.object(self.disclaimer_manager, 'get_active_disclaimers') as mock_get:
            mock_get.return_value = []
            
            result = self.disclaimer_manager.get_legal_risk_warning()
            assert "重要提示" in result
            assert "不构成正式的法律建议" in result


class TestDisclaimerMiddleware:
    """测试免责声明中间件"""
    
    def setup_method(self):
        """设置测试环境"""
        self.app = Mock()
        self.middleware = DisclaimerMiddleware(self.app)
    
    def test_should_add_disclaimer(self):
        """测试路径匹配逻辑"""
        # 应该添加免责声明的路径
        assert self.middleware._should_add_disclaimer("/api/v1/ai/chat") == True
        assert self.middleware._should_add_disclaimer("/api/v1/ai/analyze") == True
        assert self.middleware._should_add_disclaimer("/api/v1/contracts/analyze") == True
        
        # 不应该添加免责声明的路径
        assert self.middleware._should_add_disclaimer("/api/v1/users") == False
        assert self.middleware._should_add_disclaimer("/health") == False
        assert self.middleware._should_add_disclaimer("/docs") == False
    
    def test_add_disclaimer_to_text(self):
        """测试向文本添加免责声明"""
        original_text = "这是AI的回答内容"
        disclaimer = "⚠️ 免责声明：仅供参考"
        
        # 测试添加到末尾
        result = self.middleware._add_disclaimer_to_text(original_text, disclaimer)
        assert original_text in result
        assert disclaimer in result
        assert result.endswith(disclaimer)
        
        # 测试添加到开头
        self.middleware.config['disclaimer_position'] = 'header'
        result = self.middleware._add_disclaimer_to_text(original_text, disclaimer)
        assert result.startswith(disclaimer)
        assert original_text in result
        
        # 测试添加到两端
        self.middleware.config['disclaimer_position'] = 'both'
        result = self.middleware._add_disclaimer_to_text(original_text, disclaimer)
        assert result.startswith(disclaimer)
        assert result.endswith(disclaimer)
        assert original_text in result
    
    @pytest.mark.asyncio
    async def test_modify_json_response(self):
        """测试修改JSON响应"""
        # 测试包含answer字段的JSON
        response_data = {
            "answer": "这是AI的回答",
            "confidence": 0.95
        }
        response_text = json.dumps(response_data)
        disclaimer = "⚠️ 免责声明"
        
        result_bytes = await self.middleware._modify_json_response(response_text, disclaimer)
        result_data = json.loads(result_bytes.decode('utf-8'))
        
        # 验证免责声明被添加到answer字段
        assert disclaimer in result_data["answer"]
        assert result_data["confidence"] == 0.95
        assert "_disclaimer" in result_data
        assert result_data["_disclaimer"]["type"] == "ai_response"
    
    @pytest.mark.asyncio
    async def test_modify_json_response_no_ai_fields(self):
        """测试修改不包含AI字段的JSON响应"""
        response_data = {
            "status": "success",
            "data": {"id": 123}
        }
        response_text = json.dumps(response_data)
        disclaimer = "⚠️ 免责声明"
        
        result_bytes = await self.middleware._modify_json_response(response_text, disclaimer)
        result_data = json.loads(result_bytes.decode('utf-8'))
        
        # 验证免责声明被添加为独立字段
        assert result_data["legal_disclaimer"] == disclaimer
        assert result_data["status"] == "success"
        assert "_disclaimer" in result_data
    
    @pytest.mark.asyncio
    async def test_modify_text_response(self):
        """测试修改文本响应"""
        response_text = "这是AI的文本回答"
        disclaimer = "⚠️ 免责声明"
        
        result_bytes = await self.middleware._modify_text_response(response_text, disclaimer)
        result_text = result_bytes.decode('utf-8')
        
        # 验证免责声明被添加
        assert response_text in result_text
        assert disclaimer in result_text
    
    @pytest.mark.asyncio
    async def test_get_disclaimer_text(self):
        """测试获取免责声明文本"""
        # 模拟数据库和管理器
        mock_disclaimer_manager = Mock()
        mock_disclaimer_manager.get_ai_response_disclaimer.return_value = "测试免责声明"
        
        with patch('app.middleware.disclaimer_middleware.get_db') as mock_get_db:
            with patch('app.middleware.disclaimer_middleware.get_disclaimer_manager') as mock_get_manager:
                mock_get_db.return_value = iter([Mock()])  # 模拟数据库会话
                mock_get_manager.return_value = mock_disclaimer_manager
                
                result = await self.middleware._get_disclaimer_text()
                assert result == "测试免责声明"
        
        # 测试获取失败时的默认免责声明
        with patch('app.middleware.disclaimer_middleware.get_db') as mock_get_db:
            mock_get_db.side_effect = Exception("数据库连接失败")
            
            result = await self.middleware._get_disclaimer_text()
            assert result is None


class TestDefaultData:
    """测试默认数据"""
    
    def test_default_disclaimers_structure(self):
        """测试默认免责声明结构"""
        assert len(DEFAULT_DISCLAIMERS) > 0
        
        for disclaimer in DEFAULT_DISCLAIMERS:
            # 验证必需字段
            assert "template_name" in disclaimer
            assert "disclaimer_type" in disclaimer
            assert "title" in disclaimer
            assert "content" in disclaimer
            assert "summary" in disclaimer
            assert "is_mandatory" in disclaimer
            assert "display_order" in disclaimer
            
            # 验证类型
            assert isinstance(disclaimer["disclaimer_type"], DisclaimerType)
            assert isinstance(disclaimer["is_mandatory"], bool)
            assert isinstance(disclaimer["display_order"], int)
    
    def test_default_agreements_structure(self):
        """测试默认用户协议结构"""
        assert len(DEFAULT_AGREEMENTS) > 0
        
        for agreement in DEFAULT_AGREEMENTS:
            # 验证必需字段
            assert "agreement_name" in agreement
            assert "agreement_type" in agreement
            assert "title" in agreement
            assert "content" in agreement
            assert "summary" in agreement
            assert "requires_acceptance" in agreement
            
            # 验证类型
            assert isinstance(agreement["agreement_type"], AgreementType)
            assert isinstance(agreement["requires_acceptance"], bool)


class TestIntegration:
    """集成测试"""
    
    def test_get_disclaimer_manager(self):
        """测试获取免责声明管理器"""
        mock_db = Mock(spec=Session)
        manager = get_disclaimer_manager(mock_db)
        
        assert isinstance(manager, DisclaimerManager)
        assert manager.db_session == mock_db
    
    def test_enum_values(self):
        """测试枚举值"""
        # 测试免责声明类型
        assert DisclaimerType.AI_RESPONSE == "ai_response"
        assert DisclaimerType.LEGAL_ADVICE == "legal_advice"
        assert DisclaimerType.DATA_ACCURACY == "data_accuracy"
        
        # 测试协议类型
        assert AgreementType.TERMS_OF_SERVICE == "terms_of_service"
        assert AgreementType.PRIVACY_POLICY == "privacy_policy"
        assert AgreementType.USER_AGREEMENT == "user_agreement"
    
    def test_middleware_configuration(self):
        """测试中间件配置"""
        # 测试默认配置
        middleware = DisclaimerMiddleware(Mock())
        assert middleware.config['enable_auto_disclaimer'] == True
        assert middleware.config['disclaimer_position'] == 'footer'
        assert middleware.config['add_disclaimer_header'] == True
        
        # 测试自定义配置
        custom_config = {
            'enable_auto_disclaimer': False,
            'disclaimer_position': 'header',
            'add_disclaimer_header': False
        }
        middleware = DisclaimerMiddleware(Mock(), **custom_config)
        assert middleware.config['enable_auto_disclaimer'] == False
        assert middleware.config['disclaimer_position'] == 'header'
        assert middleware.config['add_disclaimer_header'] == False


if __name__ == "__main__":
    pytest.main([__file__])
