"""
数据加密系统测试
"""

import pytest
import os
from unittest.mock import patch
from app.core.encryption import Encry<PERSON><PERSON><PERSON><PERSON>, DataMasking, get_encryption_manager, init_encryption
from app.core.encrypted_types import (
    EncryptedString, EncryptedText, EncryptedEmail, EncryptedPhone,
    EncryptedName, EncryptedIDCard, EncryptedAddress, EncryptedBankCard
)


class TestEncryptionManager:
    """测试加密管理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.test_key = "test-encryption-key-for-testing-only"
        self.encryption_manager = EncryptionManager(self.test_key)
    
    def test_field_encryption_decryption(self):
        """测试字段加密和解密"""
        original_data = "这是一个测试数据"
        
        # 加密
        encrypted_data = self.encryption_manager.encrypt_field(original_data)
        assert encrypted_data != original_data
        assert len(encrypted_data) > len(original_data)
        
        # 解密
        decrypted_data = self.encryption_manager.decrypt_field(encrypted_data)
        assert decrypted_data == original_data
    
    def test_field_encryption_empty_data(self):
        """测试空数据加密"""
        # 测试None
        assert self.encryption_manager.encrypt_field(None) is None
        assert self.encryption_manager.decrypt_field(None) is None
        
        # 测试空字符串
        assert self.encryption_manager.encrypt_field("") == ""
        assert self.encryption_manager.decrypt_field("") == ""
    
    def test_aes_encryption_decryption(self):
        """测试AES加密和解密"""
        original_data = "这是AES加密测试数据"
        
        # 加密
        encrypted_data = self.encryption_manager.encrypt_aes(original_data)
        assert encrypted_data != original_data
        assert len(encrypted_data) > len(original_data)
        
        # 解密
        decrypted_data = self.encryption_manager.decrypt_aes(encrypted_data)
        assert decrypted_data == original_data
    
    def test_aes_encryption_empty_data(self):
        """测试AES空数据加密"""
        # 测试None
        assert self.encryption_manager.encrypt_aes(None) is None
        assert self.encryption_manager.decrypt_aes(None) is None
        
        # 测试空字符串
        assert self.encryption_manager.encrypt_aes("") == ""
        assert self.encryption_manager.decrypt_aes("") == ""
    
    def test_password_hashing(self):
        """测试密码哈希"""
        password = "test_password_123"
        
        # 生成哈希
        password_hash, salt = self.encryption_manager.hash_password(password)
        assert password_hash != password
        assert len(salt) > 0
        
        # 验证密码
        assert self.encryption_manager.verify_password(password, password_hash, salt)
        assert not self.encryption_manager.verify_password("wrong_password", password_hash, salt)
    
    def test_password_hashing_with_custom_salt(self):
        """测试使用自定义盐的密码哈希"""
        password = "test_password_123"
        custom_salt = "custom_salt_value"
        
        # 使用自定义盐生成哈希
        password_hash, returned_salt = self.encryption_manager.hash_password(password, custom_salt)
        assert returned_salt == custom_salt
        
        # 验证密码
        assert self.encryption_manager.verify_password(password, password_hash, custom_salt)
    
    def test_encryption_with_different_keys(self):
        """测试不同密钥的加密"""
        data = "测试数据"
        
        # 使用第一个密钥加密
        manager1 = EncryptionManager("key1")
        encrypted1 = manager1.encrypt_field(data)
        
        # 使用第二个密钥加密
        manager2 = EncryptionManager("key2")
        encrypted2 = manager2.encrypt_field(data)
        
        # 加密结果应该不同
        assert encrypted1 != encrypted2
        
        # 各自解密应该成功
        assert manager1.decrypt_field(encrypted1) == data
        assert manager2.decrypt_field(encrypted2) == data
        
        # 交叉解密应该失败
        with pytest.raises(Exception):
            manager1.decrypt_field(encrypted2)
        with pytest.raises(Exception):
            manager2.decrypt_field(encrypted1)


class TestDataMasking:
    """测试数据脱敏"""
    
    def test_mask_email(self):
        """测试邮箱脱敏"""
        # 正常邮箱
        assert DataMasking.mask_email("<EMAIL>") == "t**<EMAIL>"
        assert DataMasking.mask_email("<EMAIL>") == "*@example.com"
        assert DataMasking.mask_email("<EMAIL>") == "**@example.com"
        assert DataMasking.mask_email("<EMAIL>") == "a*<EMAIL>"
        
        # 异常情况
        assert DataMasking.mask_email("") == ""
        assert DataMasking.mask_email("invalid-email") == "invalid-email"
    
    def test_mask_phone(self):
        """测试手机号脱敏"""
        # 正常手机号
        assert DataMasking.mask_phone("13812345678") == "138****5678"
        assert DataMasking.mask_phone("1381234567") == "138***567"
        assert DataMasking.mask_phone("138123456") == "138**56"
        
        # 短号码
        assert DataMasking.mask_phone("123") == "***"
        assert DataMasking.mask_phone("1234") == "12**"
        
        # 包含非数字字符
        assert DataMasking.mask_phone("138-1234-5678") == "138****5678"
        assert DataMasking.mask_phone("+86 138 1234 5678") == "138****5678"
        
        # 异常情况
        assert DataMasking.mask_phone("") == ""
    
    def test_mask_id_card(self):
        """测试身份证号脱敏"""
        # 18位身份证
        assert DataMasking.mask_id_card("110101199001011234") == "1101**********1234"
        
        # 15位身份证
        assert DataMasking.mask_id_card("110101900101123") == "1101*******0123"
        
        # 短号码
        assert DataMasking.mask_id_card("1234567") == "*******"
        assert DataMasking.mask_id_card("123") == "***"
        
        # 异常情况
        assert DataMasking.mask_id_card("") == ""
    
    def test_mask_name(self):
        """测试姓名脱敏"""
        # 不同长度的姓名
        assert DataMasking.mask_name("张三") == "张*"
        assert DataMasking.mask_name("李四五") == "李*五"
        assert DataMasking.mask_name("王二小三") == "王**三"
        assert DataMasking.mask_name("欧阳修") == "欧*修"
        
        # 单字名
        assert DataMasking.mask_name("李") == "*"
        
        # 异常情况
        assert DataMasking.mask_name("") == ""
    
    def test_mask_bank_card(self):
        """测试银行卡号脱敏"""
        # 16位银行卡
        assert DataMasking.mask_bank_card("****************") == "1234********3456"
        
        # 19位银行卡
        assert DataMasking.mask_bank_card("****************789") == "1234***********6789"
        
        # 包含空格和连字符
        assert DataMasking.mask_bank_card("1234 5678 9012 3456") == "1234********3456"
        assert DataMasking.mask_bank_card("1234-5678-9012-3456") == "1234********3456"
        
        # 短号码
        assert DataMasking.mask_bank_card("1234567") == "*******"
        
        # 异常情况
        assert DataMasking.mask_bank_card("") == ""


class TestEncryptedTypes:
    """测试加密字段类型"""
    
    def setup_method(self):
        """设置测试环境"""
        # 模拟数据库方言
        self.dialect = None
        
        # 设置测试加密管理器
        with patch.dict(os.environ, {"ENCRYPTION_MASTER_KEY": "test-key"}):
            init_encryption()
    
    def test_encrypted_string(self):
        """测试加密字符串字段"""
        field = EncryptedString(length=100)
        
        # 测试加密
        original_value = "测试字符串"
        encrypted_value = field.process_bind_param(original_value, self.dialect)
        assert encrypted_value != original_value
        
        # 测试解密
        decrypted_value = field.process_result_value(encrypted_value, self.dialect)
        assert decrypted_value == original_value
    
    def test_encrypted_text(self):
        """测试加密文本字段"""
        field = EncryptedText()
        
        # 测试长文本
        original_value = "这是一个很长的测试文本" * 100
        encrypted_value = field.process_bind_param(original_value, self.dialect)
        assert encrypted_value != original_value
        
        # 测试解密
        decrypted_value = field.process_result_value(encrypted_value, self.dialect)
        assert decrypted_value == original_value
    
    def test_encrypted_email(self):
        """测试加密邮箱字段"""
        field = EncryptedEmail()
        
        original_value = "<EMAIL>"
        encrypted_value = field.process_bind_param(original_value, self.dialect)
        decrypted_value = field.process_result_value(encrypted_value, self.dialect)
        assert decrypted_value == original_value
    
    def test_encrypted_phone(self):
        """测试加密电话字段"""
        field = EncryptedPhone()
        
        original_value = "13812345678"
        encrypted_value = field.process_bind_param(original_value, self.dialect)
        decrypted_value = field.process_result_value(encrypted_value, self.dialect)
        assert decrypted_value == original_value
    
    def test_encrypted_name(self):
        """测试加密姓名字段"""
        field = EncryptedName()
        
        original_value = "张三"
        encrypted_value = field.process_bind_param(original_value, self.dialect)
        decrypted_value = field.process_result_value(encrypted_value, self.dialect)
        assert decrypted_value == original_value
    
    def test_encrypted_id_card(self):
        """测试加密身份证字段"""
        field = EncryptedIDCard()
        
        original_value = "110101199001011234"
        encrypted_value = field.process_bind_param(original_value, self.dialect)
        decrypted_value = field.process_result_value(encrypted_value, self.dialect)
        assert decrypted_value == original_value
    
    def test_encrypted_bank_card(self):
        """测试加密银行卡字段"""
        field = EncryptedBankCard()
        
        original_value = "****************"
        encrypted_value = field.process_bind_param(original_value, self.dialect)
        decrypted_value = field.process_result_value(encrypted_value, self.dialect)
        assert decrypted_value == original_value
    
    def test_encrypted_field_with_none(self):
        """测试加密字段处理None值"""
        field = EncryptedString()
        
        # None值应该保持不变
        assert field.process_bind_param(None, self.dialect) is None
        assert field.process_result_value(None, self.dialect) is None


class TestGlobalEncryptionManager:
    """测试全局加密管理器"""
    
    def test_get_encryption_manager_without_init(self):
        """测试未初始化时获取加密管理器"""
        # 清除全局实例
        import app.core.encryption
        app.core.encryption.encryption_manager = None
        
        # 设置环境变量
        with patch.dict(os.environ, {"ENCRYPTION_MASTER_KEY": "test-key"}):
            manager = get_encryption_manager()
            assert manager is not None
    
    def test_init_encryption(self):
        """测试初始化加密系统"""
        test_key = "test-init-key"
        init_encryption(test_key)
        
        manager = get_encryption_manager()
        assert manager is not None
        assert manager.master_key == test_key
    
    def test_encryption_manager_without_key(self):
        """测试没有密钥时的加密管理器"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="未找到加密主密钥"):
                EncryptionManager()


if __name__ == "__main__":
    pytest.main([__file__])
