"""
审计日志系统测试
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session

from app.core.anomaly_detection import (
    AnomalyDetector, AnomalyRule, AnomalyType, SeverityLevel,
    SecurityAlert, AlertStatus, get_anomaly_detector
)
from app.core.audit import AuditLog, AuditEventType, AuditLevel
from app.middleware.audit_middleware import AuditMiddleware


class TestAnomalyDetector:
    """测试异常检测器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.mock_db = Mock(spec=Session)
        self.anomaly_detector = AnomalyDetector(self.mock_db)
    
    def test_load_detection_rules(self):
        """测试加载检测规则"""
        rules = self.anomaly_detector.rules
        
        # 验证规则数量
        assert len(rules) > 0
        
        # 验证规则类型
        rule_types = [rule.anomaly_type for rule in rules]
        assert AnomalyType.FAILED_LOGIN_ATTEMPTS in rule_types
        assert AnomalyType.HIGH_FREQUENCY_REQUESTS in rule_types
        assert AnomalyType.BRUTE_FORCE_ATTACK in rule_types
        
        # 验证规则属性
        for rule in rules:
            assert isinstance(rule.rule_id, str)
            assert isinstance(rule.name, str)
            assert isinstance(rule.threshold, float)
            assert isinstance(rule.time_window_minutes, int)
    
    def test_check_failed_logins(self):
        """测试检查登录失败"""
        # 创建测试规则
        rule = AnomalyRule(
            rule_id="test_failed_login",
            name="测试登录失败",
            description="测试描述",
            anomaly_type=AnomalyType.FAILED_LOGIN_ATTEMPTS,
            severity=SeverityLevel.MEDIUM,
            threshold=3.0,
            time_window_minutes=15
        )
        
        # 创建模拟审计日志
        current_time = datetime.utcnow()
        mock_logs = []
        
        # 创建5个失败的登录尝试
        for i in range(5):
            log = Mock()
            log.action = "LOGIN_ATTEMPT"
            log.success = False
            log.ip_address = "*************"
            log.created_at = current_time - timedelta(minutes=i)
            mock_logs.append(log)
        
        # 调用检查方法
        result = self.anomaly_detector._check_failed_logins(rule, mock_logs)
        
        # 验证结果
        assert result is not None
        assert result["rule_id"] == rule.rule_id
        assert result["anomaly_type"] == rule.anomaly_type
        assert result["actual_value"] == 5.0
        assert result["ip_address"] == "*************"
        assert "evidence" in result
    
    def test_check_high_frequency_requests(self):
        """测试检查高频请求"""
        # 创建测试规则
        rule = AnomalyRule(
            rule_id="test_high_frequency",
            name="测试高频请求",
            description="测试描述",
            anomaly_type=AnomalyType.HIGH_FREQUENCY_REQUESTS,
            severity=SeverityLevel.HIGH,
            threshold=10.0,
            time_window_minutes=5
        )
        
        # 创建模拟审计日志
        current_time = datetime.utcnow()
        mock_logs = []
        
        # 创建15个来自同一IP的请求
        for i in range(15):
            log = Mock()
            log.ip_address = "*************"
            log.created_at = current_time - timedelta(seconds=i * 10)
            mock_logs.append(log)
        
        # 调用检查方法
        result = self.anomaly_detector._check_high_frequency_requests(rule, mock_logs)
        
        # 验证结果
        assert result is not None
        assert result["rule_id"] == rule.rule_id
        assert result["actual_value"] == 15.0
        assert result["ip_address"] == "*************"
    
    def test_create_security_alert(self):
        """测试创建安全告警"""
        # 创建异常数据
        anomaly = {
            "rule_id": "test_rule",
            "anomaly_type": AnomalyType.FAILED_LOGIN_ATTEMPTS,
            "severity": SeverityLevel.MEDIUM,
            "threshold": 5.0,
            "actual_value": 8.0,
            "description": "测试异常描述",
            "evidence": {"test": "data"},
            "ip_address": "*************"
        }
        
        # 模拟数据库操作
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        # 调用方法
        result = self.anomaly_detector.create_security_alert(anomaly, "test-user-id")
        
        # 验证结果
        assert result.anomaly_type == AnomalyType.FAILED_LOGIN_ATTEMPTS.value
        assert result.severity == SeverityLevel.MEDIUM.value
        assert result.rule_id == "test_rule"
        assert result.threshold_value == 5.0
        assert result.actual_value == 8.0
        assert result.ip_address == "*************"
        
        # 验证数据库操作
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
    
    def test_get_active_alerts(self):
        """测试获取活跃告警"""
        # 创建模拟告警
        mock_alerts = [
            Mock(severity=SeverityLevel.HIGH.value, status=AlertStatus.OPEN.value),
            Mock(severity=SeverityLevel.MEDIUM.value, status=AlertStatus.INVESTIGATING.value),
            Mock(severity=SeverityLevel.LOW.value, status=AlertStatus.RESOLVED.value)
        ]
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = mock_alerts[:2]  # 只返回活跃的告警
        
        self.mock_db.query.return_value = mock_query
        
        # 调用方法
        result = self.anomaly_detector.get_active_alerts()
        
        # 验证结果
        assert len(result) == 2
        assert all(alert.status in [AlertStatus.OPEN.value, AlertStatus.INVESTIGATING.value] 
                  for alert in result)


class TestAuditMiddleware:
    """测试审计中间件"""
    
    def setup_method(self):
        """设置测试环境"""
        self.app = Mock()
        self.middleware = AuditMiddleware(self.app)
    
    def test_should_exclude_path(self):
        """测试路径排除逻辑"""
        # 应该排除的路径
        assert self.middleware._should_exclude_path("/health") == True
        assert self.middleware._should_exclude_path("/metrics") == True
        assert self.middleware._should_exclude_path("/docs") == True
        assert self.middleware._should_exclude_path("/favicon.ico") == True
        
        # 不应该排除的路径
        assert self.middleware._should_exclude_path("/api/v1/users") == False
        assert self.middleware._should_exclude_path("/api/v1/audit/logs") == False
    
    def test_get_client_ip(self):
        """测试获取客户端IP"""
        # 创建模拟请求
        mock_request = Mock()
        mock_request.headers = {
            "X-Forwarded-For": "*************, ********",
            "X-Real-IP": "*************"
        }
        mock_request.client.host = "127.0.0.1"
        
        # 测试X-Forwarded-For头
        ip = self.middleware._get_client_ip(mock_request)
        assert ip == "*************"
        
        # 测试X-Real-IP头
        mock_request.headers = {"X-Real-IP": "*************"}
        ip = self.middleware._get_client_ip(mock_request)
        assert ip == "*************"
        
        # 测试直接连接
        mock_request.headers = {}
        ip = self.middleware._get_client_ip(mock_request)
        assert ip == "127.0.0.1"
    
    def test_sanitize_data(self):
        """测试数据清理"""
        # 测试字典数据
        data = {
            "username": "testuser",
            "password": "secret123",
            "token": "abc123",
            "email": "<EMAIL>"
        }
        
        sanitized = self.middleware._sanitize_dict(data)
        
        assert sanitized["username"] == "testuser"
        assert sanitized["password"] == "[REDACTED]"
        assert sanitized["token"] == "[REDACTED]"
        assert sanitized["email"] == "<EMAIL>"
    
    def test_sanitize_nested_data(self):
        """测试嵌套数据清理"""
        data = {
            "user": {
                "name": "testuser",
                "credentials": {
                    "password": "secret123",
                    "api_key": "key123"
                }
            },
            "settings": {
                "theme": "dark"
            }
        }
        
        sanitized = self.middleware._sanitize_dict(data)
        
        assert sanitized["user"]["name"] == "testuser"
        assert sanitized["user"]["credentials"]["password"] == "[REDACTED]"
        assert sanitized["user"]["credentials"]["api_key"] == "[REDACTED]"
        assert sanitized["settings"]["theme"] == "dark"
    
    @pytest.mark.asyncio
    async def test_extract_request_info(self):
        """测试提取请求信息"""
        # 创建模拟请求
        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.url.path = "/api/v1/users"
        mock_request.url = Mock()
        mock_request.url.__str__ = Mock(return_value="http://localhost/api/v1/users")
        mock_request.query_params = {"page": "1"}
        mock_request.headers = {
            "User-Agent": "Test Browser",
            "Content-Type": "application/json"
        }
        mock_request.client.host = "127.0.0.1"
        mock_request.body = AsyncMock(return_value=b'{"name": "test"}')
        
        # 模拟用户信息
        with patch.object(self.middleware, '_get_user_info', return_value={"user_id": "123"}):
            info = await self.middleware._extract_request_info(mock_request, "req-123")
        
        # 验证结果
        assert info["request_id"] == "req-123"
        assert info["method"] == "POST"
        assert info["path"] == "/api/v1/users"
        assert info["client_ip"] == "127.0.0.1"
        assert info["user_agent"] == "Test Browser"
        assert info["user_id"] == "123"


class TestIntegration:
    """集成测试"""
    
    def test_get_anomaly_detector(self):
        """测试获取异常检测器"""
        mock_db = Mock(spec=Session)
        detector = get_anomaly_detector(mock_db)
        
        assert isinstance(detector, AnomalyDetector)
        assert detector.db_session == mock_db
    
    def test_anomaly_rule_creation(self):
        """测试异常规则创建"""
        rule = AnomalyRule(
            rule_id="test_rule",
            name="测试规则",
            description="测试描述",
            anomaly_type=AnomalyType.SUSPICIOUS_LOGIN,
            severity=SeverityLevel.HIGH,
            threshold=10.0,
            time_window_minutes=30
        )
        
        assert rule.rule_id == "test_rule"
        assert rule.anomaly_type == AnomalyType.SUSPICIOUS_LOGIN
        assert rule.severity == SeverityLevel.HIGH
        assert rule.threshold == 10.0
        assert rule.enabled == True
    
    def test_security_alert_model(self):
        """测试安全告警模型"""
        current_time = datetime.utcnow()
        
        alert = SecurityAlert(
            alert_title="测试告警",
            anomaly_type=AnomalyType.FAILED_LOGIN_ATTEMPTS.value,
            severity=SeverityLevel.MEDIUM.value,
            description="测试描述",
            rule_id="test_rule",
            first_detected_at=current_time,
            last_detected_at=current_time
        )
        
        assert alert.alert_title == "测试告警"
        assert alert.anomaly_type == AnomalyType.FAILED_LOGIN_ATTEMPTS.value
        assert alert.severity == SeverityLevel.MEDIUM.value
        assert alert.status == AlertStatus.OPEN.value
        assert alert.occurrence_count == 1


if __name__ == "__main__":
    pytest.main([__file__])
