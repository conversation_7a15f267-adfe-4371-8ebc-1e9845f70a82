"""
数据安全与合规系统集成测试
验证整个安全合规系统的集成功能
"""

import pytest
import uuid
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

# 导入所有相关模块
from app.core.encryption import EncryptionManager, DataMasking, get_encryption_manager
from app.core.encrypted_types import EncryptedString, EncryptedEmail, EncryptedPhone
from app.core.audit import Audit<PERSON>ogger, AuditEventType, AuditLevel
from app.core.anomaly_detection import AnomalyDetector, AnomalyType, SeverityLevel
from app.core.privacy import PrivacyManager, ConsentType, DataRetentionType
from app.core.compliance import ComplianceManager, DataSourceType, LicenseType
from app.core.disclaimer import DisclaimerManager, DisclaimerType, AgreementType
from app.core.security import SecurityHeadersMiddleware, RateLimitMiddleware, SecurityConfig


class TestSecurityComplianceIntegration:
    """数据安全与合规系统集成测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.mock_db = Mock(spec=Session)
        
        # 设置测试加密密钥
        os.environ["ENCRYPTION_MASTER_KEY"] = "test-master-key-for-integration-testing"
        
        # 初始化各个管理器
        self.encryption_manager = EncryptionManager()
        self.audit_logger = AuditLogger(self.mock_db)
        self.anomaly_detector = AnomalyDetector(self.mock_db)
        self.privacy_manager = PrivacyManager(self.mock_db)
        self.compliance_manager = ComplianceManager(self.mock_db)
        self.disclaimer_manager = DisclaimerManager(self.mock_db)
    
    def test_end_to_end_data_protection_workflow(self):
        """测试端到端数据保护工作流"""
        # 1. 数据加密
        sensitive_data = {
            "email": "<EMAIL>",
            "phone": "13812345678",
            "name": "张三",
            "id_card": "110101199001011234"
        }
        
        # 加密敏感数据
        encrypted_data = {}
        for key, value in sensitive_data.items():
            encrypted_data[key] = self.encryption_manager.encrypt_field(value)
        
        # 验证数据已加密
        for key, encrypted_value in encrypted_data.items():
            assert encrypted_value != sensitive_data[key]
            # 验证可以解密
            decrypted_value = self.encryption_manager.decrypt_field(encrypted_value)
            assert decrypted_value == sensitive_data[key]
        
        # 2. 数据脱敏
        masked_data = {}
        for key, value in sensitive_data.items():
            if key == "email":
                masked_data[key] = DataMasking.mask_email(value)
            elif key == "phone":
                masked_data[key] = DataMasking.mask_phone(value)
            elif key == "name":
                masked_data[key] = DataMasking.mask_name(value)
            elif key == "id_card":
                masked_data[key] = DataMasking.mask_id_card(value)
        
        # 验证数据已脱敏
        assert masked_data["email"] != sensitive_data["email"]
        assert masked_data["phone"] != sensitive_data["phone"]
        assert masked_data["name"] != sensitive_data["name"]
        assert masked_data["id_card"] != sensitive_data["id_card"]
        
        # 3. 审计日志记录
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        audit_log = self.audit_logger.log_action(
            action="DATA_ENCRYPTION",
            user_id="test-user-id",
            resource_type="USER_DATA",
            resource_id="test-resource-id",
            details={"fields_encrypted": list(sensitive_data.keys())},
            success=True
        )
        
        # 验证审计日志
        assert audit_log.action == "DATA_ENCRYPTION"
        assert audit_log.success == True
        self.mock_db.add.assert_called()
        self.mock_db.commit.assert_called()
    
    def test_privacy_compliance_workflow(self):
        """测试隐私合规工作流"""
        user_id = str(uuid.uuid4())
        
        # 1. 记录用户同意
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        consent = self.privacy_manager.record_consent(
            user_id=user_id,
            consent_type=ConsentType.PRIVACY_POLICY,
            consented=True,
            ip_address="*************",
            user_agent="Test Browser"
        )
        
        # 验证同意记录
        assert consent.user_id == user_id
        assert consent.consent_type == ConsentType.PRIVACY_POLICY.value
        assert consent.consented == True
        
        # 2. 检查同意状态
        mock_consent = Mock()
        mock_consent.consented = True
        mock_consent.consent_version = "1.0"
        mock_consent.expires_at = None
        
        self.mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = mock_consent
        
        needs_consent = self.privacy_manager.check_consent_required(
            user_id, ConsentType.PRIVACY_POLICY, "1.0"
        )
        assert needs_consent == False
        
        # 3. 数据匿名化
        fields_to_anonymize = ["email", "phone", "name"]
        anonymized_data = self.privacy_manager.anonymize_user_data(user_id, fields_to_anonymize)
        
        # 验证匿名化结果
        assert "email" in anonymized_data
        assert "phone" in anonymized_data
        assert "name" in anonymized_data
        assert "@example.com" in anonymized_data["email"]
    
    def test_security_monitoring_workflow(self):
        """测试安全监控工作流"""
        # 1. 创建模拟审计日志（失败登录）
        current_time = datetime.utcnow()
        mock_logs = []
        
        for i in range(6):  # 创建6次失败登录
            log = Mock()
            log.action = "LOGIN_ATTEMPT"
            log.success = False
            log.ip_address = "*************"
            log.created_at = current_time - timedelta(minutes=i)
            mock_logs.append(log)
        
        # 2. 异常检测
        with patch.object(self.anomaly_detector, '_check_rule') as mock_check:
            mock_check.return_value = {
                "rule_id": "failed_login_threshold",
                "anomaly_type": AnomalyType.FAILED_LOGIN_ATTEMPTS,
                "severity": SeverityLevel.MEDIUM,
                "threshold": 5.0,
                "actual_value": 6.0,
                "description": "IP ************* 在 15 分钟内登录失败 6 次",
                "evidence": {
                    "failed_login_count": 6,
                    "suspect_ip": "*************"
                },
                "ip_address": "*************"
            }
            
            anomalies = self.anomaly_detector.analyze_user_behavior(
                ip_address="*************",
                time_window_hours=1
            )
            
            assert len(anomalies) > 0
            anomaly = anomalies[0]
            assert anomaly["anomaly_type"] == AnomalyType.FAILED_LOGIN_ATTEMPTS
            assert anomaly["actual_value"] == 6.0
        
        # 3. 创建安全告警
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        alert = self.anomaly_detector.create_security_alert(
            anomaly=anomaly,
            user_id="test-user-id"
        )
        
        # 验证告警
        assert alert.anomaly_type == AnomalyType.FAILED_LOGIN_ATTEMPTS.value
        assert alert.severity == SeverityLevel.MEDIUM.value
        assert alert.ip_address == "*************"
    
    def test_compliance_management_workflow(self):
        """测试合规管理工作流"""
        # 1. 注册数据源
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        data_source = self.compliance_manager.register_data_source(
            source_name="测试法律数据库",
            source_type=DataSourceType.LEGAL_DATABASE,
            license_type=LicenseType.COMMERCIAL_LICENSE,
            commercial_use_allowed=True,
            provider="测试提供商"
        )
        
        # 验证数据源
        assert data_source.source_name == "测试法律数据库"
        assert data_source.source_type == DataSourceType.LEGAL_DATABASE.value
        assert data_source.commercial_use_allowed == True
        
        # 2. 合规检查
        mock_data_source = Mock()
        mock_data_source.commercial_use_allowed = True
        mock_data_source.compliance_status = "compliant"
        mock_data_source.next_review_date = datetime.utcnow() + timedelta(days=30)
        mock_data_source.source_name = "测试数据源"
        mock_data_source.source_type = DataSourceType.LEGAL_DATABASE.value
        mock_data_source.license_type = LicenseType.COMMERCIAL_LICENSE.value
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_data_source
        
        with patch.object(self.compliance_manager, '_get_active_agreements', return_value=[]):
            compliance_result = self.compliance_manager.check_data_source_compliance(
                "test-source-id", "commercial"
            )
        
        # 验证合规检查结果
        assert compliance_result["compliant"] == True
        assert len(compliance_result["issues"]) == 0
    
    def test_disclaimer_system_workflow(self):
        """测试免责声明系统工作流"""
        # 1. 创建免责声明模板
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        disclaimer = self.disclaimer_manager.create_disclaimer_template(
            template_name="测试AI免责声明",
            disclaimer_type=DisclaimerType.AI_RESPONSE,
            title="AI回答免责声明",
            content="AI回答仅供参考，不构成法律建议"
        )
        
        # 验证免责声明
        assert disclaimer.template_name == "测试AI免责声明"
        assert disclaimer.disclaimer_type == DisclaimerType.AI_RESPONSE.value
        
        # 2. 获取免责声明
        mock_disclaimers = [disclaimer]
        
        with patch.object(self.disclaimer_manager, 'get_active_disclaimers') as mock_get:
            mock_get.return_value = mock_disclaimers
            
            ai_disclaimer = self.disclaimer_manager.get_ai_response_disclaimer()
            assert ai_disclaimer == disclaimer.content
        
        # 3. 获取法律风险提示
        legal_warning = self.disclaimer_manager.get_legal_risk_warning()
        assert "重要提示" in legal_warning or disclaimer.content == legal_warning
    
    def test_encrypted_field_types_integration(self):
        """测试加密字段类型集成"""
        # 测试不同类型的加密字段
        email_field = EncryptedEmail()
        phone_field = EncryptedPhone()
        
        # 模拟数据库方言
        dialect = None
        
        # 测试邮箱加密
        original_email = "<EMAIL>"
        encrypted_email = email_field.process_bind_param(original_email, dialect)
        decrypted_email = email_field.process_result_value(encrypted_email, dialect)
        
        assert encrypted_email != original_email
        assert decrypted_email == original_email
        
        # 测试电话加密
        original_phone = "13812345678"
        encrypted_phone = phone_field.process_bind_param(original_phone, dialect)
        decrypted_phone = phone_field.process_result_value(encrypted_phone, dialect)
        
        assert encrypted_phone != original_phone
        assert decrypted_phone == original_phone
    
    def test_security_middleware_integration(self):
        """测试安全中间件集成"""
        # 测试安全头中间件
        app = Mock()
        security_middleware = SecurityHeadersMiddleware(app)
        
        # 验证配置
        assert 'content_security_policy' in security_middleware.config
        assert 'strict_transport_security' in security_middleware.config
        
        # 测试速率限制中间件
        rate_limit_middleware = RateLimitMiddleware(app, max_requests=10, window_seconds=60)
        
        # 验证配置
        assert rate_limit_middleware.max_requests == 10
        assert rate_limit_middleware.window_seconds == 60
        
        # 测试安全配置
        security_config = SecurityConfig()
        
        # 验证默认配置
        assert isinstance(security_config.trusted_hosts, list)
        assert isinstance(security_config.cors_origins, list)
    
    def test_comprehensive_security_scenario(self):
        """测试综合安全场景"""
        # 模拟一个完整的安全场景：
        # 1. 用户注册 -> 2. 数据加密存储 -> 3. 审计日志 -> 4. 隐私同意 -> 5. 免责声明
        
        user_id = str(uuid.uuid4())
        
        # 1. 用户敏感数据加密
        user_data = {
            "email": "<EMAIL>",
            "phone": "13987654321",
            "name": "李四"
        }
        
        encrypted_user_data = {}
        for key, value in user_data.items():
            encrypted_user_data[key] = self.encryption_manager.encrypt_field(value)
        
        # 2. 记录审计日志
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        audit_log = self.audit_logger.log_action(
            action="USER_REGISTER",
            user_id=user_id,
            resource_type="USER",
            resource_id=user_id,
            details={"registration_method": "email"},
            success=True
        )
        
        # 3. 记录隐私同意
        consent = self.privacy_manager.record_consent(
            user_id=user_id,
            consent_type=ConsentType.PRIVACY_POLICY,
            consented=True
        )
        
        # 4. 创建免责声明
        disclaimer = self.disclaimer_manager.create_disclaimer_template(
            template_name="用户注册免责声明",
            disclaimer_type=DisclaimerType.AI_RESPONSE,
            title="服务使用免责声明",
            content="使用本服务即表示您同意相关条款"
        )
        
        # 验证整个流程
        assert all(encrypted_user_data[key] != user_data[key] for key in user_data.keys())
        assert audit_log.action == "USER_REGISTER"
        assert consent.consented == True
        assert disclaimer.template_name == "用户注册免责声明"
        
        # 验证数据库操作次数（每个组件都应该调用了add和commit）
        assert self.mock_db.add.call_count >= 3
        assert self.mock_db.commit.call_count >= 3


if __name__ == "__main__":
    pytest.main([__file__])
