"""
AI法律助手性能测试
使用locust进行负载测试和性能基准测试
"""

import json
import random
import uuid
from datetime import datetime
from locust import HttpUser, task, between, events
from locust.runners import MasterRunner


class AILegalAssistantUser(HttpUser):
    """AI法律助手用户行为模拟"""
    
    wait_time = between(1, 3)  # 用户操作间隔1-3秒
    
    def on_start(self):
        """用户开始测试时的初始化"""
        self.user_data = {
            "email": f"loadtest_{uuid.uuid4().hex[:8]}@example.com",
            "password": "LoadTest123!",
            "full_name": f"负载测试用户_{random.randint(1000, 9999)}",
            "phone": f"138{random.randint(10000000, 99999999)}"
        }
        
        self.access_token = None
        self.conversation_id = None
        self.document_id = None
        
        # 注册和登录
        self.register_and_login()
    
    def register_and_login(self):
        """用户注册和登录"""
        # 注册用户
        with self.client.post("/api/v1/auth/register", 
                             json=self.user_data, 
                             catch_response=True) as response:
            if response.status_code == 201:
                response.success()
            elif response.status_code == 400 and "already exists" in response.text:
                # 用户已存在，直接登录
                response.success()
            else:
                response.failure(f"注册失败: {response.status_code}")
        
        # 用户登录
        with self.client.post("/api/v1/auth/login", 
                             json={
                                 "email": self.user_data["email"],
                                 "password": self.user_data["password"]
                             },
                             catch_response=True) as response:
            if response.status_code == 200:
                self.access_token = response.json()["access_token"]
                response.success()
            else:
                response.failure(f"登录失败: {response.status_code}")
    
    def get_headers(self):
        """获取认证头"""
        if self.access_token:
            return {"Authorization": f"Bearer {self.access_token}"}
        return {}
    
    @task(3)
    def view_profile(self):
        """查看用户资料 - 高频操作"""
        with self.client.get("/api/v1/users/me", 
                           headers=self.get_headers(),
                           name="查看用户资料",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"查看资料失败: {response.status_code}")
    
    @task(5)
    def ai_chat(self):
        """AI聊天咨询 - 核心功能"""
        questions = [
            "什么是劳动合同？",
            "试用期的法律规定是什么？",
            "如何解除劳动合同？",
            "加班费如何计算？",
            "年假的规定是什么？",
            "工伤赔偿标准是什么？",
            "竞业限制协议有效吗？",
            "公司可以随意调岗吗？"
        ]
        
        message = {
            "message": random.choice(questions),
            "context": "法律咨询"
        }
        
        with self.client.post("/api/v1/ai/chat",
                            json=message,
                            headers=self.get_headers(),
                            name="AI聊天咨询",
                            catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if "conversation_id" in data:
                    self.conversation_id = data["conversation_id"]
                response.success()
            else:
                response.failure(f"AI咨询失败: {response.status_code}")
    
    @task(2)
    def get_chat_history(self):
        """获取聊天历史"""
        if not self.conversation_id:
            return
        
        with self.client.get(f"/api/v1/ai/chat/history?conversation_id={self.conversation_id}",
                           headers=self.get_headers(),
                           name="获取聊天历史",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取历史失败: {response.status_code}")
    
    @task(2)
    def upload_document(self):
        """上传文档分析"""
        # 模拟合同内容
        contract_content = f"""
        劳动合同
        
        甲方：测试公司{random.randint(1, 1000)}
        乙方：{self.user_data['full_name']}
        
        根据《劳动合同法》等相关法律法规，甲乙双方经协商一致，签订本合同：
        
        第一条 工作内容
        乙方同意担任软件工程师职务。
        
        第二条 合同期限
        本合同期限为3年，自2024年1月1日起至2026年12月31日止。
        
        第三条 试用期
        试用期为{random.choice([1, 2, 3, 6])}个月。
        
        第四条 工作时间
        实行标准工时制，每日工作8小时，每周工作40小时。
        
        第五条 劳动报酬
        月工资为{random.randint(8000, 20000)}元。
        """
        
        files = {
            "file": (f"contract_{uuid.uuid4().hex[:8]}.txt", contract_content, "text/plain")
        }
        data = {"document_type": "劳动合同"}
        
        with self.client.post("/api/v1/documents/analyze",
                            files=files,
                            data=data,
                            headers=self.get_headers(),
                            name="上传文档分析",
                            catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if "analysis_id" in data:
                    self.document_id = data["analysis_id"]
                response.success()
            else:
                response.failure(f"文档上传失败: {response.status_code}")
    
    @task(1)
    def get_document_analysis(self):
        """获取文档分析结果"""
        if not self.document_id:
            return
        
        with self.client.get(f"/api/v1/documents/analysis/{self.document_id}",
                           headers=self.get_headers(),
                           name="获取分析结果",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 202:
                # 分析中，也算成功
                response.success()
            else:
                response.failure(f"获取分析失败: {response.status_code}")
    
    @task(1)
    def search_cases(self):
        """搜索法律案例"""
        keywords = [
            "劳动纠纷", "合同违约", "工伤赔偿", "加班费",
            "竞业限制", "解除合同", "试用期", "年假"
        ]
        
        keyword = random.choice(keywords)
        
        with self.client.get(f"/api/v1/cases/search?q={keyword}&limit=10",
                           headers=self.get_headers(),
                           name="搜索法律案例",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"案例搜索失败: {response.status_code}")
    
    @task(1)
    def update_profile(self):
        """更新用户资料"""
        update_data = {
            "full_name": f"更新用户_{random.randint(1000, 9999)}",
            "phone": f"139{random.randint(10000000, 99999999)}"
        }
        
        with self.client.put("/api/v1/users/me",
                           json=update_data,
                           headers=self.get_headers(),
                           name="更新用户资料",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"更新资料失败: {response.status_code}")
    
    @task(1)
    def privacy_dashboard(self):
        """查看隐私仪表板"""
        with self.client.get("/api/v1/privacy/privacy-dashboard",
                           headers=self.get_headers(),
                           name="隐私仪表板",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"隐私仪表板失败: {response.status_code}")


class AdminUser(HttpUser):
    """管理员用户行为模拟"""
    
    wait_time = between(2, 5)  # 管理员操作间隔较长
    weight = 1  # 管理员用户权重较低
    
    def on_start(self):
        """管理员初始化"""
        self.admin_data = {
            "email": "<EMAIL>",
            "password": "AdminPassword123!"
        }
        self.access_token = None
        self.login()
    
    def login(self):
        """管理员登录"""
        with self.client.post("/api/v1/auth/login",
                             json=self.admin_data,
                             catch_response=True) as response:
            if response.status_code == 200:
                self.access_token = response.json()["access_token"]
                response.success()
            else:
                response.failure(f"管理员登录失败: {response.status_code}")
    
    def get_headers(self):
        """获取管理员认证头"""
        if self.access_token:
            return {"Authorization": f"Bearer {self.access_token}"}
        return {}
    
    @task(2)
    def view_audit_logs(self):
        """查看审计日志"""
        with self.client.get("/api/v1/audit/logs?limit=50",
                           headers=self.get_headers(),
                           name="查看审计日志",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"查看审计日志失败: {response.status_code}")
    
    @task(1)
    def view_security_alerts(self):
        """查看安全告警"""
        with self.client.get("/api/v1/audit/alerts",
                           headers=self.get_headers(),
                           name="查看安全告警",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"查看安全告警失败: {response.status_code}")
    
    @task(1)
    def system_monitoring(self):
        """系统监控"""
        with self.client.get("/api/v1/monitoring/system/status",
                           headers=self.get_headers(),
                           name="系统监控",
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"系统监控失败: {response.status_code}")


# 性能测试事件处理
@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """测试开始时的处理"""
    print("🚀 AI法律助手性能测试开始")
    print(f"测试环境: {environment.host}")
    print(f"用户数: {environment.runner.target_user_count if hasattr(environment.runner, 'target_user_count') else '未知'}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """测试结束时的处理"""
    print("✅ AI法律助手性能测试完成")
    
    # 输出性能统计
    stats = environment.runner.stats
    print("\n📊 性能测试结果:")
    print(f"总请求数: {stats.total.num_requests}")
    print(f"失败请求数: {stats.total.num_failures}")
    print(f"平均响应时间: {stats.total.avg_response_time:.2f}ms")
    print(f"最大响应时间: {stats.total.max_response_time:.2f}ms")
    print(f"RPS: {stats.total.current_rps:.2f}")
    
    # 输出各接口性能
    print("\n📈 各接口性能:")
    for name, entry in stats.entries.items():
        if entry.num_requests > 0:
            print(f"{name}: "
                  f"请求数={entry.num_requests}, "
                  f"失败数={entry.num_failures}, "
                  f"平均响应时间={entry.avg_response_time:.2f}ms, "
                  f"成功率={((entry.num_requests - entry.num_failures) / entry.num_requests * 100):.1f}%")


# 自定义性能测试场景
class QuickTest(HttpUser):
    """快速性能测试"""
    wait_time = between(0.5, 1)
    
    def on_start(self):
        self.client.verify = False  # 忽略SSL证书验证
    
    @task
    def health_check(self):
        """健康检查"""
        self.client.get("/health", name="健康检查")
    
    @task
    def metrics(self):
        """系统指标"""
        self.client.get("/metrics", name="系统指标")


if __name__ == "__main__":
    # 可以直接运行此文件进行简单测试
    import subprocess
    import sys
    
    print("启动性能测试...")
    print("使用命令: locust -f load_test.py --host=http://localhost:8000")
    
    # 或者使用编程方式启动
    try:
        subprocess.run([
            sys.executable, "-m", "locust",
            "-f", __file__,
            "--host", "http://localhost:8000",
            "--users", "10",
            "--spawn-rate", "2",
            "--run-time", "60s",
            "--headless"
        ])
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试执行出错: {e}")
