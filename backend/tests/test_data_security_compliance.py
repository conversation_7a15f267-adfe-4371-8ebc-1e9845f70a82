"""
数据安全与合规模块测试
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.core.disclaimer import (
    DisclaimerManager, DisclaimerType, AgreementType,
    get_disclaimer_manager
)
from app.core.compliance import (
    ComplianceManager, DataSourceType, LicenseType, ComplianceStatus,
    get_compliance_manager
)
from app.core.privacy import (
    PrivacyManager, ConsentType, DataRetentionType,
    get_privacy_manager
)


class TestDisclaimerManager:
    """测试免责声明管理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.mock_db = Mock(spec=Session)
        self.disclaimer_manager = DisclaimerManager(self.mock_db)
    
    def test_create_disclaimer_template(self):
        """测试创建免责声明模板"""
        template_name = "测试免责声明"
        disclaimer_type = DisclaimerType.AI_RESPONSE
        title = "AI回答免责声明"
        content = "这是一个测试免责声明内容"
        
        # 模拟数据库操作
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        # 调用方法
        result = self.disclaimer_manager.create_disclaimer_template(
            template_name=template_name,
            disclaimer_type=disclaimer_type,
            title=title,
            content=content
        )
        
        # 验证结果
        assert result.template_name == template_name
        assert result.disclaimer_type == disclaimer_type.value
        assert result.title == title
        assert result.content == content
        assert result.is_mandatory == True
        
        # 验证数据库操作
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
    
    def test_create_user_agreement(self):
        """测试创建用户协议"""
        agreement_name = "测试用户协议"
        agreement_type = AgreementType.TERMS_OF_SERVICE
        title = "用户服务协议"
        content = "这是一个测试用户协议内容"
        
        # 模拟数据库操作
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        # 调用方法
        result = self.disclaimer_manager.create_user_agreement(
            agreement_name=agreement_name,
            agreement_type=agreement_type,
            title=title,
            content=content
        )
        
        # 验证结果
        assert result.agreement_name == agreement_name
        assert result.agreement_type == agreement_type.value
        assert result.title == title
        assert result.content == content
        assert result.requires_acceptance == True
    
    def test_get_legal_risk_warning(self):
        """测试获取法律风险提示"""
        # 模拟没有找到免责声明的情况
        self.mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = []
        
        warning = self.disclaimer_manager.get_legal_risk_warning()
        
        # 验证返回默认警告
        assert "重要提示" in warning
        assert "不构成正式的法律建议" in warning
        assert "咨询专业律师" in warning


class TestComplianceManager:
    """测试合规管理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.mock_db = Mock(spec=Session)
        self.compliance_manager = ComplianceManager(self.mock_db)
    
    def test_register_data_source(self):
        """测试注册数据源"""
        source_name = "测试法律数据库"
        source_type = DataSourceType.LEGAL_DATABASE
        license_type = LicenseType.COMMERCIAL_LICENSE
        
        # 模拟数据库操作
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        # 调用方法
        result = self.compliance_manager.register_data_source(
            source_name=source_name,
            source_type=source_type,
            license_type=license_type,
            commercial_use_allowed=True,
            provider="测试提供商"
        )
        
        # 验证结果
        assert result.source_name == source_name
        assert result.source_type == source_type.value
        assert result.license_type == license_type.value
        assert result.commercial_use_allowed == True
        assert result.compliance_status == ComplianceStatus.COMPLIANT.value
        
        # 验证数据库操作
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
    
    def test_check_data_source_compliance_not_found(self):
        """测试检查不存在的数据源合规性"""
        # 模拟数据源不存在
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = self.compliance_manager.check_data_source_compliance("non-existent-id")
        
        # 验证结果
        assert result["compliant"] == False
        assert "数据源不存在" in result["reason"]
        assert "请先注册数据源" in result["recommendations"]
    
    def test_check_data_source_compliance_commercial_not_allowed(self):
        """测试检查不允许商业使用的数据源"""
        # 创建模拟数据源
        mock_data_source = Mock()
        mock_data_source.commercial_use_allowed = False
        mock_data_source.compliance_status = ComplianceStatus.COMPLIANT.value
        mock_data_source.next_review_date = datetime.utcnow() + timedelta(days=30)
        mock_data_source.source_name = "测试数据源"
        mock_data_source.source_type = DataSourceType.LEGAL_DATABASE.value
        mock_data_source.license_type = LicenseType.CREATIVE_COMMONS.value
        
        # 模拟数据库查询
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_data_source
        
        # 模拟没有协议
        with patch.object(self.compliance_manager, '_get_active_agreements', return_value=[]):
            result = self.compliance_manager.check_data_source_compliance(
                "test-id", intended_use="commercial"
            )
        
        # 验证结果
        assert result["compliant"] == False
        assert "不允许商业使用" in result["issues"]
        assert "获取商业使用许可或更换数据源" in result["recommendations"]
    
    def test_record_data_usage(self):
        """测试记录数据使用"""
        # 创建模拟数据源
        mock_data_source = Mock()
        mock_data_source.usage_count = 5
        mock_data_source.source_name = "测试数据源"
        
        # 模拟数据库查询
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_data_source
        self.mock_db.commit = Mock()
        
        # 调用方法
        result = self.compliance_manager.record_data_usage("test-id")
        
        # 验证结果
        assert result == True
        assert mock_data_source.usage_count == 6
        assert mock_data_source.last_used_date is not None
        
        # 验证数据库操作
        self.mock_db.commit.assert_called_once()


class TestPrivacyManager:
    """测试隐私管理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.mock_db = Mock(spec=Session)
        self.privacy_manager = PrivacyManager(self.mock_db)
    
    def test_record_consent(self):
        """测试记录用户同意"""
        user_id = str(uuid.uuid4())
        consent_type = ConsentType.PRIVACY_POLICY
        
        # 模拟数据库操作
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        
        # 调用方法
        result = self.privacy_manager.record_consent(
            user_id=user_id,
            consent_type=consent_type,
            consented=True,
            ip_address="***********",
            user_agent="Test Browser"
        )
        
        # 验证结果
        assert result.user_id == user_id
        assert result.consent_type == consent_type.value
        assert result.consented == True
        assert result.ip_address == "***********"
        
        # 验证数据库操作
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
    
    def test_check_consent_required_no_consent(self):
        """测试检查需要同意的情况（无同意记录）"""
        # 模拟没有找到同意记录
        self.mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = None
        
        result = self.privacy_manager.check_consent_required(
            "test-user-id", ConsentType.PRIVACY_POLICY
        )
        
        # 验证结果
        assert result == True  # 需要重新获取同意
    
    def test_check_consent_required_version_mismatch(self):
        """测试检查需要同意的情况（版本不匹配）"""
        # 创建模拟同意记录
        mock_consent = Mock()
        mock_consent.consented = True
        mock_consent.consent_version = "1.0"
        mock_consent.expires_at = None
        
        # 模拟数据库查询
        self.mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = mock_consent
        
        result = self.privacy_manager.check_consent_required(
            "test-user-id", ConsentType.PRIVACY_POLICY, current_version="2.0"
        )
        
        # 验证结果
        assert result == True  # 版本不匹配，需要重新获取同意
    
    def test_anonymize_user_data(self):
        """测试匿名化用户数据"""
        user_id = "test-user-id"
        fields_to_anonymize = ["email", "phone", "name"]
        
        result = self.privacy_manager.anonymize_user_data(user_id, fields_to_anonymize)
        
        # 验证结果
        assert "email" in result
        assert "phone" in result
        assert "name" in result
        assert "@example.com" in result["email"]
        assert "***-****-" in result["phone"]
        assert "匿名用户_" in result["name"]
    
    def test_export_user_data(self):
        """测试导出用户数据"""
        user_id = "test-user-id"
        
        # 模拟数据库查询（用户同意记录）
        mock_consents = [
            Mock(
                consent_type=ConsentType.PRIVACY_POLICY.value,
                consent_version="1.0",
                consented=True,
                created_at=datetime.utcnow()
            )
        ]
        self.mock_db.query.return_value.filter.return_value.all.return_value = mock_consents
        
        result = self.privacy_manager.export_user_data(user_id)
        
        # 验证结果
        assert result["user_id"] == user_id
        assert "export_date" in result
        assert "data" in result
        assert "consents" in result["data"]
        assert len(result["data"]["consents"]) == 1
        assert result["data"]["consents"][0]["type"] == ConsentType.PRIVACY_POLICY.value
    
    def test_delete_user_data_soft_delete(self):
        """测试软删除用户数据"""
        user_id = "test-user-id"
        
        # 模拟数据库操作
        self.mock_db.commit = Mock()
        
        # 模拟匿名化方法
        with patch.object(self.privacy_manager, 'anonymize_user_data') as mock_anonymize:
            mock_anonymize.return_value = {
                "email": "<EMAIL>",
                "phone": "***-****-1234",
                "name": "匿名用户_12345678"
            }
            
            result = self.privacy_manager.delete_user_data(user_id, hard_delete=False)
        
        # 验证结果
        assert result == True
        mock_anonymize.assert_called_once_with(
            user_id, ['email', 'phone', 'name', 'address']
        )
        self.mock_db.commit.assert_called_once()


class TestIntegration:
    """集成测试"""
    
    def test_get_manager_functions(self):
        """测试获取管理器函数"""
        mock_db = Mock(spec=Session)
        
        # 测试获取免责声明管理器
        disclaimer_manager = get_disclaimer_manager(mock_db)
        assert isinstance(disclaimer_manager, DisclaimerManager)
        assert disclaimer_manager.db_session == mock_db
        
        # 测试获取合规管理器
        compliance_manager = get_compliance_manager(mock_db)
        assert isinstance(compliance_manager, ComplianceManager)
        assert compliance_manager.db_session == mock_db
        
        # 测试获取隐私管理器
        privacy_manager = get_privacy_manager(mock_db)
        assert isinstance(privacy_manager, PrivacyManager)
        assert privacy_manager.db_session == mock_db
    
    def test_enum_values(self):
        """测试枚举值"""
        # 测试免责声明类型
        assert DisclaimerType.AI_RESPONSE == "ai_response"
        assert DisclaimerType.LEGAL_ADVICE == "legal_advice"
        
        # 测试协议类型
        assert AgreementType.TERMS_OF_SERVICE == "terms_of_service"
        assert AgreementType.PRIVACY_POLICY == "privacy_policy"
        
        # 测试数据源类型
        assert DataSourceType.GOVERNMENT == "government"
        assert DataSourceType.LEGAL_DATABASE == "legal_database"
        
        # 测试许可证类型
        assert LicenseType.PUBLIC_DOMAIN == "public_domain"
        assert LicenseType.COMMERCIAL_LICENSE == "commercial_license"
        
        # 测试合规状态
        assert ComplianceStatus.COMPLIANT == "compliant"
        assert ComplianceStatus.NON_COMPLIANT == "non_compliant"
        
        # 测试同意类型
        assert ConsentType.PRIVACY_POLICY == "privacy_policy"
        assert ConsentType.DATA_PROCESSING == "data_processing"
        
        # 测试数据保留类型
        assert DataRetentionType.USER_DATA == "user_data"
        assert DataRetentionType.AUDIT_LOGS == "audit_logs"


if __name__ == "__main__":
    pytest.main([__file__])
