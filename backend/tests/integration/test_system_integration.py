"""
系统集成测试
验证整个AI法律助手系统的端到端功能
"""

import pytest
import asyncio
import uuid
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List
from httpx import AsyncClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db
from app.core.auth import create_access_token
from app.models.user import User


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.fixture(scope="class")
    async def async_client(self):
        """异步HTTP客户端"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture(scope="class")
    def test_user_data(self):
        """测试用户数据"""
        return {
            "email": f"test_{uuid.uuid4().hex[:8]}@example.com",
            "password": "TestPassword123!",
            "full_name": "测试用户",
            "phone": "13812345678"
        }
    
    @pytest.fixture(scope="class")
    def admin_user_data(self):
        """管理员用户数据"""
        return {
            "email": f"admin_{uuid.uuid4().hex[:8]}@example.com",
            "password": "AdminPassword123!",
            "full_name": "管理员用户",
            "user_type": "admin"
        }
    
    async def test_01_user_registration_flow(self, async_client: AsyncClient, test_user_data: Dict):
        """测试用户注册流程"""
        # 1. 用户注册
        response = await async_client.post("/api/v1/auth/register", json=test_user_data)
        assert response.status_code == 201
        
        registration_data = response.json()
        assert registration_data["email"] == test_user_data["email"]
        assert registration_data["full_name"] == test_user_data["full_name"]
        assert "id" in registration_data
        
        # 存储用户ID供后续测试使用
        test_user_data["user_id"] = registration_data["id"]
        
        # 2. 验证用户登录
        login_response = await async_client.post("/api/v1/auth/login", json={
            "email": test_user_data["email"],
            "password": test_user_data["password"]
        })
        assert login_response.status_code == 200
        
        login_data = login_response.json()
        assert "access_token" in login_data
        assert "token_type" in login_data
        
        # 存储访问令牌
        test_user_data["access_token"] = login_data["access_token"]
    
    async def test_02_user_profile_management(self, async_client: AsyncClient, test_user_data: Dict):
        """测试用户资料管理"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 1. 获取用户资料
        response = await async_client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 200
        
        profile_data = response.json()
        assert profile_data["email"] == test_user_data["email"]
        
        # 2. 更新用户资料
        update_data = {
            "full_name": "更新后的测试用户",
            "phone": "13987654321"
        }
        
        response = await async_client.put("/api/v1/users/me", json=update_data, headers=headers)
        assert response.status_code == 200
        
        updated_data = response.json()
        assert updated_data["full_name"] == update_data["full_name"]
        assert updated_data["phone"] == update_data["phone"]
    
    async def test_03_ai_chat_functionality(self, async_client: AsyncClient, test_user_data: Dict):
        """测试AI聊天功能"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 1. 发送AI咨询消息
        chat_message = {
            "message": "请问劳动合同的试用期规定是什么？",
            "context": "劳动法咨询"
        }
        
        response = await async_client.post("/api/v1/ai/chat", json=chat_message, headers=headers)
        assert response.status_code == 200
        
        chat_response = response.json()
        assert "response" in chat_response
        assert "conversation_id" in chat_response
        assert len(chat_response["response"]) > 0
        
        # 存储对话ID
        conversation_id = chat_response["conversation_id"]
        
        # 2. 获取对话历史
        response = await async_client.get(
            f"/api/v1/ai/chat/history?conversation_id={conversation_id}",
            headers=headers
        )
        assert response.status_code == 200
        
        history_data = response.json()
        assert len(history_data["messages"]) >= 2  # 用户消息 + AI回复
    
    async def test_04_document_analysis(self, async_client: AsyncClient, test_user_data: Dict):
        """测试文档分析功能"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 1. 创建测试文档内容
        test_document_content = """
        劳动合同
        
        甲方：某某公司
        乙方：张三
        
        根据《劳动合同法》等相关法律法规，甲乙双方经协商一致，签订本合同：
        
        第一条 工作内容和工作地点
        乙方同意根据甲方工作需要，担任软件工程师职务。
        
        第二条 合同期限
        本合同为固定期限劳动合同，期限为3年，自2024年1月1日起至2026年12月31日止。
        
        第三条 试用期
        试用期为6个月，自2024年1月1日起至2024年6月30日止。
        """
        
        # 2. 上传文档进行分析
        files = {"file": ("test_contract.txt", test_document_content, "text/plain")}
        data = {"document_type": "劳动合同"}
        
        response = await async_client.post(
            "/api/v1/documents/analyze",
            files=files,
            data=data,
            headers=headers
        )
        assert response.status_code == 200
        
        analysis_result = response.json()
        assert "analysis_id" in analysis_result
        assert "status" in analysis_result
        
        # 3. 获取分析结果
        analysis_id = analysis_result["analysis_id"]
        
        # 等待分析完成（模拟异步处理）
        await asyncio.sleep(2)
        
        response = await async_client.get(
            f"/api/v1/documents/analysis/{analysis_id}",
            headers=headers
        )
        assert response.status_code == 200
        
        result_data = response.json()
        assert "analysis_result" in result_data
        assert "risk_assessment" in result_data
    
    async def test_05_privacy_data_management(self, async_client: AsyncClient, test_user_data: Dict):
        """测试隐私数据管理功能"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 1. 记录用户同意
        consent_data = {
            "consent_type": "privacy_policy",
            "consented": True,
            "consent_version": "1.0"
        }
        
        response = await async_client.post("/api/v1/privacy/consent", json=consent_data, headers=headers)
        assert response.status_code == 200
        
        consent_response = response.json()
        assert consent_response["consented"] == True
        
        # 2. 获取用户同意记录
        response = await async_client.get("/api/v1/privacy/consent", headers=headers)
        assert response.status_code == 200
        
        consents = response.json()
        assert len(consents) > 0
        
        # 3. 导出用户数据
        export_request = {
            "include_personal_data": True,
            "include_activity_logs": True,
            "format": "json"
        }
        
        response = await async_client.post("/api/v1/privacy/export-data", json=export_request, headers=headers)
        assert response.status_code == 200
        
        export_data = response.json()
        assert "data" in export_data
        assert "export_id" in export_data
        
        # 4. 获取隐私仪表板
        response = await async_client.get("/api/v1/privacy/privacy-dashboard", headers=headers)
        assert response.status_code == 200
        
        dashboard_data = response.json()
        assert "consents" in dashboard_data
        assert "privacy_rights" in dashboard_data
    
    async def test_06_system_monitoring(self, async_client: AsyncClient):
        """测试系统监控功能"""
        # 1. 健康检查
        response = await async_client.get("/health")
        assert response.status_code == 200
        
        health_data = response.json()
        assert health_data["status"] in ["healthy", "degraded"]
        assert "services" in health_data
        assert "performance" in health_data
        
        # 2. 系统指标（需要管理员权限，这里测试公开指标）
        response = await async_client.get("/metrics")
        assert response.status_code == 200
    
    async def test_07_audit_logging(self, async_client: AsyncClient, test_user_data: Dict):
        """测试审计日志功能"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 执行一些操作以生成审计日志
        await async_client.get("/api/v1/users/me", headers=headers)
        await async_client.post("/api/v1/ai/chat", json={
            "message": "测试消息",
            "context": "测试"
        }, headers=headers)
        
        # 等待审计日志记录
        await asyncio.sleep(1)
        
        # 验证审计日志已记录（这里需要管理员权限，实际测试中可能需要模拟）
        # 在实际环境中，可以通过数据库直接查询验证
    
    async def test_08_security_features(self, async_client: AsyncClient, test_user_data: Dict):
        """测试安全功能"""
        # 1. 测试无效令牌访问
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        
        response = await async_client.get("/api/v1/users/me", headers=invalid_headers)
        assert response.status_code == 401
        
        # 2. 测试速率限制（需要发送大量请求）
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 发送多个请求测试速率限制
        responses = []
        for _ in range(10):
            response = await async_client.get("/api/v1/users/me", headers=headers)
            responses.append(response.status_code)
        
        # 大部分请求应该成功，但可能有一些被限制
        success_count = sum(1 for status in responses if status == 200)
        assert success_count >= 5  # 至少一半的请求成功
        
        # 3. 测试数据脱敏
        response = await async_client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 200
        
        user_data = response.json()
        # 验证敏感数据已脱敏（如果有的话）
        if "phone" in user_data and user_data["phone"]:
            # 电话号码应该被脱敏
            assert "*" in user_data["phone"] or len(user_data["phone"]) < 11
    
    async def test_09_error_handling(self, async_client: AsyncClient, test_user_data: Dict):
        """测试错误处理"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 1. 测试不存在的端点
        response = await async_client.get("/api/v1/nonexistent", headers=headers)
        assert response.status_code == 404
        
        # 2. 测试无效的请求数据
        response = await async_client.post("/api/v1/ai/chat", json={
            "invalid_field": "invalid_value"
        }, headers=headers)
        assert response.status_code == 422  # 验证错误
        
        # 3. 测试权限不足
        response = await async_client.get("/api/v1/admin/users", headers=headers)
        assert response.status_code == 403  # 权限不足
    
    async def test_10_performance_benchmarks(self, async_client: AsyncClient, test_user_data: Dict):
        """测试性能基准"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 1. 测试API响应时间
        start_time = datetime.utcnow()
        
        response = await async_client.get("/api/v1/users/me", headers=headers)
        
        end_time = datetime.utcnow()
        response_time = (end_time - start_time).total_seconds()
        
        assert response.status_code == 200
        assert response_time < 1.0  # 响应时间应小于1秒
        
        # 2. 测试并发请求处理
        async def make_request():
            return await async_client.get("/api/v1/users/me", headers=headers)
        
        # 并发发送10个请求
        tasks = [make_request() for _ in range(10)]
        responses = await asyncio.gather(*tasks)
        
        # 所有请求都应该成功
        success_count = sum(1 for response in responses if response.status_code == 200)
        assert success_count == 10
    
    async def test_11_data_consistency(self, async_client: AsyncClient, test_user_data: Dict):
        """测试数据一致性"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 1. 更新用户数据
        update_data = {
            "full_name": f"一致性测试用户_{datetime.utcnow().strftime('%H%M%S')}"
        }
        
        response = await async_client.put("/api/v1/users/me", json=update_data, headers=headers)
        assert response.status_code == 200
        
        # 2. 立即读取数据验证一致性
        response = await async_client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 200
        
        user_data = response.json()
        assert user_data["full_name"] == update_data["full_name"]
        
        # 3. 等待一段时间后再次验证（测试缓存一致性）
        await asyncio.sleep(2)
        
        response = await async_client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 200
        
        user_data = response.json()
        assert user_data["full_name"] == update_data["full_name"]
    
    async def test_12_cleanup_test_data(self, async_client: AsyncClient, test_user_data: Dict):
        """清理测试数据"""
        headers = {"Authorization": f"Bearer {test_user_data['access_token']}"}
        
        # 1. 删除用户数据（如果支持）
        deletion_request = {
            "deletion_reason": "集成测试清理",
            "hard_delete": True,
            "retain_legal_data": False
        }
        
        response = await async_client.post("/api/v1/privacy/delete-data", json=deletion_request, headers=headers)
        # 根据实际实现，这可能返回200或202
        assert response.status_code in [200, 202]
        
        # 2. 验证数据已删除
        await asyncio.sleep(2)  # 等待删除处理完成
        
        # 尝试访问用户数据应该失败
        response = await async_client.get("/api/v1/users/me", headers=headers)
        # 用户数据删除后，令牌可能仍然有效，但用户数据应该不存在
        # 具体行为取决于实现


@pytest.mark.asyncio
class TestSystemIntegrationFlow:
    """系统集成测试流程"""
    
    async def test_complete_user_journey(self):
        """测试完整的用户使用流程"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 1. 用户注册
            user_data = {
                "email": f"journey_{uuid.uuid4().hex[:8]}@example.com",
                "password": "JourneyTest123!",
                "full_name": "用户旅程测试",
                "phone": "13800138000"
            }
            
            response = await client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == 201
            
            # 2. 用户登录
            response = await client.post("/api/v1/auth/login", json={
                "email": user_data["email"],
                "password": user_data["password"]
            })
            assert response.status_code == 200
            
            token = response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            # 3. 查看个人资料
            response = await client.get("/api/v1/users/me", headers=headers)
            assert response.status_code == 200
            
            # 4. 进行AI咨询
            response = await client.post("/api/v1/ai/chat", json={
                "message": "什么是劳动合同？",
                "context": "法律咨询"
            }, headers=headers)
            assert response.status_code == 200
            
            # 5. 上传文档分析
            test_doc = "这是一份测试合同文档内容"
            files = {"file": ("test.txt", test_doc, "text/plain")}
            
            response = await client.post("/api/v1/documents/analyze", files=files, headers=headers)
            assert response.status_code == 200
            
            # 6. 管理隐私设置
            response = await client.post("/api/v1/privacy/consent", json={
                "consent_type": "privacy_policy",
                "consented": True
            }, headers=headers)
            assert response.status_code == 200
            
            # 7. 导出个人数据
            response = await client.post("/api/v1/privacy/export-data", json={
                "include_personal_data": True,
                "format": "json"
            }, headers=headers)
            assert response.status_code == 200
            
            print("✅ 完整用户旅程测试通过")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
