#!/usr/bin/env python3
"""
语义分析器测试
"""

import sys
import os

# 添加app目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_semantic_analyzer_import():
    """测试语义分析器导入"""
    try:
        from services.semantic_analyzer import LegalSemanticAnalyzer, semantic_analyzer
        print("✓ 语义分析器导入成功")
        return True
    except ImportError as e:
        print(f"✗ 语义分析器导入失败: {e}")
        return False

def test_legal_concepts_extraction():
    """测试法律概念提取"""
    try:
        from services.semantic_analyzer import semantic_analyzer
        
        # 测试文本
        text = "根据劳动合同法规定，用人单位与劳动者签订劳动合同时，应当明确约定工资、工作时间等条款"
        
        concepts = semantic_analyzer.extract_legal_concepts(text)
        print(f"✓ 法律概念提取成功: {concepts}")
        
        # 验证是否提取到了预期的概念
        expected_concepts = ["劳动法", "合同法"]
        found_concepts = list(concepts.keys())
        
        if any(concept in found_concepts for concept in expected_concepts):
            print("✓ 成功识别相关法律概念")
        else:
            print("⚠ 未识别到预期的法律概念")
        
        return True
    except Exception as e:
        print(f"✗ 法律概念提取测试失败: {e}")
        return False

def test_semantic_similarity():
    """测试语义相似度计算"""
    try:
        from services.semantic_analyzer import semantic_analyzer
        
        # 测试相似文本
        text1 = "劳动合同纠纷处理"
        text2 = "处理劳动争议问题"
        text3 = "房屋买卖合同"
        
        # 计算相似度
        sim1_2 = semantic_analyzer.calculate_semantic_similarity(text1, text2)
        sim1_3 = semantic_analyzer.calculate_semantic_similarity(text1, text3)
        
        print(f"✓ 语义相似度计算成功")
        print(f"  '{text1}' vs '{text2}': {sim1_2:.3f}")
        print(f"  '{text1}' vs '{text3}': {sim1_3:.3f}")
        
        # 验证相似度逻辑
        if sim1_2 > sim1_3:
            print("✓ 相似度计算逻辑正确")
        else:
            print("⚠ 相似度计算可能需要优化")
        
        return True
    except Exception as e:
        print(f"✗ 语义相似度测试失败: {e}")
        return False

def test_semantic_structure_analysis():
    """测试语义结构分析"""
    try:
        from services.semantic_analyzer import semantic_analyzer
        
        text = """
        根据《劳动合同法》第三十九条规定，劳动者有下列情形之一的，用人单位可以解除劳动合同：
        （一）在试用期间被证明不符合录用条件的；
        （二）严重违反用人单位的规章制度的；
        （三）严重失职，营私舞弊，给用人单位造成重大损害的。
        """
        
        analysis = semantic_analyzer.analyze_semantic_structure(text)
        print(f"✓ 语义结构分析成功")
        print(f"  法律概念: {analysis['legal_concepts']}")
        print(f"  语义复杂度: {analysis['semantic_complexity']:.3f}")
        print(f"  概念分布: {analysis['concept_distribution']}")
        print(f"  语义连贯性: {analysis['semantic_coherence']:.3f}")
        
        return True
    except Exception as e:
        print(f"✗ 语义结构分析测试失败: {e}")
        return False

def test_semantic_neighbors():
    """测试语义邻居查找"""
    try:
        from services.semantic_analyzer import semantic_analyzer
        
        query_text = "劳动合同违约金问题"
        candidate_texts = [
            "员工违反劳动合同约定，公司要求支付违约金",
            "房屋租赁合同纠纷案例分析",
            "劳动者提前解除合同，用人单位主张违约责任",
            "交通事故赔偿标准",
            "劳动争议仲裁程序"
        ]
        
        neighbors = semantic_analyzer.find_semantic_neighbors(query_text, candidate_texts, top_k=3)
        print(f"✓ 语义邻居查找成功")
        
        for i, (idx, similarity) in enumerate(neighbors):
            print(f"  {i+1}. [{idx}] {candidate_texts[idx][:30]}... (相似度: {similarity:.3f})")
        
        return True
    except Exception as e:
        print(f"✗ 语义邻居查找测试失败: {e}")
        return False

def test_concept_explanation():
    """测试概念解释"""
    try:
        from services.semantic_analyzer import semantic_analyzer
        
        concepts = ["合同法", "劳动法", "民法", "刑法"]
        
        print("✓ 概念解释测试:")
        for concept in concepts:
            explanation = semantic_analyzer.get_concept_explanation(concept)
            print(f"  {concept}: {explanation[:50]}...")
        
        return True
    except Exception as e:
        print(f"✗ 概念解释测试失败: {e}")
        return False

def test_semantic_model_building():
    """测试语义模型构建"""
    try:
        from services.semantic_analyzer import LegalSemanticAnalyzer
        
        # 创建新的分析器实例用于测试
        analyzer = LegalSemanticAnalyzer()
        
        # 准备训练文本
        training_texts = [
            "劳动合同是劳动者与用人单位确立劳动关系、明确双方权利和义务的协议",
            "用人单位应当按照劳动合同约定和国家规定，向劳动者及时足额支付劳动报酬",
            "房屋买卖合同是出卖人转移房屋所有权于买受人，买受人支付价款的合同",
            "侵权责任是指民事主体因侵权行为应当承担的民事责任",
            "刑事责任是指犯罪人因实施犯罪行为应当承担的法律责任"
        ]
        
        # 构建语义模型
        analyzer.build_semantic_model(training_texts)
        print("✓ 语义模型构建成功")
        
        # 测试模型功能
        test_text = "劳动者工资支付问题"
        concepts = analyzer.extract_legal_concepts(test_text)
        print(f"  模型测试 - 概念提取: {concepts}")
        
        return True
    except Exception as e:
        print(f"✗ 语义模型构建测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始语义分析器测试...")
    print("=" * 60)
    
    tests = [
        ("语义分析器导入测试", test_semantic_analyzer_import),
        ("法律概念提取测试", test_legal_concepts_extraction),
        ("语义相似度测试", test_semantic_similarity),
        ("语义结构分析测试", test_semantic_structure_analysis),
        ("语义邻居查找测试", test_semantic_neighbors),
        ("概念解释测试", test_concept_explanation),
        ("语义模型构建测试", test_semantic_model_building),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
