#!/usr/bin/env python3
"""
初始化权限和角色数据脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import AsyncSessionLocal, create_database_engine
from app.models.permission import Permission, Role
from app.models.user import User, UserType, UserStatus
from app.services.permission import PermissionService
from app.core.security import get_password_hash


async def create_permissions():
    """创建基础权限"""
    async with AsyncSessionLocal() as session:
        permission_service = PermissionService(session)
        
        # 定义基础权限
        permissions = [
            # 用户管理权限
            {"name": "用户查看", "code": "user:read", "description": "查看用户信息", "category": "用户管理", "resource": "user", "action": "read", "level": 1},
            {"name": "用户创建", "code": "user:create", "description": "创建用户", "category": "用户管理", "resource": "user", "action": "create", "level": 2},
            {"name": "用户更新", "code": "user:update", "description": "更新用户信息", "category": "用户管理", "resource": "user", "action": "update", "level": 2},
            {"name": "用户删除", "code": "user:delete", "description": "删除用户", "category": "用户管理", "resource": "user", "action": "delete", "level": 3},
            {"name": "用户管理", "code": "user:manage", "description": "完整的用户管理权限", "category": "用户管理", "resource": "user", "action": "manage", "level": 4},
            
            # 角色权限管理
            {"name": "角色查看", "code": "role:read", "description": "查看角色信息", "category": "权限管理", "resource": "role", "action": "read", "level": 1},
            {"name": "角色创建", "code": "role:create", "description": "创建角色", "category": "权限管理", "resource": "role", "action": "create", "level": 3},
            {"name": "角色更新", "code": "role:update", "description": "更新角色", "category": "权限管理", "resource": "role", "action": "update", "level": 3},
            {"name": "角色删除", "code": "role:delete", "description": "删除角色", "category": "权限管理", "resource": "role", "action": "delete", "level": 4},
            
            # 权限管理
            {"name": "权限查看", "code": "permission:read", "description": "查看权限信息", "category": "权限管理", "resource": "permission", "action": "read", "level": 1},
            {"name": "权限创建", "code": "permission:create", "description": "创建权限", "category": "权限管理", "resource": "permission", "action": "create", "level": 4},
            {"name": "权限更新", "code": "permission:update", "description": "更新权限", "category": "权限管理", "resource": "permission", "action": "update", "level": 4},
            {"name": "权限删除", "code": "permission:delete", "description": "删除权限", "category": "权限管理", "resource": "permission", "action": "delete", "level": 5},
            
            # 法律案例权限
            {"name": "案例查看", "code": "case:read", "description": "查看法律案例", "category": "案例管理", "resource": "case", "action": "read", "level": 1},
            {"name": "案例创建", "code": "case:create", "description": "创建法律案例", "category": "案例管理", "resource": "case", "action": "create", "level": 2},
            {"name": "案例更新", "code": "case:update", "description": "更新法律案例", "category": "案例管理", "resource": "case", "action": "update", "level": 2},
            {"name": "案例删除", "code": "case:delete", "description": "删除法律案例", "category": "案例管理", "resource": "case", "action": "delete", "level": 3},
            {"name": "案例管理", "code": "case:manage", "description": "完整的案例管理权限", "category": "案例管理", "resource": "case", "action": "manage", "level": 3},
            
            # 合同权限
            {"name": "合同查看", "code": "contract:read", "description": "查看合同", "category": "合同管理", "resource": "contract", "action": "read", "level": 1},
            {"name": "合同创建", "code": "contract:create", "description": "创建合同", "category": "合同管理", "resource": "contract", "action": "create", "level": 2},
            {"name": "合同更新", "code": "contract:update", "description": "更新合同", "category": "合同管理", "resource": "contract", "action": "update", "level": 2},
            {"name": "合同删除", "code": "contract:delete", "description": "删除合同", "category": "合同管理", "resource": "contract", "action": "delete", "level": 3},
            
            # 文书权限
            {"name": "文书查看", "code": "document:read", "description": "查看法律文书", "category": "文书管理", "resource": "document", "action": "read", "level": 1},
            {"name": "文书创建", "code": "document:create", "description": "创建法律文书", "category": "文书管理", "resource": "document", "action": "create", "level": 2},
            {"name": "文书更新", "code": "document:update", "description": "更新法律文书", "category": "文书管理", "resource": "document", "action": "update", "level": 2},
            {"name": "文书删除", "code": "document:delete", "description": "删除法律文书", "category": "文书管理", "resource": "document", "action": "delete", "level": 3},
            
            # 问答权限
            {"name": "问答查看", "code": "qa:read", "description": "查看问答记录", "category": "问答管理", "resource": "qa", "action": "read", "level": 1},
            {"name": "问答创建", "code": "qa:create", "description": "创建问答", "category": "问答管理", "resource": "qa", "action": "create", "level": 1},
            {"name": "问答管理", "code": "qa:manage", "description": "管理问答记录", "category": "问答管理", "resource": "qa", "action": "manage", "level": 2},
            
            # 审计权限
            {"name": "审计查看", "code": "audit:read", "description": "查看审计日志", "category": "系统管理", "resource": "audit", "action": "read", "level": 3},
            {"name": "审计管理", "code": "audit:manage", "description": "管理审计日志", "category": "系统管理", "resource": "audit", "action": "manage", "level": 4},
            
            # 系统配置权限
            {"name": "系统配置", "code": "system:config", "description": "系统配置管理", "category": "系统管理", "resource": "system", "action": "config", "level": 5},
            {"name": "系统监控", "code": "system:monitor", "description": "系统监控", "category": "系统管理", "resource": "system", "action": "monitor", "level": 3},
        ]
        
        created_count = 0
        for perm_data in permissions:
            existing = await permission_service.get_permission_by_code(perm_data["code"])
            if not existing:
                await permission_service.create_permission(**perm_data)
                created_count += 1
                print(f"创建权限: {perm_data['name']} ({perm_data['code']})")
        
        print(f"权限初始化完成，共创建 {created_count} 个权限")


async def create_roles():
    """创建基础角色"""
    async with AsyncSessionLocal() as session:
        permission_service = PermissionService(session)
        
        # 定义基础角色
        roles = [
            {
                "name": "超级管理员",
                "code": "super_admin",
                "description": "拥有所有权限的超级管理员",
                "level": 5,
                "permission_codes": [
                    "user:manage", "role:create", "role:update", "role:delete",
                    "permission:create", "permission:update", "permission:delete",
                    "case:manage", "contract:create", "contract:update", "contract:delete",
                    "document:create", "document:update", "document:delete",
                    "qa:manage", "audit:manage", "system:config", "system:monitor"
                ]
            },
            {
                "name": "管理员",
                "code": "admin",
                "description": "系统管理员，拥有大部分管理权限",
                "level": 4,
                "permission_codes": [
                    "user:read", "user:create", "user:update",
                    "role:read", "permission:read",
                    "case:manage", "contract:create", "contract:update", "contract:delete",
                    "document:create", "document:update", "document:delete",
                    "qa:manage", "audit:read", "system:monitor"
                ]
            },
            {
                "name": "律师",
                "code": "lawyer",
                "description": "律师用户，拥有专业法律服务权限",
                "level": 3,
                "permission_codes": [
                    "user:read", "case:read", "case:create", "case:update",
                    "contract:read", "contract:create", "contract:update",
                    "document:read", "document:create", "document:update",
                    "qa:read", "qa:create"
                ]
            },
            {
                "name": "企业用户",
                "code": "enterprise",
                "description": "企业用户，拥有基本的法律服务权限",
                "level": 2,
                "permission_codes": [
                    "case:read", "contract:read", "contract:create",
                    "document:read", "document:create",
                    "qa:read", "qa:create"
                ]
            },
            {
                "name": "个人用户",
                "code": "individual",
                "description": "个人用户，拥有基础的查询和咨询权限",
                "level": 1,
                "permission_codes": [
                    "case:read", "contract:read",
                    "document:read", "qa:read", "qa:create"
                ]
            }
        ]
        
        created_count = 0
        for role_data in roles:
            existing = await permission_service.get_role_by_code(role_data["code"])
            if not existing:
                await permission_service.create_role(**role_data)
                created_count += 1
                print(f"创建角色: {role_data['name']} ({role_data['code']})")
        
        print(f"角色初始化完成，共创建 {created_count} 个角色")


async def assign_default_roles():
    """为现有用户分配默认角色"""
    async with AsyncSessionLocal() as session:
        permission_service = PermissionService(session)
        
        from sqlalchemy import select
        
        # 获取所有用户
        stmt = select(User)
        result = await session.execute(stmt)
        users = result.scalars().all()
        
        # 获取角色
        admin_role = await permission_service.get_role_by_code("admin")
        lawyer_role = await permission_service.get_role_by_code("lawyer")
        enterprise_role = await permission_service.get_role_by_code("enterprise")
        individual_role = await permission_service.get_role_by_code("individual")
        
        assigned_count = 0
        for user in users:
            # 根据用户类型分配默认角色
            if user.username == "admin":
                # 管理员用户
                if admin_role and admin_role not in user.roles:
                    await permission_service.assign_role_to_user(user.id, admin_role.id)
                    assigned_count += 1
                    print(f"为用户 {user.username} 分配管理员角色")
            elif user.user_type == UserType.LAWYER:
                # 律师用户
                if lawyer_role and lawyer_role not in user.roles:
                    await permission_service.assign_role_to_user(user.id, lawyer_role.id)
                    assigned_count += 1
                    print(f"为用户 {user.username} 分配律师角色")
            elif user.user_type == UserType.ENTERPRISE:
                # 企业用户
                if enterprise_role and enterprise_role not in user.roles:
                    await permission_service.assign_role_to_user(user.id, enterprise_role.id)
                    assigned_count += 1
                    print(f"为用户 {user.username} 分配企业用户角色")
            else:
                # 个人用户
                if individual_role and individual_role not in user.roles:
                    await permission_service.assign_role_to_user(user.id, individual_role.id)
                    assigned_count += 1
                    print(f"为用户 {user.username} 分配个人用户角色")
        
        print(f"角色分配完成，共分配 {assigned_count} 个角色")


async def main():
    """主函数"""
    print("开始初始化权限和角色数据...")
    
    # 创建数据库引擎
    create_database_engine()
    
    try:
        # 创建权限
        await create_permissions()
        
        # 创建角色
        await create_roles()
        
        # 为现有用户分配默认角色
        await assign_default_roles()
        
        print("权限和角色数据初始化完成！")
        
    except Exception as e:
        print(f"初始化失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
