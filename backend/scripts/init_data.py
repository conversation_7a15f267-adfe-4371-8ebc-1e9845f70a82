#!/usr/bin/env python3
"""
数据库初始化和基础数据导入脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.core.database import get_db, engine, Base
from app.models.user import User, UserProfile, UserType, UserStatus
from app.models.qa import QARecord
from app.core.security import get_password_hash
from app.core.config import settings
import uuid
from datetime import datetime, timedelta
import json


async def create_sample_users():
    """创建示例用户"""
    print("创建示例用户...")

    # 创建异步会话
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as db:
        # 创建管理员用户
        admin_user = User(
            id=uuid.uuid4(),
            username="admin",
            email="<EMAIL>",
            password_hash=get_password_hash("Admin123!"),
            full_name="系统管理员",
            user_type=UserType.LAWYER,
            status=UserStatus.ACTIVE,
            email_verified=True,
            phone_verified=True
        )
        
        # 创建测试用户
        test_users = [
            {
                "username": "testuser1",
                "email": "<EMAIL>",
                "password": "TestPass123!",
                "full_name": "张三",
                "user_type": UserType.INDIVIDUAL,
                "phone": "13800138001"
            },
            {
                "username": "testuser2", 
                "email": "<EMAIL>",
                "password": "TestPass123!",
                "full_name": "李四",
                "user_type": UserType.ENTERPRISE,
                "phone": "13800138002"
            },
            {
                "username": "lawyer1",
                "email": "<EMAIL>", 
                "password": "LawyerPass123!",
                "full_name": "王律师",
                "user_type": UserType.LAWYER,
                "phone": "13800138003"
            }
        ]
        
        users = [admin_user]
        
        for user_data in test_users:
            user = User(
                id=uuid.uuid4(),
                username=user_data["username"],
                email=user_data["email"],
                password_hash=get_password_hash(user_data["password"]),
                full_name=user_data["full_name"],
                phone=user_data.get("phone"),
                user_type=user_data["user_type"],
                status=UserStatus.ACTIVE,
                email_verified=True,
                phone_verified=True
            )
            users.append(user)
        
        # 批量添加用户
        db.add_all(users)
        await db.commit()
        
        print(f"✅ 成功创建 {len(users)} 个用户")
        return users


async def create_sample_qa_records():
    """创建示例问答记录"""
    print("创建示例问答记录...")

    # 创建异步会话
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as db:
        # 获取第一个用户作为示例
        result = await db.execute("SELECT id FROM users LIMIT 1")
        user_id = result.scalar()
        
        if not user_id:
            print("❌ 未找到用户，跳过问答记录创建")
            return
        
        sample_qa = [
            {
                "question": "如何起草一份标准的劳动合同？",
                "answer": """起草标准劳动合同需要包含以下要素：

1. **基本信息**
   - 用人单位名称、住所和法定代表人
   - 劳动者姓名、住址和身份证号码

2. **合同期限**
   - 固定期限、无固定期限或以完成一定工作任务为期限

3. **工作内容和工作地点**
   - 具体工作岗位和职责
   - 工作地点的明确约定

4. **工作时间和休息休假**
   - 标准工时制、不定时工时制或综合计算工时制
   - 休息日和法定节假日安排

5. **劳动报酬**
   - 工资标准、支付方式和支付时间
   - 加班费计算标准

6. **社会保险**
   - 养老、医疗、失业、工伤、生育保险

7. **劳动保护和劳动条件**
   - 职业病防护措施
   - 安全生产条件

8. **其他事项**
   - 试用期约定
   - 培训协议
   - 保密条款
   - 竞业限制

建议使用人力资源和社会保障部门提供的标准模板，并根据实际情况进行调整。""",
                "category": "劳动法",
                "tags": ["劳动合同", "合同起草", "劳动法"]
            },
            {
                "question": "交通事故责任如何认定？",
                "answer": """交通事故责任认定遵循以下原则：

1. **责任认定标准**
   - 全部责任：一方当事人的违法行为造成交通事故
   - 主要责任：一方当事人的违法行为是造成交通事故的主要原因
   - 同等责任：各方当事人的违法行为对交通事故的发生起同等作用
   - 次要责任：一方当事人的违法行为对交通事故的发生起次要作用
   - 无责任：各方当事人均无违法行为或违法行为与交通事故无因果关系

2. **认定程序**
   - 现场勘查和调查取证
   - 检验、鉴定
   - 制作交通事故认定书

3. **常见情形**
   - 追尾事故：一般后车承担全部责任
   - 变道事故：变道车辆一般承担主要责任
   - 闯红灯：闯红灯方承担全部责任
   - 酒驾：酒驾方承担主要或全部责任

4. **救济途径**
   - 对认定书有异议可申请复核
   - 复核期限为收到认定书后3日内

责任认定是后续赔偿的重要依据，当事人应积极配合调查。""",
                "category": "交通事故",
                "tags": ["交通事故", "责任认定", "交通法"]
            },
            {
                "question": "房屋买卖合同纠纷如何处理？",
                "answer": """房屋买卖合同纠纷处理方式：

1. **协商解决**
   - 双方当事人直接沟通
   - 明确争议焦点和解决方案
   - 达成补充协议

2. **调解解决**
   - 人民调解委员会调解
   - 行政调解（房管部门）
   - 司法调解

3. **仲裁解决**
   - 合同约定仲裁条款的情况下
   - 向约定的仲裁机构申请仲裁

4. **诉讼解决**
   - 向有管辖权的人民法院起诉
   - 一般由不动产所在地法院管辖

5. **常见纠纷类型**
   - 逾期交房或过户
   - 房屋质量问题
   - 价格争议
   - 违约责任

6. **证据收集**
   - 买卖合同及补充协议
   - 付款凭证
   - 房屋交付证明
   - 相关通知书

7. **法律后果**
   - 继续履行合同
   - 支付违约金
   - 赔偿损失
   - 解除合同

建议在签订合同时明确约定各项条款，避免后续纠纷。""",
                "category": "房地产",
                "tags": ["房屋买卖", "合同纠纷", "房地产法"]
            }
        ]
        
        qa_records = []
        for i, qa_data in enumerate(sample_qa):
            record = QARecord(
                id=uuid.uuid4(),
                question=qa_data["question"],
                answer=qa_data["answer"],
                user_id=user_id,
                category=qa_data["category"],
                tags=qa_data["tags"],
                status="completed",
                created_at=datetime.utcnow() - timedelta(days=i),
                updated_at=datetime.utcnow() - timedelta(days=i)
            )
            qa_records.append(record)
        
        db.add_all(qa_records)
        await db.commit()
        
        print(f"✅ 成功创建 {len(qa_records)} 条问答记录")


async def create_legal_categories():
    """创建法律分类数据"""
    print("创建法律分类数据...")
    
    categories = [
        "民法", "刑法", "行政法", "经济法", "劳动法", 
        "婚姻家庭法", "继承法", "合同法", "侵权责任法",
        "公司法", "证券法", "保险法", "银行法", "税法",
        "知识产权法", "环境法", "消费者权益保护法",
        "房地产法", "建筑法", "交通法", "医疗法"
    ]
    
    # 这里可以创建分类表，目前先打印
    print(f"✅ 法律分类包含 {len(categories)} 个类别")


async def main():
    """主函数"""
    print("🚀 开始初始化数据库数据...")
    
    try:
        # 创建示例用户
        await create_sample_users()
        
        # 创建示例问答记录
        await create_sample_qa_records()
        
        # 创建法律分类
        await create_legal_categories()
        
        print("✅ 数据库初始化完成！")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
