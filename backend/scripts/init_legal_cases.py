#!/usr/bin/env python3
"""
初始化法律案例数据脚本
"""

import asyncio
import sys
import os
from datetime import date, datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import AsyncSessionLocal, create_database_engine
from app.models.legal_case import LegalCase, LegalArticle, CaseType, CaseStatus
from app.models.user import User, UserType, UserStatus
from app.core.security import get_password_hash


async def create_sample_user():
    """创建示例用户"""
    async with AsyncSessionLocal() as session:
        # 检查是否已存在示例用户
        from sqlalchemy import select
        stmt = select(User).where(User.username == "admin")
        result = await session.execute(stmt)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            print("示例用户已存在")
            return existing_user
        
        # 创建管理员用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=get_password_hash("admin123"),
            full_name="系统管理员",
            user_type=UserType.LAWYER,
            status=UserStatus.ACTIVE,
            email_verified=True
        )
        
        session.add(admin_user)
        await session.commit()
        await session.refresh(admin_user)
        
        print(f"创建示例用户成功: {admin_user.username}")
        return admin_user


async def create_sample_legal_articles():
    """创建示例法条"""
    async with AsyncSessionLocal() as session:
        # 检查是否已存在法条
        from sqlalchemy import select, func
        stmt = select(func.count(LegalArticle.id))
        result = await session.execute(stmt)
        count = result.scalar()
        
        if count > 0:
            print(f"法条数据已存在，共 {count} 条")
            return
        
        # 创建示例法条
        articles = [
            {
                "article_number": "第一条",
                "title": "立法目的",
                "content": "为了保护民事主体的合法权益，调整民事关系，维护社会和经济秩序，适应中国特色社会主义发展要求，弘扬社会主义核心价值观，根据宪法，制定本法。",
                "law_name": "中华人民共和国民法典",
                "law_category": "民法",
                "chapter": "第一编 总则",
                "section": "第一章 基本规定",
                "effective_date": date(2021, 1, 1),
                "keywords": ["立法目的", "民事主体", "合法权益"]
            },
            {
                "article_number": "第二条",
                "title": "调整范围",
                "content": "民法调整平等主体的自然人、法人和非法人组织之间的人身关系和财产关系。",
                "law_name": "中华人民共和国民法典",
                "law_category": "民法",
                "chapter": "第一编 总则",
                "section": "第一章 基本规定",
                "effective_date": date(2021, 1, 1),
                "keywords": ["调整范围", "平等主体", "人身关系", "财产关系"]
            },
            {
                "article_number": "第三条",
                "title": "基本原则",
                "content": "民事主体的人身权利、财产权利以及其他合法权益受法律保护，任何组织或者个人不得侵犯。",
                "law_name": "中华人民共和国民法典",
                "law_category": "民法",
                "chapter": "第一编 总则",
                "section": "第一章 基本规定",
                "effective_date": date(2021, 1, 1),
                "keywords": ["基本原则", "人身权利", "财产权利", "法律保护"]
            }
        ]
        
        for article_data in articles:
            article = LegalArticle(**article_data)
            session.add(article)
        
        await session.commit()
        print(f"创建示例法条成功，共 {len(articles)} 条")


async def create_sample_legal_cases(admin_user):
    """创建示例法律案例"""
    async with AsyncSessionLocal() as session:
        # 检查是否已存在案例
        from sqlalchemy import select, func
        stmt = select(func.count(LegalCase.id))
        result = await session.execute(stmt)
        count = result.scalar()
        
        if count > 0:
            print(f"法律案例数据已存在，共 {count} 个")
            return
        
        # 创建示例案例
        cases = [
            {
                "case_number": "(2023)京01民初001号",
                "title": "张某诉李某合同纠纷案",
                "court_name": "北京市第一中级人民法院",
                "case_type": CaseType.CIVIL,
                "judgment_date": date(2023, 6, 15),
                "trial_procedure": "一审",
                "parties": [
                    {"name": "张某", "type": "原告", "role": "买方"},
                    {"name": "李某", "type": "被告", "role": "卖方"}
                ],
                "case_summary": "原告张某与被告李某签订房屋买卖合同，被告未按约定时间交付房屋，原告要求解除合同并赔偿损失。",
                "case_facts": "2023年1月，张某与李某签订房屋买卖合同，约定李某于2023年3月31日前交付房屋。但李某未按约定时间交付，张某多次催促无果，遂起诉要求解除合同。",
                "dispute_focus": ["合同是否应当解除", "违约责任如何承担", "损失如何计算"],
                "court_opinion": "被告李某未按约定履行交付义务，构成根本违约，原告有权解除合同。",
                "judgment_result": "解除原告张某与被告李某签订的房屋买卖合同；被告李某赔偿原告张某经济损失50000元。",
                "legal_basis": "《中华人民共和国民法典》第563条、第577条",
                "keywords": ["合同纠纷", "房屋买卖", "违约责任", "合同解除"],
                "tags": ["民事", "合同", "房地产"],
                "precedent_value": "中",
                "created_by": admin_user.id
            },
            {
                "case_number": "(2023)沪02刑初002号",
                "title": "王某盗窃案",
                "court_name": "上海市第二中级人民法院",
                "case_type": CaseType.CRIMINAL,
                "judgment_date": date(2023, 8, 20),
                "trial_procedure": "一审",
                "parties": [
                    {"name": "上海市人民检察院第二分院", "type": "公诉人"},
                    {"name": "王某", "type": "被告人"}
                ],
                "case_summary": "被告人王某多次入室盗窃他人财物，价值共计人民币15万元，构成盗窃罪。",
                "case_facts": "2023年3月至5月期间，被告人王某先后在上海市多个小区入室盗窃，窃取现金、首饰等财物，经鉴定价值共计人民币15万元。",
                "dispute_focus": ["盗窃数额的认定", "量刑情节的考虑"],
                "court_opinion": "被告人王某以非法占有为目的，多次盗窃他人财物，数额巨大，其行为构成盗窃罪。",
                "judgment_result": "被告人王某犯盗窃罪，判处有期徒刑三年，并处罚金人民币一万元。",
                "legal_basis": "《中华人民共和国刑法》第264条",
                "keywords": ["盗窃罪", "入室盗窃", "数额巨大"],
                "tags": ["刑事", "盗窃", "财产犯罪"],
                "precedent_value": "低",
                "created_by": admin_user.id
            },
            {
                "case_number": "(2023)粤03行初003号",
                "title": "陈某诉某市政府信息公开案",
                "court_name": "广东省第三中级人民法院",
                "case_type": CaseType.ADMINISTRATIVE,
                "judgment_date": date(2023, 9, 10),
                "trial_procedure": "一审",
                "parties": [
                    {"name": "陈某", "type": "原告"},
                    {"name": "某市人民政府", "type": "被告"}
                ],
                "case_summary": "原告陈某申请政府信息公开，被告未在法定期限内答复，原告起诉要求履行信息公开义务。",
                "case_facts": "2023年4月，陈某向某市政府申请公开土地征收补偿方案，政府未在15个工作日内答复，陈某遂提起行政诉讼。",
                "dispute_focus": ["政府是否应当公开相关信息", "未答复是否违法"],
                "court_opinion": "被告未在法定期限内对原告的信息公开申请作出答复，程序违法。",
                "judgment_result": "确认被告某市人民政府未在法定期限内答复原告信息公开申请的行为违法；责令被告在判决生效后15日内对原告的申请作出答复。",
                "legal_basis": "《中华人民共和国政府信息公开条例》第20条",
                "keywords": ["政府信息公开", "行政诉讼", "程序违法"],
                "tags": ["行政", "信息公开", "程序"],
                "precedent_value": "高",
                "created_by": admin_user.id
            }
        ]
        
        for case_data in cases:
            case = LegalCase(**case_data)
            session.add(case)
        
        await session.commit()
        print(f"创建示例法律案例成功，共 {len(cases)} 个")


async def main():
    """主函数"""
    print("开始初始化法律案例数据...")
    
    # 创建数据库引擎
    create_database_engine()
    
    try:
        # 创建示例用户
        admin_user = await create_sample_user()
        
        # 创建示例法条
        await create_sample_legal_articles()
        
        # 创建示例案例
        await create_sample_legal_cases(admin_user)
        
        print("法律案例数据初始化完成！")
        
    except Exception as e:
        print(f"初始化失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
