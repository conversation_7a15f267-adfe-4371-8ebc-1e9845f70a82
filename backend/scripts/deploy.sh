#!/bin/bash

# AI法律助手生产环境部署脚本
# 使用方法: ./deploy.sh [环境] [版本]
# 例如: ./deploy.sh production v1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必需的工具
check_prerequisites() {
    log_info "检查部署前置条件..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装，请先安装 kubectl"
        exit 1
    fi
    
    # 检查docker
    if ! command -v docker &> /dev/null; then
        log_error "docker 未安装，请先安装 docker"
        exit 1
    fi
    
    # 检查helm（如果使用）
    if ! command -v helm &> /dev/null; then
        log_warning "helm 未安装，某些功能可能不可用"
    fi
    
    log_success "前置条件检查完成"
}

# 设置环境变量
setup_environment() {
    local env=$1
    local version=$2
    
    log_info "设置环境变量..."
    
    export ENVIRONMENT=${env:-"production"}
    export VERSION=${version:-"latest"}
    export NAMESPACE="ai-legal-${ENVIRONMENT}"
    export IMAGE_TAG="ghcr.io/yourorg/ai-legal-assistant:${VERSION}"
    
    log_info "环境: ${ENVIRONMENT}"
    log_info "版本: ${VERSION}"
    log_info "命名空间: ${NAMESPACE}"
    log_info "镜像标签: ${IMAGE_TAG}"
}

# 创建命名空间
create_namespace() {
    log_info "创建Kubernetes命名空间..."
    
    if kubectl get namespace ${NAMESPACE} &> /dev/null; then
        log_warning "命名空间 ${NAMESPACE} 已存在"
    else
        kubectl create namespace ${NAMESPACE}
        log_success "命名空间 ${NAMESPACE} 创建成功"
    fi
    
    # 设置默认命名空间
    kubectl config set-context --current --namespace=${NAMESPACE}
}

# 创建密钥
create_secrets() {
    log_info "创建Kubernetes密钥..."
    
    # 检查密钥文件是否存在
    if [[ ! -f ".env.${ENVIRONMENT}" ]]; then
        log_error "环境配置文件 .env.${ENVIRONMENT} 不存在"
        exit 1
    fi
    
    # 从环境文件加载变量
    source .env.${ENVIRONMENT}
    
    # 创建应用密钥
    kubectl create secret generic ai-legal-secrets \
        --from-literal=database-url="${DATABASE_URL}" \
        --from-literal=redis-url="${REDIS_URL}" \
        --from-literal=encryption-master-key="${ENCRYPTION_MASTER_KEY}" \
        --from-literal=secret-key="${SECRET_KEY}" \
        --from-literal=openai-api-key="${OPENAI_API_KEY}" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建数据库密钥
    kubectl create secret generic postgresql-secret \
        --from-literal=database-name="${POSTGRES_DB}" \
        --from-literal=database-user="${POSTGRES_USER}" \
        --from-literal=database-password="${POSTGRES_PASSWORD}" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建Redis密钥
    kubectl create secret generic redis-secret \
        --from-literal=password="${REDIS_PASSWORD}" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建TLS密钥（如果证书文件存在）
    if [[ -f "certs/cert.pem" && -f "certs/private.key" ]]; then
        kubectl create secret tls ai-legal-tls \
            --cert=certs/cert.pem \
            --key=certs/private.key \
            --dry-run=client -o yaml | kubectl apply -f -
        log_success "TLS密钥创建成功"
    else
        log_warning "TLS证书文件不存在，跳过TLS密钥创建"
    fi
    
    log_success "密钥创建完成"
}

# 部署数据库
deploy_database() {
    log_info "部署数据库..."
    
    # 应用数据库配置
    kubectl apply -f k8s/${ENVIRONMENT}/database.yml
    
    # 等待数据库就绪
    log_info "等待PostgreSQL就绪..."
    kubectl wait --for=condition=ready pod -l app=postgresql --timeout=300s
    
    log_info "等待Redis就绪..."
    kubectl wait --for=condition=ready pod -l app=redis --timeout=300s
    
    log_success "数据库部署完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 创建迁移任务
    kubectl create job --from=cronjob/database-migration migration-$(date +%s) || true
    
    # 等待迁移完成
    kubectl wait --for=condition=complete job -l job-name=migration --timeout=300s
    
    log_success "数据库迁移完成"
}

# 部署应用
deploy_application() {
    log_info "部署应用..."
    
    # 更新部署配置中的镜像标签
    sed -i "s|image: ghcr.io/yourorg/ai-legal-assistant:.*|image: ${IMAGE_TAG}|g" k8s/${ENVIRONMENT}/deployment.yml
    
    # 应用部署配置
    kubectl apply -f k8s/${ENVIRONMENT}/deployment.yml
    
    # 等待部署完成
    log_info "等待应用部署完成..."
    kubectl rollout status deployment/ai-legal-assistant --timeout=600s
    
    log_success "应用部署完成"
}

# 部署监控
deploy_monitoring() {
    log_info "部署监控系统..."
    
    # 检查是否有监控配置
    if [[ -d "k8s/${ENVIRONMENT}/monitoring" ]]; then
        kubectl apply -f k8s/${ENVIRONMENT}/monitoring/
        log_success "监控系统部署完成"
    else
        log_warning "监控配置不存在，跳过监控部署"
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 获取服务端点
    local service_ip=$(kubectl get service ai-legal-assistant-service -o jsonpath='{.spec.clusterIP}')
    
    # 检查应用健康状态
    if kubectl exec -it deployment/ai-legal-assistant -- curl -f http://localhost:8000/health; then
        log_success "应用健康检查通过"
    else
        log_error "应用健康检查失败"
        return 1
    fi
    
    # 检查数据库连接
    if kubectl exec -it deployment/ai-legal-assistant -- python -c "
from app.core.database import engine
with engine.connect() as conn:
    result = conn.execute('SELECT 1')
    print('数据库连接正常')
"; then
        log_success "数据库连接检查通过"
    else
        log_error "数据库连接检查失败"
        return 1
    fi
}

# 性能测试
performance_test() {
    log_info "执行性能测试..."
    
    # 这里可以集成k6或其他性能测试工具
    # k6 run performance-tests/load-test.js
    
    log_warning "性能测试功能待实现"
}

# 备份验证
backup_verification() {
    log_info "验证备份功能..."
    
    # 触发一次备份任务
    kubectl create job --from=cronjob/postgresql-backup backup-test-$(date +%s)
    
    # 等待备份完成
    sleep 30
    
    log_success "备份验证完成"
}

# 清理旧版本
cleanup_old_versions() {
    log_info "清理旧版本..."
    
    # 清理旧的ReplicaSet
    kubectl delete replicaset --cascade=false --selector=app=ai-legal-assistant --field-selector='status.replicas=0'
    
    # 清理完成的Job
    kubectl delete job --field-selector=status.successful=1
    
    log_success "旧版本清理完成"
}

# 回滚函数
rollback() {
    log_warning "开始回滚..."
    
    # 回滚到上一个版本
    kubectl rollout undo deployment/ai-legal-assistant
    
    # 等待回滚完成
    kubectl rollout status deployment/ai-legal-assistant --timeout=300s
    
    log_success "回滚完成"
}

# 显示部署状态
show_status() {
    log_info "部署状态:"
    echo "=================================="
    
    # 显示Pod状态
    echo "Pod状态:"
    kubectl get pods -l app=ai-legal-assistant
    
    # 显示服务状态
    echo -e "\n服务状态:"
    kubectl get services
    
    # 显示Ingress状态
    echo -e "\nIngress状态:"
    kubectl get ingress
    
    # 显示HPA状态
    echo -e "\nHPA状态:"
    kubectl get hpa
    
    echo "=================================="
}

# 主函数
main() {
    local environment=${1:-"production"}
    local version=${2:-"latest"}
    local action=${3:-"deploy"}
    
    log_info "开始部署AI法律助手到 ${environment} 环境"
    
    case $action in
        "deploy")
            check_prerequisites
            setup_environment $environment $version
            create_namespace
            create_secrets
            deploy_database
            run_migrations
            deploy_application
            deploy_monitoring
            health_check
            backup_verification
            cleanup_old_versions
            show_status
            log_success "部署完成！"
            ;;
        "rollback")
            setup_environment $environment $version
            rollback
            health_check
            show_status
            log_success "回滚完成！"
            ;;
        "status")
            setup_environment $environment $version
            show_status
            ;;
        "test")
            setup_environment $environment $version
            health_check
            performance_test
            ;;
        *)
            echo "使用方法: $0 [环境] [版本] [动作]"
            echo "环境: production, staging, development"
            echo "版本: v1.0.0, latest, etc."
            echo "动作: deploy, rollback, status, test"
            exit 1
            ;;
    esac
}

# 捕获错误并回滚
trap 'log_error "部署失败，考虑回滚"; exit 1' ERR

# 执行主函数
main "$@"
