#!/bin/bash

# AI法律助手综合测试运行脚本
# 包含单元测试、集成测试、性能测试和安全测试

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_section() {
    echo -e "${PURPLE}[SECTION]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "AI法律助手测试运行脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -a, --all           运行所有测试"
    echo "  -u, --unit          运行单元测试"
    echo "  -i, --integration   运行集成测试"
    echo "  -p, --performance   运行性能测试"
    echo "  -s, --security      运行安全测试"
    echo "  -c, --coverage      生成测试覆盖率报告"
    echo "  -r, --report        生成详细测试报告"
    echo "  -h, --help          显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  TEST_ENV            测试环境 (development|testing|staging)"
    echo "  DATABASE_URL        测试数据库URL"
    echo "  REDIS_URL           测试Redis URL"
    echo "  ENCRYPTION_MASTER_KEY  测试加密密钥"
    echo ""
    echo "示例:"
    echo "  $0 --all --coverage     # 运行所有测试并生成覆盖率报告"
    echo "  $0 --unit --report      # 运行单元测试并生成详细报告"
    echo "  $0 --performance        # 只运行性能测试"
}

# 检查环境
check_environment() {
    log_section "检查测试环境"
    
    # 检查Python环境
    if ! command -v python &> /dev/null; then
        log_error "Python未安装"
        exit 1
    fi
    
    # 检查pytest
    if ! python -c "import pytest" &> /dev/null; then
        log_error "pytest未安装，请运行: pip install pytest"
        exit 1
    fi
    
    # 检查必要的测试依赖
    local missing_deps=()
    
    if ! python -c "import httpx" &> /dev/null; then
        missing_deps+=("httpx")
    fi
    
    if ! python -c "import pytest_asyncio" &> /dev/null; then
        missing_deps+=("pytest-asyncio")
    fi
    
    if ! python -c "import pytest_cov" &> /dev/null; then
        missing_deps+=("pytest-cov")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_warning "缺少测试依赖: ${missing_deps[*]}"
        log_info "正在安装缺少的依赖..."
        pip install "${missing_deps[@]}"
    fi
    
    # 设置测试环境变量
    export TEST_ENV=${TEST_ENV:-"testing"}
    export DATABASE_URL=${DATABASE_URL:-"postgresql://test_user:test_pass@localhost:5432/test_db"}
    export REDIS_URL=${REDIS_URL:-"redis://localhost:6379/1"}
    export ENCRYPTION_MASTER_KEY=${ENCRYPTION_MASTER_KEY:-"test-encryption-key-for-testing-only"}
    export SECRET_KEY=${SECRET_KEY:-"test-secret-key-for-testing-only"}
    
    log_success "环境检查完成"
}

# 启动测试服务
start_test_services() {
    log_section "启动测试服务"
    
    # 检查Docker是否可用
    if command -v docker &> /dev/null; then
        log_info "启动测试数据库和Redis..."
        
        # 启动PostgreSQL测试容器
        docker run -d --name test-postgres \
            -e POSTGRES_DB=test_db \
            -e POSTGRES_USER=test_user \
            -e POSTGRES_PASSWORD=test_pass \
            -p 5433:5432 \
            postgres:15-alpine || log_warning "PostgreSQL容器启动失败或已存在"
        
        # 启动Redis测试容器
        docker run -d --name test-redis \
            -p 6380:6379 \
            redis:7-alpine || log_warning "Redis容器启动失败或已存在"
        
        # 等待服务启动
        sleep 5
        
        # 更新连接URL
        export DATABASE_URL="postgresql://test_user:test_pass@localhost:5433/test_db"
        export REDIS_URL="redis://localhost:6380/1"
        
        log_success "测试服务启动完成"
    else
        log_warning "Docker不可用，请确保PostgreSQL和Redis服务正在运行"
    fi
}

# 停止测试服务
stop_test_services() {
    log_section "停止测试服务"
    
    if command -v docker &> /dev/null; then
        docker stop test-postgres test-redis 2>/dev/null || true
        docker rm test-postgres test-redis 2>/dev/null || true
        log_success "测试服务已停止"
    fi
}

# 运行单元测试
run_unit_tests() {
    log_section "运行单元测试"
    
    local pytest_args=("tests/unit/" "-v" "--tb=short")
    
    if [ "$GENERATE_COVERAGE" = "true" ]; then
        pytest_args+=("--cov=app" "--cov-report=html" "--cov-report=xml" "--cov-report=term")
    fi
    
    if [ "$GENERATE_REPORT" = "true" ]; then
        pytest_args+=("--html=reports/unit_test_report.html" "--self-contained-html")
    fi
    
    log_info "执行命令: pytest ${pytest_args[*]}"
    
    if pytest "${pytest_args[@]}"; then
        log_success "单元测试通过"
        return 0
    else
        log_error "单元测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    log_section "运行集成测试"
    
    local pytest_args=("tests/integration/" "-v" "--tb=short")
    
    if [ "$GENERATE_REPORT" = "true" ]; then
        pytest_args+=("--html=reports/integration_test_report.html" "--self-contained-html")
    fi
    
    log_info "执行命令: pytest ${pytest_args[*]}"
    
    if pytest "${pytest_args[@]}"; then
        log_success "集成测试通过"
        return 0
    else
        log_error "集成测试失败"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    log_section "运行性能测试"
    
    # 检查locust是否安装
    if ! command -v locust &> /dev/null; then
        log_warning "locust未安装，正在安装..."
        pip install locust
    fi
    
    # 启动应用服务器（后台运行）
    log_info "启动应用服务器..."
    uvicorn app.main:app --host 0.0.0.0 --port 8001 &
    local server_pid=$!
    
    # 等待服务器启动
    sleep 10
    
    # 运行性能测试
    log_info "运行性能测试..."
    
    local test_results_dir="reports/performance"
    mkdir -p "$test_results_dir"
    
    # 运行负载测试
    locust -f tests/performance/load_test.py \
        --host=http://localhost:8001 \
        --users=50 \
        --spawn-rate=5 \
        --run-time=120s \
        --headless \
        --html="$test_results_dir/performance_report.html" \
        --csv="$test_results_dir/performance_stats"
    
    local performance_result=$?
    
    # 停止应用服务器
    kill $server_pid 2>/dev/null || true
    
    if [ $performance_result -eq 0 ]; then
        log_success "性能测试完成"
        return 0
    else
        log_error "性能测试失败"
        return 1
    fi
}

# 运行安全测试
run_security_tests() {
    log_section "运行安全测试"
    
    local security_passed=true
    
    # 1. 代码安全扫描
    log_info "运行代码安全扫描..."
    if command -v bandit &> /dev/null; then
        if bandit -r app/ -f json -o reports/security_scan.json; then
            log_success "代码安全扫描通过"
        else
            log_warning "代码安全扫描发现问题，请查看报告"
            security_passed=false
        fi
    else
        log_warning "bandit未安装，跳过代码安全扫描"
    fi
    
    # 2. 依赖安全检查
    log_info "检查依赖安全性..."
    if command -v safety &> /dev/null; then
        if safety check --json --output reports/dependency_security.json; then
            log_success "依赖安全检查通过"
        else
            log_warning "发现不安全的依赖，请查看报告"
            security_passed=false
        fi
    else
        log_warning "safety未安装，跳过依赖安全检查"
    fi
    
    # 3. 运行安全相关的单元测试
    log_info "运行安全测试用例..."
    if pytest tests/ -k "security or auth or encryption" -v; then
        log_success "安全测试用例通过"
    else
        log_error "安全测试用例失败"
        security_passed=false
    fi
    
    if [ "$security_passed" = true ]; then
        log_success "安全测试通过"
        return 0
    else
        log_error "安全测试失败"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    log_section "生成测试报告"
    
    local report_dir="reports"
    mkdir -p "$report_dir"
    
    # 生成综合报告
    cat > "$report_dir/test_summary.md" << EOF
# AI法律助手测试报告

## 测试概览

- **测试时间**: $(date)
- **测试环境**: $TEST_ENV
- **Python版本**: $(python --version)
- **测试框架**: pytest $(python -c "import pytest; print(pytest.__version__)")

## 测试结果

### 单元测试
$([ "$RUN_UNIT_TESTS" = "true" ] && echo "✅ 已执行" || echo "⏭️ 跳过")

### 集成测试
$([ "$RUN_INTEGRATION_TESTS" = "true" ] && echo "✅ 已执行" || echo "⏭️ 跳过")

### 性能测试
$([ "$RUN_PERFORMANCE_TESTS" = "true" ] && echo "✅ 已执行" || echo "⏭️ 跳过")

### 安全测试
$([ "$RUN_SECURITY_TESTS" = "true" ] && echo "✅ 已执行" || echo "⏭️ 跳过")

## 测试覆盖率

$([ -f "htmlcov/index.html" ] && echo "📊 覆盖率报告: htmlcov/index.html" || echo "❌ 未生成覆盖率报告")

## 详细报告

- 单元测试报告: unit_test_report.html
- 集成测试报告: integration_test_report.html
- 性能测试报告: performance/performance_report.html
- 安全扫描报告: security_scan.json

## 建议

1. 定期运行完整测试套件
2. 关注测试覆盖率，目标保持在80%以上
3. 及时修复安全扫描发现的问题
4. 监控性能测试结果，确保系统性能稳定

---
生成时间: $(date)
EOF
    
    log_success "测试报告已生成: $report_dir/test_summary.md"
}

# 清理函数
cleanup() {
    log_section "清理测试环境"
    stop_test_services
    
    # 清理临时文件
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    
    log_success "清理完成"
}

# 主函数
main() {
    # 默认参数
    local RUN_ALL=false
    local RUN_UNIT_TESTS=false
    local RUN_INTEGRATION_TESTS=false
    local RUN_PERFORMANCE_TESTS=false
    local RUN_SECURITY_TESTS=false
    local GENERATE_COVERAGE=false
    local GENERATE_REPORT=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -a|--all)
                RUN_ALL=true
                shift
                ;;
            -u|--unit)
                RUN_UNIT_TESTS=true
                shift
                ;;
            -i|--integration)
                RUN_INTEGRATION_TESTS=true
                shift
                ;;
            -p|--performance)
                RUN_PERFORMANCE_TESTS=true
                shift
                ;;
            -s|--security)
                RUN_SECURITY_TESTS=true
                shift
                ;;
            -c|--coverage)
                GENERATE_COVERAGE=true
                shift
                ;;
            -r|--report)
                GENERATE_REPORT=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果指定了--all，启用所有测试
    if [ "$RUN_ALL" = true ]; then
        RUN_UNIT_TESTS=true
        RUN_INTEGRATION_TESTS=true
        RUN_PERFORMANCE_TESTS=true
        RUN_SECURITY_TESTS=true
        GENERATE_COVERAGE=true
        GENERATE_REPORT=true
    fi
    
    # 如果没有指定任何测试，默认运行单元测试
    if [ "$RUN_UNIT_TESTS" = false ] && [ "$RUN_INTEGRATION_TESTS" = false ] && \
       [ "$RUN_PERFORMANCE_TESTS" = false ] && [ "$RUN_SECURITY_TESTS" = false ]; then
        RUN_UNIT_TESTS=true
    fi
    
    # 设置陷阱以确保清理
    trap cleanup EXIT
    
    # 创建报告目录
    mkdir -p reports
    
    # 开始测试
    log_section "开始AI法律助手测试"
    
    check_environment
    start_test_services
    
    local overall_result=0
    
    # 运行各种测试
    if [ "$RUN_UNIT_TESTS" = true ]; then
        run_unit_tests || overall_result=1
    fi
    
    if [ "$RUN_INTEGRATION_TESTS" = true ]; then
        run_integration_tests || overall_result=1
    fi
    
    if [ "$RUN_PERFORMANCE_TESTS" = true ]; then
        run_performance_tests || overall_result=1
    fi
    
    if [ "$RUN_SECURITY_TESTS" = true ]; then
        run_security_tests || overall_result=1
    fi
    
    # 生成报告
    if [ "$GENERATE_REPORT" = true ]; then
        generate_test_report
    fi
    
    # 显示最终结果
    if [ $overall_result -eq 0 ]; then
        log_success "🎉 所有测试通过！"
    else
        log_error "❌ 部分测试失败，请查看详细日志"
    fi
    
    exit $overall_result
}

# 运行主函数
main "$@"
