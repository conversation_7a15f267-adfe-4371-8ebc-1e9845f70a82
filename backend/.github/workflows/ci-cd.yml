name: AI法律助手 CI/CD 流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt

    - name: 代码格式检查
      run: |
        cd backend
        black --check .
        isort --check-only .

    - name: 代码质量检查
      run: |
        cd backend
        flake8 .
        mypy app/

    - name: 安全扫描
      run: |
        cd backend
        bandit -r app/

  # 单元测试
  test:
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt

    - name: 运行测试
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        ENCRYPTION_MASTER_KEY: test-encryption-key-for-ci-testing
        SECRET_KEY: test-secret-key-for-ci-testing
      run: |
        cd backend
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=html

    - name: 上传测试覆盖率
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 构建Docker镜像
  build:
    runs-on: ubuntu-latest
    needs: test
    permissions:
      contents: read
      packages: write

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 登录容器注册表
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: 构建并推送Docker镜像
      uses: docker/build-push-action@v5
      with:
        context: backend
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 运行Trivy漏洞扫描
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: 上传Trivy扫描结果
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到开发环境
  deploy-dev:
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: development

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 部署到开发环境
      run: |
        echo "部署到开发环境"
        # 这里添加实际的部署脚本
        # 例如：kubectl apply -f k8s/dev/
        # 或者：docker-compose -f docker-compose.dev.yml up -d

    - name: 运行健康检查
      run: |
        echo "运行健康检查"
        # curl -f http://dev.yourdomain.com/health

  # 部署到生产环境
  deploy-prod:
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'

    - name: 配置Kubernetes
      run: |
        echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: 部署到生产环境
      run: |
        echo "部署到生产环境"
        # 蓝绿部署或滚动更新
        kubectl set image deployment/ai-legal-assistant \
          app=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        kubectl rollout status deployment/ai-legal-assistant

    - name: 运行生产环境健康检查
      run: |
        echo "运行生产环境健康检查"
        # curl -f https://yourdomain.com/health

    - name: 发送部署通知
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # 性能测试
  performance-test:
    runs-on: ubuntu-latest
    needs: deploy-dev
    if: github.ref == 'refs/heads/develop'

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 运行性能测试
      run: |
        echo "运行性能测试"
        # 使用k6或其他性能测试工具
        # k6 run performance-tests/load-test.js

    - name: 上传性能测试报告
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: performance-report.html

  # 数据库迁移
  database-migration:
    runs-on: ubuntu-latest
    needs: deploy-prod
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install alembic psycopg2-binary

    - name: 运行数据库迁移
      env:
        DATABASE_URL: ${{ secrets.PROD_DATABASE_URL }}
      run: |
        cd backend
        alembic upgrade head

    - name: 验证迁移
      env:
        DATABASE_URL: ${{ secrets.PROD_DATABASE_URL }}
      run: |
        cd backend
        alembic current

  # 备份验证
  backup-verification:
    runs-on: ubuntu-latest
    needs: database-migration
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: 验证数据库备份
      run: |
        echo "验证数据库备份"
        # 检查最新备份是否存在和完整

    - name: 测试备份恢复
      run: |
        echo "测试备份恢复流程"
        # 在测试环境中恢复备份并验证

  # 监控和告警设置
  setup-monitoring:
    runs-on: ubuntu-latest
    needs: deploy-prod
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: 配置监控告警
      run: |
        echo "配置Prometheus告警规则"
        # kubectl apply -f monitoring/prometheus-rules.yml

    - name: 验证监控状态
      run: |
        echo "验证监控系统状态"
        # curl -f http://prometheus.yourdomain.com/-/healthy

  # 清理旧版本
  cleanup:
    runs-on: ubuntu-latest
    needs: [deploy-prod, backup-verification]
    if: github.ref == 'refs/heads/main'

    steps:
    - name: 清理旧Docker镜像
      run: |
        echo "清理旧版本Docker镜像"
        # 保留最近5个版本，删除其他版本

    - name: 清理旧部署
      run: |
        echo "清理旧Kubernetes部署"
        # kubectl delete replicaset --cascade=false --selector=app=ai-legal-assistant
