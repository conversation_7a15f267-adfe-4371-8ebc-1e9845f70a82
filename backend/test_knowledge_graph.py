#!/usr/bin/env python3
"""
知识图谱测试
"""

import sys
import os
import json

# 添加app目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_knowledge_graph_import():
    """测试知识图谱导入"""
    try:
        from services.knowledge_graph import LegalKnowledgeGraph, knowledge_graph
        print("✓ 知识图谱导入成功")
        return True
    except ImportError as e:
        print(f"✗ 知识图谱导入失败: {e}")
        return False

def test_entity_operations():
    """测试实体操作"""
    try:
        from services.knowledge_graph import LegalKnowledgeGraph
        
        kg = LegalKnowledgeGraph()
        
        # 测试添加实体
        success = kg.add_entity("test_001", "测试法律", "法律法规", {"年份": "2023"})
        assert success, "添加实体失败"
        
        # 测试查找实体
        entity_id = kg.find_entity_by_name("测试法律")
        assert entity_id == "test_001", "查找实体失败"
        
        # 测试获取实体
        entity = kg.get_entity("test_001")
        assert entity is not None, "获取实体失败"
        assert entity.name == "测试法律", "实体名称不匹配"
        
        print("✓ 实体操作测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 实体操作测试失败: {e}")
        return False

def test_relationship_operations():
    """测试关系操作"""
    try:
        from services.knowledge_graph import LegalKnowledgeGraph
        
        kg = LegalKnowledgeGraph()
        
        # 添加测试实体
        kg.add_entity("entity_1", "实体1", "法律概念")
        kg.add_entity("entity_2", "实体2", "法律概念")
        
        # 测试添加关系
        success = kg.add_relationship("entity_1", "关联", "entity_2", {"强度": "高"})
        assert success, "添加关系失败"
        
        # 测试查找关系
        relationships = kg.find_relationships(source_id="entity_1")
        assert len(relationships) > 0, "查找关系失败"
        
        # 测试获取邻居
        neighbors = kg.get_neighbors("entity_1")
        assert len(neighbors) > 0, "获取邻居失败"
        assert neighbors[0][0] == "entity_2", "邻居不匹配"
        
        print("✓ 关系操作测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 关系操作测试失败: {e}")
        return False

def test_initial_knowledge():
    """测试初始知识"""
    try:
        from services.knowledge_graph import knowledge_graph
        
        # 检查初始实体
        stats = knowledge_graph.get_statistics()
        print(f"✓ 初始知识统计: {stats}")
        
        assert stats["total_entities"] > 0, "没有初始实体"
        assert "法律法规" in stats["entity_types"], "缺少法律法规类型"
        assert "法律概念" in stats["entity_types"], "缺少法律概念类型"
        
        # 检查特定实体
        civil_code_id = knowledge_graph.find_entity_by_name("民法典")
        assert civil_code_id is not None, "找不到民法典"
        
        contract_id = knowledge_graph.find_entity_by_name("合同")
        assert contract_id is not None, "找不到合同概念"
        
        print("✓ 初始知识测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 初始知识测试失败: {e}")
        return False

def test_entity_extraction():
    """测试实体提取"""
    try:
        from services.knowledge_graph import knowledge_graph
        
        # 测试文本
        text = "根据民法典规定，合同是当事人之间设立权利义务关系的协议"
        
        entities = knowledge_graph.extract_entities_from_text(text)
        print(f"✓ 提取的实体: {entities}")
        
        # 检查是否提取到了预期实体
        entity_names = [entity[0] for entity in entities]
        assert "民法典" in entity_names, "未提取到民法典"
        assert "合同" in entity_names, "未提取到合同"
        
        print("✓ 实体提取测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 实体提取测试失败: {e}")
        return False

def test_pattern_query():
    """测试模式查询"""
    try:
        from services.knowledge_graph import knowledge_graph
        
        # 查询所有法律法规
        pattern = {"entity_type": "法律法规"}
        results = knowledge_graph.query_by_pattern(pattern)
        
        print(f"✓ 法律法规查询结果: {len(results)}个")
        assert len(results) > 0, "没有查询到法律法规"
        
        # 查询所有法律概念
        pattern = {"entity_type": "法律概念"}
        results = knowledge_graph.query_by_pattern(pattern)
        
        print(f"✓ 法律概念查询结果: {len(results)}个")
        assert len(results) > 0, "没有查询到法律概念"
        
        print("✓ 模式查询测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 模式查询测试失败: {e}")
        return False

def test_shortest_path():
    """测试最短路径"""
    try:
        from services.knowledge_graph import knowledge_graph
        
        # 查找民法典和合同之间的路径
        civil_code_id = knowledge_graph.find_entity_by_name("民法典")
        contract_id = knowledge_graph.find_entity_by_name("合同")
        
        if civil_code_id and contract_id:
            path = knowledge_graph.shortest_path(civil_code_id, contract_id)
            if path:
                path_names = [knowledge_graph.get_entity(eid).name for eid in path]
                print(f"✓ 路径查找: {' -> '.join(path_names)}")
            else:
                print("⚠ 未找到路径（这是正常的，因为可能没有直接关系）")
        
        print("✓ 最短路径测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 最短路径测试失败: {e}")
        return False

def test_knowledge_query():
    """测试知识查询便捷函数"""
    try:
        from services.knowledge_graph import query_legal_knowledge
        
        query = "民法典规定了合同的相关内容"
        result = query_legal_knowledge(query)
        
        print(f"✓ 知识查询结果:")
        print(f"  提取实体: {len(result['extracted_entities'])}个")
        print(f"  知识结果: {len(result['knowledge_results'])}个")
        print(f"  图谱统计: {result['statistics']['total_entities']}个实体")
        
        assert len(result['extracted_entities']) > 0, "未提取到实体"
        
        print("✓ 知识查询测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 知识查询测试失败: {e}")
        return False

def test_relationship_finding():
    """测试关系查找便捷函数"""
    try:
        from services.knowledge_graph import find_legal_relationships
        
        # 查找民法典和合同的关系
        path = find_legal_relationships("民法典", "合同")
        
        if path:
            print(f"✓ 关系路径: {' -> '.join(path)}")
        else:
            print("⚠ 未找到直接关系路径")
        
        print("✓ 关系查找测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 关系查找测试失败: {e}")
        return False

def test_json_export():
    """测试JSON导出"""
    try:
        from services.knowledge_graph import knowledge_graph
        
        json_data = knowledge_graph.export_to_json()
        
        # 验证JSON格式
        parsed_data = json.loads(json_data)
        assert "entities" in parsed_data, "JSON中缺少entities字段"
        assert "statistics" in parsed_data, "JSON中缺少statistics字段"
        
        print(f"✓ JSON导出成功，包含{len(parsed_data['entities'])}个实体")
        return True
        
    except Exception as e:
        print(f"✗ JSON导出测试失败: {e}")
        return False

def test_entity_types():
    """测试实体类型分类"""
    try:
        from services.knowledge_graph import knowledge_graph
        
        # 获取各类型实体
        laws = knowledge_graph.get_entities_by_type("法律法规")
        concepts = knowledge_graph.get_entities_by_type("法律概念")
        subjects = knowledge_graph.get_entities_by_type("法律主体")
        
        print(f"✓ 实体类型统计:")
        print(f"  法律法规: {len(laws)}个")
        print(f"  法律概念: {len(concepts)}个")
        print(f"  法律主体: {len(subjects)}个")
        
        assert len(laws) > 0, "没有法律法规实体"
        assert len(concepts) > 0, "没有法律概念实体"
        
        print("✓ 实体类型测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 实体类型测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始知识图谱测试...")
    print("=" * 50)
    
    tests = [
        ("知识图谱导入测试", test_knowledge_graph_import),
        ("实体操作测试", test_entity_operations),
        ("关系操作测试", test_relationship_operations),
        ("初始知识测试", test_initial_knowledge),
        ("实体提取测试", test_entity_extraction),
        ("模式查询测试", test_pattern_query),
        ("最短路径测试", test_shortest_path),
        ("知识查询测试", test_knowledge_query),
        ("关系查找测试", test_relationship_finding),
        ("JSON导出测试", test_json_export),
        ("实体类型测试", test_entity_types),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
