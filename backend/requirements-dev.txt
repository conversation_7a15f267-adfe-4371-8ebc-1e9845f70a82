# AI法律助手开发环境依赖

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-html==4.1.1
pytest-xdist==3.5.0

# 代码质量工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5
safety==2.3.5

# 开发工具
ipython==8.17.2
ipdb==0.13.13
pre-commit==3.6.0
watchdog==3.0.0

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8
mkdocs-mermaid2-plugin==1.1.1

# 性能测试
locust==2.17.0
memory-profiler==0.61.0
py-spy==0.3.14

# 数据库工具
pgcli==4.0.1
redis-cli==2.0.2

# HTTP客户端（测试用）
httpx==0.25.2
requests==2.31.0

# 调试工具
flask-debugtoolbar==0.13.1
werkzeug==3.0.1

# 日志分析
loguru==0.7.2

# 环境管理
python-dotenv==1.0.0

# 类型检查支持
types-redis==********
types-requests==*********
types-python-dateutil==*********

# 开发服务器
uvicorn[standard]==0.24.0

# 代码覆盖率
coverage==7.3.2

# 性能分析
cProfile==0.1
snakeviz==2.2.0

# API文档
swagger-ui-bundle==0.0.9
redoc==2.1.0

# 开发辅助工具
rich==13.7.0
click==8.1.7
typer==0.9.0

# 数据生成（测试用）
faker==20.1.0
factory-boy==3.3.0

# 时间处理
freezegun==1.2.2

# 配置管理
pydantic-settings==2.1.0

# 异步测试
aiofiles==23.2.1
aioredis==2.0.1

# 开发环境监控
psutil==5.9.6
py-cpuinfo==9.0.0

# 代码格式化
autopep8==2.0.4
yapf==0.40.2

# 依赖分析
pipdeptree==2.13.1
pip-audit==2.6.1

# 开发环境邮件测试
aiosmtpd==1.4.4.post2

# 开发环境SSL证书生成
cryptography==41.0.7

# 开发环境数据库迁移
alembic==1.13.0

# 开发环境任务队列
celery[redis]==5.3.4
flower==2.0.1

# 开发环境文件监控
watchfiles==0.21.0

# 开发环境配置验证
pydantic==2.5.0

# 开发环境日志
structlog==23.2.0

# 开发环境缓存
redis==5.0.1

# 开发环境数据库
psycopg2-binary==2.9.9
SQLAlchemy==2.0.23

# 开发环境Web框架
fastapi==0.104.1
starlette==0.27.0

# 开发环境模板引擎
jinja2==3.1.2

# 开发环境静态文件
aiofiles==23.2.1

# 开发环境中间件
python-multipart==0.0.6

# 开发环境认证
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# 开发环境CORS
fastapi-cors==0.0.6

# 开发环境限流
slowapi==0.1.9

# 开发环境监控
prometheus-client==0.19.0

# 开发环境健康检查
healthcheck==1.3.3
