# AI法律助手 - 数据安全与合规系统

## 概述

本文档描述了AI法律助手项目中实现的数据安全与合规系统，该系统提供了全面的数据保护、隐私合规、安全监控和法律风险管理功能。

## 系统架构

### 核心组件

1. **数据加密系统** (`app/core/encryption.py`)
   - 字段级数据加密
   - 敏感数据脱敏
   - 密钥管理

2. **审计日志系统** (`app/core/audit.py`)
   - 用户操作审计
   - 系统访问日志
   - 安全事件记录

3. **异常检测系统** (`app/core/anomaly_detection.py`)
   - 行为模式分析
   - 异常行为检测
   - 安全告警管理

4. **隐私保护系统** (`app/core/privacy.py`)
   - 用户同意管理
   - 数据保留策略
   - 数据匿名化

5. **合规管理系统** (`app/core/compliance.py`)
   - 数据来源合规审查
   - 使用协议管理
   - 版权合规检查

6. **免责声明系统** (`app/core/disclaimer.py`)
   - AI回答免责声明
   - 用户协议管理
   - 法律风险提示

## 功能特性

### 数据加密

#### 字段级加密
```python
from app.core.encryption import get_encryption_manager

# 获取加密管理器
encryption_manager = get_encryption_manager()

# 加密敏感数据
encrypted_email = encryption_manager.encrypt_field("<EMAIL>")

# 解密数据
decrypted_email = encryption_manager.decrypt_field(encrypted_email)
```

#### 加密字段类型
```python
from app.core.encrypted_types import EncryptedEmail, EncryptedPhone, EncryptedName

# 在SQLAlchemy模型中使用
class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID, primary_key=True)
    email = Column(EncryptedEmail())  # 自动加密/解密
    phone = Column(EncryptedPhone())
    full_name = Column(EncryptedName())
```

#### 数据脱敏
```python
from app.core.encryption import DataMasking

# 脱敏邮箱
masked_email = DataMasking.mask_email("<EMAIL>")  # u**<EMAIL>

# 脱敏手机号
masked_phone = DataMasking.mask_phone("13812345678")  # 138****5678

# 脱敏姓名
masked_name = DataMasking.mask_name("张三")  # 张*
```

### 审计日志

#### 记录用户操作
```python
from app.core.audit import get_audit_logger

audit_logger = get_audit_logger(db_session)

# 记录登录尝试
audit_logger.log_login_attempt(
    username="testuser",
    success=True,
    request=request,
    user_id="user-id"
)

# 记录数据访问
audit_logger.log_data_access(
    user_id="user-id",
    resource_type="CONTRACT",
    resource_id="contract-id",
    action="READ",
    request=request
)
```

#### 审计中间件
```python
from app.middleware.audit_middleware import create_audit_middleware

# 在FastAPI应用中添加审计中间件
app.add_middleware(
    create_audit_middleware(
        log_request_body=True,
        log_response_body=False,
        enable_anomaly_detection=True
    )
)
```

### 异常检测

#### 行为分析
```python
from app.core.anomaly_detection import get_anomaly_detector

anomaly_detector = get_anomaly_detector(db_session)

# 分析用户行为
anomalies = anomaly_detector.analyze_user_behavior(
    user_id="user-id",
    ip_address="*************",
    time_window_hours=24
)

# 创建安全告警
for anomaly in anomalies:
    alert = anomaly_detector.create_security_alert(
        anomaly=anomaly,
        user_id="user-id"
    )
```

### 隐私保护

#### 用户同意管理
```python
from app.core.privacy import get_privacy_manager, ConsentType

privacy_manager = get_privacy_manager(db_session)

# 记录用户同意
consent = privacy_manager.record_consent(
    user_id="user-id",
    consent_type=ConsentType.PRIVACY_POLICY,
    consented=True,
    ip_address="*************"
)

# 检查是否需要重新获取同意
needs_consent = privacy_manager.check_consent_required(
    user_id="user-id",
    consent_type=ConsentType.PRIVACY_POLICY,
    current_version="2.0"
)
```

#### 数据匿名化
```python
# 匿名化用户数据
anonymized_data = privacy_manager.anonymize_user_data(
    user_id="user-id",
    fields_to_anonymize=["email", "phone", "name"]
)

# 导出用户数据（GDPR权利）
user_data_export = privacy_manager.export_user_data("user-id")

# 删除用户数据（被遗忘权）
privacy_manager.delete_user_data("user-id", hard_delete=False)
```

### 合规管理

#### 数据源注册
```python
from app.core.compliance import get_compliance_manager, DataSourceType, LicenseType

compliance_manager = get_compliance_manager(db_session)

# 注册数据源
data_source = compliance_manager.register_data_source(
    source_name="法律数据库",
    source_type=DataSourceType.LEGAL_DATABASE,
    license_type=LicenseType.COMMERCIAL_LICENSE,
    commercial_use_allowed=True,
    provider="数据提供商"
)

# 检查合规性
compliance_result = compliance_manager.check_data_source_compliance(
    data_source_id=str(data_source.id),
    intended_use="commercial"
)
```

### 免责声明

#### 创建免责声明
```python
from app.core.disclaimer import get_disclaimer_manager, DisclaimerType

disclaimer_manager = get_disclaimer_manager(db_session)

# 创建AI回答免责声明
disclaimer = disclaimer_manager.create_disclaimer_template(
    template_name="AI回答免责声明",
    disclaimer_type=DisclaimerType.AI_RESPONSE,
    title="AI助手免责声明",
    content="AI提供的信息仅供参考，不构成法律建议"
)

# 获取免责声明
ai_disclaimer = disclaimer_manager.get_ai_response_disclaimer()
legal_warning = disclaimer_manager.get_legal_risk_warning()
```

#### 免责声明中间件
```python
from app.middleware.disclaimer_middleware import create_disclaimer_middleware

# 自动为AI回答添加免责声明
app.add_middleware(
    create_disclaimer_middleware(
        ai_paths=['/api/v1/ai/chat', '/api/v1/ai/analyze'],
        disclaimer_position='footer',
        add_disclaimer_header=True
    )
)
```

## 安全配置

### 环境变量配置
```bash
# 数据加密配置
ENCRYPTION_MASTER_KEY=your-super-secret-master-key
ENCRYPTION_KEY=your-encryption-key

# HTTPS和SSL配置
FORCE_HTTPS=true
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/private.key

# 速率限制配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 审计日志配置
AUDIT_LOG_ENABLED=true
AUDIT_LOG_LEVEL=INFO

# 数据脱敏配置
DATA_MASKING_ENABLED=true
MASK_EMAIL=true
MASK_PHONE=true
MASK_ID_CARD=true
```

### 安全中间件配置
```python
from app.core.security import setup_security_middleware, SecurityConfig

# 配置安全中间件
security_config = SecurityConfig()
setup_security_middleware(app, security_config)
```

## API接口

### 审计日志API
- `GET /api/v1/audit/logs` - 获取审计日志列表
- `GET /api/v1/audit/logs/{log_id}` - 获取单个审计日志
- `GET /api/v1/audit/alerts` - 获取安全告警列表
- `POST /api/v1/audit/analyze-behavior` - 分析用户行为

### 合规管理API
- `POST /api/v1/compliance/data-sources` - 注册数据源
- `GET /api/v1/compliance/data-sources` - 获取数据源列表
- `POST /api/v1/compliance/data-sources/{id}/check-compliance` - 检查合规性
- `GET /api/v1/compliance/compliance-report` - 获取合规报告

### 免责声明API
- `POST /api/v1/disclaimer/disclaimer-templates` - 创建免责声明模板
- `GET /api/v1/disclaimer/ai-disclaimer` - 获取AI免责声明
- `GET /api/v1/disclaimer/legal-warning` - 获取法律风险提示
- `GET /api/v1/disclaimer/active-disclaimers` - 获取激活的免责声明

## 数据库架构

### 主要数据表

1. **audit_logs** - 审计日志表
2. **security_alerts** - 安全告警表
3. **user_consents** - 用户同意记录表
4. **data_retention_policies** - 数据保留策略表
5. **data_sources** - 数据源表
6. **data_usage_agreements** - 数据使用协议表
7. **disclaimer_templates** - 免责声明模板表
8. **user_agreements** - 用户协议表

### 数据库迁移
```bash
# 运行数据库迁移
alembic upgrade head

# 创建新的迁移
alembic revision --autogenerate -m "Add security tables"
```

## 测试

### 运行测试
```bash
# 运行所有安全合规相关测试
pytest backend/tests/test_encryption.py
pytest backend/tests/test_audit_system.py
pytest backend/tests/test_data_security_compliance.py
pytest backend/tests/test_disclaimer_system.py
pytest backend/tests/test_security_compliance_integration.py

# 运行集成测试
pytest backend/tests/test_security_compliance_integration.py -v
```

### 测试覆盖率
```bash
# 生成测试覆盖率报告
pytest --cov=app.core --cov=app.middleware --cov-report=html
```

## 部署注意事项

### 生产环境配置

1. **加密密钥管理**
   - 使用强随机密钥
   - 定期轮换密钥
   - 使用密钥管理服务（如AWS KMS）

2. **数据库安全**
   - 启用SSL连接
   - 配置防火墙规则
   - 定期备份和加密

3. **日志管理**
   - 配置日志轮转
   - 设置日志保留期限
   - 监控日志异常

4. **监控告警**
   - 配置安全事件告警
   - 设置性能监控
   - 建立事件响应流程

## 合规认证

本系统支持以下合规标准：

- **GDPR** - 欧盟通用数据保护条例
- **CCPA** - 加州消费者隐私法案
- **等保2.0** - 网络安全等级保护2.0
- **ISO 27001** - 信息安全管理体系

## 联系方式

如有问题或建议，请联系开发团队：
- 邮箱：<EMAIL>
- 文档：https://docs.yourdomain.com/security
- 问题反馈：https://github.com/yourorg/ai-legal-assistant/issues
