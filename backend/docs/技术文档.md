# AI法律助手 - 技术文档

## 目录

1. [系统架构](#系统架构)
2. [技术栈](#技术栈)
3. [API文档](#api文档)
4. [数据库设计](#数据库设计)
5. [部署指南](#部署指南)
6. [开发指南](#开发指南)
7. [监控运维](#监控运维)

## 系统架构

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   负载均衡器    │    │   API网关       │
│   (React/Vue)   │◄──►│   (Nginx)       │◄──►│   (FastAPI)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────────────────────┼─────────────────────────────────┐
                       │                                 │                                 │
                ┌─────────────┐                   ┌─────────────┐                 ┌─────────────┐
                │  业务服务   │                   │  数据服务   │                 │  AI服务     │
                │  层         │                   │  层         │                 │  层         │
                └─────────────┘                   └─────────────┘                 └─────────────┘
                       │                                 │                                 │
        ┌──────────────┼──────────────┐                 │                                 │
        │              │              │                 │                                 │
┌─────────────┐ ┌─────────────┐ ┌─────────────┐  ┌─────────────┐                 ┌─────────────┐
│  用户管理   │ │  合同分析   │ │  案例检索   │  │ PostgreSQL  │                 │  OpenAI     │
│  服务       │ │  服务       │ │  服务       │  │  数据库     │                 │  API        │
└─────────────┘ └─────────────┘ └─────────────┘  └─────────────┘                 └─────────────┘
                                                        │
                                                 ┌─────────────┐
                                                 │   Redis     │
                                                 │   缓存      │
                                                 └─────────────┘
```

### 微服务架构

- **API网关**：统一入口，路由分发，认证授权
- **用户服务**：用户管理、认证、权限控制
- **AI服务**：智能问答、文档分析、合同审查
- **数据服务**：数据存储、检索、分析
- **监控服务**：系统监控、日志收集、告警

### 安全架构

- **网络安全**：HTTPS/TLS加密、防火墙、VPN
- **应用安全**：JWT认证、RBAC权限、API限流
- **数据安全**：字段级加密、数据脱敏、审计日志
- **基础设施安全**：容器安全、密钥管理、安全扫描

## 技术栈

### 后端技术

| 组件 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| Web框架 | FastAPI | 0.104+ | 高性能异步Web框架 |
| 数据库 | PostgreSQL | 15+ | 关系型数据库 |
| 缓存 | Redis | 7+ | 内存数据库 |
| 搜索引擎 | Elasticsearch | 8.11+ | 全文搜索引擎 |
| 消息队列 | Celery + Redis | 5.3+ | 异步任务处理 |
| ORM | SQLAlchemy | 2.0+ | 数据库ORM |
| 认证 | JWT | - | 无状态认证 |
| 加密 | Cryptography | 41+ | 数据加密库 |

### 前端技术

| 组件 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| 框架 | React | 18+ | 用户界面框架 |
| 状态管理 | Redux Toolkit | 1.9+ | 状态管理 |
| UI组件 | Ant Design | 5+ | UI组件库 |
| 路由 | React Router | 6+ | 前端路由 |
| HTTP客户端 | Axios | 1.5+ | HTTP请求库 |
| 构建工具 | Vite | 4+ | 前端构建工具 |

### 基础设施

| 组件 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| 容器化 | Docker | 24+ | 应用容器化 |
| 编排 | Kubernetes | 1.28+ | 容器编排 |
| 服务网格 | Istio | 1.19+ | 微服务治理 |
| 监控 | Prometheus | 2.47+ | 指标监控 |
| 日志 | ELK Stack | 8.11+ | 日志收集分析 |
| CI/CD | GitHub Actions | - | 持续集成部署 |

## API文档

### 认证接口

#### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "张三",
  "phone": "13812345678"
}
```

#### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### AI咨询接口

#### 发送消息
```http
POST /api/v1/ai/chat
Authorization: Bearer <token>
Content-Type: application/json

{
  "message": "请问劳动合同的试用期规定是什么？",
  "context": "劳动法咨询"
}
```

#### 获取对话历史
```http
GET /api/v1/ai/chat/history?limit=20&offset=0
Authorization: Bearer <token>
```

### 合同分析接口

#### 上传合同
```http
POST /api/v1/contracts/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <contract.pdf>
contract_type: "劳动合同"
```

#### 获取分析结果
```http
GET /api/v1/contracts/{contract_id}/analysis
Authorization: Bearer <token>
```

### 案例检索接口

#### 搜索案例
```http
GET /api/v1/cases/search?q=劳动纠纷&limit=10&offset=0
Authorization: Bearer <token>
```

#### 获取案例详情
```http
GET /api/v1/cases/{case_id}
Authorization: Bearer <token>
```

### 监控接口

#### 系统健康检查
```http
GET /health
```

#### 系统指标
```http
GET /metrics
```

## 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    user_type VARCHAR(20) DEFAULT 'regular',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(50) NOT NULL,
    level VARCHAR(20) NOT NULL,
    user_id UUID REFERENCES users(id),
    user_ip INET,
    user_agent TEXT,
    resource_type VARCHAR(50),
    resource_id VARCHAR(100),
    action VARCHAR(50),
    message TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    request_id VARCHAR(50),
    session_id VARCHAR(100),
    success BOOLEAN DEFAULT true,
    error_code VARCHAR(20),
    error_message TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 数据源表 (data_sources)
```sql
CREATE TABLE data_sources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_name VARCHAR(200) NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    source_url VARCHAR(500),
    provider VARCHAR(200),
    license_type VARCHAR(50) NOT NULL,
    commercial_use_allowed BOOLEAN DEFAULT false,
    compliance_status VARCHAR(50) NOT NULL,
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 索引优化

```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 审计日志索引
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_event_type ON audit_logs(event_type);

-- 数据源索引
CREATE INDEX idx_data_sources_type ON data_sources(source_type);
CREATE INDEX idx_data_sources_status ON data_sources(compliance_status);
```

## 部署指南

### 开发环境部署

1. **克隆代码**
```bash
git clone https://github.com/yourorg/ai-legal-assistant.git
cd ai-legal-assistant
```

2. **安装依赖**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env.development
# 编辑 .env.development 文件
```

4. **启动服务**
```bash
# 启动数据库和Redis
docker-compose -f docker-compose.dev.yml up -d db redis

# 运行数据库迁移
alembic upgrade head

# 启动应用
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 生产环境部署

1. **使用Docker Compose**
```bash
# 配置生产环境变量
cp .env.production.template .env.production
# 编辑配置文件

# 启动所有服务
docker-compose -f docker-compose.security.yml up -d
```

2. **使用Kubernetes**
```bash
# 创建命名空间和密钥
kubectl create namespace ai-legal-prod
kubectl create secret generic ai-legal-secrets --from-env-file=.env.production

# 部署应用
kubectl apply -f k8s/production/
```

3. **使用部署脚本**
```bash
./scripts/deploy.sh production v1.0.0
```

## 开发指南

### 代码规范

1. **Python代码规范**
   - 使用Black进行代码格式化
   - 使用isort进行导入排序
   - 使用flake8进行代码检查
   - 使用mypy进行类型检查

2. **提交规范**
   - 使用Conventional Commits规范
   - 格式：`type(scope): description`
   - 类型：feat, fix, docs, style, refactor, test, chore

3. **分支策略**
   - main：生产环境分支
   - develop：开发环境分支
   - feature/*：功能开发分支
   - hotfix/*：紧急修复分支

### 测试指南

1. **单元测试**
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_auth.py

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

2. **集成测试**
```bash
# 运行集成测试
pytest tests/integration/

# 运行API测试
pytest tests/api/
```

3. **性能测试**
```bash
# 使用k6进行负载测试
k6 run performance-tests/load-test.js
```

### 调试指南

1. **日志配置**
```python
import logging

# 配置日志级别
logging.basicConfig(level=logging.INFO)

# 使用结构化日志
import structlog
logger = structlog.get_logger()
logger.info("用户登录", user_id="123", ip="***********")
```

2. **性能分析**
```python
# 使用性能分析装饰器
from app.core.performance import performance_timer

@performance_timer
async def slow_function():
    # 耗时操作
    pass
```

## 监控运维

### 监控指标

1. **系统指标**
   - CPU使用率
   - 内存使用率
   - 磁盘使用率
   - 网络I/O

2. **应用指标**
   - 请求响应时间
   - 请求成功率
   - 并发用户数
   - 错误率

3. **业务指标**
   - 用户注册数
   - 活跃用户数
   - API调用次数
   - 合同分析次数

### 告警配置

1. **Prometheus告警规则**
```yaml
groups:
- name: ai-legal-assistant
  rules:
  - alert: HighCPUUsage
    expr: cpu_usage_percent > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CPU使用率过高"
      description: "CPU使用率超过80%"
```

2. **告警通知**
   - 邮件通知
   - Slack通知
   - 短信通知（紧急情况）

### 日志管理

1. **日志收集**
   - 使用Filebeat收集应用日志
   - 使用Logstash处理日志
   - 使用Elasticsearch存储日志

2. **日志分析**
   - 使用Kibana进行日志可视化
   - 设置日志告警规则
   - 定期清理过期日志

### 备份策略

1. **数据库备份**
   - 每日全量备份
   - 每小时增量备份
   - 异地备份存储

2. **配置备份**
   - Kubernetes配置备份
   - 应用配置备份
   - 证书文件备份

---

**文档版本**：v1.0.0  
**更新日期**：2024年8月26日  
**维护团队**：AI法律助手开发团队
