# AI法律助手 - 安全部署指南

## 概述

本指南提供了AI法律助手系统的安全部署步骤和最佳实践，确保系统在生产环境中的安全性和合规性。

## 部署前准备

### 1. 系统要求

**最低硬件要求：**
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 100GB SSD
- 网络: 1Gbps

**推荐硬件配置：**
- CPU: 8核心
- 内存: 16GB RAM
- 存储: 500GB SSD
- 网络: 10Gbps

**操作系统：**
- Ubuntu 20.04 LTS 或更高版本
- CentOS 8 或更高版本
- RHEL 8 或更高版本

### 2. 软件依赖

```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. 防火墙配置

```bash
# 配置UFW防火墙
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow from 10.0.0.0/8 to any port 5432  # PostgreSQL (内网)
sudo ufw allow from 10.0.0.0/8 to any port 6379  # Redis (内网)
```

## SSL证书配置

### 1. 生成SSL证书

**使用Let's Encrypt（推荐）：**
```bash
# 安装Certbot
sudo apt-get update
sudo apt-get install certbot

# 生成证书
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# 证书文件位置
# /etc/letsencrypt/live/yourdomain.com/fullchain.pem
# /etc/letsencrypt/live/yourdomain.com/privkey.pem
```

**使用自签名证书（开发环境）：**
```bash
# 创建证书目录
mkdir -p certs

# 生成私钥
openssl genrsa -out certs/private.key 2048

# 生成证书签名请求
openssl req -new -key certs/private.key -out certs/cert.csr

# 生成自签名证书
openssl x509 -req -days 365 -in certs/cert.csr -signkey certs/private.key -out certs/cert.pem
```

### 2. 数据库SSL配置

```bash
# 生成数据库SSL证书
mkdir -p db-certs

# 生成CA私钥
openssl genrsa -out db-certs/ca.key 2048

# 生成CA证书
openssl req -new -x509 -days 365 -key db-certs/ca.key -out db-certs/ca.crt

# 生成服务器私钥
openssl genrsa -out db-certs/server.key 2048

# 生成服务器证书签名请求
openssl req -new -key db-certs/server.key -out db-certs/server.csr

# 生成服务器证书
openssl x509 -req -days 365 -in db-certs/server.csr -CA db-certs/ca.crt -CAkey db-certs/ca.key -CAcreateserial -out db-certs/server.crt

# 设置权限
chmod 600 db-certs/server.key
chmod 644 db-certs/server.crt db-certs/ca.crt
```

## 环境配置

### 1. 创建生产环境配置

```bash
# 复制环境变量模板
cp .env.production.template .env.production

# 编辑配置文件
nano .env.production
```

**重要配置项：**
```bash
# 数据库密码（必须更改）
POSTGRES_PASSWORD=your-super-secure-database-password

# 加密密钥（必须更改，至少32字符）
ENCRYPTION_MASTER_KEY=your-super-secret-master-key-minimum-32-characters

# JWT密钥（必须更改，至少32字符）
SECRET_KEY=your-super-secret-jwt-key-minimum-32-characters

# Redis密码（必须更改）
REDIS_PASSWORD=your-redis-password

# 域名配置
TRUSTED_HOSTS=yourdomain.com,*.yourdomain.com
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# 告警邮箱
ALERT_EMAIL=<EMAIL>
```

### 2. 创建必要目录

```bash
# 创建目录结构
mkdir -p logs/{nginx,app,audit}
mkdir -p backups
mkdir -p monitoring
mkdir -p nginx/conf.d
mkdir -p backup-scripts
```

## 部署步骤

### 1. 克隆代码仓库

```bash
git clone https://github.com/yourorg/ai-legal-assistant.git
cd ai-legal-assistant/backend
```

### 2. 构建和启动服务

```bash
# 构建镜像
docker-compose -f docker-compose.security.yml build

# 启动服务
docker-compose -f docker-compose.security.yml up -d

# 检查服务状态
docker-compose -f docker-compose.security.yml ps
```

### 3. 数据库初始化

```bash
# 运行数据库迁移
docker-compose -f docker-compose.security.yml exec app alembic upgrade head

# 创建管理员用户
docker-compose -f docker-compose.security.yml exec app python -c "
from app.core.auth import create_admin_user
create_admin_user('<EMAIL>', 'secure-admin-password')
"

# 初始化默认数据
docker-compose -f docker-compose.security.yml exec app python -c "
from app.core.disclaimer import init_default_disclaimers
from app.core.privacy import init_default_retention_policies
init_default_disclaimers()
init_default_retention_policies()
"
```

### 4. 验证部署

```bash
# 检查应用健康状态
curl -k https://yourdomain.com/health

# 检查数据库连接
docker-compose -f docker-compose.security.yml exec app python -c "
from app.core.database import engine
with engine.connect() as conn:
    result = conn.execute('SELECT 1')
    print('数据库连接正常')
"

# 检查加密功能
docker-compose -f docker-compose.security.yml exec app python -c "
from app.core.encryption import get_encryption_manager
manager = get_encryption_manager()
test_data = '测试数据'
encrypted = manager.encrypt_field(test_data)
decrypted = manager.decrypt_field(encrypted)
assert test_data == decrypted
print('加密功能正常')
"
```

## 安全配置

### 1. Nginx安全配置

创建 `nginx/conf.d/security.conf`：
```nginx
# 安全头配置
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;" always;

# HTTPS重定向
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS配置
server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/nginx/certs/cert.pem;
    ssl_certificate_key /etc/nginx/certs/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    location / {
        proxy_pass http://app:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. 监控配置

创建 `monitoring/prometheus.yml`：
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-legal-assistant'
    static_configs:
      - targets: ['app:8000']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['db:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
```

### 3. 备份脚本

创建 `backup-scripts/backup.sh`：
```bash
#!/bin/bash

# 数据库备份
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_BACKUP_FILE="$BACKUP_DIR/db_backup_$DATE.sql"

# 创建数据库备份
pg_dump -h db -U $POSTGRES_USER -d $POSTGRES_DB > $DB_BACKUP_FILE

# 加密备份文件
if [ "$BACKUP_ENCRYPTION_ENABLED" = "true" ]; then
    openssl enc -aes-256-cbc -salt -in $DB_BACKUP_FILE -out $DB_BACKUP_FILE.enc -k $BACKUP_ENCRYPTION_KEY
    rm $DB_BACKUP_FILE
fi

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "db_backup_*.sql*" -mtime +30 -delete

echo "备份完成: $DB_BACKUP_FILE"
```

## 监控和告警

### 1. 健康检查

```bash
# 创建健康检查脚本
cat > health-check.sh << 'EOF'
#!/bin/bash

# 检查应用状态
if ! curl -f -s https://yourdomain.com/health > /dev/null; then
    echo "应用健康检查失败" | mail -s "AI法律助手告警" <EMAIL>
fi

# 检查数据库状态
if ! docker-compose -f docker-compose.security.yml exec -T db pg_isready -U $POSTGRES_USER > /dev/null; then
    echo "数据库健康检查失败" | mail -s "AI法律助手告警" <EMAIL>
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "磁盘空间不足: ${DISK_USAGE}%" | mail -s "AI法律助手告警" <EMAIL>
fi
EOF

chmod +x health-check.sh

# 添加到crontab
echo "*/5 * * * * /path/to/health-check.sh" | crontab -
```

### 2. 日志监控

```bash
# 监控错误日志
tail -f logs/app/app.log | grep -i error

# 监控审计日志
tail -f logs/audit/audit.log

# 监控安全告警
docker-compose -f docker-compose.security.yml logs -f app | grep -i "security\|alert\|anomaly"
```

## 维护和更新

### 1. 定期维护任务

```bash
# 每日任务
0 2 * * * /path/to/backup.sh
0 3 * * * docker system prune -f
0 4 * * * /path/to/health-check.sh

# 每周任务
0 1 * * 0 docker-compose -f docker-compose.security.yml restart

# 每月任务
0 2 1 * * /path/to/security-audit.sh
```

### 2. 更新流程

```bash
# 1. 备份当前系统
./backup.sh

# 2. 拉取最新代码
git pull origin main

# 3. 构建新镜像
docker-compose -f docker-compose.security.yml build

# 4. 滚动更新
docker-compose -f docker-compose.security.yml up -d --no-deps app

# 5. 运行数据库迁移
docker-compose -f docker-compose.security.yml exec app alembic upgrade head

# 6. 验证更新
curl -k https://yourdomain.com/health
```

## 故障排除

### 1. 常见问题

**应用无法启动：**
```bash
# 检查日志
docker-compose -f docker-compose.security.yml logs app

# 检查配置
docker-compose -f docker-compose.security.yml config
```

**数据库连接失败：**
```bash
# 检查数据库状态
docker-compose -f docker-compose.security.yml exec db pg_isready

# 检查网络连接
docker-compose -f docker-compose.security.yml exec app ping db
```

**SSL证书问题：**
```bash
# 检查证书有效期
openssl x509 -in certs/cert.pem -text -noout | grep "Not After"

# 更新Let's Encrypt证书
sudo certbot renew
```

### 2. 紧急恢复

```bash
# 从备份恢复数据库
docker-compose -f docker-compose.security.yml exec -T db psql -U $POSTGRES_USER -d $POSTGRES_DB < /backups/db_backup_latest.sql

# 重启所有服务
docker-compose -f docker-compose.security.yml restart

# 检查系统状态
docker-compose -f docker-compose.security.yml ps
```

## 安全审计

### 1. 定期审计检查

- 检查用户权限和访问日志
- 审查安全配置和证书有效期
- 验证备份和恢复流程
- 检查系统漏洞和更新
- 审计第三方依赖安全性

### 2. 合规性检查

- GDPR合规性审计
- 数据保留策略执行
- 用户同意记录完整性
- 数据加密和脱敏验证
- 审计日志完整性检查

## 联系方式

如有部署问题或安全疑虑，请联系：
- 技术支持：<EMAIL>
- 安全团队：<EMAIL>
- 紧急联系：<EMAIL>
