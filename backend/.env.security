# 数据加密配置
ENCRYPTION_MASTER_KEY=your-super-secret-master-key-change-this-in-production
ENCRYPTION_KEY=your-encryption-key-for-data-encryption

# HTTPS和SSL配置
FORCE_HTTPS=false
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/private.key

# 信任的主机配置
TRUSTED_HOSTS=localhost,127.0.0.1,*.localhost,*.yourdomain.com

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://yourdomain.com
CORS_ALLOW_CREDENTIALS=true

# 速率限制配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 安全头配置
SECURITY_HEADERS_ENABLED=true

# 压缩配置
GZIP_ENABLED=true
GZIP_MINIMUM_SIZE=1000

# 数据库连接加密
DATABASE_SSL_MODE=require
DATABASE_SSL_CERT=/path/to/client-cert.pem
DATABASE_SSL_KEY=/path/to/client-key.pem
DATABASE_SSL_ROOT_CERT=/path/to/ca-cert.pem

# Redis连接加密
REDIS_SSL=true
REDIS_SSL_CERT_REQS=required
REDIS_SSL_CA_CERTS=/path/to/ca-cert.pem

# 会话安全配置
SESSION_SECURE=true
SESSION_HTTPONLY=true
SESSION_SAMESITE=strict

# JWT安全配置
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=30

# 密码策略配置
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_DIGITS=true
PASSWORD_REQUIRE_SPECIAL_CHARS=true

# 审计日志配置
AUDIT_LOG_ENABLED=true
AUDIT_LOG_LEVEL=INFO
AUDIT_LOG_FILE=/var/log/ai-legal-assistant/audit.log

# 数据脱敏配置
DATA_MASKING_ENABLED=true
MASK_EMAIL=true
MASK_PHONE=true
MASK_ID_CARD=true
MASK_BANK_CARD=true

# 备份加密配置
BACKUP_ENCRYPTION_ENABLED=true
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# 文件上传安全配置
MAX_FILE_SIZE=********  # 10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png
SCAN_UPLOADED_FILES=true

# API安全配置
API_KEY_REQUIRED=false
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=3600

# 监控和告警配置
SECURITY_MONITORING_ENABLED=true
FAILED_LOGIN_THRESHOLD=5
FAILED_LOGIN_WINDOW=300
ALERT_EMAIL=<EMAIL>

# 数据保留策略
DATA_RETENTION_DAYS=2555  # 7年
LOG_RETENTION_DAYS=365    # 1年
SESSION_RETENTION_DAYS=30 # 30天

# 合规性配置
GDPR_COMPLIANCE=true
CCPA_COMPLIANCE=true
DATA_ANONYMIZATION=true

# 开发环境安全配置（仅开发环境使用）
DEBUG_MODE=false
ALLOW_HTTP_IN_DEV=true
DISABLE_CSRF_IN_DEV=false
