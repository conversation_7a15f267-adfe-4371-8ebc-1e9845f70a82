version: '3.8'

services:
  # 开发环境应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ai-legal-assistant-dev
    restart: unless-stopped
    environment:
      # 基础配置
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      
      # 数据库配置
      - DATABASE_URL=************************************************/ai_legal_assistant_dev
      - DATABASE_SSL_MODE=disable
      
      # Redis配置
      - REDIS_URL=redis://redis:6379/0
      
      # 加密配置（开发环境使用固定密钥）
      - ENCRYPTION_MASTER_KEY=dev-encryption-key-32-characters-long
      - ENCRYPTION_KEY=dev-encryption-key-for-development
      
      # JWT配置
      - SECRET_KEY=dev-secret-key-for-development-only
      - JWT_ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=60
      
      # 安全配置（开发环境宽松设置）
      - FORCE_HTTPS=false
      - TRUSTED_HOSTS=localhost,127.0.0.1,0.0.0.0
      - CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
      - CORS_ALLOW_CREDENTIALS=true
      
      # 速率限制（开发环境宽松）
      - RATE_LIMIT_ENABLED=false
      - RATE_LIMIT_REQUESTS=1000
      - RATE_LIMIT_WINDOW=60
      
      # 审计日志
      - AUDIT_LOG_ENABLED=true
      - AUDIT_LOG_LEVEL=DEBUG
      
      # 数据脱敏（开发环境关闭）
      - DATA_MASKING_ENABLED=false
      
      # 监控配置
      - SECURITY_MONITORING_ENABLED=true
      - FAILED_LOGIN_THRESHOLD=10
      
      # 消息队列配置
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      
      # 邮件配置（开发环境使用MailHog）
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
      - SMTP_USERNAME=
      - SMTP_PASSWORD=
      - SMTP_USE_TLS=false
      - SMTP_FROM_EMAIL=dev@localhost
      
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
      - mailhog
    networks:
      - ai-legal-dev-network
    command: >
      sh -c "
        echo '等待数据库启动...' &&
        sleep 10 &&
        echo '运行数据库迁移...' &&
        alembic upgrade head &&
        echo '启动开发服务器...' &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "

  # PostgreSQL数据库（开发环境）
  db:
    image: postgres:15-alpine
    container_name: ai-legal-assistant-db-dev
    restart: unless-stopped
    environment:
      - POSTGRES_DB=ai_legal_assistant_dev
      - POSTGRES_USER=ai_legal_user
      - POSTGRES_PASSWORD=ai_legal_pass
      - POSTGRES_INITDB_ARGS=--auth-host=md5
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./db-init-dev:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ai-legal-dev-network
    command: >
      postgres
      -c log_statement=all
      -c log_destination=stderr
      -c logging_collector=off
      -c max_connections=100

  # Redis缓存（开发环境）
  redis:
    image: redis:7-alpine
    container_name: ai-legal-assistant-redis-dev
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - ai-legal-dev-network

  # Celery Worker（开发环境）
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ai-legal-assistant-celery-dev
    restart: unless-stopped
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=************************************************/ai_legal_assistant_dev
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - ENCRYPTION_MASTER_KEY=dev-encryption-key-32-characters-long
      - SECRET_KEY=dev-secret-key-for-development-only
    volumes:
      - .:/app
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    networks:
      - ai-legal-dev-network
    command: >
      sh -c "
        echo '等待Redis启动...' &&
        sleep 5 &&
        echo '启动Celery Worker...' &&
        celery -A app.core.message_queue.celery_app worker --loglevel=info --concurrency=2
      "

  # Celery Beat（定时任务调度）
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ai-legal-assistant-beat-dev
    restart: unless-stopped
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=************************************************/ai_legal_assistant_dev
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    networks:
      - ai-legal-dev-network
    command: >
      sh -c "
        echo '等待Redis启动...' &&
        sleep 5 &&
        echo '启动Celery Beat...' &&
        celery -A app.core.message_queue.celery_app beat --loglevel=info
      "

  # Flower（Celery监控）
  flower:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ai-legal-assistant-flower-dev
    restart: unless-stopped
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - ai-legal-dev-network
    command: >
      sh -c "
        echo '等待Redis启动...' &&
        sleep 5 &&
        echo '启动Flower...' &&
        celery -A app.core.message_queue.celery_app flower --port=5555
      "

  # MailHog（开发环境邮件测试）
  mailhog:
    image: mailhog/mailhog:latest
    container_name: ai-legal-assistant-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP端口
      - "8025:8025"  # Web界面端口
    networks:
      - ai-legal-dev-network

  # pgAdmin（数据库管理工具）
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ai-legal-assistant-pgadmin-dev
    restart: unless-stopped
    environment:
      - PGADMIN_DEFAULT_EMAIL=admin@localhost
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    depends_on:
      - db
    networks:
      - ai-legal-dev-network

  # Redis Commander（Redis管理工具）
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ai-legal-assistant-redis-commander-dev
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - ai-legal-dev-network

  # 文档服务器（开发环境API文档）
  docs:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ai-legal-assistant-docs-dev
    restart: unless-stopped
    volumes:
      - ./docs:/app/docs
    ports:
      - "8080:8080"
    networks:
      - ai-legal-dev-network
    command: >
      sh -c "
        echo '启动文档服务器...' &&
        python -m http.server 8080 --directory /app/docs
      "

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local

networks:
  ai-legal-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
