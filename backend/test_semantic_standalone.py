#!/usr/bin/env python3
"""
独立的语义分析器测试
"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict, Counter
import math
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import TruncatedSVD
from sklearn.cluster import KMeans


class SimpleLegalSemanticAnalyzer:
    """简化的法律语义分析器"""
    
    def __init__(self):
        """初始化语义分析器"""
        self.tfidf_vectorizer = None
        self.legal_concepts = {
            "合同法": [
                "合同", "协议", "约定", "条款", "违约", "履行", "解除", "终止",
                "要约", "承诺", "违约金", "定金", "格式条款"
            ],
            "劳动法": [
                "劳动合同", "用人单位", "劳动者", "工资", "加班费", "社会保险",
                "工伤", "职业病", "劳动争议", "仲裁", "辞职", "辞退"
            ],
            "民法": [
                "民事权利", "民事义务", "民事责任", "人格权", "财产权",
                "物权", "债权", "侵权", "损害赔偿", "精神损害"
            ],
            "刑法": [
                "犯罪", "刑罚", "犯罪构成", "故意", "过失", "正当防卫",
                "紧急避险", "共同犯罪", "累犯", "自首", "立功"
            ]
        }
    
    def extract_legal_concepts(self, text: str) -> Dict[str, float]:
        """提取法律概念"""
        concepts = {}
        
        for concept_category, concept_words in self.legal_concepts.items():
            concept_score = 0
            for word in concept_words:
                if word in text:
                    weight = len(word) / 10.0 + 0.5
                    concept_score += weight
            
            if concept_score > 0:
                concepts[concept_category] = concept_score
        
        # 归一化
        if concepts:
            max_score = max(concepts.values())
            concepts = {k: v / max_score for k, v in concepts.items()}
        
        return concepts
    
    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """计算语义相似度"""
        # 基础Jaccard相似度
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        jaccard_sim = intersection / union if union > 0 else 0.0
        
        # 概念相似度
        concepts1 = self.extract_legal_concepts(text1)
        concepts2 = self.extract_legal_concepts(text2)
        
        concept_sim = 0.0
        if concepts1 and concepts2:
            common_concepts = set(concepts1.keys()).intersection(set(concepts2.keys()))
            total_concepts = set(concepts1.keys()).union(set(concepts2.keys()))
            concept_sim = len(common_concepts) / len(total_concepts) if total_concepts else 0.0
        
        # 加权平均
        return 0.7 * jaccard_sim + 0.3 * concept_sim
    
    def analyze_semantic_structure(self, text: str) -> Dict[str, Any]:
        """分析语义结构"""
        concepts = self.extract_legal_concepts(text)
        
        # 计算语义复杂度
        concept_count = len(concepts)
        sentences = re.split(r'[。！？；]', text)
        avg_sentence_length = sum(len(s) for s in sentences) / len(sentences) if sentences else 0
        
        words = text.split()
        unique_words = set(words)
        lexical_diversity = len(unique_words) / len(words) if words else 0
        
        complexity = min(
            concept_count * 0.4 +
            min(avg_sentence_length / 50, 1.0) * 0.3 +
            lexical_diversity * 0.3,
            1.0
        )
        
        # 概念分布
        distribution = {}
        if concepts:
            total_score = sum(concepts.values())
            distribution = {k: v / total_score for k, v in concepts.items()}
        
        return {
            "legal_concepts": concepts,
            "semantic_complexity": complexity,
            "concept_distribution": distribution,
            "lexical_diversity": lexical_diversity
        }
    
    def find_semantic_neighbors(self, text: str, candidate_texts: List[str], top_k: int = 5) -> List[Tuple[int, float]]:
        """查找语义相似的文本"""
        similarities = []
        
        for i, candidate in enumerate(candidate_texts):
            similarity = self.calculate_semantic_similarity(text, candidate)
            similarities.append((i, similarity))
        
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]


def test_concept_extraction():
    """测试概念提取"""
    analyzer = SimpleLegalSemanticAnalyzer()
    
    text = "根据劳动合同法规定，用人单位与劳动者签订劳动合同时，应当明确约定工资、工作时间等条款"
    concepts = analyzer.extract_legal_concepts(text)
    
    print(f"✓ 法律概念提取: {concepts}")
    return len(concepts) > 0


def test_semantic_similarity():
    """测试语义相似度"""
    analyzer = SimpleLegalSemanticAnalyzer()
    
    text1 = "劳动合同纠纷处理"
    text2 = "处理劳动争议问题"
    text3 = "房屋买卖合同"
    
    sim1_2 = analyzer.calculate_semantic_similarity(text1, text2)
    sim1_3 = analyzer.calculate_semantic_similarity(text1, text3)
    
    print(f"✓ 语义相似度计算:")
    print(f"  '{text1}' vs '{text2}': {sim1_2:.3f}")
    print(f"  '{text1}' vs '{text3}': {sim1_3:.3f}")
    
    return sim1_2 > sim1_3


def test_semantic_structure():
    """测试语义结构分析"""
    analyzer = SimpleLegalSemanticAnalyzer()
    
    text = """
    根据《劳动合同法》第三十九条规定，劳动者有下列情形之一的，用人单位可以解除劳动合同：
    （一）在试用期间被证明不符合录用条件的；
    （二）严重违反用人单位的规章制度的。
    """
    
    analysis = analyzer.analyze_semantic_structure(text)
    
    print(f"✓ 语义结构分析:")
    print(f"  法律概念: {analysis['legal_concepts']}")
    print(f"  语义复杂度: {analysis['semantic_complexity']:.3f}")
    print(f"  词汇多样性: {analysis['lexical_diversity']:.3f}")
    
    return analysis['semantic_complexity'] > 0


def test_semantic_neighbors():
    """测试语义邻居查找"""
    analyzer = SimpleLegalSemanticAnalyzer()
    
    query_text = "劳动合同违约金问题"
    candidate_texts = [
        "员工违反劳动合同约定，公司要求支付违约金",
        "房屋租赁合同纠纷案例分析",
        "劳动者提前解除合同，用人单位主张违约责任",
        "交通事故赔偿标准",
        "劳动争议仲裁程序"
    ]
    
    neighbors = analyzer.find_semantic_neighbors(query_text, candidate_texts, top_k=3)
    
    print(f"✓ 语义邻居查找:")
    for i, (idx, similarity) in enumerate(neighbors):
        print(f"  {i+1}. [{idx}] {candidate_texts[idx][:30]}... (相似度: {similarity:.3f})")
    
    return len(neighbors) > 0


def test_tfidf_integration():
    """测试TF-IDF集成"""
    try:
        texts = [
            "劳动合同是劳动者与用人单位确立劳动关系的协议",
            "用人单位应当按照劳动合同约定支付劳动报酬",
            "房屋买卖合同是出卖人转移房屋所有权的合同"
        ]
        
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform(texts)
        
        # 计算相似度矩阵
        similarity_matrix = cosine_similarity(tfidf_matrix)
        
        print(f"✓ TF-IDF集成测试成功")
        print(f"  相似度矩阵形状: {similarity_matrix.shape}")
        print(f"  前两个文本相似度: {similarity_matrix[0, 1]:.3f}")
        
        return True
    except Exception as e:
        print(f"✗ TF-IDF集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始独立语义分析器测试...")
    print("=" * 50)
    
    tests = [
        ("概念提取测试", test_concept_extraction),
        ("语义相似度测试", test_semantic_similarity),
        ("语义结构分析测试", test_semantic_structure),
        ("语义邻居查找测试", test_semantic_neighbors),
        ("TF-IDF集成测试", test_tfidf_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
