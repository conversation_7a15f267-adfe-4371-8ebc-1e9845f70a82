[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 最小版本要求
minversion = 7.0

# 添加选项
addopts = 
    # 详细输出
    -v
    # 显示测试覆盖率
    --cov=app
    # 覆盖率报告格式
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    # 覆盖率最小要求
    --cov-fail-under=80
    # 严格模式
    --strict-markers
    --strict-config
    # 警告处理
    --disable-warnings
    # 并行执行
    -n auto
    # 失败时停止
    --maxfail=5
    # 显示最慢的10个测试
    --durations=10

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    auth: 认证相关测试
    database: 数据库相关测试
    api: API测试
    service: 服务层测试
    model: 模型测试
    utils: 工具函数测试
    security: 安全测试
    performance: 性能测试

# 测试发现
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    venv
    __pycache__
    .pytest_cache
    htmlcov

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 异步测试支持
asyncio_mode = auto

# 测试数据库配置
env =
    DATABASE_URL = sqlite:///./test.db
    REDIS_URL = redis://localhost:6379/1
    SECRET_KEY = test-secret-key
    ENVIRONMENT = testing
    DEBUG = true
