#!/usr/bin/env python3
"""
独立的合同解析器测试
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import json
from datetime import datetime
import jieba


class SimpleContractClause:
    """简化的合同条款类"""
    
    def __init__(self, clause_id: str, title: str, content: str, clause_type: str = "general"):
        self.clause_id = clause_id
        self.title = title
        self.content = content
        self.clause_type = clause_type
        self.key_terms = []
        self.entities = {}
        self.risk_level = "low"
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "clause_id": self.clause_id,
            "title": self.title,
            "content": self.content,
            "clause_type": self.clause_type,
            "key_terms": self.key_terms,
            "entities": self.entities,
            "risk_level": self.risk_level
        }


class SimpleContractParser:
    """简化的合同解析器"""
    
    def __init__(self):
        self.clause_patterns = {
            "标题条款": [
                r"第[一二三四五六七八九十\d]+条",
                r"\d+\.\d+",
                r"第\d+章",
                r"[一二三四五六七八九十]+、"
            ],
            "定义条款": [
                r"本合同所称.*是指",
                r".*定义如下",
                r"术语解释",
                r"定义与解释"
            ],
            "权利义务": [
                r"甲方.*权利",
                r"乙方.*义务",
                r"双方.*责任",
                r"当事人.*应当"
            ],
            "违约责任": [
                r"违约责任",
                r"违约金",
                r"损害赔偿",
                r"承担责任"
            ],
            "争议解决": [
                r"争议解决",
                r"仲裁",
                r"诉讼",
                r"管辖法院"
            ],
            "生效条件": [
                r"生效条件",
                r"合同生效",
                r"签署生效",
                r"履行期限"
            ]
        }
        
        self.entity_patterns = {
            "合同金额": r"(?:金额|价款|费用).*?(\d+(?:\.\d+)?(?:万|千|百)?元)",
            "合同期限": r"(?:期限|有效期).*?(\d+(?:年|月|日|天))",
            "违约金": r"违约金.*?(\d+(?:\.\d+)?(?:万|千|百)?元|百分之\d+)",
            "利率": r"(?:利率|年利率).*?(\d+(?:\.\d+)?%)",
            "保证金": r"(?:保证金|押金).*?(\d+(?:\.\d+)?(?:万|千|百)?元)",
            "交付时间": r"(?:交付|交货|完成).*?(\d{4}年\d{1,2}月\d{1,2}日|\d+(?:个)?(?:工作日|日|月))",
            "当事人": r"(甲方|乙方|丙方)[:：]\s*([^，。！？\n]*)"
        }
        
        self.contract_types = {
            "买卖合同": ["买卖", "购销", "采购", "销售", "商品"],
            "租赁合同": ["租赁", "出租", "承租", "租金", "房屋"],
            "服务合同": ["服务", "委托", "咨询", "技术", "劳务"],
            "借款合同": ["借款", "贷款", "融资", "利息", "还款"],
            "劳动合同": ["劳动", "雇佣", "工作", "薪资", "员工"]
        }
    
    def parse_contract_text(self, text: str) -> Dict[str, Any]:
        """解析合同文本"""
        try:
            # 1. 基本信息提取
            basic_info = self._extract_basic_info(text)
            
            # 2. 合同类型识别
            contract_type = self._identify_contract_type(text)
            
            # 3. 条款分割和分类
            clauses = self._extract_clauses(text)
            
            # 4. 实体提取
            entities = self._extract_entities(text)
            
            # 5. 关键术语提取
            key_terms = self._extract_key_terms(text)
            
            # 6. 结构化分析
            structure_analysis = self._analyze_structure(clauses)
            
            return {
                "basic_info": basic_info,
                "contract_type": contract_type,
                "clauses": [clause.to_dict() for clause in clauses],
                "entities": entities,
                "key_terms": key_terms,
                "structure_analysis": structure_analysis,
                "parsing_timestamp": datetime.now().isoformat(),
                "text_length": len(text),
                "clause_count": len(clauses)
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "parsing_timestamp": datetime.now().isoformat()
            }
    
    def _extract_basic_info(self, text: str) -> Dict[str, Any]:
        """提取合同基本信息"""
        basic_info = {
            "title": "",
            "parties": [],
            "date": "",
            "place": ""
        }
        
        # 提取合同标题
        title_patterns = [
            r"^([^。！？\n]*合同[^。！？\n]*)",
            r"^([^。！？\n]*协议[^。！？\n]*)"
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, text.strip(), re.MULTILINE)
            if match:
                basic_info["title"] = match.group(1).strip()
                break
        
        # 提取当事人信息
        party_pattern = r"(甲方|乙方|丙方)[:：]\s*([^，。！？\n]*)"
        parties = re.findall(party_pattern, text)
        basic_info["parties"] = [{"role": role, "name": name.strip()} for role, name in parties]
        
        # 提取签署日期
        date_pattern = r"(\d{4}年\d{1,2}月\d{1,2}日)"
        date_match = re.search(date_pattern, text)
        if date_match:
            basic_info["date"] = date_match.group(1)
        
        return basic_info
    
    def _identify_contract_type(self, text: str) -> Dict[str, Any]:
        """识别合同类型"""
        type_scores = defaultdict(int)
        
        for contract_type, keywords in self.contract_types.items():
            for keyword in keywords:
                count = text.count(keyword)
                type_scores[contract_type] += count
        
        if type_scores:
            best_type = max(type_scores, key=type_scores.get)
            confidence = type_scores[best_type] / sum(type_scores.values())
        else:
            best_type = "通用合同"
            confidence = 0.0
        
        return {
            "primary_type": best_type,
            "confidence": confidence,
            "all_scores": dict(type_scores)
        }
    
    def _extract_clauses(self, text: str) -> List[SimpleContractClause]:
        """提取和分类合同条款"""
        clauses = []
        paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
        
        clause_id = 1
        for paragraph in paragraphs:
            if len(paragraph) < 10:
                continue
            
            clause_type = self._classify_clause(paragraph)
            title = self._extract_clause_title(paragraph)
            
            clause = SimpleContractClause(
                clause_id=f"clause_{clause_id}",
                title=title,
                content=paragraph,
                clause_type=clause_type
            )
            
            clause.key_terms = self._extract_clause_terms(paragraph)
            clause.entities = self._extract_clause_entities(paragraph)
            
            clauses.append(clause)
            clause_id += 1
        
        return clauses
    
    def _classify_clause(self, text: str) -> str:
        """分类条款类型"""
        for clause_type, patterns in self.clause_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    return clause_type
        
        if any(keyword in text for keyword in ["定义", "术语", "解释"]):
            return "定义条款"
        elif any(keyword in text for keyword in ["权利", "义务", "责任"]):
            return "权利义务"
        elif any(keyword in text for keyword in ["违约", "赔偿", "损失"]):
            return "违约责任"
        elif any(keyword in text for keyword in ["争议", "仲裁", "诉讼"]):
            return "争议解决"
        elif any(keyword in text for keyword in ["生效", "终止", "解除"]):
            return "生效条件"
        else:
            return "一般条款"
    
    def _extract_clause_title(self, text: str) -> str:
        """提取条款标题"""
        title_patterns = [
            r"^(第[一二三四五六七八九十\d]+条[^。！？]*)",
            r"^(\d+\.\d+[^。！？]*)",
            r"^([一二三四五六七八九十]+、[^。！？]*)"
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, text.strip())
            if match:
                return match.group(1).strip()
        
        return text[:30] + "..." if len(text) > 30 else text
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取合同实体"""
        entities = defaultdict(list)
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                if entity_type == "当事人":
                    entities[entity_type].extend([f"{role}: {name}" for role, name in matches])
                else:
                    entities[entity_type].extend(matches)
        
        # 去重
        for entity_type in entities:
            entities[entity_type] = list(set(entities[entity_type]))
        
        return dict(entities)
    
    def _extract_clause_entities(self, text: str) -> Dict[str, List[str]]:
        """提取单个条款中的实体"""
        return self._extract_entities(text)
    
    def _extract_key_terms(self, text: str) -> List[Dict[str, Any]]:
        """提取关键术语"""
        tokens = list(jieba.cut(text))
        
        legal_terms = []
        legal_keywords = [
            "合同", "协议", "条款", "当事人", "甲方", "乙方", "权利", "义务",
            "责任", "违约", "赔偿", "损失", "仲裁", "诉讼", "生效", "终止"
        ]
        
        for token in tokens:
            if len(token) > 1 and (token in legal_keywords or 
                                 any(char in token for char in ["法", "律", "权", "责"])):
                legal_terms.append({
                    "term": token,
                    "frequency": tokens.count(token),
                    "type": "legal"
                })
        
        # 去重并按频率排序
        unique_terms = {}
        for term in legal_terms:
            if term["term"] not in unique_terms:
                unique_terms[term["term"]] = term
        
        return sorted(unique_terms.values(), key=lambda x: x["frequency"], reverse=True)
    
    def _extract_clause_terms(self, text: str) -> List[str]:
        """提取条款关键术语"""
        terms = self._extract_key_terms(text)
        return [term["term"] for term in terms[:5]]
    
    def _analyze_structure(self, clauses: List[SimpleContractClause]) -> Dict[str, Any]:
        """分析合同结构"""
        structure = {
            "total_clauses": len(clauses),
            "clause_types": defaultdict(int),
            "completeness_score": 0.0,
            "missing_clauses": [],
            "structure_quality": "good"
        }
        
        # 统计条款类型
        for clause in clauses:
            structure["clause_types"][clause.clause_type] += 1
        
        # 检查合同完整性
        essential_clauses = [
            "定义条款", "权利义务", "违约责任", "争议解决", "生效条件"
        ]
        
        present_types = set(structure["clause_types"].keys())
        missing_types = [ct for ct in essential_clauses if ct not in present_types]
        
        structure["missing_clauses"] = missing_types
        structure["completeness_score"] = (len(essential_clauses) - len(missing_types)) / len(essential_clauses)
        
        # 评估结构质量
        if structure["completeness_score"] >= 0.8:
            structure["structure_quality"] = "excellent"
        elif structure["completeness_score"] >= 0.6:
            structure["structure_quality"] = "good"
        elif structure["completeness_score"] >= 0.4:
            structure["structure_quality"] = "fair"
        else:
            structure["structure_quality"] = "poor"
        
        return dict(structure)


def test_basic_contract_parsing():
    """测试基本合同解析"""
    parser = SimpleContractParser()
    
    contract_text = """
    房屋租赁合同
    
    甲方：张三（出租方）
    乙方：李四（承租方）
    
    第一条 租赁房屋基本情况
    甲方将位于北京市朝阳区的房屋出租给乙方使用。
    
    第二条 租赁期限
    租赁期限为2年，自2023年1月1日起至2024年12月31日止。
    
    第三条 租金及支付方式
    月租金为5000元，乙方应于每月1日前支付当月租金。
    
    第四条 违约责任
    如乙方逾期支付租金超过10日，甲方有权解除合同，乙方应支付违约金2000元。
    
    第五条 争议解决
    因本合同发生争议，双方应协商解决；协商不成的，提交北京仲裁委员会仲裁。
    
    本合同自双方签字之日起生效。
    甲方签字：_______    乙方签字：_______
    日期：2023年1月1日
    """
    
    result = parser.parse_contract_text(contract_text)
    
    print("✓ 基本合同解析测试:")
    print(f"  合同类型: {result['contract_type']['primary_type']}")
    print(f"  条款数量: {result['clause_count']}")
    print(f"  当事人数量: {len(result['basic_info']['parties'])}")
    print(f"  实体类型数: {len(result['entities'])}")
    print(f"  结构质量: {result['structure_analysis']['structure_quality']}")
    
    return result['contract_type']['primary_type'] == "租赁合同" and result['clause_count'] > 0


def test_contract_type_identification():
    """测试合同类型识别"""
    parser = SimpleContractParser()
    
    test_cases = [
        ("这是一份房屋买卖合同，甲方出售房屋给乙方", "买卖合同"),
        ("劳动合同：甲方聘用乙方为员工，月薪8000元", "劳动合同"),
        ("借款合同：甲方向乙方借款10万元，年利率5%", "借款合同"),
        ("技术服务合同：甲方为乙方提供软件开发服务", "服务合同"),
        ("房屋租赁协议：甲方将房屋出租给乙方", "租赁合同")
    ]
    
    print("✓ 合同类型识别测试:")
    correct = 0
    for text, expected_type in test_cases:
        result = parser._identify_contract_type(text)
        predicted_type = result["primary_type"]
        confidence = result["confidence"]
        
        is_correct = predicted_type == expected_type
        if is_correct:
            correct += 1
        
        status = "✓" if is_correct else "✗"
        print(f"  {status} '{text[:20]}...' -> {predicted_type} (置信度: {confidence:.2f})")
    
    accuracy = correct / len(test_cases)
    print(f"  准确率: {accuracy:.2%}")
    
    return accuracy >= 0.6


def test_entity_extraction():
    """测试实体提取"""
    parser = SimpleContractParser()
    
    test_text = """
    合同金额为100万元，租赁期限为3年，违约金为合同金额的10%。
    利率为年利率5.5%，保证金为5万元。
    交付时间为2023年6月30日。
    甲方：北京科技有限公司
    乙方：上海贸易有限公司
    """
    
    entities = parser._extract_entities(test_text)
    
    print("✓ 实体提取测试:")
    for entity_type, values in entities.items():
        print(f"  {entity_type}: {values}")
    
    return len(entities) > 0


def test_clause_classification():
    """测试条款分类"""
    parser = SimpleContractParser()
    
    test_clauses = [
        ("第一条 定义：本合同所称房屋是指...", "定义条款"),
        ("第二条 甲方权利：甲方有权...", "权利义务"),
        ("第三条 违约责任：如乙方违约...", "违约责任"),
        ("第四条 争议解决：双方发生争议时...", "争议解决"),
        ("第五条 合同生效：本合同自签署之日起生效", "生效条件")
    ]
    
    print("✓ 条款分类测试:")
    correct = 0
    for clause_text, expected_type in test_clauses:
        predicted_type = parser._classify_clause(clause_text)
        
        is_correct = predicted_type == expected_type
        if is_correct:
            correct += 1
        
        status = "✓" if is_correct else "✗"
        print(f"  {status} '{clause_text[:20]}...' -> {predicted_type}")
    
    accuracy = correct / len(test_clauses)
    print(f"  准确率: {accuracy:.2%}")
    
    return accuracy >= 0.6


def main():
    """主测试函数"""
    print("开始独立合同解析器测试...")
    print("=" * 50)
    
    tests = [
        ("基本合同解析测试", test_basic_contract_parsing),
        ("合同类型识别测试", test_contract_type_identification),
        ("实体提取测试", test_entity_extraction),
        ("条款分类测试", test_clause_classification),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
