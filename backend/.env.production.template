# AI法律助手生产环境配置模板
# 复制此文件为 .env.production 并填入实际值

# ===========================================
# 基础配置
# ===========================================
APP_NAME=AI法律助手
APP_VERSION=1.0.0
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# ===========================================
# 数据库配置
# ===========================================
POSTGRES_DB=ai_legal_assistant_prod
POSTGRES_USER=ai_legal_user
POSTGRES_PASSWORD=your-super-secure-database-password-change-this
DATABASE_URL=**********************************************************************************/ai_legal_assistant_prod
DATABASE_SSL_MODE=require
DATABASE_SSL_CERT=/app/certs/client-cert.pem
DATABASE_SSL_KEY=/app/certs/client-key.pem
DATABASE_SSL_ROOT_CERT=/app/certs/ca-cert.pem

# ===========================================
# Redis配置
# ===========================================
REDIS_URL=redis://:your-redis-password@redis:6379/0
REDIS_PASSWORD=your-redis-password-change-this
REDIS_SSL=false
REDIS_SSL_CERT_REQS=required
REDIS_SSL_CA_CERTS=/app/certs/redis-ca-cert.pem

# ===========================================
# 数据加密配置
# ===========================================
ENCRYPTION_MASTER_KEY=your-super-secret-master-key-minimum-32-characters-change-this-in-production
ENCRYPTION_KEY=your-encryption-key-for-data-encryption-change-this

# ===========================================
# JWT和认证配置
# ===========================================
SECRET_KEY=your-super-secret-jwt-key-minimum-32-characters-change-this
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=30

# ===========================================
# HTTPS和SSL配置
# ===========================================
FORCE_HTTPS=true
SSL_CERT_PATH=/app/certs/cert.pem
SSL_KEY_PATH=/app/certs/private.key

# ===========================================
# 安全配置
# ===========================================
TRUSTED_HOSTS=yourdomain.com,*.yourdomain.com,localhost,127.0.0.1
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com,https://admin.yourdomain.com
CORS_ALLOW_CREDENTIALS=true

# 速率限制配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 安全头配置
SECURITY_HEADERS_ENABLED=true

# 会话安全配置
SESSION_SECURE=true
SESSION_HTTPONLY=true
SESSION_SAMESITE=strict

# ===========================================
# 审计日志配置
# ===========================================
AUDIT_LOG_ENABLED=true
AUDIT_LOG_LEVEL=INFO
AUDIT_LOG_FILE=/app/logs/audit.log

# ===========================================
# 数据脱敏配置
# ===========================================
DATA_MASKING_ENABLED=true
MASK_EMAIL=true
MASK_PHONE=true
MASK_ID_CARD=true
MASK_BANK_CARD=true

# ===========================================
# 备份加密配置
# ===========================================
BACKUP_ENCRYPTION_ENABLED=true
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key-change-this

# ===========================================
# 文件上传安全配置
# ===========================================
MAX_FILE_SIZE=********  # 10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png
SCAN_UPLOADED_FILES=true

# ===========================================
# API安全配置
# ===========================================
API_KEY_REQUIRED=false
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=3600

# ===========================================
# 监控和告警配置
# ===========================================
SECURITY_MONITORING_ENABLED=true
FAILED_LOGIN_THRESHOLD=5
FAILED_LOGIN_WINDOW=300
ALERT_EMAIL=<EMAIL>

# ===========================================
# 数据保留策略
# ===========================================
DATA_RETENTION_DAYS=2555  # 7年
LOG_RETENTION_DAYS=365    # 1年
SESSION_RETENTION_DAYS=30 # 30天

# ===========================================
# 合规性配置
# ===========================================
GDPR_COMPLIANCE=true
CCPA_COMPLIANCE=true
DATA_ANONYMIZATION=true

# ===========================================
# Elasticsearch配置
# ===========================================
ELASTIC_PASSWORD=your-elasticsearch-password-change-this
ELASTICSEARCH_URL=https://elasticsearch:9200
ELASTICSEARCH_USERNAME=elastic

# ===========================================
# 邮件配置
# ===========================================
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_USE_TLS=true
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=AI法律助手

# ===========================================
# 第三方服务配置
# ===========================================
# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# 阿里云配置（如果使用）
ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-aliyun-access-key-secret
ALIYUN_REGION=cn-hangzhou

# 腾讯云配置（如果使用）
TENCENT_SECRET_ID=your-tencent-secret-id
TENCENT_SECRET_KEY=your-tencent-secret-key
TENCENT_REGION=ap-beijing

# ===========================================
# 监控配置
# ===========================================
PROMETHEUS_ENABLED=true
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true

# ===========================================
# 缓存配置
# ===========================================
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000

# ===========================================
# 日志配置
# ===========================================
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10

# ===========================================
# 性能配置
# ===========================================
WORKERS=4
MAX_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65
CLIENT_TIMEOUT=60

# ===========================================
# 开发和调试配置（生产环境应设为false）
# ===========================================
DEBUG_MODE=false
ALLOW_HTTP_IN_DEV=false
DISABLE_CSRF_IN_DEV=false
ENABLE_PROFILING=false

# ===========================================
# 特性开关
# ===========================================
FEATURE_AI_CHAT=true
FEATURE_CONTRACT_ANALYSIS=true
FEATURE_DOCUMENT_ANALYSIS=true
FEATURE_LEGAL_SEARCH=true
FEATURE_USER_MANAGEMENT=true
FEATURE_AUDIT_LOGS=true
FEATURE_COMPLIANCE_CHECK=true

# ===========================================
# 部署配置
# ===========================================
DEPLOYMENT_DATE=2024-01-01T00:00:00Z
GIT_COMMIT_HASH=your-git-commit-hash
BUILD_NUMBER=1

# ===========================================
# 安全扫描配置
# ===========================================
SECURITY_SCAN_ENABLED=true
VULNERABILITY_SCAN_ENABLED=true
DEPENDENCY_CHECK_ENABLED=true

# ===========================================
# 备份配置
# ===========================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/app/backups
BACKUP_ENCRYPTION_ENABLED=true

# ===========================================
# 灾难恢复配置
# ===========================================
DR_ENABLED=true
DR_BACKUP_LOCATION=s3://your-backup-bucket/disaster-recovery
DR_RTO_MINUTES=60  # 恢复时间目标：60分钟
DR_RPO_MINUTES=15  # 恢复点目标：15分钟

# ===========================================
# 注意事项
# ===========================================
# 1. 所有密码和密钥必须在生产环境中更改
# 2. 确保SSL证书文件存在于指定路径
# 3. 定期轮换密钥和密码
# 4. 监控日志文件大小和磁盘空间
# 5. 定期备份数据库和配置文件
# 6. 测试灾难恢复流程
# 7. 定期进行安全审计
# 8. 保持系统和依赖项更新
