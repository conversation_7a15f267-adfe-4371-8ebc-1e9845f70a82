#!/usr/bin/env python3
"""
独立的合同修改建议生成器测试
"""

import re
from typing import Dict, List, Any, Optional
from collections import defaultdict
from datetime import datetime


class SimpleModificationSuggestion:
    """简化的修改建议类"""
    
    def __init__(self, suggestion_id: str, suggestion_type: str, priority: str):
        self.suggestion_id = suggestion_id
        self.suggestion_type = suggestion_type
        self.priority = priority
        self.title = ""
        self.description = ""
        self.original_text = ""
        self.suggested_text = ""
        self.reason = ""
        self.legal_basis = ""
        self.impact_assessment = ""
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "suggestion_id": self.suggestion_id,
            "suggestion_type": self.suggestion_type,
            "priority": self.priority,
            "title": self.title,
            "description": self.description,
            "original_text": self.original_text,
            "suggested_text": self.suggested_text,
            "reason": self.reason,
            "legal_basis": self.legal_basis,
            "impact_assessment": self.impact_assessment
        }


class SimpleContractSuggestionGenerator:
    """简化的合同修改建议生成器"""
    
    def __init__(self):
        self.modification_patterns = {
            "免责条款": [
                {
                    "pattern": r"甲方.*不承担.*责任",
                    "suggestion": "建议明确甲方免责的具体情形和范围",
                    "template": "甲方在以下情形下不承担责任：(1)不可抗力；(2)乙方违约导致的损失。"
                }
            ],
            "违约责任": [
                {
                    "pattern": r"违约金.*(\d+)%",
                    "suggestion": "建议将违约金调整到合理范围内",
                    "template": "违约方应支付违约金，违约金数额不超过合同总金额的20%。"
                }
            ]
        }
        
        self.legal_templates = {
            "不可抗力": "因不可抗力导致合同无法履行的，受影响方应及时通知对方。",
            "保密条款": "双方应对在合同履行过程中知悉的对方商业秘密予以保密。"
        }
    
    def generate_suggestions(self, risk_analysis: Dict[str, Any], 
                           clause_analysis: Dict[str, Any] = None,
                           contract_text: str = "") -> Dict[str, Any]:
        """生成修改建议"""
        try:
            suggestions = []
            
            # 1. 基于风险分析生成建议
            risk_suggestions = self._generate_risk_based_suggestions(risk_analysis)
            suggestions.extend(risk_suggestions)
            
            # 2. 基于条款分析生成建议
            if clause_analysis:
                clause_suggestions = self._generate_clause_based_suggestions(clause_analysis)
                suggestions.extend(clause_suggestions)
            
            # 3. 基于合同文本生成建议
            if contract_text:
                text_suggestions = self._generate_text_based_suggestions(contract_text)
                suggestions.extend(text_suggestions)
            
            # 4. 生成标准条款建议
            standard_suggestions = self._generate_standard_clause_suggestions(contract_text)
            suggestions.extend(standard_suggestions)
            
            # 5. 按优先级排序
            suggestions = self._prioritize_suggestions(suggestions)
            
            # 6. 生成修改方案
            modification_plan = self._generate_modification_plan(suggestions)
            
            return {
                "suggestions": [s.to_dict() for s in suggestions],
                "modification_plan": modification_plan,
                "summary": {
                    "total_suggestions": len(suggestions),
                    "urgent_count": len([s for s in suggestions if s.priority == "urgent"]),
                    "high_count": len([s for s in suggestions if s.priority == "high"]),
                    "medium_count": len([s for s in suggestions if s.priority == "medium"]),
                    "low_count": len([s for s in suggestions if s.priority == "low"])
                },
                "generation_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "generation_timestamp": datetime.now().isoformat()
            }
    
    def _generate_risk_based_suggestions(self, risk_analysis: Dict[str, Any]) -> List[SimpleModificationSuggestion]:
        """基于风险分析生成建议"""
        suggestions = []
        risks = risk_analysis.get("risks", [])
        
        suggestion_id = 1
        for risk in risks:
            risk_type = risk.get("risk_type", "")
            severity = risk.get("severity", "low")
            description = risk.get("description", "")
            
            # 确定优先级
            if severity == "critical":
                priority = "urgent"
            elif severity == "high":
                priority = "high"
            elif severity == "medium":
                priority = "medium"
            else:
                priority = "low"
            
            suggestion = SimpleModificationSuggestion(
                suggestion_id=f"risk_{suggestion_id}",
                suggestion_type="风险消除",
                priority=priority
            )
            
            suggestion.title = f"消除{risk_type}风险"
            suggestion.description = description
            suggestion.reason = f"存在{severity}级别的{risk_type}风险"
            suggestion.impact_assessment = self._assess_risk_impact(severity)
            
            suggestions.append(suggestion)
            suggestion_id += 1
        
        return suggestions
    
    def _generate_clause_based_suggestions(self, clause_analysis: Dict[str, Any]) -> List[SimpleModificationSuggestion]:
        """基于条款分析生成建议"""
        suggestions = []
        clause_analyses = clause_analysis.get("clause_analyses", [])
        
        suggestion_id = 1000
        for clause in clause_analyses:
            scores = clause.get("scores", {})
            clause_title = clause.get("clause_title", "")
            
            if scores.get("legality", 100) < 80:
                suggestion = SimpleModificationSuggestion(
                    suggestion_id=f"clause_{suggestion_id}",
                    suggestion_type="合规优化",
                    priority="high"
                )
                suggestion.title = f"优化{clause_title}的合法性"
                suggestion.description = "条款存在合法性问题，需要修改"
                suggestions.append(suggestion)
                suggestion_id += 1
            
            if scores.get("completeness", 100) < 70:
                suggestion = SimpleModificationSuggestion(
                    suggestion_id=f"clause_{suggestion_id}",
                    suggestion_type="条款补充",
                    priority="medium"
                )
                suggestion.title = f"完善{clause_title}的内容"
                suggestion.description = "条款内容不够完整，需要补充"
                suggestions.append(suggestion)
                suggestion_id += 1
        
        return suggestions
    
    def _generate_text_based_suggestions(self, contract_text: str) -> List[SimpleModificationSuggestion]:
        """基于合同文本生成建议"""
        suggestions = []
        suggestion_id = 2000
        
        for category, patterns in self.modification_patterns.items():
            for pattern_info in patterns:
                pattern = pattern_info["pattern"]
                matches = list(re.finditer(pattern, contract_text, re.IGNORECASE))
                
                for match in matches:
                    suggestion = SimpleModificationSuggestion(
                        suggestion_id=f"text_{suggestion_id}",
                        suggestion_type="条款优化",
                        priority="medium"
                    )
                    
                    suggestion.title = f"优化{category}"
                    suggestion.description = pattern_info["suggestion"]
                    suggestion.original_text = match.group(0)
                    suggestion.suggested_text = pattern_info["template"]
                    
                    suggestions.append(suggestion)
                    suggestion_id += 1
        
        return suggestions
    
    def _generate_standard_clause_suggestions(self, contract_text: str) -> List[SimpleModificationSuggestion]:
        """生成标准条款建议"""
        suggestions = []
        suggestion_id = 3000
        
        standard_clauses = {
            "不可抗力": [r"不可抗力"],
            "保密条款": [r"保密", r"机密"]
        }
        
        for clause_name, patterns in standard_clauses.items():
            found = any(re.search(pattern, contract_text, re.IGNORECASE) for pattern in patterns)
            
            if not found:
                suggestion = SimpleModificationSuggestion(
                    suggestion_id=f"standard_{suggestion_id}",
                    suggestion_type="条款补充",
                    priority="low"
                )
                
                suggestion.title = f"添加{clause_name}条款"
                suggestion.description = f"建议添加{clause_name}相关条款"
                suggestion.suggested_text = self.legal_templates.get(clause_name, "")
                
                suggestions.append(suggestion)
                suggestion_id += 1
        
        return suggestions
    
    def _prioritize_suggestions(self, suggestions: List[SimpleModificationSuggestion]) -> List[SimpleModificationSuggestion]:
        """按优先级排序建议"""
        priority_order = {"urgent": 4, "high": 3, "medium": 2, "low": 1}
        return sorted(suggestions, key=lambda s: priority_order.get(s.priority, 0), reverse=True)
    
    def _generate_modification_plan(self, suggestions: List[SimpleModificationSuggestion]) -> Dict[str, Any]:
        """生成修改方案"""
        plan = {
            "phase_1_urgent": [],
            "phase_2_high": [],
            "phase_3_medium": [],
            "phase_4_low": [],
            "estimated_timeline": ""
        }
        
        for suggestion in suggestions:
            if suggestion.priority == "urgent":
                plan["phase_1_urgent"].append(suggestion.suggestion_id)
            elif suggestion.priority == "high":
                plan["phase_2_high"].append(suggestion.suggestion_id)
            elif suggestion.priority == "medium":
                plan["phase_3_medium"].append(suggestion.suggestion_id)
            else:
                plan["phase_4_low"].append(suggestion.suggestion_id)
        
        total_suggestions = len(suggestions)
        estimated_days = max(1, total_suggestions // 2)
        plan["estimated_timeline"] = f"预计需要{estimated_days}个工作日完成所有修改"
        
        return plan
    
    def _assess_risk_impact(self, severity: str) -> str:
        """评估风险影响"""
        impact_map = {
            "critical": "可能导致合同无效或面临严重法律后果",
            "high": "可能导致重大经济损失或法律纠纷",
            "medium": "可能影响合同履行或增加争议风险",
            "low": "对合同履行影响较小，但建议优化"
        }
        return impact_map.get(severity, "影响程度待评估")


def test_risk_based_suggestions():
    """测试基于风险的建议生成"""
    generator = SimpleContractSuggestionGenerator()
    
    # 模拟风险分析结果
    risk_analysis = {
        "risks": [
            {
                "risk_type": "法律风险",
                "severity": "critical",
                "description": "可能存在违法条款",
                "suggestion": "建议删除或修改违法内容"
            },
            {
                "risk_type": "不公平条款",
                "severity": "high",
                "description": "甲方免责条款过于宽泛",
                "suggestion": "建议明确免责范围"
            },
            {
                "risk_type": "商业风险",
                "severity": "medium",
                "description": "存在价格调整风险",
                "suggestion": "建议明确价格调整条件"
            }
        ]
    }
    
    result = generator.generate_suggestions(risk_analysis)
    
    print("✓ 基于风险的建议生成测试:")
    print(f"  生成建议数: {result['summary']['total_suggestions']}")
    print(f"  紧急建议: {result['summary']['urgent_count']}")
    print(f"  高优先级建议: {result['summary']['high_count']}")
    print(f"  中优先级建议: {result['summary']['medium_count']}")
    
    # 验证建议生成
    assert result['summary']['total_suggestions'] >= 3
    assert result['summary']['urgent_count'] >= 1
    
    return True


def test_clause_based_suggestions():
    """测试基于条款分析的建议生成"""
    generator = SimpleContractSuggestionGenerator()
    
    # 模拟条款分析结果
    clause_analysis = {
        "clause_analyses": [
            {
                "clause_title": "免责条款",
                "scores": {
                    "legality": 60,  # 低合法性得分
                    "completeness": 80,
                    "reasonableness": 70
                }
            },
            {
                "clause_title": "价格条款",
                "scores": {
                    "legality": 90,
                    "completeness": 50,  # 低完整性得分
                    "reasonableness": 80
                }
            }
        ]
    }
    
    result = generator.generate_suggestions({}, clause_analysis)
    
    print("✓ 基于条款分析的建议生成测试:")
    print(f"  生成建议数: {result['summary']['total_suggestions']}")
    
    # 验证建议生成
    assert result['summary']['total_suggestions'] >= 2
    
    return True


def test_text_based_suggestions():
    """测试基于文本的建议生成"""
    generator = SimpleContractSuggestionGenerator()
    
    # 包含问题的合同文本
    contract_text = """
    第一条 免责条款
    甲方不承担任何责任，包括直接损失和间接损失。
    
    第二条 违约责任
    乙方违约的，应支付违约金为合同金额的50%。
    """
    
    result = generator.generate_suggestions({}, contract_text=contract_text)
    
    print("✓ 基于文本的建议生成测试:")
    print(f"  生成建议数: {result['summary']['total_suggestions']}")
    
    # 验证建议生成
    assert result['summary']['total_suggestions'] >= 2
    
    return True


def test_standard_clause_suggestions():
    """测试标准条款建议生成"""
    generator = SimpleContractSuggestionGenerator()
    
    # 缺少标准条款的合同文本
    simple_contract = """
    甲方：公司A
    乙方：公司B
    服务内容：软件开发
    费用：10万元
    """
    
    result = generator.generate_suggestions({}, contract_text=simple_contract)
    
    print("✓ 标准条款建议生成测试:")
    print(f"  生成建议数: {result['summary']['total_suggestions']}")
    print(f"  低优先级建议: {result['summary']['low_count']}")
    
    # 验证标准条款建议
    assert result['summary']['low_count'] >= 1  # 应该有标准条款建议
    
    return True


def test_priority_sorting():
    """测试优先级排序"""
    generator = SimpleContractSuggestionGenerator()
    
    # 包含不同优先级风险的分析结果
    mixed_risk_analysis = {
        "risks": [
            {"risk_type": "商业风险", "severity": "low", "description": "低风险"},
            {"risk_type": "法律风险", "severity": "critical", "description": "严重风险"},
            {"risk_type": "不公平条款", "severity": "medium", "description": "中等风险"},
            {"risk_type": "合规风险", "severity": "high", "description": "高风险"}
        ]
    }
    
    result = generator.generate_suggestions(mixed_risk_analysis)
    suggestions = result['suggestions']
    
    print("✓ 优先级排序测试:")
    print("  建议优先级顺序:")
    for i, suggestion in enumerate(suggestions[:4], 1):
        print(f"    {i}. {suggestion['title']} ({suggestion['priority']})")
    
    # 验证排序正确性
    priorities = [s['priority'] for s in suggestions]
    expected_order = ['urgent', 'high', 'medium', 'low']
    
    # 检查是否按优先级排序
    current_priority_index = 0
    for priority in priorities:
        priority_index = expected_order.index(priority) if priority in expected_order else 3
        assert priority_index >= current_priority_index, "优先级排序错误"
        current_priority_index = priority_index
    
    return True


def test_modification_plan_generation():
    """测试修改方案生成"""
    generator = SimpleContractSuggestionGenerator()
    
    risk_analysis = {
        "risks": [
            {"risk_type": "法律风险", "severity": "critical", "description": "严重风险"},
            {"risk_type": "商业风险", "severity": "high", "description": "高风险"},
            {"risk_type": "格式风险", "severity": "medium", "description": "中等风险"},
            {"risk_type": "其他风险", "severity": "low", "description": "低风险"}
        ]
    }
    
    result = generator.generate_suggestions(risk_analysis)
    plan = result['modification_plan']
    
    print("✓ 修改方案生成测试:")
    print(f"  第一阶段(紧急): {len(plan['phase_1_urgent'])}项")
    print(f"  第二阶段(高): {len(plan['phase_2_high'])}项")
    print(f"  第三阶段(中): {len(plan['phase_3_medium'])}项")
    print(f"  第四阶段(低): {len(plan['phase_4_low'])}项")
    print(f"  预计时间: {plan['estimated_timeline']}")
    
    # 验证方案生成
    assert len(plan['phase_1_urgent']) >= 1
    assert len(plan['phase_2_high']) >= 1
    assert "工作日" in plan['estimated_timeline']
    
    return True


def test_comprehensive_suggestion_generation():
    """测试综合建议生成"""
    generator = SimpleContractSuggestionGenerator()
    
    # 综合测试数据
    risk_analysis = {
        "risks": [
            {"risk_type": "法律风险", "severity": "high", "description": "存在违法条款"}
        ]
    }
    
    clause_analysis = {
        "clause_analyses": [
            {
                "clause_title": "测试条款",
                "scores": {"legality": 70, "completeness": 60, "reasonableness": 80}
            }
        ]
    }
    
    contract_text = "甲方不承担任何责任。服务合同内容。"
    
    result = generator.generate_suggestions(risk_analysis, clause_analysis, contract_text)
    
    print("✓ 综合建议生成测试:")
    print(f"  总建议数: {result['summary']['total_suggestions']}")
    print(f"  各优先级分布: 紧急{result['summary']['urgent_count']}, "
          f"高{result['summary']['high_count']}, 中{result['summary']['medium_count']}, "
          f"低{result['summary']['low_count']}")
    
    # 验证综合建议
    assert result['summary']['total_suggestions'] >= 3  # 应该有多种来源的建议
    
    return True


def main():
    """主测试函数"""
    print("开始独立合同修改建议生成器测试...")
    print("=" * 50)
    
    tests = [
        ("基于风险的建议生成测试", test_risk_based_suggestions),
        ("基于条款分析的建议生成测试", test_clause_based_suggestions),
        ("基于文本的建议生成测试", test_text_based_suggestions),
        ("标准条款建议生成测试", test_standard_clause_suggestions),
        ("优先级排序测试", test_priority_sorting),
        ("修改方案生成测试", test_modification_plan_generation),
        ("综合建议生成测试", test_comprehensive_suggestion_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
