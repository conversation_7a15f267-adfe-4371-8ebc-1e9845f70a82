#!/usr/bin/env python3
"""
性能监控和优化系统测试
"""

import time
import threading
import tempfile
import shutil
import os
from datetime import datetime


# 简化的性能监控测试类
class TestPerformanceMonitoring:
    """性能监控测试类"""
    
    def __init__(self):
        self.test_results = []
    
    def test_performance_metrics_recording(self):
        """测试性能指标记录"""
        print("✓ 性能指标记录测试:")
        
        # 模拟性能指标
        metrics = {
            'response_times': [],
            'memory_usage': [],
            'cpu_usage': [],
            'error_count': 0,
            'request_count': 0
        }
        
        # 记录一些测试数据
        for i in range(10):
            metrics['response_times'].append({
                'endpoint': f'/api/test{i%3}',
                'response_time': 0.1 + i * 0.05,
                'timestamp': datetime.now().isoformat()
            })
            metrics['request_count'] += 1
        
        # 记录内存和CPU使用
        metrics['memory_usage'].append({
            'used_percent': 45.2,
            'timestamp': datetime.now().isoformat()
        })
        metrics['cpu_usage'].append({
            'cpu_percent': 23.5,
            'timestamp': datetime.now().isoformat()
        })
        
        print(f"  记录响应时间: {len(metrics['response_times'])}条")
        print(f"  记录系统指标: 内存{metrics['memory_usage'][0]['used_percent']}%, CPU{metrics['cpu_usage'][0]['cpu_percent']}%")
        print(f"  总请求数: {metrics['request_count']}")
        
        assert len(metrics['response_times']) == 10
        assert metrics['request_count'] == 10
        
        return True
    
    def test_performance_summary_calculation(self):
        """测试性能摘要计算"""
        print("✓ 性能摘要计算测试:")
        
        # 模拟响应时间数据
        response_times = [0.1, 0.2, 0.15, 0.3, 0.25, 0.18, 0.22, 0.12, 0.28, 0.16]
        
        # 计算统计信息
        avg_response_time = sum(response_times) / len(response_times)
        min_response_time = min(response_times)
        max_response_time = max(response_times)
        
        # 计算P95
        sorted_times = sorted(response_times)
        p95_index = int(len(sorted_times) * 0.95)
        p95_response_time = sorted_times[p95_index]
        
        summary = {
            'total_requests': len(response_times),
            'avg_response_time': avg_response_time,
            'min_response_time': min_response_time,
            'max_response_time': max_response_time,
            'p95_response_time': p95_response_time
        }
        
        print(f"  总请求数: {summary['total_requests']}")
        print(f"  平均响应时间: {summary['avg_response_time']:.3f}秒")
        print(f"  最小响应时间: {summary['min_response_time']:.3f}秒")
        print(f"  最大响应时间: {summary['max_response_time']:.3f}秒")
        print(f"  P95响应时间: {summary['p95_response_time']:.3f}秒")
        
        assert summary['total_requests'] == 10
        assert 0.1 <= summary['avg_response_time'] <= 0.3
        
        return True
    
    def test_performance_alerts(self):
        """测试性能警告"""
        print("✓ 性能警告测试:")
        
        # 模拟不同的性能指标
        test_cases = [
            {'memory_usage': 85.0, 'cpu_usage': 45.0, 'avg_response_time': 0.5, 'error_rate': 1.0},
            {'memory_usage': 45.0, 'cpu_usage': 85.0, 'avg_response_time': 0.5, 'error_rate': 1.0},
            {'memory_usage': 45.0, 'cpu_usage': 45.0, 'avg_response_time': 2.5, 'error_rate': 1.0},
            {'memory_usage': 45.0, 'cpu_usage': 45.0, 'avg_response_time': 0.5, 'error_rate': 8.0},
        ]
        
        alert_types = ['内存使用率过高', 'CPU使用率过高', '平均响应时间过长', '错误率过高']
        
        for i, metrics in enumerate(test_cases):
            alerts = []
            
            if metrics['memory_usage'] > 80:
                alerts.append(f"内存使用率过高: {metrics['memory_usage']:.1f}%")
            
            if metrics['cpu_usage'] > 80:
                alerts.append(f"CPU使用率过高: {metrics['cpu_usage']:.1f}%")
            
            if metrics['avg_response_time'] > 2.0:
                alerts.append(f"平均响应时间过长: {metrics['avg_response_time']:.2f}秒")
            
            if metrics['error_rate'] > 5.0:
                alerts.append(f"错误率过高: {metrics['error_rate']:.1f}%")
            
            print(f"  测试案例{i+1}: {alert_types[i] if alerts else '无警告'}")
            
            assert len(alerts) > 0 or i >= len(alert_types)
        
        return True
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        print("✓ 缓存功能测试:")
        
        # 简化的LRU缓存实现
        class SimpleLRUCache:
            def __init__(self, max_size=5):
                self.max_size = max_size
                self.cache = {}
                self.access_order = []
                self.hits = 0
                self.misses = 0
            
            def get(self, key):
                if key in self.cache:
                    # 更新访问顺序
                    self.access_order.remove(key)
                    self.access_order.append(key)
                    self.hits += 1
                    return self.cache[key]
                else:
                    self.misses += 1
                    return None
            
            def put(self, key, value):
                if key in self.cache:
                    self.access_order.remove(key)
                elif len(self.cache) >= self.max_size:
                    # 删除最久未使用的项
                    oldest = self.access_order.pop(0)
                    del self.cache[oldest]
                
                self.cache[key] = value
                self.access_order.append(key)
            
            def hit_rate(self):
                total = self.hits + self.misses
                return self.hits / total if total > 0 else 0.0
        
        # 测试缓存
        cache = SimpleLRUCache(max_size=3)
        
        # 添加数据
        cache.put("key1", "value1")
        cache.put("key2", "value2")
        cache.put("key3", "value3")
        
        # 测试命中
        assert cache.get("key1") == "value1"
        assert cache.get("key2") == "value2"
        
        # 添加新数据，应该淘汰key3
        cache.put("key4", "value4")
        assert cache.get("key3") is None  # 应该被淘汰
        assert cache.get("key4") == "value4"
        
        print(f"  缓存大小: {len(cache.cache)}")
        print(f"  缓存命中率: {cache.hit_rate():.2f}")
        print(f"  缓存内容: {list(cache.cache.keys())}")
        
        assert len(cache.cache) == 3
        assert cache.hit_rate() > 0
        
        return True
    
    def test_logging_system(self):
        """测试日志系统"""
        print("✓ 日志系统测试:")
        
        # 创建临时日志目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 模拟日志配置
            log_files = {
                'app': os.path.join(temp_dir, 'app.log'),
                'error': os.path.join(temp_dir, 'error.log'),
                'access': os.path.join(temp_dir, 'access.log'),
                'performance': os.path.join(temp_dir, 'performance.log')
            }
            
            # 模拟写入日志
            for log_type, log_file in log_files.items():
                with open(log_file, 'w', encoding='utf-8') as f:
                    f.write(f"[{datetime.now().isoformat()}] INFO - 测试{log_type}日志\n")
                    f.write(f"[{datetime.now().isoformat()}] DEBUG - 调试信息\n")
            
            # 检查日志文件
            created_files = []
            total_size = 0
            
            for log_type, log_file in log_files.items():
                if os.path.exists(log_file):
                    size = os.path.getsize(log_file)
                    created_files.append(log_type)
                    total_size += size
            
            print(f"  创建日志文件: {len(created_files)}个")
            print(f"  日志文件类型: {', '.join(created_files)}")
            print(f"  总日志大小: {total_size}字节")
            
            assert len(created_files) == 4
            assert total_size > 0
            
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir)
        
        return True
    
    def test_system_optimization_recommendations(self):
        """测试系统优化建议"""
        print("✓ 系统优化建议测试:")
        
        # 模拟不同的系统状态
        system_states = [
            {
                'memory_usage': 85.0,
                'cpu_usage': 45.0,
                'avg_response_time': 0.5,
                'error_rate': 1.0,
                'request_count': 1000
            },
            {
                'memory_usage': 45.0,
                'cpu_usage': 85.0,
                'avg_response_time': 1.5,
                'error_rate': 3.0,
                'request_count': 15000
            }
        ]
        
        for i, state in enumerate(system_states):
            recommendations = []
            
            if state['memory_usage'] > 70:
                recommendations.append("内存使用率较高，建议优化内存使用或增加内存")
            
            if state['cpu_usage'] > 70:
                recommendations.append("CPU使用率较高，建议优化算法或增加CPU资源")
            
            if state['avg_response_time'] > 1.0:
                recommendations.append("平均响应时间较长，建议优化数据库查询或添加缓存")
            
            if state['error_rate'] > 2.0:
                recommendations.append("错误率较高，建议检查错误日志并修复相关问题")
            
            if state['request_count'] > 10000:
                recommendations.append("请求量较大，建议考虑负载均衡和水平扩展")
            
            if not recommendations:
                recommendations.append("系统运行良好，继续保持")
            
            print(f"  系统状态{i+1}建议数: {len(recommendations)}")
            for j, rec in enumerate(recommendations[:2]):  # 只显示前2个
                print(f"    {j+1}. {rec}")
            
            assert len(recommendations) > 0
        
        return True
    
    def test_concurrent_monitoring(self):
        """测试并发监控"""
        print("✓ 并发监控测试:")
        
        # 模拟并发请求监控
        results = []
        
        def simulate_request(request_id):
            start_time = time.time()
            time.sleep(0.01)  # 模拟处理时间
            end_time = time.time()
            
            results.append({
                'request_id': request_id,
                'response_time': end_time - start_time,
                'timestamp': datetime.now().isoformat()
            })
        
        # 创建多个线程模拟并发请求
        threads = []
        for i in range(5):
            thread = threading.Thread(target=simulate_request, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print(f"  并发请求数: {len(results)}")
        print(f"  平均响应时间: {sum(r['response_time'] for r in results) / len(results):.4f}秒")
        
        assert len(results) == 5
        assert all(r['response_time'] > 0 for r in results)
        
        return True


def main():
    """主测试函数"""
    print("开始性能监控和优化系统测试...")
    print("=" * 50)
    
    tester = TestPerformanceMonitoring()
    
    tests = [
        ("性能指标记录测试", tester.test_performance_metrics_recording),
        ("性能摘要计算测试", tester.test_performance_summary_calculation),
        ("性能警告测试", tester.test_performance_alerts),
        ("缓存功能测试", tester.test_cache_functionality),
        ("日志系统测试", tester.test_logging_system),
        ("系统优化建议测试", tester.test_system_optimization_recommendations),
        ("并发监控测试", tester.test_concurrent_monitoring),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 性能监控和优化系统测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
