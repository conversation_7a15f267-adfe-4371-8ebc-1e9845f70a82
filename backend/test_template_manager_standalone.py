#!/usr/bin/env python3
"""
独立的法律文书模板管理器测试
"""

import re
from typing import Dict, List, Any, Optional
from collections import defaultdict
from datetime import datetime


class SimpleDocumentTemplate:
    """简化的法律文书模板类"""
    
    def __init__(self, template_id: str, name: str, category: str, content: str):
        self.template_id = template_id
        self.name = name
        self.category = category
        self.content = content
        self.variables = []
        self.description = ""
        self.usage_count = 0
        self.created_time = datetime.now().isoformat()
        self.tags = []
        
        self._extract_variables()
    
    def _extract_variables(self):
        """提取模板中的变量"""
        pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(pattern, self.content)
        self.variables = list(set(matches))
    
    def render(self, variables: Dict[str, str]) -> str:
        """渲染模板"""
        rendered_content = self.content
        
        for var_name, var_value in variables.items():
            placeholder = f"{{{{{var_name}}}}}"
            rendered_content = rendered_content.replace(placeholder, str(var_value))
        
        return rendered_content
    
    def validate_variables(self, variables: Dict[str, str]) -> List[str]:
        """验证变量完整性"""
        missing_vars = []
        for var in self.variables:
            if var not in variables or not variables[var]:
                missing_vars.append(var)
        return missing_vars
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "template_id": self.template_id,
            "name": self.name,
            "category": self.category,
            "content": self.content,
            "variables": self.variables,
            "description": self.description,
            "usage_count": self.usage_count,
            "created_time": self.created_time,
            "tags": self.tags
        }


class SimpleDocumentTemplateManager:
    """简化的法律文书模板管理器"""
    
    def __init__(self):
        self.templates = {}
        self.categories = defaultdict(list)
        self.tags_index = defaultdict(list)
        
        self._initialize_builtin_templates()
    
    def _initialize_builtin_templates(self):
        """初始化内置模板"""
        builtin_templates = [
            {
                "template_id": "contract_service_001",
                "name": "服务合同模板",
                "category": "合同",
                "description": "标准服务合同模板",
                "content": """
服务合同

甲方：{{甲方名称}}
乙方：{{乙方名称}}

服务内容：{{服务内容}}
服务期限：{{服务期限}}
服务费用：{{服务费用}}元

甲方签字：_______
乙方签字：_______
签订日期：{{签订日期}}
                """,
                "tags": ["合同", "服务"]
            },
            {
                "template_id": "lawsuit_civil_001",
                "name": "民事起诉状模板",
                "category": "诉讼文书",
                "description": "民事诉讼起诉状模板",
                "content": """
民事起诉状

原告：{{原告姓名}}，住址：{{原告住址}}
被告：{{被告姓名}}，住址：{{被告住址}}

诉讼请求：
{{诉讼请求}}

事实和理由：
{{事实和理由}}

此致
{{法院名称}}

具状人：{{原告姓名}}
{{起诉日期}}
                """,
                "tags": ["起诉状", "民事"]
            }
        ]
        
        for template_data in builtin_templates:
            template = SimpleDocumentTemplate(
                template_id=template_data["template_id"],
                name=template_data["name"],
                category=template_data["category"],
                content=template_data["content"]
            )
            template.description = template_data["description"]
            template.tags = template_data["tags"]
            
            self.add_template(template)
    
    def add_template(self, template: SimpleDocumentTemplate) -> bool:
        """添加模板"""
        try:
            self.templates[template.template_id] = template
            self.categories[template.category].append(template.template_id)
            
            for tag in template.tags:
                self.tags_index[tag].append(template.template_id)
            
            return True
        except Exception:
            return False
    
    def get_template(self, template_id: str) -> Optional[SimpleDocumentTemplate]:
        """获取模板"""
        return self.templates.get(template_id)
    
    def get_templates_by_category(self, category: str) -> List[SimpleDocumentTemplate]:
        """按分类获取模板"""
        template_ids = self.categories.get(category, [])
        return [self.templates[tid] for tid in template_ids if tid in self.templates]
    
    def search_templates(self, keyword: str) -> List[SimpleDocumentTemplate]:
        """搜索模板"""
        results = []
        keyword_lower = keyword.lower()
        
        for template in self.templates.values():
            if (keyword_lower in template.name.lower() or
                keyword_lower in template.description.lower() or
                any(keyword_lower in tag.lower() for tag in template.tags)):
                results.append(template)
        
        return results
    
    def get_all_categories(self) -> List[str]:
        """获取所有分类"""
        return list(self.categories.keys())
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        stats = {
            "total_templates": len(self.templates),
            "categories": {},
            "tags": {}
        }
        
        for category, template_ids in self.categories.items():
            stats["categories"][category] = len(template_ids)
        
        for tag, template_ids in self.tags_index.items():
            stats["tags"][tag] = len(template_ids)
        
        return stats


def test_template_initialization():
    """测试模板初始化"""
    manager = SimpleDocumentTemplateManager()
    
    print("✓ 模板初始化测试:")
    print(f"  总模板数: {len(manager.templates)}")
    print(f"  分类数: {len(manager.categories)}")
    
    # 验证内置模板
    assert len(manager.templates) >= 2
    assert "合同" in manager.categories
    assert "诉讼文书" in manager.categories
    
    return True


def test_template_retrieval():
    """测试模板获取"""
    manager = SimpleDocumentTemplateManager()
    
    # 测试按ID获取
    template = manager.get_template("contract_service_001")
    
    print("✓ 模板获取测试:")
    print(f"  获取模板: {template.name if template else 'None'}")
    
    assert template is not None
    assert template.name == "服务合同模板"
    
    return True


def test_template_search():
    """测试模板搜索"""
    manager = SimpleDocumentTemplateManager()
    
    # 测试关键词搜索
    results = manager.search_templates("合同")
    
    print("✓ 模板搜索测试:")
    print(f"  搜索'合同'结果: {len(results)}个")
    for result in results:
        print(f"    - {result.name}")
    
    assert len(results) >= 1
    
    return True


def test_template_by_category():
    """测试按分类获取模板"""
    manager = SimpleDocumentTemplateManager()
    
    # 测试按分类获取
    contract_templates = manager.get_templates_by_category("合同")
    lawsuit_templates = manager.get_templates_by_category("诉讼文书")
    
    print("✓ 按分类获取模板测试:")
    print(f"  合同类模板: {len(contract_templates)}个")
    print(f"  诉讼文书类模板: {len(lawsuit_templates)}个")
    
    assert len(contract_templates) >= 1
    assert len(lawsuit_templates) >= 1
    
    return True


def test_template_variables():
    """测试模板变量提取"""
    manager = SimpleDocumentTemplateManager()
    
    template = manager.get_template("contract_service_001")
    
    print("✓ 模板变量提取测试:")
    print(f"  模板变量数: {len(template.variables)}")
    print(f"  变量列表: {template.variables}")
    
    # 验证变量提取
    expected_vars = ["甲方名称", "乙方名称", "服务内容", "服务期限", "服务费用", "签订日期"]
    for var in expected_vars:
        assert var in template.variables, f"缺少变量: {var}"
    
    return True


def test_template_rendering():
    """测试模板渲染"""
    manager = SimpleDocumentTemplateManager()
    
    template = manager.get_template("contract_service_001")
    
    # 准备变量值
    variables = {
        "甲方名称": "北京科技有限公司",
        "乙方名称": "上海服务有限公司",
        "服务内容": "软件开发服务",
        "服务期限": "6个月",
        "服务费用": "50万",
        "签订日期": "2023年12月1日"
    }
    
    # 渲染模板
    rendered_content = template.render(variables)
    
    print("✓ 模板渲染测试:")
    print("  渲染结果预览:")
    print(rendered_content[:200] + "..." if len(rendered_content) > 200 else rendered_content)
    
    # 验证渲染结果
    assert "北京科技有限公司" in rendered_content
    assert "软件开发服务" in rendered_content
    assert "50万" in rendered_content
    
    return True


def test_template_validation():
    """测试模板变量验证"""
    manager = SimpleDocumentTemplateManager()
    
    template = manager.get_template("contract_service_001")
    
    # 测试完整变量
    complete_vars = {
        "甲方名称": "公司A",
        "乙方名称": "公司B",
        "服务内容": "咨询服务",
        "服务期限": "1年",
        "服务费用": "10万",
        "签订日期": "2023年12月1日"
    }
    
    missing_complete = template.validate_variables(complete_vars)
    
    # 测试不完整变量
    incomplete_vars = {
        "甲方名称": "公司A",
        "乙方名称": "公司B"
        # 缺少其他变量
    }
    
    missing_incomplete = template.validate_variables(incomplete_vars)
    
    print("✓ 模板变量验证测试:")
    print(f"  完整变量缺失: {len(missing_complete)}个")
    print(f"  不完整变量缺失: {len(missing_incomplete)}个")
    print(f"  缺失变量: {missing_incomplete}")
    
    assert len(missing_complete) == 0
    assert len(missing_incomplete) > 0
    
    return True


def test_template_statistics():
    """测试模板统计"""
    manager = SimpleDocumentTemplateManager()
    
    stats = manager.get_template_statistics()
    
    print("✓ 模板统计测试:")
    print(f"  总模板数: {stats['total_templates']}")
    print(f"  分类统计: {stats['categories']}")
    print(f"  标签统计: {stats['tags']}")
    
    assert stats['total_templates'] >= 2
    assert len(stats['categories']) >= 2
    
    return True


def test_template_addition():
    """测试添加新模板"""
    manager = SimpleDocumentTemplateManager()
    
    # 创建新模板
    new_template = SimpleDocumentTemplate(
        template_id="test_001",
        name="测试模板",
        category="测试",
        content="这是一个测试模板，包含变量{{测试变量}}。"
    )
    new_template.tags = ["测试"]
    
    # 添加模板
    success = manager.add_template(new_template)
    
    # 验证添加结果
    retrieved = manager.get_template("test_001")
    
    print("✓ 添加新模板测试:")
    print(f"  添加成功: {success}")
    print(f"  可以获取: {retrieved is not None}")
    print(f"  模板变量: {retrieved.variables if retrieved else 'None'}")
    
    assert success
    assert retrieved is not None
    assert "测试变量" in retrieved.variables
    
    return True


def main():
    """主测试函数"""
    print("开始独立法律文书模板管理器测试...")
    print("=" * 50)
    
    tests = [
        ("模板初始化测试", test_template_initialization),
        ("模板获取测试", test_template_retrieval),
        ("模板搜索测试", test_template_search),
        ("按分类获取模板测试", test_template_by_category),
        ("模板变量提取测试", test_template_variables),
        ("模板渲染测试", test_template_rendering),
        ("模板变量验证测试", test_template_validation),
        ("模板统计测试", test_template_statistics),
        ("添加新模板测试", test_template_addition),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
