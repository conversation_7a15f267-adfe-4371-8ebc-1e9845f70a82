#!/usr/bin/env python3
"""
独立的文书合规性检查器测试
"""

import re
from typing import Dict, List, Any, Optional
from collections import defaultdict
from datetime import datetime


class SimpleComplianceIssue:
    """简化的合规性问题类"""
    
    def __init__(self, issue_id: str, issue_type: str, severity: str, description: str):
        self.issue_id = issue_id
        self.issue_type = issue_type
        self.severity = severity
        self.description = description
        self.location = ""
        self.suggestion = ""
        self.legal_basis = ""
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "issue_id": self.issue_id,
            "issue_type": self.issue_type,
            "severity": self.severity,
            "description": self.description,
            "location": self.location,
            "suggestion": self.suggestion,
            "legal_basis": self.legal_basis
        }


class SimpleDocumentComplianceChecker:
    """简化的文书合规性检查器"""
    
    def __init__(self):
        self.format_rules = {
            "当事人信息": [
                {
                    "rule": "当事人格式",
                    "pattern": r"(甲方|乙方|原告|被告)[:：]\s*[^\n]+",
                    "description": "当事人信息格式应规范",
                    "severity": "high"
                }
            ],
            "日期格式": [
                {
                    "rule": "日期标准格式",
                    "pattern": r"\d{4}年\d{1,2}月\d{1,2}日",
                    "description": "日期应使用标准格式",
                    "severity": "medium"
                }
            ]
        }
        
        self.content_rules = {
            "必要条款": [
                {
                    "rule": "当事人条款",
                    "required_elements": ["甲方", "乙方"],
                    "description": "必须包含当事人信息",
                    "severity": "critical"
                }
            ]
        }
        
        self.legal_rules = {
            "违法内容": [
                {
                    "rule": "违法条款",
                    "forbidden_patterns": [
                        r"规避.*税收|避税|逃税",
                        r"洗钱|非法.*资金"
                    ],
                    "description": "不得包含违法内容",
                    "severity": "critical"
                }
            ],
            "不公平条款": [
                {
                    "rule": "过度免责",
                    "warning_patterns": [
                        r"不承担.*任何.*责任",
                        r"免除.*全部.*责任"
                    ],
                    "description": "免责条款可能过于宽泛",
                    "severity": "medium"
                }
            ]
        }
        
        self.template_requirements = {
            "合同": {
                "required_sections": ["当事人信息", "标的条款", "价款条款"],
                "format_requirements": ["标题", "签名栏"]
            },
            "起诉状": {
                "required_sections": ["当事人信息", "诉讼请求", "事实和理由"],
                "format_requirements": ["法院名称", "具状人"]
            }
        }
    
    def check_document_compliance(self, document_content: str, 
                                document_type: str = "通用") -> Dict[str, Any]:
        """检查文书合规性"""
        try:
            issues = []
            
            # 1. 格式检查
            format_issues = self._check_format_compliance(document_content)
            issues.extend(format_issues)
            
            # 2. 内容检查
            content_issues = self._check_content_compliance(document_content)
            issues.extend(content_issues)
            
            # 3. 法律合规检查
            legal_issues = self._check_legal_compliance(document_content)
            issues.extend(legal_issues)
            
            # 4. 模板特定检查
            if document_type in self.template_requirements:
                template_issues = self._check_template_compliance(document_content, document_type)
                issues.extend(template_issues)
            
            # 5. 计算合规性得分
            compliance_score = self._calculate_compliance_score(issues)
            
            # 6. 生成合规性报告
            compliance_report = self._generate_compliance_report(issues, compliance_score)
            
            return {
                "compliance_score": compliance_score,
                "total_issues": len(issues),
                "issues": [issue.to_dict() for issue in issues],
                "compliance_report": compliance_report,
                "check_timestamp": datetime.now().isoformat(),
                "document_type": document_type,
                "recommendations": self._generate_recommendations(issues)
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "check_timestamp": datetime.now().isoformat()
            }
    
    def _check_format_compliance(self, content: str) -> List[SimpleComplianceIssue]:
        """检查格式合规性"""
        issues = []
        issue_id = 1
        
        for category, rules in self.format_rules.items():
            for rule in rules:
                pattern = rule["pattern"]
                description = rule["description"]
                severity = rule["severity"]
                
                if not re.search(pattern, content, re.MULTILINE | re.IGNORECASE):
                    issue = SimpleComplianceIssue(
                        issue_id=f"format_{issue_id}",
                        issue_type="格式问题",
                        severity=severity,
                        description=f"{category}: {description}"
                    )
                    issue.suggestion = f"请检查{category}是否符合规范"
                    issues.append(issue)
                    issue_id += 1
        
        return issues
    
    def _check_content_compliance(self, content: str) -> List[SimpleComplianceIssue]:
        """检查内容合规性"""
        issues = []
        issue_id = 1000
        
        for category, rules in self.content_rules.items():
            for rule in rules:
                description = rule["description"]
                severity = rule["severity"]
                
                if "required_elements" in rule:
                    missing_elements = []
                    for element in rule["required_elements"]:
                        if element not in content:
                            missing_elements.append(element)
                    
                    if missing_elements:
                        issue = SimpleComplianceIssue(
                            issue_id=f"content_{issue_id}",
                            issue_type="内容缺失",
                            severity=severity,
                            description=f"{description}: 缺少{', '.join(missing_elements)}"
                        )
                        issue.suggestion = f"请添加{', '.join(missing_elements)}相关内容"
                        issues.append(issue)
                        issue_id += 1
        
        return issues
    
    def _check_legal_compliance(self, content: str) -> List[SimpleComplianceIssue]:
        """检查法律合规性"""
        issues = []
        issue_id = 2000
        
        for category, rules in self.legal_rules.items():
            for rule in rules:
                description = rule["description"]
                severity = rule["severity"]
                
                # 检查禁止内容
                if "forbidden_patterns" in rule:
                    for pattern in rule["forbidden_patterns"]:
                        matches = list(re.finditer(pattern, content, re.IGNORECASE))
                        for match in matches:
                            issue = SimpleComplianceIssue(
                                issue_id=f"legal_{issue_id}",
                                issue_type="法律合规问题",
                                severity=severity,
                                description=f"{description}: {match.group(0)}"
                            )
                            issue.location = f"位置: {match.start()}-{match.end()}"
                            issue.suggestion = "建议删除或修改相关内容"
                            issues.append(issue)
                            issue_id += 1
                
                # 检查警告内容
                if "warning_patterns" in rule:
                    for pattern in rule["warning_patterns"]:
                        matches = list(re.finditer(pattern, content, re.IGNORECASE))
                        for match in matches:
                            issue = SimpleComplianceIssue(
                                issue_id=f"legal_{issue_id}",
                                issue_type="法律风险提醒",
                                severity=severity,
                                description=f"{description}: {match.group(0)}"
                            )
                            issue.location = f"位置: {match.start()}-{match.end()}"
                            issue.suggestion = "建议审查相关条款的合理性"
                            issues.append(issue)
                            issue_id += 1
        
        return issues
    
    def _check_template_compliance(self, content: str, document_type: str) -> List[SimpleComplianceIssue]:
        """检查模板特定合规性"""
        issues = []
        issue_id = 3000
        
        requirements = self.template_requirements.get(document_type, {})
        
        # 检查必需章节
        required_sections = requirements.get("required_sections", [])
        for section in required_sections:
            if section not in content:
                issue = SimpleComplianceIssue(
                    issue_id=f"template_{issue_id}",
                    issue_type="模板要求",
                    severity="high",
                    description=f"{document_type}缺少必需章节: {section}"
                )
                issue.suggestion = f"请添加{section}相关内容"
                issues.append(issue)
                issue_id += 1
        
        return issues
    
    def _calculate_compliance_score(self, issues: List[SimpleComplianceIssue]) -> float:
        """计算合规性得分"""
        if not issues:
            return 100.0
        
        deduction_map = {
            "critical": 20,
            "high": 10,
            "medium": 5,
            "low": 2
        }
        
        total_deduction = sum(deduction_map.get(issue.severity, 2) for issue in issues)
        score = max(0.0, 100.0 - total_deduction)
        
        return round(score, 2)
    
    def _generate_compliance_report(self, issues: List[SimpleComplianceIssue], score: float) -> str:
        """生成合规性报告"""
        if not issues:
            return "文书合规性检查通过，未发现问题。"
        
        report_lines = [
            "文书合规性检查报告",
            "=" * 30,
            f"合规性得分: {score}/100",
            f"发现问题: {len(issues)}个",
            ""
        ]
        
        # 按严重程度分组
        severity_groups = defaultdict(list)
        for issue in issues:
            severity_groups[issue.severity].append(issue)
        
        severity_names = {
            "critical": "严重问题",
            "high": "重要问题", 
            "medium": "一般问题",
            "low": "轻微问题"
        }
        
        for severity in ["critical", "high", "medium", "low"]:
            if severity in severity_groups:
                report_lines.append(f"{severity_names[severity]} ({len(severity_groups[severity])}个):")
                for issue in severity_groups[severity][:2]:  # 只显示前2个
                    report_lines.append(f"  - {issue.description}")
                report_lines.append("")
        
        if score >= 90:
            report_lines.append("总体评价: 合规性良好")
        elif score >= 70:
            report_lines.append("总体评价: 基本合规，建议优化")
        else:
            report_lines.append("总体评价: 存在合规风险，需要修改")
        
        return "\n".join(report_lines)
    
    def _generate_recommendations(self, issues: List[SimpleComplianceIssue]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        critical_issues = [i for i in issues if i.severity == "critical"]
        high_issues = [i for i in issues if i.severity == "high"]
        
        if critical_issues:
            recommendations.append("立即处理严重合规问题，避免法律风险")
        
        if high_issues:
            recommendations.append("尽快解决重要问题，完善文书内容")
        
        if not recommendations:
            recommendations.append("文书质量良好，可以使用")
        
        return recommendations


def test_perfect_document_compliance():
    """测试完美文书的合规性检查"""
    checker = SimpleDocumentComplianceChecker()
    
    perfect_document = """
    服务合同
    
    甲方：北京科技有限公司
    乙方：上海服务有限公司
    
    服务内容：软件开发服务
    服务费用：10万元
    
    签订日期：2023年12月1日
    
    甲方签字：_______
    乙方签字：_______
    """
    
    result = checker.check_document_compliance(perfect_document, "合同")
    
    print("✓ 完美文书合规性检查测试:")
    print(f"  合规性得分: {result['compliance_score']}")
    print(f"  发现问题数: {result['total_issues']}")
    print(f"  建议数量: {len(result['recommendations'])}")
    
    assert result['compliance_score'] >= 80  # 应该有较高得分
    
    return True


def test_problematic_document_compliance():
    """测试有问题文书的合规性检查"""
    checker = SimpleDocumentComplianceChecker()
    
    problematic_document = """
    这是一个有问题的文书
    
    本合同帮助甲方规避税收
    甲方不承担任何责任
    
    没有当事人信息
    没有日期格式
    """
    
    result = checker.check_document_compliance(problematic_document, "合同")
    
    print("✓ 有问题文书合规性检查测试:")
    print(f"  合规性得分: {result['compliance_score']}")
    print(f"  发现问题数: {result['total_issues']}")
    print("  问题类型:")
    for issue in result['issues']:
        print(f"    - {issue['severity']}: {issue['description']}")
    
    assert result['compliance_score'] < 80  # 应该有较低得分
    assert result['total_issues'] > 0  # 应该发现问题
    
    return True


def test_format_compliance_check():
    """测试格式合规性检查"""
    checker = SimpleDocumentComplianceChecker()
    
    # 格式不规范的文书
    bad_format_document = """
    合同内容
    
    当事人信息缺失
    日期格式错误：2023-12-01
    """
    
    result = checker.check_document_compliance(bad_format_document)
    
    print("✓ 格式合规性检查测试:")
    print(f"  发现格式问题: {any('格式问题' in issue['issue_type'] for issue in result['issues'])}")
    
    format_issues = [issue for issue in result['issues'] if '格式问题' in issue['issue_type']]
    print(f"  格式问题数量: {len(format_issues)}")
    
    assert len(format_issues) > 0  # 应该发现格式问题
    
    return True


def test_legal_compliance_check():
    """测试法律合规性检查"""
    checker = SimpleDocumentComplianceChecker()
    
    # 包含违法内容的文书
    illegal_document = """
    服务合同
    
    甲方：公司A
    乙方：公司B
    
    本合同帮助甲方规避税收
    甲方不承担任何责任，免除全部责任
    """
    
    result = checker.check_document_compliance(illegal_document)
    
    print("✓ 法律合规性检查测试:")
    
    legal_issues = [issue for issue in result['issues'] if '法律' in issue['issue_type']]
    print(f"  法律问题数量: {len(legal_issues)}")
    
    critical_issues = [issue for issue in result['issues'] if issue['severity'] == 'critical']
    print(f"  严重问题数量: {len(critical_issues)}")
    
    assert len(legal_issues) > 0  # 应该发现法律问题
    assert len(critical_issues) > 0  # 应该有严重问题
    
    return True


def test_template_specific_compliance():
    """测试模板特定合规性检查"""
    checker = SimpleDocumentComplianceChecker()
    
    # 缺少必需章节的合同
    incomplete_contract = """
    服务合同
    
    甲方：公司A
    乙方：公司B
    
    # 缺少标的条款和价款条款
    """
    
    result = checker.check_document_compliance(incomplete_contract, "合同")
    
    print("✓ 模板特定合规性检查测试:")
    
    template_issues = [issue for issue in result['issues'] if '模板要求' in issue['issue_type']]
    print(f"  模板问题数量: {len(template_issues)}")
    
    for issue in template_issues:
        print(f"    - {issue['description']}")
    
    assert len(template_issues) > 0  # 应该发现模板问题
    
    return True


def test_compliance_score_calculation():
    """测试合规性得分计算"""
    checker = SimpleDocumentComplianceChecker()
    
    # 不同严重程度的问题
    mixed_issues_document = """
    有问题的合同
    
    甲方：公司A  # 有当事人信息
    
    本合同规避税收  # 严重问题
    甲方不承担任何责任  # 中等问题
    """
    
    result = checker.check_document_compliance(mixed_issues_document, "合同")
    
    print("✓ 合规性得分计算测试:")
    print(f"  合规性得分: {result['compliance_score']}")
    print(f"  问题分布:")
    
    severity_count = defaultdict(int)
    for issue in result['issues']:
        severity_count[issue['severity']] += 1
    
    for severity, count in severity_count.items():
        print(f"    {severity}: {count}个")
    
    # 验证得分合理性
    assert 0 <= result['compliance_score'] <= 100
    assert result['compliance_score'] < 100  # 有问题应该扣分
    
    return True


def test_compliance_report_generation():
    """测试合规性报告生成"""
    checker = SimpleDocumentComplianceChecker()
    
    document_with_issues = """
    问题文书
    
    甲方：公司A
    
    本合同规避税收
    甲方不承担任何责任
    """
    
    result = checker.check_document_compliance(document_with_issues)
    report = result['compliance_report']
    
    print("✓ 合规性报告生成测试:")
    print("  报告内容预览:")
    print(report[:300] + "..." if len(report) > 300 else report)
    
    # 验证报告包含关键信息
    assert "合规性检查报告" in report
    assert "合规性得分" in report
    assert "发现问题" in report
    
    return True


def test_recommendations_generation():
    """测试建议生成"""
    checker = SimpleDocumentComplianceChecker()
    
    # 有严重问题的文书
    critical_document = """
    违法合同
    
    本合同帮助规避税收
    进行洗钱活动
    """
    
    result = checker.check_document_compliance(critical_document)
    recommendations = result['recommendations']
    
    print("✓ 建议生成测试:")
    print(f"  生成建议数: {len(recommendations)}")
    for i, rec in enumerate(recommendations, 1):
        print(f"    {i}. {rec}")
    
    assert len(recommendations) > 0
    assert any("严重" in rec for rec in recommendations)  # 应该有严重问题的建议
    
    return True


def main():
    """主测试函数"""
    print("开始独立文书合规性检查器测试...")
    print("=" * 50)
    
    tests = [
        ("完美文书合规性检查测试", test_perfect_document_compliance),
        ("有问题文书合规性检查测试", test_problematic_document_compliance),
        ("格式合规性检查测试", test_format_compliance_check),
        ("法律合规性检查测试", test_legal_compliance_check),
        ("模板特定合规性检查测试", test_template_specific_compliance),
        ("合规性得分计算测试", test_compliance_score_calculation),
        ("合规性报告生成测试", test_compliance_report_generation),
        ("建议生成测试", test_recommendations_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
