#!/usr/bin/env python3
"""
独立的合同风险分析器测试
"""

import re
from typing import Dict, List, Any, Optional
from collections import defaultdict
from datetime import datetime


class SimpleRiskItem:
    """简化的风险项类"""
    
    def __init__(self, risk_id: str, risk_type: str, severity: str, 
                 description: str, location: str = "", suggestion: str = ""):
        self.risk_id = risk_id
        self.risk_type = risk_type
        self.severity = severity
        self.description = description
        self.location = location
        self.suggestion = suggestion
        self.confidence = 0.0
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "risk_id": self.risk_id,
            "risk_type": self.risk_type,
            "severity": self.severity,
            "description": self.description,
            "location": self.location,
            "suggestion": self.suggestion,
            "confidence": self.confidence
        }


class SimpleContractRiskAnalyzer:
    """简化的合同风险分析器"""
    
    def __init__(self):
        self.risk_patterns = {
            "不公平条款": [
                {
                    "pattern": r"甲方.*免责|甲方不承担.*责任",
                    "severity": "high",
                    "description": "甲方免责条款可能过于宽泛",
                    "suggestion": "建议明确甲方免责的具体情形和范围"
                },
                {
                    "pattern": r"乙方承担.*全部责任|乙方负责.*所有",
                    "severity": "medium",
                    "description": "乙方责任条款可能过重",
                    "suggestion": "建议平衡双方责任分配"
                }
            ],
            "法律风险": [
                {
                    "pattern": r"违反.*法律|不符合.*规定",
                    "severity": "critical",
                    "description": "可能存在违法条款",
                    "suggestion": "建议删除或修改违法内容"
                },
                {
                    "pattern": r"排除.*适用|不适用.*法律",
                    "severity": "high",
                    "description": "试图排除法律适用",
                    "suggestion": "建议删除排除法律适用的条款"
                }
            ],
            "商业风险": [
                {
                    "pattern": r"价格.*调整|费用.*变更",
                    "severity": "medium",
                    "description": "存在价格调整风险",
                    "suggestion": "建议明确价格调整的条件和幅度"
                },
                {
                    "pattern": r"不可抗力",
                    "severity": "low",
                    "description": "不可抗力条款需要关注",
                    "suggestion": "建议明确不可抗力的范围和处理程序"
                }
            ]
        }
        
        self.risk_weights = {
            "critical": 10,
            "high": 7,
            "medium": 4,
            "low": 1
        }
    
    def analyze_contract_risks(self, contract_text: str) -> Dict[str, Any]:
        """分析合同风险"""
        try:
            # 基于规则的风险识别
            risks = self._identify_rule_based_risks(contract_text)
            
            # 综合风险评估
            risk_summary = self._calculate_risk_summary(risks)
            
            # 生成风险报告
            risk_report = self._generate_risk_report(risks, risk_summary)
            
            return {
                "risks": [risk.to_dict() for risk in risks],
                "risk_summary": risk_summary,
                "risk_report": risk_report,
                "analysis_timestamp": datetime.now().isoformat(),
                "total_risks": len(risks),
                "high_severity_count": len([r for r in risks if r.severity in ["high", "critical"]])
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    def _identify_rule_based_risks(self, text: str) -> List[SimpleRiskItem]:
        """基于规则识别风险"""
        risks = []
        risk_id = 1
        
        for risk_category, patterns in self.risk_patterns.items():
            for pattern_info in patterns:
                pattern = pattern_info["pattern"]
                matches = list(re.finditer(pattern, text, re.IGNORECASE))
                
                for match in matches:
                    risk = SimpleRiskItem(
                        risk_id=f"rule_{risk_id}",
                        risk_type=risk_category,
                        severity=pattern_info["severity"],
                        description=pattern_info["description"],
                        location=f"位置: {match.start()}-{match.end()}",
                        suggestion=pattern_info["suggestion"]
                    )
                    risk.confidence = 0.8
                    risks.append(risk)
                    risk_id += 1
        
        return risks
    
    def _calculate_risk_summary(self, risks: List[SimpleRiskItem]) -> Dict[str, Any]:
        """计算风险摘要"""
        if not risks:
            return {
                "total_risks": 0,
                "risk_score": 0,
                "risk_level": "low",
                "severity_distribution": {},
                "type_distribution": {}
            }
        
        # 统计严重程度分布
        severity_counts = defaultdict(int)
        for risk in risks:
            severity_counts[risk.severity] += 1
        
        # 统计风险类型分布
        type_counts = defaultdict(int)
        for risk in risks:
            type_counts[risk.risk_type] += 1
        
        # 计算风险得分
        total_score = sum(self.risk_weights.get(risk.severity, 1) for risk in risks)
        
        # 确定整体风险等级
        if total_score >= 50:
            risk_level = "critical"
        elif total_score >= 30:
            risk_level = "high"
        elif total_score >= 15:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        return {
            "total_risks": len(risks),
            "risk_score": total_score,
            "risk_level": risk_level,
            "severity_distribution": dict(severity_counts),
            "type_distribution": dict(type_counts)
        }
    
    def _generate_risk_report(self, risks: List[SimpleRiskItem], summary: Dict[str, Any]) -> str:
        """生成风险报告"""
        if not risks:
            return "本合同未发现明显风险点。"
        
        report_lines = [
            f"合同风险分析报告",
            f"=" * 30,
            f"总体风险等级: {summary['risk_level'].upper()}",
            f"风险总数: {summary['total_risks']}",
            f"风险得分: {summary['risk_score']}",
            "",
            "主要风险点:",
        ]
        
        # 按严重程度排序风险
        sorted_risks = sorted(risks, key=lambda x: self.risk_weights.get(x.severity, 1), reverse=True)
        
        for i, risk in enumerate(sorted_risks[:5], 1):  # 只显示前5个风险
            report_lines.extend([
                f"{i}. [{risk.severity.upper()}] {risk.description}",
                f"   建议: {risk.suggestion}",
                ""
            ])
        
        return "\n".join(report_lines)


def test_basic_risk_analysis():
    """测试基本风险分析"""
    analyzer = SimpleContractRiskAnalyzer()
    
    # 包含多种风险的合同文本
    risky_contract = """
    服务合同
    
    第一条 甲方免责条款
    甲方不承担任何责任，包括但不限于直接损失、间接损失等。
    
    第二条 乙方责任
    乙方承担全部责任，包括所有可能的风险和损失。
    
    第三条 价格调整
    甲方有权根据市场情况随时调整价格和费用。
    
    第四条 不可抗力
    因不可抗力导致的损失由乙方承担。
    
    第五条 法律适用
    本合同不适用相关法律法规的限制性规定。
    """
    
    result = analyzer.analyze_contract_risks(risky_contract)
    
    print("✓ 基本风险分析测试:")
    print(f"  风险总数: {result['total_risks']}")
    print(f"  高风险数: {result['high_severity_count']}")
    print(f"  风险等级: {result['risk_summary']['risk_level']}")
    print(f"  风险得分: {result['risk_summary']['risk_score']}")
    
    # 验证是否识别到风险
    assert result['total_risks'] > 0
    assert result['high_severity_count'] > 0
    
    return True


def test_risk_type_identification():
    """测试风险类型识别"""
    analyzer = SimpleContractRiskAnalyzer()
    
    test_cases = [
        ("甲方不承担任何责任", "不公平条款"),
        ("本合同不适用法律规定", "法律风险"),
        ("价格可能随时调整", "商业风险"),
        ("因不可抗力导致的问题", "商业风险")
    ]
    
    print("✓ 风险类型识别测试:")
    for text, expected_type in test_cases:
        result = analyzer.analyze_contract_risks(text)
        
        if result['total_risks'] > 0:
            identified_types = [risk['risk_type'] for risk in result['risks']]
            found_expected = expected_type in identified_types
            status = "✓" if found_expected else "✗"
            print(f"  {status} '{text}' -> {identified_types}")
        else:
            print(f"  ✗ '{text}' -> 未识别到风险")
    
    return True


def test_severity_assessment():
    """测试严重程度评估"""
    analyzer = SimpleContractRiskAnalyzer()
    
    test_cases = [
        ("违反相关法律规定", "critical"),
        ("甲方不承担责任", "high"),
        ("价格可能调整", "medium"),
        ("不可抗力条款", "low")
    ]
    
    print("✓ 严重程度评估测试:")
    for text, expected_severity in test_cases:
        result = analyzer.analyze_contract_risks(text)
        
        if result['total_risks'] > 0:
            severities = [risk['severity'] for risk in result['risks']]
            found_expected = expected_severity in severities
            status = "✓" if found_expected else "✗"
            print(f"  {status} '{text}' -> {severities}")
        else:
            print(f"  ✗ '{text}' -> 未识别到风险")
    
    return True


def test_risk_summary_calculation():
    """测试风险摘要计算"""
    analyzer = SimpleContractRiskAnalyzer()
    
    # 创建包含不同严重程度风险的文本
    mixed_risk_text = """
    甲方不承担任何责任。
    本合同违反相关法律规定。
    价格可能随时调整。
    存在不可抗力情况。
    """
    
    result = analyzer.analyze_contract_risks(mixed_risk_text)
    summary = result['risk_summary']
    
    print("✓ 风险摘要计算测试:")
    print(f"  总风险数: {summary['total_risks']}")
    print(f"  风险得分: {summary['risk_score']}")
    print(f"  风险等级: {summary['risk_level']}")
    print(f"  严重程度分布: {summary['severity_distribution']}")
    print(f"  类型分布: {summary['type_distribution']}")
    
    # 验证摘要计算
    assert summary['total_risks'] > 0
    assert summary['risk_score'] > 0
    assert summary['risk_level'] in ['low', 'medium', 'high', 'critical']
    
    return True


def test_risk_report_generation():
    """测试风险报告生成"""
    analyzer = SimpleContractRiskAnalyzer()
    
    risky_text = """
    甲方免责：甲方不承担任何责任。
    违法条款：本合同违反相关法律规定。
    """
    
    result = analyzer.analyze_contract_risks(risky_text)
    report = result['risk_report']
    
    print("✓ 风险报告生成测试:")
    print("报告内容预览:")
    print(report[:200] + "..." if len(report) > 200 else report)
    
    # 验证报告包含关键信息
    assert "风险分析报告" in report
    assert "风险等级" in report
    assert "建议" in report
    
    return True


def test_no_risk_scenario():
    """测试无风险场景"""
    analyzer = SimpleContractRiskAnalyzer()
    
    safe_contract = """
    正常的服务合同
    
    第一条 服务内容
    甲方为乙方提供技术服务。
    
    第二条 双方权利义务
    甲乙双方应当按照合同约定履行各自义务。
    
    第三条 合同生效
    本合同自双方签字之日起生效。
    """
    
    result = analyzer.analyze_contract_risks(safe_contract)
    
    print("✓ 无风险场景测试:")
    print(f"  风险总数: {result['total_risks']}")
    print(f"  风险等级: {result['risk_summary']['risk_level']}")
    
    # 验证无风险或低风险
    assert result['risk_summary']['risk_level'] in ['low', 'medium']  # 允许低到中等风险
    
    return True


def test_comprehensive_risk_analysis():
    """测试综合风险分析"""
    analyzer = SimpleContractRiskAnalyzer()
    
    # 复杂的高风险合同
    complex_contract = """
    高风险服务合同
    
    第一条 免责声明
    甲方对任何直接或间接损失不承担责任，包括但不限于经济损失、数据丢失等。
    
    第二条 乙方义务
    乙方承担全部责任和风险，包括所有可能的法律后果。
    
    第三条 价格条款
    甲方有权根据市场变化随时调整价格和费用标准。
    
    第四条 法律适用
    本合同排除适用消费者权益保护法等相关法律规定。
    
    第五条 争议解决
    如发生争议，乙方不得提起诉讼，只能接受甲方的决定。
    
    第六条 不可抗力
    因不可抗力造成的所有损失均由乙方承担。
    """
    
    result = analyzer.analyze_contract_risks(complex_contract)
    
    print("✓ 综合风险分析测试:")
    print(f"  风险总数: {result['total_risks']}")
    print(f"  高严重风险数: {result['high_severity_count']}")
    print(f"  风险得分: {result['risk_summary']['risk_score']}")
    print(f"  整体风险等级: {result['risk_summary']['risk_level']}")
    
    # 按风险类型统计
    type_dist = result['risk_summary']['type_distribution']
    print(f"  风险类型分布: {type_dist}")
    
    # 验证高风险合同的识别
    assert result['total_risks'] >= 3  # 应该识别出多个风险
    assert result['high_severity_count'] >= 1  # 应该有高严重度风险
    assert result['risk_summary']['risk_level'] in ['high', 'critical']  # 整体风险应该较高
    
    return True


def main():
    """主测试函数"""
    print("开始独立合同风险分析器测试...")
    print("=" * 50)
    
    tests = [
        ("基本风险分析测试", test_basic_risk_analysis),
        ("风险类型识别测试", test_risk_type_identification),
        ("严重程度评估测试", test_severity_assessment),
        ("风险摘要计算测试", test_risk_summary_calculation),
        ("风险报告生成测试", test_risk_report_generation),
        ("无风险场景测试", test_no_risk_scenario),
        ("综合风险分析测试", test_comprehensive_risk_analysis),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
