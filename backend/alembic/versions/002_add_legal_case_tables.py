"""添加法律案例相关表

Revision ID: 002
Revises: 001
Create Date: 2024-01-02 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库 - 添加法律案例相关表"""

    # 创建法条表
    op.create_table('legal_articles',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('article_number', sa.String(length=50), nullable=True),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('law_name', sa.String(length=200), nullable=False),
        sa.Column('law_category', sa.String(length=100), nullable=True),
        sa.Column('chapter', sa.String(length=100), nullable=True),
        sa.Column('section', sa.String(length=100), nullable=True),
        sa.Column('effective_date', sa.Date(), nullable=True),
        sa.Column('expiry_date', sa.Date(), nullable=True),
        sa.Column('status', sa.Enum('active', 'archived', 'deleted', name='casestatus'), nullable=False),
        sa.Column('parent_article_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('keywords', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('extra_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['parent_article_id'], ['legal_articles.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        comment='法条表'
    )
    op.create_index(op.f('ix_legal_articles_id'), 'legal_articles', ['id'], unique=False)
    op.create_index(op.f('ix_legal_articles_article_number'), 'legal_articles', ['article_number'], unique=False)
    op.create_index(op.f('ix_legal_articles_law_name'), 'legal_articles', ['law_name'], unique=False)
    op.create_index(op.f('ix_legal_articles_law_category'), 'legal_articles', ['law_category'], unique=False)
    op.create_index(op.f('ix_legal_articles_status'), 'legal_articles', ['status'], unique=False)
    op.create_index(op.f('ix_legal_articles_created_at'), 'legal_articles', ['created_at'], unique=False)

    # 创建法律案例表
    op.create_table('legal_cases',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('case_number', sa.String(length=100), nullable=False),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('court_name', sa.String(length=200), nullable=False),
        sa.Column('case_type', sa.Enum('civil', 'criminal', 'administrative', 'commercial', 'labor', 'intellectual_property', 'environmental', 'other', name='casetype'), nullable=False),
        sa.Column('judgment_date', sa.Date(), nullable=True),
        sa.Column('trial_procedure', sa.String(length=50), nullable=True),
        sa.Column('parties', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('case_summary', sa.Text(), nullable=True),
        sa.Column('case_facts', sa.Text(), nullable=True),
        sa.Column('dispute_focus', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('court_opinion', sa.Text(), nullable=True),
        sa.Column('judgment_result', sa.Text(), nullable=True),
        sa.Column('related_articles', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('legal_basis', sa.Text(), nullable=True),
        sa.Column('keywords', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('precedent_value', sa.String(length=20), nullable=True),
        sa.Column('citation_count', sa.String(), nullable=True),
        sa.Column('status', sa.Enum('active', 'archived', 'deleted', name='casestatus'), nullable=False),
        sa.Column('is_public', sa.Boolean(), nullable=True),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.Column('extra_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        comment='法律案例表'
    )
    op.create_index(op.f('ix_legal_cases_id'), 'legal_cases', ['id'], unique=False)
    op.create_index(op.f('ix_legal_cases_case_number'), 'legal_cases', ['case_number'], unique=True)
    op.create_index(op.f('ix_legal_cases_title'), 'legal_cases', ['title'], unique=False)
    op.create_index(op.f('ix_legal_cases_court_name'), 'legal_cases', ['court_name'], unique=False)
    op.create_index(op.f('ix_legal_cases_case_type'), 'legal_cases', ['case_type'], unique=False)
    op.create_index(op.f('ix_legal_cases_judgment_date'), 'legal_cases', ['judgment_date'], unique=False)
    op.create_index(op.f('ix_legal_cases_status'), 'legal_cases', ['status'], unique=False)
    op.create_index(op.f('ix_legal_cases_created_by'), 'legal_cases', ['created_by'], unique=False)
    op.create_index(op.f('ix_legal_cases_created_at'), 'legal_cases', ['created_at'], unique=False)

    # 创建案例相似度表
    op.create_table('case_similarities',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('case_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('similar_case_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('similarity_score', sa.String(), nullable=False),
        sa.Column('similarity_type', sa.String(length=50), nullable=True),
        sa.Column('algorithm_version', sa.String(length=20), nullable=True),
        sa.Column('computed_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['case_id'], ['legal_cases.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['similar_case_id'], ['legal_cases.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        comment='案例相似度表'
    )
    op.create_index(op.f('ix_case_similarities_id'), 'case_similarities', ['id'], unique=False)
    op.create_index(op.f('ix_case_similarities_case_id'), 'case_similarities', ['case_id'], unique=False)
    op.create_index(op.f('ix_case_similarities_similar_case_id'), 'case_similarities', ['similar_case_id'], unique=False)

    # 创建全文搜索索引
    op.execute("""
        CREATE INDEX ix_legal_cases_title_gin 
        ON legal_cases 
        USING gin(to_tsvector('chinese', title))
    """)
    
    op.execute("""
        CREATE INDEX ix_legal_cases_case_summary_gin 
        ON legal_cases 
        USING gin(to_tsvector('chinese', case_summary))
    """)
    
    op.execute("""
        CREATE INDEX ix_legal_articles_content_gin 
        ON legal_articles 
        USING gin(to_tsvector('chinese', content))
    """)


def downgrade() -> None:
    """降级数据库 - 删除法律案例相关表"""

    # 删除全文搜索索引
    op.execute("DROP INDEX IF EXISTS ix_legal_cases_title_gin")
    op.execute("DROP INDEX IF EXISTS ix_legal_cases_case_summary_gin")
    op.execute("DROP INDEX IF EXISTS ix_legal_articles_content_gin")

    # 删除表（按依赖关系逆序）
    op.drop_table('case_similarities')
    op.drop_table('legal_cases')
    op.drop_table('legal_articles')

    # 删除枚举类型
    op.execute('DROP TYPE IF EXISTS casetype')
    # 注意：casestatus 枚举类型被两个表共用，所以不删除
