"""添加加密字段支持

Revision ID: 001_add_encryption_fields
Revises: 
Create Date: 2025-08-26 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_add_encryption_fields'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    升级数据库架构，添加加密字段支持
    """
    # 创建用户表
    op.create_table(
        'users',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('username', sa.String(50), unique=True, nullable=False, index=True),
        sa.Column('email', sa.String(500), unique=True, nullable=False, index=True),  # 加密后长度增加
        sa.Column('password_hash', sa.String(255), nullable=False),
        sa.Column('full_name', sa.String(500), nullable=True),  # 加密后长度增加
        sa.Column('phone', sa.String(500), nullable=True),  # 加密后长度增加
        sa.Column('user_type', sa.Enum('individual', 'enterprise', 'lawyer', name='usertype'), 
                 default='individual', nullable=False, index=True),
        sa.Column('status', sa.Enum('active', 'inactive', 'suspended', name='userstatus'), 
                 default='active', nullable=False, index=True),
        sa.Column('email_verified', sa.Boolean, default=False),
        sa.Column('phone_verified', sa.Boolean, default=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False, index=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('username'),
        sa.UniqueConstraint('email'),
        comment='用户表'
    )
    
    # 创建用户配置表
    op.create_table(
        'user_profiles',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False, unique=True, index=True),
        sa.Column('avatar_url', sa.String(255), nullable=True),
        sa.Column('bio', sa.Text, nullable=True),
        sa.Column('location', sa.String(500), nullable=True),  # 加密后长度增加
        sa.Column('specialization', sa.String(500), nullable=True),  # 加密后长度增加
        sa.Column('license_number', sa.String(500), nullable=True),  # 加密后长度增加
        sa.Column('company_name', sa.String(500), nullable=True),  # 加密后长度增加
        sa.Column('company_position', sa.String(500), nullable=True),  # 加密后长度增加
        sa.Column('preferences', postgresql.JSONB, default=dict),
        sa.Column('total_questions', sa.String, default='0'),
        sa.Column('total_contracts', sa.String, default='0'),
        sa.Column('total_documents', sa.String, default='0'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id'),
        comment='用户配置表'
    )
    
    # 创建用户会话表
    op.create_table(
        'user_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column('session_token', sa.String(255), unique=True, nullable=False, index=True),
        sa.Column('refresh_token', sa.String(255), unique=True, nullable=True, index=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False, index=True),
        sa.Column('ip_address', postgresql.INET, nullable=True),
        sa.Column('user_agent', sa.Text, nullable=True),
        sa.Column('device_info', postgresql.JSONB, default=dict),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False, index=True),
        sa.Column('last_accessed_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('session_token'),
        comment='用户会话表'
    )
    
    # 创建审计日志表
    op.create_table(
        'audit_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True, index=True),
        sa.Column('action', sa.String(100), nullable=False, index=True),
        sa.Column('resource_type', sa.String(50), nullable=True, index=True),
        sa.Column('resource_id', sa.String(100), nullable=True, index=True),
        sa.Column('details', postgresql.JSONB, nullable=True),
        sa.Column('ip_address', postgresql.INET, nullable=True),
        sa.Column('user_agent', sa.Text, nullable=True),
        sa.Column('success', sa.Boolean, default=True, index=True),
        sa.Column('error_message', sa.Text, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False, index=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        comment='审计日志表'
    )
    
    # 创建数据保留策略表
    op.create_table(
        'data_retention_policies',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('policy_name', sa.String(100), unique=True, nullable=False),
        sa.Column('resource_type', sa.String(50), nullable=False, index=True),
        sa.Column('retention_days', sa.Integer, nullable=False),
        sa.Column('auto_delete', sa.Boolean, default=False),
        sa.Column('archive_before_delete', sa.Boolean, default=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('is_active', sa.Boolean, default=True, index=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('policy_name'),
        comment='数据保留策略表'
    )
    
    # 创建用户同意记录表
    op.create_table(
        'user_consents',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column('consent_type', sa.String(50), nullable=False, index=True),
        sa.Column('consent_version', sa.String(20), nullable=False),
        sa.Column('consented', sa.Boolean, nullable=False),
        sa.Column('consent_text', sa.Text, nullable=True),
        sa.Column('ip_address', postgresql.INET, nullable=True),
        sa.Column('user_agent', sa.Text, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False, index=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True, index=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        comment='用户同意记录表'
    )
    
    # 创建数据源表
    op.create_table(
        'data_sources',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('source_name', sa.String(200), nullable=False, comment="数据源名称"),
        sa.Column('source_type', sa.String(50), nullable=False, index=True, comment="数据源类型"),
        sa.Column('source_url', sa.String(500), nullable=True, comment="数据源URL"),
        sa.Column('provider', sa.String(200), nullable=True, comment="数据提供方"),
        sa.Column('license_type', sa.String(50), nullable=False, index=True, comment="许可证类型"),
        sa.Column('license_text', sa.Text, nullable=True, comment="许可证文本"),
        sa.Column('license_url', sa.String(500), nullable=True, comment="许可证URL"),
        sa.Column('commercial_use_allowed', sa.Boolean, default=False, comment="是否允许商业使用"),
        sa.Column('modification_allowed', sa.Boolean, default=False, comment="是否允许修改"),
        sa.Column('redistribution_allowed', sa.Boolean, default=False, comment="是否允许再分发"),
        sa.Column('attribution_required', sa.Boolean, default=True, comment="是否需要署名"),
        sa.Column('compliance_status', sa.String(50), nullable=False, index=True, comment="合规状态"),
        sa.Column('last_review_date', sa.DateTime(timezone=True), nullable=True, comment="最后审查日期"),
        sa.Column('next_review_date', sa.DateTime(timezone=True), nullable=True, comment="下次审查日期"),
        sa.Column('usage_count', sa.Integer, default=0, comment="使用次数"),
        sa.Column('last_used_date', sa.DateTime(timezone=True), nullable=True, comment="最后使用日期"),
        sa.Column('cost_per_use', sa.Numeric(10, 4), nullable=True, comment="每次使用费用"),
        sa.Column('monthly_cost', sa.Numeric(10, 2), nullable=True, comment="月度费用"),
        sa.Column('contact_person', sa.String(100), nullable=True, comment="联系人"),
        sa.Column('contact_email', sa.String(200), nullable=True, comment="联系邮箱"),
        sa.Column('contact_phone', sa.String(50), nullable=True, comment="联系电话"),
        sa.Column('notes', sa.Text, nullable=True, comment="备注"),
        sa.Column('metadata', postgresql.JSONB, default=dict, comment="元数据"),
        sa.Column('is_active', sa.Boolean, default=True, index=True, comment="是否激活"),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        comment='数据源表'
    )

    # 创建数据使用协议表
    op.create_table(
        'data_usage_agreements',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('agreement_name', sa.String(200), nullable=False, comment="协议名称"),
        sa.Column('agreement_number', sa.String(100), nullable=True, unique=True, comment="协议编号"),
        sa.Column('data_source_id', postgresql.UUID(as_uuid=True), nullable=False, index=True, comment="数据源ID"),
        sa.Column('provider_name', sa.String(200), nullable=False, comment="提供方名称"),
        sa.Column('provider_contact', sa.String(200), nullable=True, comment="提供方联系方式"),
        sa.Column('agreement_content', sa.Text, nullable=False, comment="协议内容"),
        sa.Column('usage_scope', sa.Text, nullable=True, comment="使用范围"),
        sa.Column('restrictions', sa.Text, nullable=True, comment="使用限制"),
        sa.Column('effective_date', sa.DateTime(timezone=True), nullable=False, comment="生效日期"),
        sa.Column('expiry_date', sa.DateTime(timezone=True), nullable=True, comment="到期日期"),
        sa.Column('auto_renewal', sa.Boolean, default=False, comment="是否自动续约"),
        sa.Column('total_cost', sa.Numeric(12, 2), nullable=True, comment="总费用"),
        sa.Column('payment_terms', sa.String(200), nullable=True, comment="付款条款"),
        sa.Column('is_active', sa.Boolean, default=True, index=True, comment="是否激活"),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['data_source_id'], ['data_sources.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('agreement_number'),
        comment='数据使用协议表'
    )

    # 创建安全告警表
    op.create_table(
        'security_alerts',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('alert_title', sa.String(200), nullable=False, comment="告警标题"),
        sa.Column('anomaly_type', sa.String(50), nullable=False, index=True, comment="异常类型"),
        sa.Column('severity', sa.String(20), nullable=False, index=True, comment="严重程度"),
        sa.Column('description', sa.Text, nullable=False, comment="告警描述"),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True, index=True, comment="相关用户ID"),
        sa.Column('ip_address', postgresql.INET, nullable=True, index=True, comment="IP地址"),
        sa.Column('user_agent', sa.Text, nullable=True, comment="用户代理"),
        sa.Column('rule_id', sa.String(100), nullable=False, index=True, comment="触发规则ID"),
        sa.Column('threshold_value', sa.Float, nullable=True, comment="阈值"),
        sa.Column('actual_value', sa.Float, nullable=True, comment="实际值"),
        sa.Column('evidence', postgresql.JSONB, default=dict, comment="证据数据"),
        sa.Column('metadata', postgresql.JSONB, default=dict, comment="元数据"),
        sa.Column('status', sa.String(20), default='open', index=True, comment="告警状态"),
        sa.Column('assigned_to', sa.String(100), nullable=True, comment="分配给"),
        sa.Column('resolution_notes', sa.Text, nullable=True, comment="解决备注"),
        sa.Column('first_detected_at', sa.DateTime(timezone=True), nullable=False, index=True, comment="首次检测时间"),
        sa.Column('last_detected_at', sa.DateTime(timezone=True), nullable=False, comment="最后检测时间"),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True, comment="解决时间"),
        sa.Column('occurrence_count', sa.Integer, default=1, comment="发生次数"),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        comment='安全告警表'
    )

    # 创建免责声明模板表
    op.create_table(
        'disclaimer_templates',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('template_name', sa.String(100), nullable=False, comment="模板名称"),
        sa.Column('disclaimer_type', sa.String(50), nullable=False, index=True, comment="免责声明类型"),
        sa.Column('version', sa.String(20), nullable=False, comment="版本号"),
        sa.Column('title', sa.String(200), nullable=False, comment="标题"),
        sa.Column('content', sa.Text, nullable=False, comment="内容"),
        sa.Column('summary', sa.Text, nullable=True, comment="摘要"),
        sa.Column('is_mandatory', sa.Boolean, default=True, comment="是否强制显示"),
        sa.Column('display_order', sa.Integer, default=0, comment="显示顺序"),
        sa.Column('auto_accept_seconds', sa.Integer, nullable=True, comment="自动接受秒数"),
        sa.Column('is_active', sa.Boolean, default=True, index=True, comment="是否激活"),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        comment='免责声明模板表'
    )

    # 创建用户协议表
    op.create_table(
        'user_agreements',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, nullable=False),
        sa.Column('agreement_name', sa.String(100), nullable=False, comment="协议名称"),
        sa.Column('agreement_type', sa.String(50), nullable=False, index=True, comment="协议类型"),
        sa.Column('version', sa.String(20), nullable=False, comment="版本号"),
        sa.Column('title', sa.String(200), nullable=False, comment="标题"),
        sa.Column('content', sa.Text, nullable=False, comment="内容"),
        sa.Column('summary', sa.Text, nullable=True, comment="摘要"),
        sa.Column('effective_date', sa.DateTime(timezone=True), nullable=False, comment="生效日期"),
        sa.Column('expiry_date', sa.DateTime(timezone=True), nullable=True, comment="失效日期"),
        sa.Column('is_active', sa.Boolean, default=True, index=True, comment="是否激活"),
        sa.Column('requires_acceptance', sa.Boolean, default=True, comment="是否需要用户接受"),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        comment='用户协议表'
    )

    # 创建索引
    op.create_index('idx_users_email_verified', 'users', ['email_verified'])
    op.create_index('idx_users_phone_verified', 'users', ['phone_verified'])
    op.create_index('idx_user_sessions_expires_at', 'user_sessions', ['expires_at'])
    op.create_index('idx_user_sessions_is_active', 'user_sessions', ['is_active'])
    op.create_index('idx_audit_logs_action_created_at', 'audit_logs', ['action', 'created_at'])
    op.create_index('idx_audit_logs_resource', 'audit_logs', ['resource_type', 'resource_id'])
    op.create_index('idx_user_consents_type_version', 'user_consents', ['consent_type', 'consent_version'])
    op.create_index('idx_data_sources_source_type', 'data_sources', ['source_type'])
    op.create_index('idx_data_sources_license_type', 'data_sources', ['license_type'])
    op.create_index('idx_data_sources_compliance_status', 'data_sources', ['compliance_status'])
    op.create_index('idx_data_usage_agreements_data_source_id', 'data_usage_agreements', ['data_source_id'])
    op.create_index('idx_data_usage_agreements_effective_date', 'data_usage_agreements', ['effective_date'])
    op.create_index('idx_data_usage_agreements_expiry_date', 'data_usage_agreements', ['expiry_date'])
    op.create_index('idx_security_alerts_anomaly_type', 'security_alerts', ['anomaly_type'])
    op.create_index('idx_security_alerts_severity', 'security_alerts', ['severity'])
    op.create_index('idx_security_alerts_status', 'security_alerts', ['status'])
    op.create_index('idx_security_alerts_first_detected_at', 'security_alerts', ['first_detected_at'])
    op.create_index('idx_disclaimer_templates_type', 'disclaimer_templates', ['disclaimer_type'])
    op.create_index('idx_user_agreements_type', 'user_agreements', ['agreement_type'])


def downgrade() -> None:
    """
    降级数据库架构，移除加密字段支持
    """
    # 删除索引
    op.drop_index('idx_user_agreements_type', table_name='user_agreements')
    op.drop_index('idx_disclaimer_templates_type', table_name='disclaimer_templates')
    op.drop_index('idx_security_alerts_first_detected_at', table_name='security_alerts')
    op.drop_index('idx_security_alerts_status', table_name='security_alerts')
    op.drop_index('idx_security_alerts_severity', table_name='security_alerts')
    op.drop_index('idx_security_alerts_anomaly_type', table_name='security_alerts')
    op.drop_index('idx_data_usage_agreements_expiry_date', table_name='data_usage_agreements')
    op.drop_index('idx_data_usage_agreements_effective_date', table_name='data_usage_agreements')
    op.drop_index('idx_data_usage_agreements_data_source_id', table_name='data_usage_agreements')
    op.drop_index('idx_data_sources_compliance_status', table_name='data_sources')
    op.drop_index('idx_data_sources_license_type', table_name='data_sources')
    op.drop_index('idx_data_sources_source_type', table_name='data_sources')
    op.drop_index('idx_user_consents_type_version', table_name='user_consents')
    op.drop_index('idx_audit_logs_resource', table_name='audit_logs')
    op.drop_index('idx_audit_logs_action_created_at', table_name='audit_logs')
    op.drop_index('idx_user_sessions_is_active', table_name='user_sessions')
    op.drop_index('idx_user_sessions_expires_at', table_name='user_sessions')
    op.drop_index('idx_users_phone_verified', table_name='users')
    op.drop_index('idx_users_email_verified', table_name='users')

    # 删除表
    op.drop_table('user_agreements')
    op.drop_table('disclaimer_templates')
    op.drop_table('security_alerts')
    op.drop_table('data_usage_agreements')
    op.drop_table('data_sources')
    op.drop_table('user_consents')
    op.drop_table('data_retention_policies')
    op.drop_table('audit_logs')
    op.drop_table('user_sessions')
    op.drop_table('user_profiles')
    op.drop_table('users')

    # 删除枚举类型
    op.execute('DROP TYPE IF EXISTS userstatus')
    op.execute('DROP TYPE IF EXISTS usertype')
