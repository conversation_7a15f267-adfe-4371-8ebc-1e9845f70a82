"""添加安全相关表

Revision ID: 003
Revises: 002
Create Date: 2024-01-03 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库 - 添加安全相关表"""

    # 创建审计事件类型枚举
    audit_event_type = postgresql.ENUM(
        'user_login', 'user_logout', 'user_register', 'password_change', 'password_reset',
        'user_create', 'user_update', 'user_delete', 'user_view',
        'data_create', 'data_read', 'data_update', 'data_delete', 'data_export', 'data_import',
        'system_config_change', 'system_backup', 'system_restore',
        'security_violation', 'access_denied', 'suspicious_activity',
        'api_call', 'api_error', 'api_rate_limit',
        name='auditeventtype'
    )
    audit_event_type.create(op.get_bind())

    # 创建审计级别枚举
    audit_level = postgresql.ENUM(
        'info', 'warning', 'error', 'critical',
        name='auditlevel'
    )
    audit_level.create(op.get_bind())

    # 创建同意类型枚举
    consent_type = postgresql.ENUM(
        'necessary', 'functional', 'analytics', 'marketing',
        name='consenttype'
    )
    consent_type.create(op.get_bind())

    # 创建同意状态枚举
    consent_status = postgresql.ENUM(
        'granted', 'denied', 'withdrawn', 'expired',
        name='consentstatus'
    )
    consent_status.create(op.get_bind())

    # 创建数据分类枚举
    data_category = postgresql.ENUM(
        'public', 'internal', 'confidential', 'restricted', 'personal', 'sensitive_personal',
        name='datacategory'
    )
    data_category.create(op.get_bind())

    # 创建审计日志表
    op.create_table('audit_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('event_type', audit_event_type, nullable=False),
        sa.Column('level', audit_level, nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('username', sa.String(length=100), nullable=True),
        sa.Column('user_ip', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('resource_type', sa.String(length=100), nullable=True),
        sa.Column('resource_id', sa.String(length=100), nullable=True),
        sa.Column('action', sa.String(length=100), nullable=True),
        sa.Column('message', sa.Text(), nullable=True),
        sa.Column('details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('request_id', sa.String(length=100), nullable=True),
        sa.Column('session_id', sa.String(length=100), nullable=True),
        sa.Column('success', sa.String(), nullable=True),
        sa.Column('error_code', sa.String(length=50), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        comment='审计日志表'
    )
    op.create_index(op.f('ix_audit_logs_id'), 'audit_logs', ['id'], unique=False)
    op.create_index(op.f('ix_audit_logs_event_type'), 'audit_logs', ['event_type'], unique=False)
    op.create_index(op.f('ix_audit_logs_level'), 'audit_logs', ['level'], unique=False)
    op.create_index(op.f('ix_audit_logs_user_id'), 'audit_logs', ['user_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_username'), 'audit_logs', ['username'], unique=False)
    op.create_index(op.f('ix_audit_logs_user_ip'), 'audit_logs', ['user_ip'], unique=False)
    op.create_index(op.f('ix_audit_logs_resource_type'), 'audit_logs', ['resource_type'], unique=False)
    op.create_index(op.f('ix_audit_logs_resource_id'), 'audit_logs', ['resource_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_action'), 'audit_logs', ['action'], unique=False)
    op.create_index(op.f('ix_audit_logs_request_id'), 'audit_logs', ['request_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_session_id'), 'audit_logs', ['session_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_timestamp'), 'audit_logs', ['timestamp'], unique=False)

    # 创建用户同意记录表
    op.create_table('user_consents',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('consent_type', consent_type, nullable=False),
        sa.Column('status', consent_status, nullable=False),
        sa.Column('purpose', sa.String(length=200), nullable=True),
        sa.Column('data_categories', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('legal_basis', sa.String(length=100), nullable=True),
        sa.Column('granted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('withdrawn_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('consent_version', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        comment='用户同意记录表'
    )
    op.create_index(op.f('ix_user_consents_id'), 'user_consents', ['id'], unique=False)
    op.create_index(op.f('ix_user_consents_user_id'), 'user_consents', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_consents_consent_type'), 'user_consents', ['consent_type'], unique=False)
    op.create_index(op.f('ix_user_consents_status'), 'user_consents', ['status'], unique=False)
    op.create_index(op.f('ix_user_consents_created_at'), 'user_consents', ['created_at'], unique=False)

    # 创建数据保留策略表
    op.create_table('data_retention_policies',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('data_category', data_category, nullable=False),
        sa.Column('retention_period_days', sa.String(), nullable=False),
        sa.Column('auto_delete', sa.Boolean(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        comment='数据保留策略表'
    )
    op.create_index(op.f('ix_data_retention_policies_id'), 'data_retention_policies', ['id'], unique=False)
    op.create_index(op.f('ix_data_retention_policies_data_category'), 'data_retention_policies', ['data_category'], unique=False)

    # 为现有用户表添加隐私相关字段
    op.add_column('users', sa.Column('data_processing_consent', sa.Boolean(), nullable=True, default=False, comment='数据处理同意'))
    op.add_column('users', sa.Column('marketing_consent', sa.Boolean(), nullable=True, default=False, comment='营销同意'))
    op.add_column('users', sa.Column('privacy_settings', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='隐私设置'))
    op.add_column('users', sa.Column('data_retention_date', sa.DateTime(timezone=True), nullable=True, comment='数据保留截止日期'))


def downgrade() -> None:
    """降级数据库 - 删除安全相关表"""

    # 删除用户表的新增字段
    op.drop_column('users', 'data_retention_date')
    op.drop_column('users', 'privacy_settings')
    op.drop_column('users', 'marketing_consent')
    op.drop_column('users', 'data_processing_consent')

    # 删除表
    op.drop_table('data_retention_policies')
    op.drop_table('user_consents')
    op.drop_table('audit_logs')

    # 删除枚举类型
    op.execute('DROP TYPE IF EXISTS datacategory')
    op.execute('DROP TYPE IF EXISTS consentstatus')
    op.execute('DROP TYPE IF EXISTS consenttype')
    op.execute('DROP TYPE IF EXISTS auditlevel')
    op.execute('DROP TYPE IF EXISTS auditeventtype')
