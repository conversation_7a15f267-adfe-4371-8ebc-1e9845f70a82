#!/usr/bin/env python3
"""
意图分类器测试
"""

import sys
import os

# 添加app目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_intent_classifier_import():
    """测试意图分类器导入"""
    try:
        from services.intent_classifier import LegalIntentClassifier, intent_classifier
        print("✓ 意图分类器导入成功")
        return True
    except ImportError as e:
        print(f"✗ 意图分类器导入失败: {e}")
        return False

def test_basic_intent_classification():
    """测试基本意图分类"""
    try:
        from services.intent_classifier import intent_classifier
        
        test_cases = [
            ("请问劳动合同法是怎么规定的？", "咨询"),
            ("我要投诉这家公司违法用工", "投诉"),
            ("如何申请劳动仲裁？", "申请"),
            ("查询我的案件处理进度", "查询"),
            ("紧急求助，不知道该怎么办", "求助"),
            ("建议我应该怎么处理这个问题", "建议"),
            ("确认一下这个合同是否有效", "确认"),
            ("请分析一下这个案例的胜诉可能性", "分析")
        ]
        
        correct_predictions = 0
        total_predictions = len(test_cases)
        
        print("✓ 基本意图分类测试:")
        for text, expected_intent in test_cases:
            result = intent_classifier.classify_intent(text)
            predicted_intent = result["intent"]
            confidence = result["confidence"]
            
            is_correct = predicted_intent == expected_intent
            if is_correct:
                correct_predictions += 1
            
            status = "✓" if is_correct else "✗"
            print(f"  {status} '{text[:20]}...' -> {predicted_intent} (置信度: {confidence:.3f})")
        
        accuracy = correct_predictions / total_predictions
        print(f"  准确率: {accuracy:.2%} ({correct_predictions}/{total_predictions})")
        
        return accuracy >= 0.6  # 60%以上准确率认为通过
        
    except Exception as e:
        print(f"✗ 基本意图分类测试失败: {e}")
        return False

def test_intent_explanation():
    """测试意图解释"""
    try:
        from services.intent_classifier import intent_classifier
        
        intents = ["咨询", "投诉", "申请", "查询", "求助", "建议", "确认", "分析"]
        
        print("✓ 意图解释测试:")
        for intent in intents:
            explanation = intent_classifier.get_intent_explanation(intent)
            print(f"  {intent}: {explanation[:30]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ 意图解释测试失败: {e}")
        return False

def test_intent_entities_extraction():
    """测试意图实体提取"""
    try:
        from services.intent_classifier import intent_classifier
        
        test_cases = [
            ("我要投诉ABC公司违法解除劳动合同，要求赔偿5000元", "投诉"),
            ("请问《劳动合同法》第39条是怎么规定的？", "咨询"),
            ("申请劳动仲裁需要什么材料？", "申请"),
            ("查询案件进度，案件编号是2023年123号", "查询")
        ]
        
        print("✓ 意图实体提取测试:")
        for text, intent in test_cases:
            entities = intent_classifier.extract_intent_entities(text, intent)
            print(f"  '{text[:25]}...' -> {entities}")
        
        return True
        
    except Exception as e:
        print(f"✗ 意图实体提取测试失败: {e}")
        return False

def test_intent_suggestions():
    """测试意图建议"""
    try:
        from services.intent_classifier import intent_classifier
        
        intents = ["咨询", "投诉", "申请", "查询"]
        
        print("✓ 意图建议测试:")
        for intent in intents:
            suggestions = intent_classifier.get_intent_suggestions(intent)
            print(f"  {intent}: {len(suggestions)}个建议")
            for i, suggestion in enumerate(suggestions[:2]):  # 只显示前2个
                print(f"    {i+1}. {suggestion}")
        
        return True
        
    except Exception as e:
        print(f"✗ 意图建议测试失败: {e}")
        return False

def test_rule_based_classification():
    """测试基于规则的分类"""
    try:
        from services.intent_classifier import LegalIntentClassifier
        
        classifier = LegalIntentClassifier()
        
        # 测试规则匹配
        test_text = "请问如何申请劳动仲裁？"
        rule_scores = classifier._rule_based_classification(test_text)
        
        print(f"✓ 规则分类测试: {rule_scores}")
        
        # 应该能识别出"咨询"和"申请"意图
        expected_intents = ["咨询", "申请"]
        found_intents = [intent for intent in expected_intents if intent in rule_scores and rule_scores[intent] > 0]
        
        return len(found_intents) > 0
        
    except Exception as e:
        print(f"✗ 规则分类测试失败: {e}")
        return False

def test_keyword_based_classification():
    """测试基于关键词的分类"""
    try:
        from services.intent_classifier import LegalIntentClassifier
        
        classifier = LegalIntentClassifier()
        
        # 测试关键词匹配
        test_text = "我要投诉公司违法行为，要求赔偿"
        keyword_scores = classifier._keyword_based_classification(test_text)
        
        print(f"✓ 关键词分类测试: {keyword_scores}")
        
        # 应该能识别出"投诉"意图
        return "投诉" in keyword_scores and keyword_scores["投诉"] > 0
        
    except Exception as e:
        print(f"✗ 关键词分类测试失败: {e}")
        return False

def test_ml_classifier_training():
    """测试机器学习分类器训练"""
    try:
        from services.intent_classifier import LegalIntentClassifier
        
        classifier = LegalIntentClassifier()
        
        # 准备训练数据
        training_data = [
            ("请问劳动法是怎么规定的", "咨询"),
            ("咨询一下合同法的相关条款", "咨询"),
            ("我想了解婚姻法的内容", "咨询"),
            ("投诉公司违法用工", "投诉"),
            ("举报企业违规操作", "投诉"),
            ("要投诉这家单位", "投诉"),
            ("如何申请劳动仲裁", "申请"),
            ("申请法律援助的流程", "申请"),
            ("办理离婚手续需要什么", "申请"),
            ("查询案件进度", "查询"),
            ("查看审理结果", "查询"),
            ("搜索相关法条", "查询")
        ]
        
        # 训练分类器
        classifier.train_ml_classifier(training_data)
        
        if classifier.is_trained:
            print("✓ 机器学习分类器训练成功")
            
            # 测试预测
            test_text = "我想咨询一下房产法的问题"
            result = classifier.classify_intent(test_text)
            print(f"  测试预测: '{test_text}' -> {result['intent']} (置信度: {result['confidence']:.3f})")
            
            return True
        else:
            print("⚠ 机器学习分类器未训练成功")
            return False
        
    except Exception as e:
        print(f"✗ 机器学习分类器训练测试失败: {e}")
        return False

def test_combined_classification():
    """测试综合分类"""
    try:
        from services.intent_classifier import LegalIntentClassifier
        
        classifier = LegalIntentClassifier()
        
        # 先训练ML分类器
        training_data = [
            ("请问法律是怎么规定的", "咨询"),
            ("咨询相关条款", "咨询"),
            ("投诉违法行为", "投诉"),
            ("举报违规操作", "投诉"),
            ("申请仲裁", "申请"),
            ("办理手续", "申请")
        ]
        classifier.train_ml_classifier(training_data)
        
        # 测试综合分类
        test_text = "请问我可以投诉这家公司的违法行为吗？"
        result = classifier.classify_intent(test_text)
        
        print(f"✓ 综合分类测试:")
        print(f"  文本: '{test_text}'")
        print(f"  意图: {result['intent']}")
        print(f"  置信度: {result['confidence']:.3f}")
        print(f"  规则分数: {result['rule_scores']}")
        print(f"  关键词分数: {result['keyword_scores']}")
        if result['ml_scores']:
            print(f"  ML分数: {result['ml_scores']}")
        
        return result['intent'] in ["咨询", "投诉"]
        
    except Exception as e:
        print(f"✗ 综合分类测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始意图分类器测试...")
    print("=" * 60)
    
    tests = [
        ("意图分类器导入测试", test_intent_classifier_import),
        ("基本意图分类测试", test_basic_intent_classification),
        ("意图解释测试", test_intent_explanation),
        ("意图实体提取测试", test_intent_entities_extraction),
        ("意图建议测试", test_intent_suggestions),
        ("规则分类测试", test_rule_based_classification),
        ("关键词分类测试", test_keyword_based_classification),
        ("ML分类器训练测试", test_ml_classifier_training),
        ("综合分类测试", test_combined_classification),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:  # 80%以上通过率
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
