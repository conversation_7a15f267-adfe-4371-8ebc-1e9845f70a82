"""
权限管理相关数据模型
"""

import uuid
from typing import Optional, List
from sqlalchemy import Column, String, Text, Boolean, ForeignKey, DateTime, Table
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum

from app.core.database import Base


# 角色权限关联表
role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', UUID(as_uuid=True), ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True),
    Column('permission_id', UUID(as_uuid=True), ForeignKey('permissions.id', ondelete='CASCADE'), primary_key=True),
    comment='角色权限关联表'
)

# 用户角色关联表
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', UUID(as_uuid=True), ForeignKey('users.id', ondelete='CASCADE'), primary_key=True),
    Column('role_id', UUID(as_uuid=True), ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True),
    Column('assigned_at', DateTime(timezone=True), server_default=func.now(), comment='分配时间'),
    Column('assigned_by', UUID(as_uuid=True), ForeignKey('users.id', ondelete='SET NULL'), comment='分配者ID'),
    comment='用户角色关联表'
)


class Permission(Base):
    """权限表"""
    __tablename__ = "permissions"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 权限信息
    name = Column(String(100), unique=True, nullable=False, index=True, comment="权限名称")
    code = Column(String(100), unique=True, nullable=False, index=True, comment="权限代码")
    description = Column(Text, comment="权限描述")
    
    # 权限分类
    category = Column(String(50), index=True, comment="权限分类")
    resource = Column(String(100), index=True, comment="资源类型")
    action = Column(String(50), index=True, comment="操作动作")
    
    # 权限级别
    level = Column(String, default=1, comment="权限级别")  # 1-5，数字越大权限越高
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_system = Column(Boolean, default=False, comment="是否系统权限")
    
    # 元数据
    metadata = Column(JSONB, default=dict, comment="权限元数据")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")
    
    def __repr__(self):
        return f"<Permission(id={self.id}, name={self.name}, code={self.code})>"


class Role(Base):
    """角色表"""
    __tablename__ = "roles"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 角色信息
    name = Column(String(100), unique=True, nullable=False, index=True, comment="角色名称")
    code = Column(String(100), unique=True, nullable=False, index=True, comment="角色代码")
    description = Column(Text, comment="角色描述")
    
    # 角色层级
    level = Column(String, default=1, comment="角色级别")  # 1-5，数字越大权限越高
    parent_role_id = Column(
        UUID(as_uuid=True),
        ForeignKey("roles.id", ondelete="SET NULL"),
        comment="上级角色ID"
    )
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_system = Column(Boolean, default=False, comment="是否系统角色")
    is_default = Column(Boolean, default=False, comment="是否默认角色")
    
    # 元数据
    metadata = Column(JSONB, default=dict, comment="角色元数据")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")
    users = relationship("User", secondary=user_roles, back_populates="roles")
    parent_role = relationship("Role", remote_side=[id])
    child_roles = relationship("Role", back_populates="parent_role")
    
    def __repr__(self):
        return f"<Role(id={self.id}, name={self.name}, code={self.code})>"


class ResourcePermission(Base):
    """资源权限表"""
    __tablename__ = "resource_permissions"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 权限主体
    subject_type = Column(String(20), nullable=False, index=True, comment="主体类型")  # user, role
    subject_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="主体ID")
    
    # 权限对象
    resource_type = Column(String(100), nullable=False, index=True, comment="资源类型")
    resource_id = Column(String(100), index=True, comment="资源ID")  # 可为空表示所有资源
    
    # 权限动作
    actions = Column(JSONB, default=list, comment="允许的动作列表")
    
    # 权限条件
    conditions = Column(JSONB, default=dict, comment="权限条件")
    
    # 权限效果
    effect = Column(String(10), default="allow", comment="权限效果")  # allow, deny
    
    # 优先级
    priority = Column(String, default=0, comment="优先级")
    
    # 有效期
    valid_from = Column(DateTime(timezone=True), comment="生效时间")
    valid_until = Column(DateTime(timezone=True), comment="失效时间")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 创建信息
    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        comment="创建者ID"
    )
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    creator = relationship("User", foreign_keys=[created_by])
    
    def __repr__(self):
        return f"<ResourcePermission(id={self.id}, subject_type={self.subject_type}, resource_type={self.resource_type})>"


class PermissionGroup(Base):
    """权限组表"""
    __tablename__ = "permission_groups"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 组信息
    name = Column(String(100), unique=True, nullable=False, index=True, comment="权限组名称")
    code = Column(String(100), unique=True, nullable=False, index=True, comment="权限组代码")
    description = Column(Text, comment="权限组描述")
    
    # 组类型
    group_type = Column(String(50), index=True, comment="权限组类型")
    
    # 权限列表
    permission_codes = Column(JSONB, default=list, comment="权限代码列表")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_system = Column(Boolean, default=False, comment="是否系统权限组")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    def __repr__(self):
        return f"<PermissionGroup(id={self.id}, name={self.name}, code={self.code})>"
