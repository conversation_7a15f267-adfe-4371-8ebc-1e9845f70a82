"""
用户相关数据模型
"""

import uuid
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class UserType(str, enum.Enum):
    """用户类型枚举"""
    INDIVIDUAL = "individual"  # 个人用户
    ENTERPRISE = "enterprise"  # 企业用户
    LAWYER = "lawyer"  # 律师用户


class UserStatus(str, enum.Enum):
    """用户状态枚举"""
    ACTIVE = "active"  # 活跃
    INACTIVE = "inactive"  # 非活跃
    SUSPENDED = "suspended"  # 暂停


class User(Base):
    """用户表"""
    __tablename__ = "users"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 基本信息
    username = Column(String(50), unique=True, nullable=False, index=True, comment="用户名")
    email = Column(String(100), unique=True, nullable=False, index=True, comment="邮箱")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    full_name = Column(String(100), comment="全名")
    phone = Column(String(20), comment="手机号")
    
    # 用户类型和状态
    user_type = Column(
        SQLEnum(UserType),
        default=UserType.INDIVIDUAL,
        nullable=False,
        index=True,
        comment="用户类型"
    )
    status = Column(
        SQLEnum(UserStatus),
        default=UserStatus.ACTIVE,
        nullable=False,
        index=True,
        comment="用户状态"
    )
    
    # 验证状态
    email_verified = Column(Boolean, default=False, comment="邮箱是否已验证")
    phone_verified = Column(Boolean, default=False, comment="手机是否已验证")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    last_login_at = Column(DateTime(timezone=True), comment="最后登录时间")
    
    # 关系
    profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    qa_records = relationship("QARecord", back_populates="user", cascade="all, delete-orphan")
    contract_reviews = relationship("ContractReview", back_populates="user", cascade="all, delete-orphan")
    document_generations = relationship("DocumentGeneration", back_populates="user", cascade="all, delete-orphan")
    favorites = relationship("UserFavorite", back_populates="user", cascade="all, delete-orphan")
    system_logs = relationship("SystemLog", back_populates="user", cascade="all, delete-orphan")

    # 权限相关关系
    roles = relationship("Role", secondary="user_roles", back_populates="users")

    def __repr__(self):
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"


class UserProfile(Base):
    """用户配置表"""
    __tablename__ = "user_profiles"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 外键
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
        comment="用户ID"
    )
    
    # 个人信息
    avatar_url = Column(String(255), comment="头像URL")
    bio = Column(Text, comment="个人简介")
    location = Column(String(100), comment="所在地")
    
    # 专业信息（律师用户）
    specialization = Column(String(100), comment="专业领域")
    license_number = Column(String(50), comment="执业证号")
    
    # 企业信息（企业用户）
    company_name = Column(String(100), comment="公司名称")
    company_position = Column(String(50), comment="职位")
    
    # 用户偏好设置
    preferences = Column(JSONB, default=dict, comment="用户偏好设置")
    
    # 统计信息
    total_questions = Column(String, default="0", comment="总提问数")
    total_contracts = Column(String, default="0", comment="总合同数")
    total_documents = Column(String, default="0", comment="总文书数")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    user = relationship("User", back_populates="profile")

    def __repr__(self):
        return f"<UserProfile(id={self.id}, user_id={self.user_id})>"


class UserSession(Base):
    """用户会话表"""
    __tablename__ = "user_sessions"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 外键
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="用户ID"
    )
    
    # 会话信息
    session_token = Column(String(255), unique=True, nullable=False, index=True, comment="会话令牌")
    refresh_token = Column(String(255), unique=True, index=True, comment="刷新令牌")
    expires_at = Column(DateTime(timezone=True), nullable=False, index=True, comment="过期时间")
    
    # 客户端信息
    ip_address = Column(INET, comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    device_info = Column(JSONB, default=dict, comment="设备信息")
    
    # 会话状态
    is_active = Column(Boolean, default=True, comment="是否活跃")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    last_accessed_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后访问时间"
    )
    
    # 关系
    user = relationship("User", back_populates="sessions")

    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, expires_at={self.expires_at})>"

    @property
    def is_expired(self) -> bool:
        """检查会话是否已过期"""
        return datetime.utcnow() > self.expires_at

    def extend_session(self, hours: int = 24) -> None:
        """延长会话时间"""
        from datetime import timedelta
        self.expires_at = datetime.utcnow() + timedelta(hours=hours)
