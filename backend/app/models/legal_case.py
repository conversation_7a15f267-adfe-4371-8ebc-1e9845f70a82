"""
法律案例相关数据模型
"""

import uuid
from typing import Optional
from sqlalchemy import Column, String, Text, Date, ForeignKey, DateTime, Enum as SQLE<PERSON>, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class CaseType(str, enum.Enum):
    """案例类型枚举"""
    CIVIL = "civil"  # 民事
    CRIMINAL = "criminal"  # 刑事
    ADMINISTRATIVE = "administrative"  # 行政
    COMMERCIAL = "commercial"  # 商事
    LABOR = "labor"  # 劳动
    INTELLECTUAL_PROPERTY = "intellectual_property"  # 知识产权
    ENVIRONMENTAL = "environmental"  # 环境
    OTHER = "other"  # 其他


class CaseStatus(str, enum.Enum):
    """案例状态枚举"""
    ACTIVE = "active"  # 有效
    ARCHIVED = "archived"  # 已归档
    DELETED = "deleted"  # 已删除


class LegalCase(Base):
    """法律案例表"""
    __tablename__ = "legal_cases"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 案例基本信息
    case_number = Column(String(100), unique=True, nullable=False, index=True, comment="案件编号")
    title = Column(String(500), nullable=False, index=True, comment="案件标题")
    court_name = Column(String(200), nullable=False, index=True, comment="审理法院")
    
    # 案例分类
    case_type = Column(
        SQLEnum(CaseType),
        nullable=False,
        index=True,
        comment="案件类型"
    )
    
    # 审理信息
    judgment_date = Column(Date, index=True, comment="判决日期")
    trial_procedure = Column(String(50), comment="审理程序")  # 一审、二审、再审等
    
    # 当事人信息 (JSON格式存储)
    parties = Column(JSONB, default=list, comment="当事人信息")
    
    # 案例内容
    case_summary = Column(Text, comment="案件摘要")
    case_facts = Column(Text, comment="案件事实")
    dispute_focus = Column(JSONB, default=list, comment="争议焦点")
    court_opinion = Column(Text, comment="法院观点")
    judgment_result = Column(Text, comment="判决结果")
    
    # 法条关联
    related_articles = Column(JSONB, default=list, comment="相关法条ID数组")
    legal_basis = Column(Text, comment="法律依据")
    
    # 关键词和标签
    keywords = Column(JSONB, default=list, comment="关键词")
    tags = Column(JSONB, default=list, comment="标签")
    
    # 案例价值评估
    precedent_value = Column(String(20), comment="判例价值")  # 高、中、低
    citation_count = Column(String, default="0", comment="引用次数")
    
    # 状态和元数据
    status = Column(
        SQLEnum(CaseStatus),
        default=CaseStatus.ACTIVE,
        nullable=False,
        index=True,
        comment="案例状态"
    )
    is_public = Column(Boolean, default=True, comment="是否公开")
    source_url = Column(String(500), comment="来源URL")
    extra_data = Column(JSONB, default=dict, comment="额外元数据")
    
    # 创建者信息
    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        index=True,
        comment="创建者ID"
    )
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")  # 软删除
    
    # 关系
    creator = relationship("User", foreign_keys=[created_by])
    case_similarities = relationship("CaseSimilarity", back_populates="case", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<LegalCase(id={self.id}, case_number={self.case_number}, title={self.title[:50]})>"

    @property
    def is_deleted(self) -> bool:
        """检查案例是否已被软删除"""
        return self.deleted_at is not None


class CaseSimilarity(Base):
    """案例相似度表"""
    __tablename__ = "case_similarities"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 外键
    case_id = Column(
        UUID(as_uuid=True),
        ForeignKey("legal_cases.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="案例ID"
    )
    similar_case_id = Column(
        UUID(as_uuid=True),
        ForeignKey("legal_cases.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="相似案例ID"
    )
    
    # 相似度信息
    similarity_score = Column(String, nullable=False, comment="相似度分数")
    similarity_type = Column(String(50), comment="相似度类型")  # 事实相似、法条相似、结果相似等
    
    # 计算信息
    algorithm_version = Column(String(20), comment="算法版本")
    computed_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="计算时间"
    )
    
    # 关系
    case = relationship("LegalCase", foreign_keys=[case_id], back_populates="case_similarities")
    similar_case = relationship("LegalCase", foreign_keys=[similar_case_id])
    
    def __repr__(self):
        return f"<CaseSimilarity(case_id={self.case_id}, similar_case_id={self.similar_case_id}, score={self.similarity_score})>"


class LegalArticle(Base):
    """法条表"""
    __tablename__ = "legal_articles"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 法条基本信息
    article_number = Column(String(50), index=True, comment="法条编号")
    title = Column(String(500), nullable=False, comment="法条标题")
    content = Column(Text, nullable=False, comment="法条内容")
    
    # 法律信息
    law_name = Column(String(200), nullable=False, index=True, comment="法律名称")
    law_category = Column(String(100), index=True, comment="法律分类")
    chapter = Column(String(100), comment="章节")
    section = Column(String(100), comment="节")
    
    # 生效信息
    effective_date = Column(Date, comment="生效日期")
    expiry_date = Column(Date, comment="失效日期")
    
    # 状态
    status = Column(
        SQLEnum(CaseStatus),
        default=CaseStatus.ACTIVE,
        nullable=False,
        index=True,
        comment="法条状态"
    )
    
    # 关联信息
    parent_article_id = Column(
        UUID(as_uuid=True),
        ForeignKey("legal_articles.id", ondelete="SET NULL"),
        comment="上级法条ID"
    )
    
    # 元数据
    keywords = Column(JSONB, default=list, comment="关键词")
    extra_data = Column(JSONB, default=dict, comment="额外元数据")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    parent_article = relationship("LegalArticle", remote_side=[id])
    child_articles = relationship("LegalArticle", back_populates="parent_article")
    
    def __repr__(self):
        return f"<LegalArticle(id={self.id}, law_name={self.law_name}, article_number={self.article_number})>"
