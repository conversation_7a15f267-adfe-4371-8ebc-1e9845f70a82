"""
Elasticsearch配置和工具类
"""

import json
import logging
from typing import Dict, List, Any, Optional
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError

from app.core.config import settings

logger = logging.getLogger(__name__)


class ElasticsearchClient:
    """Elasticsearch客户端"""
    
    def __init__(self):
        self.client: Optional[AsyncElasticsearch] = None
        self.index_name = "legal_cases"
        
    async def connect(self):
        """连接到Elasticsearch"""
        try:
            # 如果没有配置Elasticsearch，使用模拟模式
            if not hasattr(settings, 'ELASTICSEARCH_URL') or not settings.ELASTICSEARCH_URL:
                logger.warning("Elasticsearch未配置，使用模拟搜索模式")
                self.client = None
                return
                
            self.client = AsyncElasticsearch(
                [settings.ELASTICSEARCH_URL],
                timeout=30,
                max_retries=3,
                retry_on_timeout=True
            )
            
            # 测试连接
            await self.client.ping()
            logger.info("Elasticsearch连接成功")
            
            # 创建索引（如果不存在）
            await self.create_index_if_not_exists()
            
        except Exception as e:
            logger.error(f"Elasticsearch连接失败: {e}")
            self.client = None
    
    async def disconnect(self):
        """断开连接"""
        if self.client:
            await self.client.close()
    
    async def create_index_if_not_exists(self):
        """创建索引（如果不存在）"""
        if not self.client:
            return
            
        try:
            exists = await self.client.indices.exists(index=self.index_name)
            if not exists:
                # 定义索引映射
                mapping = {
                    "mappings": {
                        "properties": {
                            "id": {"type": "keyword"},
                            "case_number": {"type": "keyword"},
                            "title": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "search_analyzer": "ik_smart"
                            },
                            "court_name": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "fields": {
                                    "keyword": {"type": "keyword"}
                                }
                            },
                            "case_type": {"type": "keyword"},
                            "judgment_date": {"type": "date"},
                            "case_summary": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "search_analyzer": "ik_smart"
                            },
                            "full_text": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "search_analyzer": "ik_smart"
                            },
                            "keywords": {"type": "keyword"},
                            "precedent_value": {"type": "keyword"},
                            "citation_count": {"type": "integer"},
                            "created_at": {"type": "date"},
                            "updated_at": {"type": "date"}
                        }
                    },
                    "settings": {
                        "number_of_shards": 1,
                        "number_of_replicas": 0,
                        "analysis": {
                            "analyzer": {
                                "ik_max_word": {
                                    "type": "custom",
                                    "tokenizer": "standard",
                                    "filter": ["lowercase"]
                                },
                                "ik_smart": {
                                    "type": "custom", 
                                    "tokenizer": "standard",
                                    "filter": ["lowercase"]
                                }
                            }
                        }
                    }
                }
                
                await self.client.indices.create(
                    index=self.index_name,
                    body=mapping
                )
                logger.info(f"创建索引成功: {self.index_name}")
                
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
    
    async def index_case(self, case_data: Dict[str, Any]) -> bool:
        """索引案例数据"""
        if not self.client:
            return False
            
        try:
            await self.client.index(
                index=self.index_name,
                id=case_data["id"],
                body=case_data
            )
            return True
        except Exception as e:
            logger.error(f"索引案例失败: {e}")
            return False
    
    async def bulk_index_cases(self, cases: List[Dict[str, Any]]) -> bool:
        """批量索引案例"""
        if not self.client or not cases:
            return False
            
        try:
            actions = []
            for case in cases:
                actions.append({
                    "_index": self.index_name,
                    "_id": case["id"],
                    "_source": case
                })
            
            from elasticsearch.helpers import async_bulk
            await async_bulk(self.client, actions)
            logger.info(f"批量索引完成: {len(cases)} 个案例")
            return True
            
        except Exception as e:
            logger.error(f"批量索引失败: {e}")
            return False
    
    async def search_cases(
        self,
        query: str,
        filters: Dict[str, Any] = None,
        size: int = 20,
        from_: int = 0,
        sort: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """搜索案例"""
        
        # 如果没有Elasticsearch客户端，使用模拟搜索
        if not self.client:
            return await self._mock_search(query, filters, size, from_)
        
        try:
            # 构建搜索查询
            search_body = self._build_search_query(query, filters, sort)
            
            response = await self.client.search(
                index=self.index_name,
                body=search_body,
                size=size,
                from_=from_
            )
            
            # 处理搜索结果
            return self._process_search_response(response)
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return {"hits": [], "total": 0, "took": 0}
    
    def _build_search_query(
        self,
        query: str,
        filters: Dict[str, Any] = None,
        sort: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """构建搜索查询"""
        
        search_body = {
            "query": {
                "bool": {
                    "must": [],
                    "filter": []
                }
            }
        }
        
        # 主查询
        if query:
            search_body["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": query,
                    "fields": [
                        "title^3",
                        "case_summary^2", 
                        "full_text",
                        "keywords^2"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        else:
            search_body["query"]["bool"]["must"].append({
                "match_all": {}
            })
        
        # 过滤条件
        if filters:
            if filters.get("case_type"):
                search_body["query"]["bool"]["filter"].append({
                    "term": {"case_type": filters["case_type"]}
                })
            
            if filters.get("court_name"):
                search_body["query"]["bool"]["filter"].append({
                    "match": {"court_name": filters["court_name"]}
                })
            
            if filters.get("precedent_value"):
                search_body["query"]["bool"]["filter"].append({
                    "term": {"precedent_value": filters["precedent_value"]}
                })
            
            if filters.get("date_range"):
                date_range = filters["date_range"]
                range_filter = {"range": {"judgment_date": {}}}
                if date_range.get("gte"):
                    range_filter["range"]["judgment_date"]["gte"] = date_range["gte"]
                if date_range.get("lte"):
                    range_filter["range"]["judgment_date"]["lte"] = date_range["lte"]
                search_body["query"]["bool"]["filter"].append(range_filter)
        
        # 排序
        if sort:
            search_body["sort"] = sort
        else:
            search_body["sort"] = [
                {"_score": {"order": "desc"}},
                {"citation_count": {"order": "desc"}},
                {"judgment_date": {"order": "desc"}}
            ]
        
        # 高亮
        search_body["highlight"] = {
            "fields": {
                "title": {},
                "case_summary": {},
                "full_text": {"fragment_size": 150, "number_of_fragments": 3}
            }
        }
        
        return search_body
    
    def _process_search_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """处理搜索响应"""
        hits = []
        
        for hit in response["hits"]["hits"]:
            case_data = hit["_source"]
            case_data["score"] = hit["_score"]
            
            # 添加高亮信息
            if "highlight" in hit:
                case_data["highlight"] = hit["highlight"]
            
            hits.append(case_data)
        
        return {
            "hits": hits,
            "total": response["hits"]["total"]["value"] if isinstance(response["hits"]["total"], dict) else response["hits"]["total"],
            "took": response["took"],
            "max_score": response["hits"]["max_score"]
        }
    
    async def _mock_search(
        self,
        query: str,
        filters: Dict[str, Any] = None,
        size: int = 20,
        from_: int = 0
    ) -> Dict[str, Any]:
        """模拟搜索（当Elasticsearch不可用时）"""
        
        # 模拟搜索结果
        mock_cases = [
            {
                "id": "mock_case_1",
                "case_number": "（2023）京01民终1234号",
                "title": "张某与李某合同纠纷案",
                "court_name": "北京市第一中级人民法院",
                "case_type": "civil",
                "judgment_date": "2023-06-15",
                "case_summary": "本案涉及买卖合同违约责任的认定和赔偿标准问题。",
                "precedent_value": "中",
                "citation_count": 15,
                "score": 0.95,
                "created_at": "2023-06-20T10:00:00Z"
            },
            {
                "id": "mock_case_2", 
                "case_number": "（2023）沪02民终5678号",
                "title": "某公司劳动争议案",
                "court_name": "上海市第二中级人民法院",
                "case_type": "labor",
                "judgment_date": "2023-05-20",
                "case_summary": "涉及加班费计算和劳动合同解除的法律适用问题。",
                "precedent_value": "高",
                "citation_count": 28,
                "score": 0.87,
                "created_at": "2023-05-25T14:30:00Z"
            }
        ]
        
        # 简单的关键词匹配
        if query:
            filtered_cases = []
            query_lower = query.lower()
            for case in mock_cases:
                if (query_lower in case["title"].lower() or 
                    query_lower in case["case_summary"].lower()):
                    filtered_cases.append(case)
            mock_cases = filtered_cases
        
        # 应用过滤器
        if filters:
            if filters.get("case_type"):
                mock_cases = [c for c in mock_cases if c["case_type"] == filters["case_type"]]
            if filters.get("court_name"):
                mock_cases = [c for c in mock_cases if filters["court_name"] in c["court_name"]]
        
        # 分页
        total = len(mock_cases)
        mock_cases = mock_cases[from_:from_ + size]
        
        return {
            "hits": mock_cases,
            "total": total,
            "took": 5,
            "max_score": 0.95 if mock_cases else 0
        }
    
    async def delete_case(self, case_id: str) -> bool:
        """删除案例"""
        if not self.client:
            return False
            
        try:
            await self.client.delete(
                index=self.index_name,
                id=case_id
            )
            return True
        except NotFoundError:
            return False
        except Exception as e:
            logger.error(f"删除案例失败: {e}")
            return False
    
    async def get_case_by_id(self, case_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取案例"""
        if not self.client:
            return None
            
        try:
            response = await self.client.get(
                index=self.index_name,
                id=case_id
            )
            return response["_source"]
        except NotFoundError:
            return None
        except Exception as e:
            logger.error(f"获取案例失败: {e}")
            return None


# 全局Elasticsearch客户端实例
es_client = ElasticsearchClient()


async def get_elasticsearch_client() -> ElasticsearchClient:
    """获取Elasticsearch客户端"""
    if not es_client.client:
        await es_client.connect()
    return es_client
