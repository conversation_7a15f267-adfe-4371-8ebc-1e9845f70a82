"""
SQLAlchemy加密字段类型
提供透明的数据库字段加密功能
"""

from typing import Any, Optional
from sqlalchemy import TypeDecorator, String, Text
from sqlalchemy.engine import Dialect
from app.core.encryption import get_encryption_manager
import logging

logger = logging.getLogger(__name__)


class EncryptedType(TypeDecorator):
    """
    加密字段基类
    自动处理数据的加密和解密
    """
    
    impl = String
    cache_ok = True
    
    def __init__(self, length: Optional[int] = None, **kwargs):
        """
        初始化加密字段类型
        
        Args:
            length: 字段长度
            **kwargs: 其他参数
        """
        # 加密后的数据会比原始数据长，需要预留空间
        if length:
            # Base64编码会增加约33%的长度，再加上加密开销
            encrypted_length = int(length * 1.5) + 100
        else:
            encrypted_length = 500  # 默认长度
            
        super().__init__(length=encrypted_length, **kwargs)
    
    def process_bind_param(self, value: Any, dialect: Dialect) -> Optional[str]:
        """
        在存储到数据库前加密数据
        
        Args:
            value: 原始值
            dialect: 数据库方言
            
        Returns:
            加密后的值
        """
        if value is None:
            return value
        
        try:
            encryption_manager = get_encryption_manager()
            encrypted_value = encryption_manager.encrypt_field(str(value))
            logger.debug(f"字段数据已加密，原始长度: {len(str(value))}, 加密后长度: {len(encrypted_value)}")
            return encrypted_value
        except Exception as e:
            logger.error(f"字段加密失败: {e}")
            # 在生产环境中，可能需要抛出异常而不是返回原始值
            raise
    
    def process_result_value(self, value: Any, dialect: Dialect) -> Optional[str]:
        """
        从数据库读取后解密数据
        
        Args:
            value: 加密的值
            dialect: 数据库方言
            
        Returns:
            解密后的值
        """
        if value is None:
            return value
        
        try:
            encryption_manager = get_encryption_manager()
            decrypted_value = encryption_manager.decrypt_field(str(value))
            logger.debug(f"字段数据已解密，加密长度: {len(str(value))}, 解密后长度: {len(decrypted_value)}")
            return decrypted_value
        except Exception as e:
            logger.error(f"字段解密失败: {e}")
            # 在生产环境中，可能需要抛出异常
            raise


class EncryptedString(EncryptedType):
    """
    加密字符串字段
    适用于短文本字段如姓名、电话等
    """
    
    impl = String
    
    def __init__(self, length: int = 255, **kwargs):
        """
        初始化加密字符串字段
        
        Args:
            length: 原始字符串的最大长度
            **kwargs: 其他参数
        """
        super().__init__(length=length, **kwargs)


class EncryptedText(EncryptedType):
    """
    加密文本字段
    适用于长文本字段如地址、备注等
    """
    
    impl = Text
    
    def __init__(self, **kwargs):
        """
        初始化加密文本字段
        
        Args:
            **kwargs: 其他参数
        """
        # Text类型不需要指定长度
        TypeDecorator.__init__(self, **kwargs)


class EncryptedEmail(EncryptedString):
    """
    加密邮箱字段
    专门用于邮箱地址的加密存储
    """
    
    def __init__(self, **kwargs):
        """
        初始化加密邮箱字段
        """
        super().__init__(length=100, **kwargs)


class EncryptedPhone(EncryptedString):
    """
    加密电话字段
    专门用于电话号码的加密存储
    """
    
    def __init__(self, **kwargs):
        """
        初始化加密电话字段
        """
        super().__init__(length=20, **kwargs)


class EncryptedName(EncryptedString):
    """
    加密姓名字段
    专门用于姓名的加密存储
    """
    
    def __init__(self, **kwargs):
        """
        初始化加密姓名字段
        """
        super().__init__(length=100, **kwargs)


class EncryptedIDCard(EncryptedString):
    """
    加密身份证字段
    专门用于身份证号的加密存储
    """
    
    def __init__(self, **kwargs):
        """
        初始化加密身份证字段
        """
        super().__init__(length=18, **kwargs)


class EncryptedAddress(EncryptedText):
    """
    加密地址字段
    专门用于地址信息的加密存储
    """
    
    def __init__(self, **kwargs):
        """
        初始化加密地址字段
        """
        super().__init__(**kwargs)


class EncryptedBankCard(EncryptedString):
    """
    加密银行卡字段
    专门用于银行卡号的加密存储
    """
    
    def __init__(self, **kwargs):
        """
        初始化加密银行卡字段
        """
        super().__init__(length=19, **kwargs)


# 便捷的字段创建函数
def encrypted_string(length: int = 255, **kwargs) -> EncryptedString:
    """
    创建加密字符串字段
    
    Args:
        length: 字段长度
        **kwargs: 其他参数
        
    Returns:
        加密字符串字段实例
    """
    return EncryptedString(length=length, **kwargs)


def encrypted_text(**kwargs) -> EncryptedText:
    """
    创建加密文本字段
    
    Args:
        **kwargs: 其他参数
        
    Returns:
        加密文本字段实例
    """
    return EncryptedText(**kwargs)


def encrypted_email(**kwargs) -> EncryptedEmail:
    """
    创建加密邮箱字段
    
    Args:
        **kwargs: 其他参数
        
    Returns:
        加密邮箱字段实例
    """
    return EncryptedEmail(**kwargs)


def encrypted_phone(**kwargs) -> EncryptedPhone:
    """
    创建加密电话字段
    
    Args:
        **kwargs: 其他参数
        
    Returns:
        加密电话字段实例
    """
    return EncryptedPhone(**kwargs)


def encrypted_name(**kwargs) -> EncryptedName:
    """
    创建加密姓名字段
    
    Args:
        **kwargs: 其他参数
        
    Returns:
        加密姓名字段实例
    """
    return EncryptedName(**kwargs)


def encrypted_id_card(**kwargs) -> EncryptedIDCard:
    """
    创建加密身份证字段
    
    Args:
        **kwargs: 其他参数
        
    Returns:
        加密身份证字段实例
    """
    return EncryptedIDCard(**kwargs)


def encrypted_address(**kwargs) -> EncryptedAddress:
    """
    创建加密地址字段
    
    Args:
        **kwargs: 其他参数
        
    Returns:
        加密地址字段实例
    """
    return EncryptedAddress(**kwargs)


def encrypted_bank_card(**kwargs) -> EncryptedBankCard:
    """
    创建加密银行卡字段
    
    Args:
        **kwargs: 其他参数
        
    Returns:
        加密银行卡字段实例
    """
    return EncryptedBankCard(**kwargs)
