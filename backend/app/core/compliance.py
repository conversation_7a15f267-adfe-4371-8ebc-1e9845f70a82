"""
数据来源合规审查模块
提供法律数据使用授权、版权合规性检查和数据使用协议管理
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from enum import Enum
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, Numeric
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
import logging

from app.core.database import Base

logger = logging.getLogger(__name__)


class DataSourceType(str, Enum):
    """数据源类型枚举"""
    GOVERNMENT = "government"  # 政府公开数据
    COURT_DECISION = "court_decision"  # 法院判决书
    LEGAL_DATABASE = "legal_database"  # 法律数据库
    ACADEMIC_PAPER = "academic_paper"  # 学术论文
    NEWS_ARTICLE = "news_article"  # 新闻文章
    LEGAL_COMMENTARY = "legal_commentary"  # 法律评论
    REGULATION = "regulation"  # 法规条文
    CASE_LAW = "case_law"  # 案例法
    THIRD_PARTY_API = "third_party_api"  # 第三方API


class LicenseType(str, Enum):
    """许可证类型枚举"""
    PUBLIC_DOMAIN = "public_domain"  # 公有领域
    CREATIVE_COMMONS = "creative_commons"  # 知识共享
    COMMERCIAL_LICENSE = "commercial_license"  # 商业许可
    FAIR_USE = "fair_use"  # 合理使用
    CUSTOM_LICENSE = "custom_license"  # 自定义许可
    RESTRICTED = "restricted"  # 受限使用


class ComplianceStatus(str, Enum):
    """合规状态枚举"""
    COMPLIANT = "compliant"  # 合规
    NON_COMPLIANT = "non_compliant"  # 不合规
    UNDER_REVIEW = "under_review"  # 审查中
    PENDING_APPROVAL = "pending_approval"  # 待批准
    EXPIRED = "expired"  # 已过期


class DataSource(Base):
    """
    数据源表
    记录所有使用的数据源及其合规信息
    """
    __tablename__ = "data_sources"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 数据源基本信息
    source_name = Column(String(200), nullable=False, comment="数据源名称")
    source_type = Column(String(50), nullable=False, index=True, comment="数据源类型")
    source_url = Column(String(500), nullable=True, comment="数据源URL")
    provider = Column(String(200), nullable=True, comment="数据提供方")
    
    # 许可信息
    license_type = Column(String(50), nullable=False, index=True, comment="许可证类型")
    license_text = Column(Text, nullable=True, comment="许可证文本")
    license_url = Column(String(500), nullable=True, comment="许可证URL")
    
    # 使用权限
    commercial_use_allowed = Column(Boolean, default=False, comment="是否允许商业使用")
    modification_allowed = Column(Boolean, default=False, comment="是否允许修改")
    redistribution_allowed = Column(Boolean, default=False, comment="是否允许再分发")
    attribution_required = Column(Boolean, default=True, comment="是否需要署名")
    
    # 合规状态
    compliance_status = Column(String(50), nullable=False, index=True, comment="合规状态")
    last_review_date = Column(DateTime(timezone=True), nullable=True, comment="最后审查日期")
    next_review_date = Column(DateTime(timezone=True), nullable=True, comment="下次审查日期")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    last_used_date = Column(DateTime(timezone=True), nullable=True, comment="最后使用日期")
    
    # 费用信息
    cost_per_use = Column(Numeric(10, 4), nullable=True, comment="每次使用费用")
    monthly_cost = Column(Numeric(10, 2), nullable=True, comment="月度费用")
    
    # 联系信息
    contact_person = Column(String(100), nullable=True, comment="联系人")
    contact_email = Column(String(200), nullable=True, comment="联系邮箱")
    contact_phone = Column(String(50), nullable=True, comment="联系电话")
    
    # 备注
    notes = Column(Text, nullable=True, comment="备注")
    metadata = Column(JSONB, default=dict, comment="元数据")
    
    # 状态
    is_active = Column(Boolean, default=True, index=True, comment="是否激活")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    def __repr__(self):
        return f"<DataSource(id={self.id}, name={self.source_name})>"


class DataUsageAgreement(Base):
    """
    数据使用协议表
    记录与数据提供方签署的使用协议
    """
    __tablename__ = "data_usage_agreements"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 协议基本信息
    agreement_name = Column(String(200), nullable=False, comment="协议名称")
    agreement_number = Column(String(100), nullable=True, unique=True, comment="协议编号")
    data_source_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="数据源ID")
    
    # 协议方信息
    provider_name = Column(String(200), nullable=False, comment="提供方名称")
    provider_contact = Column(String(200), nullable=True, comment="提供方联系方式")
    
    # 协议内容
    agreement_content = Column(Text, nullable=False, comment="协议内容")
    usage_scope = Column(Text, nullable=True, comment="使用范围")
    restrictions = Column(Text, nullable=True, comment="使用限制")
    
    # 协议期限
    effective_date = Column(DateTime(timezone=True), nullable=False, comment="生效日期")
    expiry_date = Column(DateTime(timezone=True), nullable=True, comment="到期日期")
    auto_renewal = Column(Boolean, default=False, comment="是否自动续约")
    
    # 费用信息
    total_cost = Column(Numeric(12, 2), nullable=True, comment="总费用")
    payment_terms = Column(String(200), nullable=True, comment="付款条款")
    
    # 状态
    is_active = Column(Boolean, default=True, index=True, comment="是否激活")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    def __repr__(self):
        return f"<DataUsageAgreement(id={self.id}, name={self.agreement_name})>"


class ComplianceManager:
    """
    合规管理器
    提供数据来源合规审查和管理功能
    """
    
    def __init__(self, db_session: Session):
        """
        初始化合规管理器
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    def register_data_source(
        self,
        source_name: str,
        source_type: DataSourceType,
        license_type: LicenseType,
        source_url: Optional[str] = None,
        provider: Optional[str] = None,
        license_text: Optional[str] = None,
        license_url: Optional[str] = None,
        commercial_use_allowed: bool = False,
        modification_allowed: bool = False,
        redistribution_allowed: bool = False,
        attribution_required: bool = True,
        cost_per_use: Optional[float] = None,
        monthly_cost: Optional[float] = None,
        contact_person: Optional[str] = None,
        contact_email: Optional[str] = None,
        contact_phone: Optional[str] = None,
        notes: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> DataSource:
        """
        注册数据源
        
        Args:
            source_name: 数据源名称
            source_type: 数据源类型
            license_type: 许可证类型
            source_url: 数据源URL
            provider: 数据提供方
            license_text: 许可证文本
            license_url: 许可证URL
            commercial_use_allowed: 是否允许商业使用
            modification_allowed: 是否允许修改
            redistribution_allowed: 是否允许再分发
            attribution_required: 是否需要署名
            cost_per_use: 每次使用费用
            monthly_cost: 月度费用
            contact_person: 联系人
            contact_email: 联系邮箱
            contact_phone: 联系电话
            notes: 备注
            metadata: 元数据
            
        Returns:
            数据源记录
        """
        try:
            # 确定初始合规状态
            compliance_status = self._determine_initial_compliance_status(
                license_type, commercial_use_allowed
            )
            
            # 设置下次审查日期（6个月后）
            next_review_date = datetime.utcnow() + timedelta(days=180)
            
            data_source = DataSource(
                source_name=source_name,
                source_type=source_type.value,
                source_url=source_url,
                provider=provider,
                license_type=license_type.value,
                license_text=license_text,
                license_url=license_url,
                commercial_use_allowed=commercial_use_allowed,
                modification_allowed=modification_allowed,
                redistribution_allowed=redistribution_allowed,
                attribution_required=attribution_required,
                compliance_status=compliance_status.value,
                next_review_date=next_review_date,
                cost_per_use=cost_per_use,
                monthly_cost=monthly_cost,
                contact_person=contact_person,
                contact_email=contact_email,
                contact_phone=contact_phone,
                notes=notes,
                metadata=metadata or {}
            )
            
            self.db_session.add(data_source)
            self.db_session.commit()
            
            logger.info(f"数据源已注册: {source_name} - 状态: {compliance_status.value}")
            return data_source
            
        except Exception as e:
            logger.error(f"注册数据源失败: {e}")
            self.db_session.rollback()
            raise
    
    def create_usage_agreement(
        self,
        agreement_name: str,
        data_source_id: str,
        provider_name: str,
        agreement_content: str,
        effective_date: datetime,
        expiry_date: Optional[datetime] = None,
        agreement_number: Optional[str] = None,
        provider_contact: Optional[str] = None,
        usage_scope: Optional[str] = None,
        restrictions: Optional[str] = None,
        auto_renewal: bool = False,
        total_cost: Optional[float] = None,
        payment_terms: Optional[str] = None
    ) -> DataUsageAgreement:
        """
        创建数据使用协议
        
        Args:
            agreement_name: 协议名称
            data_source_id: 数据源ID
            provider_name: 提供方名称
            agreement_content: 协议内容
            effective_date: 生效日期
            expiry_date: 到期日期
            agreement_number: 协议编号
            provider_contact: 提供方联系方式
            usage_scope: 使用范围
            restrictions: 使用限制
            auto_renewal: 是否自动续约
            total_cost: 总费用
            payment_terms: 付款条款
            
        Returns:
            数据使用协议记录
        """
        try:
            agreement = DataUsageAgreement(
                agreement_name=agreement_name,
                agreement_number=agreement_number,
                data_source_id=data_source_id,
                provider_name=provider_name,
                provider_contact=provider_contact,
                agreement_content=agreement_content,
                usage_scope=usage_scope,
                restrictions=restrictions,
                effective_date=effective_date,
                expiry_date=expiry_date,
                auto_renewal=auto_renewal,
                total_cost=total_cost,
                payment_terms=payment_terms
            )
            
            self.db_session.add(agreement)
            self.db_session.commit()
            
            logger.info(f"数据使用协议已创建: {agreement_name}")
            return agreement
            
        except Exception as e:
            logger.error(f"创建数据使用协议失败: {e}")
            self.db_session.rollback()
            raise
    
    def check_data_source_compliance(
        self,
        data_source_id: str,
        intended_use: str = "commercial"
    ) -> Dict[str, Any]:
        """
        检查数据源合规性
        
        Args:
            data_source_id: 数据源ID
            intended_use: 预期用途
            
        Returns:
            合规检查结果
        """
        data_source = (
            self.db_session.query(DataSource)
            .filter(DataSource.id == data_source_id)
            .first()
        )
        
        if not data_source:
            return {
                "compliant": False,
                "reason": "数据源不存在",
                "recommendations": ["请先注册数据源"]
            }
        
        issues = []
        recommendations = []
        
        # 检查商业使用权限
        if intended_use == "commercial" and not data_source.commercial_use_allowed:
            issues.append("不允许商业使用")
            recommendations.append("获取商业使用许可或更换数据源")
        
        # 检查许可证状态
        if data_source.compliance_status == ComplianceStatus.NON_COMPLIANT.value:
            issues.append("数据源不合规")
            recommendations.append("联系数据提供方解决合规问题")
        
        # 检查是否需要审查
        if (data_source.next_review_date and 
            data_source.next_review_date <= datetime.utcnow()):
            issues.append("需要进行合规审查")
            recommendations.append("安排合规审查")
        
        # 检查使用协议是否过期
        agreements = self._get_active_agreements(data_source_id)
        expired_agreements = [
            agreement for agreement in agreements
            if agreement.expiry_date and agreement.expiry_date <= datetime.utcnow()
        ]
        
        if expired_agreements:
            issues.append("使用协议已过期")
            recommendations.append("续签或更新使用协议")
        
        return {
            "compliant": len(issues) == 0,
            "issues": issues,
            "recommendations": recommendations,
            "data_source": {
                "name": data_source.source_name,
                "type": data_source.source_type,
                "license_type": data_source.license_type,
                "compliance_status": data_source.compliance_status
            }
        }
    
    def record_data_usage(
        self,
        data_source_id: str,
        usage_context: Optional[str] = None
    ) -> bool:
        """
        记录数据使用
        
        Args:
            data_source_id: 数据源ID
            usage_context: 使用上下文
            
        Returns:
            记录是否成功
        """
        try:
            data_source = (
                self.db_session.query(DataSource)
                .filter(DataSource.id == data_source_id)
                .first()
            )
            
            if data_source:
                data_source.usage_count += 1
                data_source.last_used_date = datetime.utcnow()
                self.db_session.commit()
                
                logger.info(f"数据使用已记录: {data_source.source_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"记录数据使用失败: {e}")
            self.db_session.rollback()
            return False
    
    def _determine_initial_compliance_status(
        self,
        license_type: LicenseType,
        commercial_use_allowed: bool
    ) -> ComplianceStatus:
        """
        确定初始合规状态
        
        Args:
            license_type: 许可证类型
            commercial_use_allowed: 是否允许商业使用
            
        Returns:
            合规状态
        """
        if license_type in [LicenseType.PUBLIC_DOMAIN, LicenseType.CREATIVE_COMMONS]:
            return ComplianceStatus.COMPLIANT
        elif license_type == LicenseType.COMMERCIAL_LICENSE and commercial_use_allowed:
            return ComplianceStatus.COMPLIANT
        elif license_type == LicenseType.FAIR_USE:
            return ComplianceStatus.UNDER_REVIEW
        else:
            return ComplianceStatus.PENDING_APPROVAL
    
    def _get_active_agreements(self, data_source_id: str) -> List[DataUsageAgreement]:
        """
        获取激活的使用协议
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            使用协议列表
        """
        return (
            self.db_session.query(DataUsageAgreement)
            .filter(
                DataUsageAgreement.data_source_id == data_source_id,
                DataUsageAgreement.is_active == True
            )
            .all()
        )


def get_compliance_manager(db_session: Session) -> ComplianceManager:
    """
    获取合规管理器实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        合规管理器
    """
    return ComplianceManager(db_session)
