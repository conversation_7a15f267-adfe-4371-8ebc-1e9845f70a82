"""
日志配置模块
"""

import logging
import logging.handlers
import os
import sys
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from app.core.config import settings


class ChineseFormatter(logging.Formatter):
    """中文日志格式化器"""

    # 日志级别中文映射
    LEVEL_MAPPING = {
        'DEBUG': '调试',
        'INFO': '信息',
        'WARNING': '警告',
        'ERROR': '错误',
        'CRITICAL': '严重'
    }

    def format(self, record):
        """格式化日志记录"""
        # 转换日志级别为中文
        record.chinese_levelname = self.LEVEL_MAPPING.get(record.levelname, record.levelname)

        # 格式化时间为中文格式
        record.chinese_time = datetime.fromtimestamp(record.created).strftime('%Y年%m月%d日 %H:%M:%S')

        # 如果有结构化数据，添加到消息中
        if hasattr(record, 'structured_data') and record.structured_data:
            structured_str = json.dumps(record.structured_data, ensure_ascii=False, indent=2)
            record.msg = f"{record.msg}\n结构化数据: {structured_str}"

        return super().format(record)


class JSONFormatter(logging.Formatter):
    """JSON格式化器，用于结构化日志"""

    def format(self, record):
        """格式化为JSON格式"""
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # 添加结构化数据
        if hasattr(record, 'structured_data') and record.structured_data:
            log_entry.update(record.structured_data)

        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging() -> None:
    """设置应用日志配置"""

    # 创建日志目录
    log_dir = Path(settings.LOG_FILE).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    # 中文日志格式
    chinese_format = (
        "%(chinese_time)s - [%(chinese_levelname)s] - %(name)s - "
        "%(filename)s:%(lineno)d - %(funcName)s - %(message)s"
    )

    # 英文日志格式（用于JSON日志）
    english_format = (
        "%(asctime)s - %(name)s - %(levelname)s - "
        "%(filename)s:%(lineno)d - %(funcName)s - %(message)s"
    )

    # 创建格式化器
    chinese_formatter = ChineseFormatter(chinese_format)
    english_formatter = logging.Formatter(english_format)
    json_formatter = JSONFormatter()

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))

    # 清除现有处理器
    root_logger.handlers.clear()

    # 控制台处理器（中文格式）
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(chinese_formatter)
    root_logger.addHandler(console_handler)

    # 文件处理器（中文格式）
    file_handler = logging.handlers.RotatingFileHandler(
        filename=settings.LOG_FILE,
        maxBytes=settings.LOG_MAX_SIZE,
        backupCount=settings.LOG_BACKUP_COUNT,
        encoding="utf-8",
    )
    file_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    file_handler.setFormatter(chinese_formatter)
    root_logger.addHandler(file_handler)

    # 错误日志文件处理器（中文格式）
    error_log_file = str(Path(settings.LOG_FILE).with_suffix('.error.log'))
    error_handler = logging.handlers.RotatingFileHandler(
        filename=error_log_file,
        maxBytes=settings.LOG_MAX_SIZE,
        backupCount=settings.LOG_BACKUP_COUNT,
        encoding="utf-8",
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(chinese_formatter)
    root_logger.addHandler(error_handler)

    # JSON结构化日志处理器
    json_log_file = str(Path(settings.LOG_FILE).with_suffix('.json.log'))
    json_handler = logging.handlers.RotatingFileHandler(
        filename=json_log_file,
        maxBytes=settings.LOG_MAX_SIZE,
        backupCount=settings.LOG_BACKUP_COUNT,
        encoding="utf-8",
    )
    json_handler.setLevel(logging.DEBUG)
    json_handler.setFormatter(json_formatter)
    root_logger.addHandler(json_handler)

    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)

    # 输出日志系统初始化完成信息
    logger = logging.getLogger(__name__)
    logger.info("日志系统初始化完成 - 支持中文输出和结构化日志")


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志记录器"""
    return logging.getLogger(name)


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def _log(self, level: int, message: str, **kwargs: Any) -> None:
        """记录结构化日志"""
        extra_data = {
            "structured_data": kwargs
        } if kwargs else {}
        
        self.logger.log(level, message, extra=extra_data)
    
    def debug(self, message: str, **kwargs: Any) -> None:
        """记录调试日志"""
        self._log(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs: Any) -> None:
        """记录信息日志"""
        self._log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs: Any) -> None:
        """记录警告日志"""
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs: Any) -> None:
        """记录错误日志"""
        self._log(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs: Any) -> None:
        """记录严重错误日志"""
        self._log(logging.CRITICAL, message, **kwargs)


def get_structured_logger(name: str) -> StructuredLogger:
    """获取结构化日志记录器"""
    return StructuredLogger(name)


# 业务日志记录器
class BusinessLogger:
    """业务日志记录器"""

    def __init__(self):
        self.logger = get_structured_logger("business")

    def log_user_action(
        self,
        user_id: str,
        action: str,
        resource_type: str = None,
        resource_id: str = None,
        details: Dict[str, Any] = None,
        ip_address: str = None,
        user_agent: str = None,
    ) -> None:
        """记录用户操作日志"""
        self.logger.info(
            f"用户操作: {action}",
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details or {},
        )

    def log_api_call(
        self,
        endpoint: str,
        method: str,
        user_id: str = None,
        status_code: int = None,
        response_time: float = None,
        request_id: str = None,
        error: str = None,
    ) -> None:
        """记录API调用日志"""
        self.logger.info(
            f"API调用: {method} {endpoint}",
            endpoint=endpoint,
            method=method,
            user_id=user_id,
            status_code=status_code,
            response_time=response_time,
            request_id=request_id,
            error=error,
        )

    def log_system_event(
        self,
        event_type: str,
        message: str,
        details: Dict[str, Any] = None,
        severity: str = "info",
    ) -> None:
        """记录系统事件日志"""
        log_method = getattr(self.logger, severity.lower(), self.logger.info)
        log_method(
            f"系统事件: {event_type} - {message}",
            event_type=event_type,
            severity=severity,
            details=details or {},
        )

    def log_database_operation(
        self,
        operation: str,
        table: str,
        user_id: str = None,
        record_id: str = None,
        changes: Dict[str, Any] = None,
        execution_time: float = None,
    ) -> None:
        """记录数据库操作日志"""
        self.logger.info(
            f"数据库操作: {operation} - 表: {table}",
            operation=operation,
            table=table,
            user_id=user_id,
            record_id=record_id,
            changes=changes or {},
            execution_time=execution_time,
        )

    def log_ai_interaction(
        self,
        user_id: str,
        question: str,
        answer: str = None,
        model: str = None,
        confidence: float = None,
        processing_time: float = None,
        error: str = None,
    ) -> None:
        """记录AI交互日志"""
        self.logger.info(
            f"AI交互 - 用户: {user_id}",
            user_id=user_id,
            question=question[:200] + "..." if len(question) > 200 else question,
            answer=answer[:200] + "..." if answer and len(answer) > 200 else answer,
            model=model,
            confidence=confidence,
            processing_time=processing_time,
            error=error,
        )

    def log_security_event(
        self,
        event_type: str,
        user_id: str = None,
        ip_address: str = None,
        details: Dict[str, Any] = None,
        severity: str = "warning",
    ) -> None:
        """记录安全事件日志"""
        log_method = getattr(self.logger, severity.lower(), self.logger.warning)
        log_method(
            f"安全事件: {event_type}",
            event_type=event_type,
            user_id=user_id,
            ip_address=ip_address,
            severity=severity,
            details=details or {},
        )

    def log_performance_metric(
        self,
        metric_name: str,
        value: float,
        unit: str = None,
        tags: Dict[str, str] = None,
    ) -> None:
        """记录性能指标日志"""
        self.logger.info(
            f"性能指标: {metric_name} = {value} {unit or ''}",
            metric_name=metric_name,
            value=value,
            unit=unit,
            tags=tags or {},
        )


# 日志分析器
class LogAnalyzer:
    """日志分析器"""

    def __init__(self, log_file: str = None):
        self.log_file = log_file or settings.LOG_FILE
        self.json_log_file = str(Path(self.log_file).with_suffix('.json.log'))

    def analyze_error_patterns(self, hours: int = 24) -> Dict[str, Any]:
        """分析错误模式"""
        try:
            error_stats = {
                "total_errors": 0,
                "error_types": {},
                "error_trends": [],
                "top_errors": [],
            }

            # 这里可以实现具体的日志分析逻辑
            # 由于时间限制，先返回基本结构

            return error_stats
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"日志分析失败: {e}")
            return {}

    def get_performance_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            metrics = {
                "avg_response_time": 0.0,
                "request_count": 0,
                "error_rate": 0.0,
                "slow_requests": [],
            }

            # 这里可以实现具体的性能分析逻辑

            return metrics
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"性能指标获取失败: {e}")
            return {}


# 日志监控器
class LogMonitor:
    """日志监控器"""

    def __init__(self):
        self.logger = get_structured_logger("monitor")
        self.alert_thresholds = {
            "error_rate": 0.05,  # 5%错误率
            "response_time": 2.0,  # 2秒响应时间
            "memory_usage": 0.8,   # 80%内存使用率
        }

    def check_error_rate(self, current_rate: float) -> None:
        """检查错误率"""
        if current_rate > self.alert_thresholds["error_rate"]:
            self.logger.warning(
                f"错误率过高警告: {current_rate:.2%}",
                metric="error_rate",
                current_value=current_rate,
                threshold=self.alert_thresholds["error_rate"],
                alert_type="high_error_rate",
            )

    def check_response_time(self, avg_time: float) -> None:
        """检查响应时间"""
        if avg_time > self.alert_thresholds["response_time"]:
            self.logger.warning(
                f"响应时间过长警告: {avg_time:.2f}秒",
                metric="response_time",
                current_value=avg_time,
                threshold=self.alert_thresholds["response_time"],
                alert_type="slow_response",
            )

    def check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_status = {
            "status": "healthy",
            "checks": {},
            "alerts": [],
        }

        try:
            # 检查日志文件大小
            log_file_size = Path(settings.LOG_FILE).stat().st_size
            if log_file_size > settings.LOG_MAX_SIZE * 0.9:
                health_status["alerts"].append({
                    "type": "log_file_size",
                    "message": "日志文件接近最大大小",
                    "current_size": log_file_size,
                    "max_size": settings.LOG_MAX_SIZE,
                })

            health_status["checks"]["log_file_size"] = {
                "status": "ok" if log_file_size < settings.LOG_MAX_SIZE * 0.9 else "warning",
                "current_size": log_file_size,
                "max_size": settings.LOG_MAX_SIZE,
            }

        except Exception as e:
            health_status["status"] = "error"
            health_status["alerts"].append({
                "type": "system_check_error",
                "message": f"系统检查失败: {e}",
            })

        return health_status


# 创建全局实例
business_logger = BusinessLogger()
log_analyzer = LogAnalyzer()
log_monitor = LogMonitor()


# 便捷函数
def log_user_action(user_id: str, action: str, **kwargs):
    """记录用户操作的便捷函数"""
    business_logger.log_user_action(user_id, action, **kwargs)


def log_api_call(endpoint: str, method: str, **kwargs):
    """记录API调用的便捷函数"""
    business_logger.log_api_call(endpoint, method, **kwargs)


def log_system_event(event_type: str, message: str, **kwargs):
    """记录系统事件的便捷函数"""
    business_logger.log_system_event(event_type, message, **kwargs)


def log_security_event(event_type: str, **kwargs):
    """记录安全事件的便捷函数"""
    business_logger.log_security_event(event_type, **kwargs)
