"""
异常行为检测和告警系统
提供用户行为分析、异常检测和安全告警功能
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
from enum import Enum
from dataclasses import dataclass
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, Float
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
import logging
import statistics
from collections import defaultdict, Counter

from app.core.database import Base
from app.core.audit import AuditLog, get_audit_logger

logger = logging.getLogger(__name__)


class AnomalyType(str, Enum):
    """异常类型枚举"""
    SUSPICIOUS_LOGIN = "suspicious_login"  # 可疑登录
    UNUSUAL_ACCESS_PATTERN = "unusual_access_pattern"  # 异常访问模式
    HIGH_FREQUENCY_REQUESTS = "high_frequency_requests"  # 高频请求
    FAILED_LOGIN_ATTEMPTS = "failed_login_attempts"  # 登录失败尝试
    PRIVILEGE_ESCALATION = "privilege_escalation"  # 权限提升
    DATA_EXFILTRATION = "data_exfiltration"  # 数据泄露
    UNUSUAL_LOCATION = "unusual_location"  # 异常位置
    BRUTE_FORCE_ATTACK = "brute_force_attack"  # 暴力破解
    SQL_INJECTION_ATTEMPT = "sql_injection_attempt"  # SQL注入尝试
    XSS_ATTEMPT = "xss_attempt"  # XSS攻击尝试


class SeverityLevel(str, Enum):
    """严重程度枚举"""
    LOW = "low"  # 低
    MEDIUM = "medium"  # 中
    HIGH = "high"  # 高
    CRITICAL = "critical"  # 严重


class AlertStatus(str, Enum):
    """告警状态枚举"""
    OPEN = "open"  # 开放
    INVESTIGATING = "investigating"  # 调查中
    RESOLVED = "resolved"  # 已解决
    FALSE_POSITIVE = "false_positive"  # 误报


@dataclass
class AnomalyRule:
    """异常检测规则"""
    rule_id: str
    name: str
    description: str
    anomaly_type: AnomalyType
    severity: SeverityLevel
    threshold: float
    time_window_minutes: int
    enabled: bool = True


class SecurityAlert(Base):
    """
    安全告警表
    记录检测到的异常行为和安全事件
    """
    __tablename__ = "security_alerts"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 告警信息
    alert_title = Column(String(200), nullable=False, comment="告警标题")
    anomaly_type = Column(String(50), nullable=False, index=True, comment="异常类型")
    severity = Column(String(20), nullable=False, index=True, comment="严重程度")
    description = Column(Text, nullable=False, comment="告警描述")
    
    # 相关信息
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True, comment="相关用户ID")
    ip_address = Column(INET, nullable=True, index=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    
    # 检测信息
    rule_id = Column(String(100), nullable=False, index=True, comment="触发规则ID")
    threshold_value = Column(Float, nullable=True, comment="阈值")
    actual_value = Column(Float, nullable=True, comment="实际值")
    
    # 详细数据
    evidence = Column(JSONB, default=dict, comment="证据数据")
    metadata = Column(JSONB, default=dict, comment="元数据")
    
    # 状态信息
    status = Column(String(20), default=AlertStatus.OPEN.value, index=True, comment="告警状态")
    assigned_to = Column(String(100), nullable=True, comment="分配给")
    resolution_notes = Column(Text, nullable=True, comment="解决备注")
    
    # 时间信息
    first_detected_at = Column(DateTime(timezone=True), nullable=False, index=True, comment="首次检测时间")
    last_detected_at = Column(DateTime(timezone=True), nullable=False, comment="最后检测时间")
    resolved_at = Column(DateTime(timezone=True), nullable=True, comment="解决时间")
    
    # 统计信息
    occurrence_count = Column(Integer, default=1, comment="发生次数")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    def __repr__(self):
        return f"<SecurityAlert(id={self.id}, type={self.anomaly_type}, severity={self.severity})>"


class AnomalyDetector:
    """
    异常检测器
    分析用户行为并检测异常模式
    """
    
    def __init__(self, db_session: Session):
        """
        初始化异常检测器
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
        self.audit_logger = get_audit_logger(db_session)
        self.rules = self._load_detection_rules()
    
    def _load_detection_rules(self) -> List[AnomalyRule]:
        """
        加载检测规则
        
        Returns:
            检测规则列表
        """
        return [
            AnomalyRule(
                rule_id="failed_login_threshold",
                name="登录失败次数过多",
                description="在短时间内多次登录失败",
                anomaly_type=AnomalyType.FAILED_LOGIN_ATTEMPTS,
                severity=SeverityLevel.MEDIUM,
                threshold=5.0,
                time_window_minutes=15
            ),
            AnomalyRule(
                rule_id="high_frequency_requests",
                name="高频请求",
                description="在短时间内发送大量请求",
                anomaly_type=AnomalyType.HIGH_FREQUENCY_REQUESTS,
                severity=SeverityLevel.HIGH,
                threshold=100.0,
                time_window_minutes=5
            ),
            AnomalyRule(
                rule_id="brute_force_detection",
                name="暴力破解检测",
                description="检测到暴力破解攻击模式",
                anomaly_type=AnomalyType.BRUTE_FORCE_ATTACK,
                severity=SeverityLevel.CRITICAL,
                threshold=10.0,
                time_window_minutes=10
            ),
            AnomalyRule(
                rule_id="unusual_access_time",
                name="异常访问时间",
                description="在非正常时间访问系统",
                anomaly_type=AnomalyType.UNUSUAL_ACCESS_PATTERN,
                severity=SeverityLevel.LOW,
                threshold=0.1,
                time_window_minutes=60
            )
        ]
    
    def analyze_user_behavior(
        self,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        time_window_hours: int = 24
    ) -> List[Dict[str, Any]]:
        """
        分析用户行为
        
        Args:
            user_id: 用户ID
            ip_address: IP地址
            time_window_hours: 时间窗口（小时）
            
        Returns:
            异常检测结果列表
        """
        anomalies = []
        
        # 获取时间范围内的审计日志
        since = datetime.utcnow() - timedelta(hours=time_window_hours)
        
        query = (
            self.db_session.query(AuditLog)
            .filter(AuditLog.created_at >= since)
        )
        
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        if ip_address:
            query = query.filter(AuditLog.ip_address == ip_address)
        
        audit_logs = query.all()
        
        # 对每个规则进行检测
        for rule in self.rules:
            if not rule.enabled:
                continue
            
            anomaly = self._check_rule(rule, audit_logs)
            if anomaly:
                anomalies.append(anomaly)
        
        return anomalies
    
    def _check_rule(
        self,
        rule: AnomalyRule,
        audit_logs: List[AuditLog]
    ) -> Optional[Dict[str, Any]]:
        """
        检查单个规则
        
        Args:
            rule: 检测规则
            audit_logs: 审计日志列表
            
        Returns:
            异常检测结果
        """
        time_window = timedelta(minutes=rule.time_window_minutes)
        current_time = datetime.utcnow()
        window_start = current_time - time_window
        
        # 过滤时间窗口内的日志
        window_logs = [
            log for log in audit_logs
            if log.created_at >= window_start
        ]
        
        if rule.anomaly_type == AnomalyType.FAILED_LOGIN_ATTEMPTS:
            return self._check_failed_logins(rule, window_logs)
        elif rule.anomaly_type == AnomalyType.HIGH_FREQUENCY_REQUESTS:
            return self._check_high_frequency_requests(rule, window_logs)
        elif rule.anomaly_type == AnomalyType.BRUTE_FORCE_ATTACK:
            return self._check_brute_force(rule, window_logs)
        elif rule.anomaly_type == AnomalyType.UNUSUAL_ACCESS_PATTERN:
            return self._check_unusual_access_pattern(rule, window_logs)
        
        return None
    
    def _check_failed_logins(
        self,
        rule: AnomalyRule,
        logs: List[AuditLog]
    ) -> Optional[Dict[str, Any]]:
        """
        检查登录失败次数
        
        Args:
            rule: 检测规则
            logs: 审计日志列表
            
        Returns:
            异常检测结果
        """
        failed_logins = [
            log for log in logs
            if log.action == "LOGIN_ATTEMPT" and not log.success
        ]
        
        if len(failed_logins) >= rule.threshold:
            # 按IP地址分组
            ip_groups = defaultdict(list)
            for log in failed_logins:
                if log.ip_address:
                    ip_groups[str(log.ip_address)].append(log)
            
            # 找出失败次数最多的IP
            max_failures = 0
            suspect_ip = None
            for ip, ip_logs in ip_groups.items():
                if len(ip_logs) > max_failures:
                    max_failures = len(ip_logs)
                    suspect_ip = ip
            
            return {
                "rule_id": rule.rule_id,
                "anomaly_type": rule.anomaly_type,
                "severity": rule.severity,
                "threshold": rule.threshold,
                "actual_value": float(max_failures),
                "description": f"IP {suspect_ip} 在 {rule.time_window_minutes} 分钟内登录失败 {max_failures} 次",
                "evidence": {
                    "failed_login_count": max_failures,
                    "suspect_ip": suspect_ip,
                    "time_window_minutes": rule.time_window_minutes
                },
                "ip_address": suspect_ip
            }
        
        return None
    
    def _check_high_frequency_requests(
        self,
        rule: AnomalyRule,
        logs: List[AuditLog]
    ) -> Optional[Dict[str, Any]]:
        """
        检查高频请求
        
        Args:
            rule: 检测规则
            logs: 审计日志列表
            
        Returns:
            异常检测结果
        """
        # 按IP地址分组统计请求数
        ip_request_counts = Counter()
        for log in logs:
            if log.ip_address:
                ip_request_counts[str(log.ip_address)] += 1
        
        # 检查是否有IP超过阈值
        for ip, count in ip_request_counts.items():
            if count >= rule.threshold:
                return {
                    "rule_id": rule.rule_id,
                    "anomaly_type": rule.anomaly_type,
                    "severity": rule.severity,
                    "threshold": rule.threshold,
                    "actual_value": float(count),
                    "description": f"IP {ip} 在 {rule.time_window_minutes} 分钟内发送了 {count} 个请求",
                    "evidence": {
                        "request_count": count,
                        "suspect_ip": ip,
                        "time_window_minutes": rule.time_window_minutes
                    },
                    "ip_address": ip
                }
        
        return None
    
    def _check_brute_force(
        self,
        rule: AnomalyRule,
        logs: List[AuditLog]
    ) -> Optional[Dict[str, Any]]:
        """
        检查暴力破解攻击
        
        Args:
            rule: 检测规则
            logs: 审计日志列表
            
        Returns:
            异常检测结果
        """
        # 查找连续的登录失败
        failed_logins = [
            log for log in logs
            if log.action == "LOGIN_ATTEMPT" and not log.success
        ]
        
        # 按IP地址分组
        ip_groups = defaultdict(list)
        for log in failed_logins:
            if log.ip_address:
                ip_groups[str(log.ip_address)].append(log)
        
        # 检查每个IP的失败模式
        for ip, ip_logs in ip_groups.items():
            if len(ip_logs) >= rule.threshold:
                # 检查时间间隔是否表明暴力破解
                ip_logs.sort(key=lambda x: x.created_at)
                intervals = []
                for i in range(1, len(ip_logs)):
                    interval = (ip_logs[i].created_at - ip_logs[i-1].created_at).total_seconds()
                    intervals.append(interval)
                
                # 如果平均间隔很短，可能是暴力破解
                if intervals and statistics.mean(intervals) < 30:  # 30秒内
                    return {
                        "rule_id": rule.rule_id,
                        "anomaly_type": rule.anomaly_type,
                        "severity": rule.severity,
                        "threshold": rule.threshold,
                        "actual_value": float(len(ip_logs)),
                        "description": f"检测到来自 IP {ip} 的暴力破解攻击，{len(ip_logs)} 次失败尝试",
                        "evidence": {
                            "failed_attempts": len(ip_logs),
                            "suspect_ip": ip,
                            "average_interval_seconds": statistics.mean(intervals),
                            "time_window_minutes": rule.time_window_minutes
                        },
                        "ip_address": ip
                    }
        
        return None
    
    def _check_unusual_access_pattern(
        self,
        rule: AnomalyRule,
        logs: List[AuditLog]
    ) -> Optional[Dict[str, Any]]:
        """
        检查异常访问模式
        
        Args:
            rule: 检测规则
            logs: 审计日志列表
            
        Returns:
            异常检测结果
        """
        # 检查非工作时间访问（简单示例：晚上10点到早上6点）
        unusual_time_logs = []
        for log in logs:
            hour = log.created_at.hour
            if hour >= 22 or hour <= 6:  # 晚上10点到早上6点
                unusual_time_logs.append(log)
        
        # 计算异常时间访问的比例
        if logs:
            unusual_ratio = len(unusual_time_logs) / len(logs)
            if unusual_ratio >= rule.threshold:
                return {
                    "rule_id": rule.rule_id,
                    "anomaly_type": rule.anomaly_type,
                    "severity": rule.severity,
                    "threshold": rule.threshold,
                    "actual_value": unusual_ratio,
                    "description": f"检测到异常访问模式，{unusual_ratio:.1%} 的访问发生在非工作时间",
                    "evidence": {
                        "total_requests": len(logs),
                        "unusual_time_requests": len(unusual_time_logs),
                        "unusual_ratio": unusual_ratio,
                        "time_window_minutes": rule.time_window_minutes
                    }
                }
        
        return None
    
    def create_security_alert(
        self,
        anomaly: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> SecurityAlert:
        """
        创建安全告警
        
        Args:
            anomaly: 异常检测结果
            user_id: 用户ID
            
        Returns:
            安全告警记录
        """
        try:
            current_time = datetime.utcnow()
            
            alert = SecurityAlert(
                alert_title=f"{anomaly['anomaly_type'].replace('_', ' ').title()} 检测",
                anomaly_type=anomaly['anomaly_type'].value,
                severity=anomaly['severity'].value,
                description=anomaly['description'],
                user_id=user_id,
                ip_address=anomaly.get('ip_address'),
                rule_id=anomaly['rule_id'],
                threshold_value=anomaly.get('threshold'),
                actual_value=anomaly.get('actual_value'),
                evidence=anomaly.get('evidence', {}),
                first_detected_at=current_time,
                last_detected_at=current_time
            )
            
            self.db_session.add(alert)
            self.db_session.commit()
            
            logger.warning(f"安全告警已创建: {alert.alert_title} - 严重程度: {alert.severity}")
            return alert
            
        except Exception as e:
            logger.error(f"创建安全告警失败: {e}")
            self.db_session.rollback()
            raise
    
    def get_active_alerts(
        self,
        severity: Optional[SeverityLevel] = None,
        limit: int = 100
    ) -> List[SecurityAlert]:
        """
        获取活跃的安全告警
        
        Args:
            severity: 严重程度过滤
            limit: 限制数量
            
        Returns:
            安全告警列表
        """
        query = (
            self.db_session.query(SecurityAlert)
            .filter(SecurityAlert.status.in_([AlertStatus.OPEN.value, AlertStatus.INVESTIGATING.value]))
        )
        
        if severity:
            query = query.filter(SecurityAlert.severity == severity.value)
        
        return (
            query.order_by(SecurityAlert.created_at.desc())
            .limit(limit)
            .all()
        )


def get_anomaly_detector(db_session: Session) -> AnomalyDetector:
    """
    获取异常检测器实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        异常检测器
    """
    return AnomalyDetector(db_session)
