"""
数据加密系统
提供字段级加密、敏感数据脱敏和密钥管理功能
"""

import os
import base64
import hashlib
import secrets
from typing import Optional, Union, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)


class EncryptionManager:
    """
    数据加密管理器
    提供AES-256加密、字段级加密和敏感数据处理功能
    """
    
    def __init__(self, master_key: Optional[str] = None):
        """
        初始化加密管理器
        
        Args:
            master_key: 主密钥，如果未提供则从环境变量获取
        """
        self.master_key = master_key or os.getenv("ENCRYPTION_MASTER_KEY")
        if not self.master_key:
            raise ValueError("未找到加密主密钥，请设置ENCRYPTION_MASTER_KEY环境变量")
        
        # 生成Fernet密钥用于字段加密
        self._fernet_key = self._derive_fernet_key(self.master_key)
        self._fernet = Fernet(self._fernet_key)
        
        # 初始化AES加密器
        self._aes_key = self._derive_aes_key(self.master_key)
        
    def _derive_fernet_key(self, password: str) -> bytes:
        """
        从主密钥派生Fernet密钥
        
        Args:
            password: 主密钥
            
        Returns:
            派生的Fernet密钥
        """
        password_bytes = password.encode()
        salt = b'legal_assistant_salt'  # 在生产环境中应该使用随机盐
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key
    
    def _derive_aes_key(self, password: str) -> bytes:
        """
        从主密钥派生AES密钥
        
        Args:
            password: 主密钥
            
        Returns:
            派生的AES密钥
        """
        password_bytes = password.encode()
        salt = b'aes_encryption_salt'  # 在生产环境中应该使用随机盐
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        return kdf.derive(password_bytes)
    
    def encrypt_field(self, data: str) -> str:
        """
        加密字段数据（使用Fernet）
        
        Args:
            data: 要加密的数据
            
        Returns:
            加密后的数据（Base64编码）
        """
        if not data:
            return data
        
        try:
            encrypted_data = self._fernet.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"字段加密失败: {e}")
            raise
    
    def decrypt_field(self, encrypted_data: str) -> str:
        """
        解密字段数据
        
        Args:
            encrypted_data: 加密的数据
            
        Returns:
            解密后的原始数据
        """
        if not encrypted_data:
            return encrypted_data
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"字段解密失败: {e}")
            raise
    
    def encrypt_aes(self, data: str) -> str:
        """
        使用AES-256-CBC加密数据
        
        Args:
            data: 要加密的数据
            
        Returns:
            加密后的数据（Base64编码，包含IV）
        """
        if not data:
            return data
        
        try:
            # 生成随机IV
            iv = os.urandom(16)
            
            # 创建AES加密器
            cipher = Cipher(
                algorithms.AES(self._aes_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            
            # 填充数据到16字节的倍数
            padded_data = self._pad_data(data.encode())
            
            # 加密数据
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
            
            # 将IV和加密数据组合并Base64编码
            combined_data = iv + encrypted_data
            return base64.urlsafe_b64encode(combined_data).decode()
        except Exception as e:
            logger.error(f"AES加密失败: {e}")
            raise
    
    def decrypt_aes(self, encrypted_data: str) -> str:
        """
        使用AES-256-CBC解密数据
        
        Args:
            encrypted_data: 加密的数据
            
        Returns:
            解密后的原始数据
        """
        if not encrypted_data:
            return encrypted_data
        
        try:
            # Base64解码
            combined_data = base64.urlsafe_b64decode(encrypted_data.encode())
            
            # 分离IV和加密数据
            iv = combined_data[:16]
            encrypted_bytes = combined_data[16:]
            
            # 创建AES解密器
            cipher = Cipher(
                algorithms.AES(self._aes_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            
            # 解密数据
            padded_data = decryptor.update(encrypted_bytes) + decryptor.finalize()
            
            # 去除填充
            original_data = self._unpad_data(padded_data)
            
            return original_data.decode()
        except Exception as e:
            logger.error(f"AES解密失败: {e}")
            raise
    
    def _pad_data(self, data: bytes) -> bytes:
        """
        PKCS7填充数据到16字节的倍数
        
        Args:
            data: 原始数据
            
        Returns:
            填充后的数据
        """
        padding_length = 16 - (len(data) % 16)
        padding = bytes([padding_length] * padding_length)
        return data + padding
    
    def _unpad_data(self, padded_data: bytes) -> bytes:
        """
        去除PKCS7填充
        
        Args:
            padded_data: 填充后的数据
            
        Returns:
            原始数据
        """
        padding_length = padded_data[-1]
        return padded_data[:-padding_length]
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> tuple[str, str]:
        """
        使用PBKDF2哈希密码
        
        Args:
            password: 原始密码
            salt: 盐值，如果未提供则生成随机盐
            
        Returns:
            (哈希值, 盐值)
        """
        if salt is None:
            salt = secrets.token_hex(32)
        
        # 使用PBKDF2进行密码哈希
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
            backend=default_backend()
        )
        
        password_hash = base64.urlsafe_b64encode(kdf.derive(password.encode())).decode()
        return password_hash, salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """
        验证密码
        
        Args:
            password: 原始密码
            password_hash: 存储的密码哈希
            salt: 盐值
            
        Returns:
            密码是否正确
        """
        try:
            computed_hash, _ = self.hash_password(password, salt)
            return secrets.compare_digest(password_hash, computed_hash)
        except Exception as e:
            logger.error(f"密码验证失败: {e}")
            return False


class DataMasking:
    """
    敏感数据脱敏处理器
    提供各种敏感数据的脱敏功能
    """
    
    @staticmethod
    def mask_email(email: str) -> str:
        """
        脱敏邮箱地址
        
        Args:
            email: 原始邮箱
            
        Returns:
            脱敏后的邮箱
        """
        if not email or '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """
        脱敏手机号
        
        Args:
            phone: 原始手机号
            
        Returns:
            脱敏后的手机号
        """
        if not phone:
            return phone
        
        # 移除非数字字符
        digits = ''.join(filter(str.isdigit, phone))
        
        if len(digits) < 4:
            return '*' * len(digits)
        elif len(digits) <= 7:
            return digits[:2] + '*' * (len(digits) - 4) + digits[-2:]
        else:
            return digits[:3] + '*' * (len(digits) - 6) + digits[-3:]
    
    @staticmethod
    def mask_id_card(id_card: str) -> str:
        """
        脱敏身份证号
        
        Args:
            id_card: 原始身份证号
            
        Returns:
            脱敏后的身份证号
        """
        if not id_card:
            return id_card
        
        if len(id_card) < 8:
            return '*' * len(id_card)
        
        return id_card[:4] + '*' * (len(id_card) - 8) + id_card[-4:]
    
    @staticmethod
    def mask_name(name: str) -> str:
        """
        脱敏姓名
        
        Args:
            name: 原始姓名
            
        Returns:
            脱敏后的姓名
        """
        if not name:
            return name
        
        if len(name) <= 1:
            return '*'
        elif len(name) == 2:
            return name[0] + '*'
        else:
            return name[0] + '*' * (len(name) - 2) + name[-1]
    
    @staticmethod
    def mask_bank_card(card_number: str) -> str:
        """
        脱敏银行卡号
        
        Args:
            card_number: 原始银行卡号
            
        Returns:
            脱敏后的银行卡号
        """
        if not card_number:
            return card_number
        
        # 移除非数字字符
        digits = ''.join(filter(str.isdigit, card_number))
        
        if len(digits) < 8:
            return '*' * len(digits)
        
        return digits[:4] + '*' * (len(digits) - 8) + digits[-4:]


# 全局加密管理器实例
encryption_manager = None

def get_encryption_manager() -> EncryptionManager:
    """
    获取全局加密管理器实例
    
    Returns:
        加密管理器实例
    """
    global encryption_manager
    if encryption_manager is None:
        encryption_manager = EncryptionManager()
    return encryption_manager


def init_encryption(master_key: Optional[str] = None) -> None:
    """
    初始化加密系统
    
    Args:
        master_key: 主密钥
    """
    global encryption_manager
    encryption_manager = EncryptionManager(master_key)
    logger.info("数据加密系统初始化完成")
