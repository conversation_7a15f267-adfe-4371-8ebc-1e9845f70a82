"""
安全相关功能模块
包含认证、加密、HTTPS配置和安全中间件
"""

import os
import ssl
import secrets
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Any, Union, Optional, List
from jose import jwt, JWTError
from passlib.context import CryptContext
from passlib.hash import bcrypt
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from fastapi import FastAPI, Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from starlette.responses import Response as StarletteResponse
import logging

from app.core.config import settings
from app.core.exceptions import AuthenticationException

logger = logging.getLogger(__name__)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """创建访问令牌"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    
    logger.info(f"创建访问令牌成功: subject={subject}")
    return encoded_jwt


def create_refresh_token(subject: Union[str, Any]) -> str:
    """创建刷新令牌"""
    expire = datetime.utcnow() + timedelta(days=30)  # 刷新令牌30天有效
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    
    logger.info(f"创建刷新令牌成功: subject={subject}")
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """验证令牌并返回用户ID"""
    try:
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.warning("令牌中缺少用户ID")
            return None
        
        # 检查令牌是否过期
        exp = payload.get("exp")
        if exp and datetime.utcnow() > datetime.fromtimestamp(exp):
            logger.warning("令牌已过期")
            return None
            
        return user_id
        
    except JWTError as e:
        logger.error(f"令牌验证失败: {e}")
        return None


def verify_refresh_token(token: str) -> Optional[str]:
    """验证刷新令牌并返回用户ID"""
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # 检查令牌类型
        token_type = payload.get("type")
        if token_type != "refresh":
            logger.warning("不是刷新令牌")
            return None
        
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.warning("刷新令牌中缺少用户ID")
            return None
            
        return user_id
        
    except JWTError as e:
        logger.error(f"刷新令牌验证失败: {e}")
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"密码验证失败: {e}")
        return False


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def generate_password_reset_token(email: str) -> str:
    """生成密码重置令牌"""
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email},
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """验证密码重置令牌"""
    try:
        decoded_token = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        return decoded_token["sub"]
    except JWTError:
        return None


def generate_session_token() -> str:
    """生成会话令牌"""
    return secrets.token_urlsafe(32)


def generate_api_key() -> str:
    """生成API密钥"""
    return secrets.token_urlsafe(32)


class PasswordValidator:
    """密码验证器"""
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, list[str]]:
        """验证密码强度"""
        errors = []
        
        # 长度检查
        if len(password) < 8:
            errors.append("密码长度至少8位")
        
        # 字符类型检查
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if not has_upper:
            errors.append("密码必须包含大写字母")
        if not has_lower:
            errors.append("密码必须包含小写字母")
        if not has_digit:
            errors.append("密码必须包含数字")
        if not has_special:
            errors.append("密码必须包含特殊字符")
        
        # 常见密码检查
        common_passwords = [
            "password", "123456", "123456789", "qwerty", 
            "abc123", "password123", "admin", "root"
        ]
        if password.lower() in common_passwords:
            errors.append("不能使用常见密码")
        
        return len(errors) == 0, errors


class TokenBlacklist:
    """令牌黑名单管理"""
    
    def __init__(self):
        self._blacklist = set()
    
    def add_token(self, token: str) -> None:
        """添加令牌到黑名单"""
        self._blacklist.add(token)
        logger.info(f"令牌已添加到黑名单")
    
    def is_blacklisted(self, token: str) -> bool:
        """检查令牌是否在黑名单中"""
        return token in self._blacklist
    
    def remove_token(self, token: str) -> None:
        """从黑名单中移除令牌"""
        self._blacklist.discard(token)
        logger.info(f"令牌已从黑名单中移除")
    
    def clear(self) -> None:
        """清空黑名单"""
        self._blacklist.clear()
        logger.info("令牌黑名单已清空")


# 全局令牌黑名单实例
token_blacklist = TokenBlacklist()


def create_email_verification_token(email: str) -> str:
    """创建邮箱验证令牌"""
    delta = timedelta(hours=24)  # 24小时有效
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email, "type": "email_verification"},
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_email_verification_token(token: str) -> Optional[str]:
    """验证邮箱验证令牌"""
    try:
        decoded_token = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # 检查令牌类型
        if decoded_token.get("type") != "email_verification":
            return None
            
        return decoded_token["sub"]
    except JWTError:
        return None


def create_phone_verification_code() -> str:
    """创建手机验证码"""
    import random
    return str(random.randint(100000, 999999))


def hash_sensitive_data(data: str) -> str:
    """哈希敏感数据"""
    return bcrypt.hash(data)


def verify_sensitive_data(data: str, hashed_data: str) -> bool:
    """验证敏感数据"""
    try:
        return bcrypt.verify(data, hashed_data)
    except Exception:
        return False


class DataEncryption:
    """数据加密工具类"""

    def __init__(self, password: str = None):
        """初始化加密器"""
        if password is None:
            password = settings.ENCRYPTION_KEY

        # 使用PBKDF2从密码生成密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'legal_assistant_salt',  # 在生产环境中应该使用随机盐
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        self.cipher_suite = Fernet(key)

    def encrypt(self, data: str) -> str:
        """加密数据"""
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"数据加密失败: {e}")
            raise

    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"数据解密失败: {e}")
            raise

    def encrypt_dict(self, data: dict, fields: list) -> dict:
        """加密字典中的指定字段"""
        encrypted_data = data.copy()
        for field in fields:
            if field in encrypted_data and encrypted_data[field]:
                encrypted_data[field] = self.encrypt(str(encrypted_data[field]))
        return encrypted_data

    def decrypt_dict(self, data: dict, fields: list) -> dict:
        """解密字典中的指定字段"""
        decrypted_data = data.copy()
        for field in fields:
            if field in decrypted_data and decrypted_data[field]:
                try:
                    decrypted_data[field] = self.decrypt(decrypted_data[field])
                except Exception:
                    # 如果解密失败，可能是未加密的数据，保持原样
                    pass
        return decrypted_data


class SecurityUtils:
    """安全工具类"""

    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """生成安全随机令牌"""
        return secrets.token_urlsafe(length)

    @staticmethod
    def generate_api_key() -> str:
        """生成API密钥"""
        return f"ak_{secrets.token_urlsafe(32)}"

    @staticmethod
    def hash_data(data: str, salt: str = None) -> str:
        """哈希数据"""
        if salt is None:
            salt = secrets.token_hex(16)

        hash_obj = hashlib.sha256()
        hash_obj.update((data + salt).encode())
        return f"{salt}:{hash_obj.hexdigest()}"

    @staticmethod
    def verify_hash(data: str, hashed_data: str) -> bool:
        """验证哈希数据"""
        try:
            salt, hash_value = hashed_data.split(":", 1)
            hash_obj = hashlib.sha256()
            hash_obj.update((data + salt).encode())
            return hash_obj.hexdigest() == hash_value
        except ValueError:
            return False

    @staticmethod
    def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
        """掩码敏感数据"""
        if len(data) <= visible_chars:
            return mask_char * len(data)

        visible_start = visible_chars // 2
        visible_end = visible_chars - visible_start

        if visible_end == 0:
            return data[:visible_start] + mask_char * (len(data) - visible_start)
        else:
            return (data[:visible_start] +
                   mask_char * (len(data) - visible_chars) +
                   data[-visible_end:])

    @staticmethod
    def validate_password_strength(password: str) -> dict:
        """验证密码强度"""
        result = {
            "is_valid": True,
            "score": 0,
            "issues": []
        }

        # 长度检查
        if len(password) < 8:
            result["issues"].append("密码长度至少8个字符")
            result["is_valid"] = False
        else:
            result["score"] += 1

        # 包含大写字母
        if not any(c.isupper() for c in password):
            result["issues"].append("密码必须包含大写字母")
            result["is_valid"] = False
        else:
            result["score"] += 1

        # 包含小写字母
        if not any(c.islower() for c in password):
            result["issues"].append("密码必须包含小写字母")
            result["is_valid"] = False
        else:
            result["score"] += 1

        # 包含数字
        if not any(c.isdigit() for c in password):
            result["issues"].append("密码必须包含数字")
            result["is_valid"] = False
        else:
            result["score"] += 1

        # 包含特殊字符
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            result["issues"].append("密码建议包含特殊字符")
            result["score"] += 0.5
        else:
            result["score"] += 1

        return result


# 全局加密器实例
data_encryptor = DataEncryption()


def encrypt_sensitive_data(data: str) -> str:
    """加密敏感数据的便捷函数"""
    return data_encryptor.encrypt(data)


def decrypt_sensitive_data(encrypted_data: str) -> str:
    """解密敏感数据的便捷函数"""
    return data_encryptor.decrypt(encrypted_data)


def mask_email(email: str) -> str:
    """掩码邮箱地址"""
    if "@" not in email:
        return SecurityUtils.mask_sensitive_data(email)

    local, domain = email.split("@", 1)
    masked_local = SecurityUtils.mask_sensitive_data(local, visible_chars=2)
    return f"{masked_local}@{domain}"


def mask_phone(phone: str) -> str:
    """掩码手机号码"""
    return SecurityUtils.mask_sensitive_data(phone, visible_chars=7)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    安全头中间件
    添加各种安全相关的HTTP头
    """

    def __init__(self, app, **kwargs):
        """
        初始化安全头中间件

        Args:
            app: FastAPI应用实例
            **kwargs: 其他参数
        """
        super().__init__(app)
        self.config = {
            # 内容安全策略
            'content_security_policy': kwargs.get('csp',
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            ),
            # 严格传输安全
            'strict_transport_security': kwargs.get('hsts',
                "max-age=31536000; includeSubDomains; preload"
            ),
            # X-Frame-Options
            'x_frame_options': kwargs.get('frame_options', 'DENY'),
            # X-Content-Type-Options
            'x_content_type_options': kwargs.get('content_type_options', 'nosniff'),
            # X-XSS-Protection
            'x_xss_protection': kwargs.get('xss_protection', '1; mode=block'),
            # Referrer Policy
            'referrer_policy': kwargs.get('referrer_policy', 'strict-origin-when-cross-origin'),
            # Permissions Policy
            'permissions_policy': kwargs.get('permissions_policy',
                "geolocation=(), microphone=(), camera=(), payment=(), usb=(), "
                "magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=()"
            ),
        }

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        处理请求并添加安全头

        Args:
            request: 请求对象
            call_next: 下一个中间件

        Returns:
            响应对象
        """
        response = await call_next(request)

        # 添加安全头
        if self.config['content_security_policy']:
            response.headers["Content-Security-Policy"] = self.config['content_security_policy']

        if self.config['strict_transport_security'] and request.url.scheme == 'https':
            response.headers["Strict-Transport-Security"] = self.config['strict_transport_security']

        if self.config['x_frame_options']:
            response.headers["X-Frame-Options"] = self.config['x_frame_options']

        if self.config['x_content_type_options']:
            response.headers["X-Content-Type-Options"] = self.config['x_content_type_options']

        if self.config['x_xss_protection']:
            response.headers["X-XSS-Protection"] = self.config['x_xss_protection']

        if self.config['referrer_policy']:
            response.headers["Referrer-Policy"] = self.config['referrer_policy']

        if self.config['permissions_policy']:
            response.headers["Permissions-Policy"] = self.config['permissions_policy']

        # 移除可能泄露服务器信息的头
        response.headers.pop("Server", None)
        response.headers.pop("X-Powered-By", None)

        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    简单的速率限制中间件
    基于IP地址进行请求频率限制
    """

    def __init__(self, app, max_requests: int = 100, window_seconds: int = 60):
        """
        初始化速率限制中间件

        Args:
            app: FastAPI应用实例
            max_requests: 时间窗口内的最大请求数
            window_seconds: 时间窗口大小（秒）
        """
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # 在生产环境中应该使用Redis等外部存储

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        处理请求并检查速率限制

        Args:
            request: 请求对象
            call_next: 下一个中间件

        Returns:
            响应对象
        """
        import time

        client_ip = self._get_client_ip(request)
        current_time = time.time()

        # 清理过期的记录
        self._cleanup_expired_requests(current_time)

        # 检查当前IP的请求记录
        if client_ip not in self.requests:
            self.requests[client_ip] = []

        # 过滤时间窗口内的请求
        window_start = current_time - self.window_seconds
        recent_requests = [
            req_time for req_time in self.requests[client_ip]
            if req_time > window_start
        ]

        # 检查是否超过限制
        if len(recent_requests) >= self.max_requests:
            logger.warning(f"IP {client_ip} 超过速率限制: {len(recent_requests)}/{self.max_requests}")
            return StarletteResponse(
                content="Rate limit exceeded",
                status_code=429,
                headers={
                    "Retry-After": str(self.window_seconds),
                    "X-RateLimit-Limit": str(self.max_requests),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": str(int(current_time + self.window_seconds))
                }
            )

        # 记录当前请求
        recent_requests.append(current_time)
        self.requests[client_ip] = recent_requests

        # 继续处理请求
        response = await call_next(request)

        # 添加速率限制头
        remaining = self.max_requests - len(recent_requests)
        response.headers["X-RateLimit-Limit"] = str(self.max_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(current_time + self.window_seconds))

        return response

    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端IP地址

        Args:
            request: 请求对象

        Returns:
            客户端IP地址
        """
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def _cleanup_expired_requests(self, current_time: float) -> None:
        """
        清理过期的请求记录

        Args:
            current_time: 当前时间戳
        """
        window_start = current_time - self.window_seconds
        expired_ips = []

        for ip, requests in self.requests.items():
            # 过滤掉过期的请求
            recent_requests = [req_time for req_time in requests if req_time > window_start]

            if recent_requests:
                self.requests[ip] = recent_requests
            else:
                expired_ips.append(ip)

        # 删除没有活跃请求的IP记录
        for ip in expired_ips:
            del self.requests[ip]


class SecurityConfig:
    """
    安全配置类
    管理应用的安全设置
    """

    def __init__(self):
        """
        初始化安全配置
        """
        # HTTPS配置
        self.force_https = os.getenv("FORCE_HTTPS", "false").lower() == "true"
        self.ssl_cert_path = os.getenv("SSL_CERT_PATH")
        self.ssl_key_path = os.getenv("SSL_KEY_PATH")

        # 信任的主机
        self.trusted_hosts = self._parse_trusted_hosts()

        # CORS配置
        self.cors_origins = self._parse_cors_origins()
        self.cors_credentials = os.getenv("CORS_ALLOW_CREDENTIALS", "true").lower() == "true"

        # 速率限制配置
        self.rate_limit_enabled = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
        self.rate_limit_requests = int(os.getenv("RATE_LIMIT_REQUESTS", "100"))
        self.rate_limit_window = int(os.getenv("RATE_LIMIT_WINDOW", "60"))

        # 安全头配置
        self.security_headers_enabled = os.getenv("SECURITY_HEADERS_ENABLED", "true").lower() == "true"

        # 压缩配置
        self.gzip_enabled = os.getenv("GZIP_ENABLED", "true").lower() == "true"
        self.gzip_minimum_size = int(os.getenv("GZIP_MINIMUM_SIZE", "1000"))

    def _parse_trusted_hosts(self) -> List[str]:
        """
        解析信任的主机列表

        Returns:
            信任的主机列表
        """
        hosts_str = os.getenv("TRUSTED_HOSTS", "localhost,127.0.0.1,*.localhost")
        return [host.strip() for host in hosts_str.split(",") if host.strip()]

    def _parse_cors_origins(self) -> List[str]:
        """
        解析CORS允许的源列表

        Returns:
            CORS允许的源列表
        """
        origins_str = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://127.0.0.1:3000")
        return [origin.strip() for origin in origins_str.split(",") if origin.strip()]

    def get_ssl_context(self) -> Optional[ssl.SSLContext]:
        """
        获取SSL上下文

        Returns:
            SSL上下文对象，如果未配置则返回None
        """
        if not (self.ssl_cert_path and self.ssl_key_path):
            return None

        if not (os.path.exists(self.ssl_cert_path) and os.path.exists(self.ssl_key_path)):
            logger.warning(f"SSL证书文件不存在: {self.ssl_cert_path}, {self.ssl_key_path}")
            return None

        try:
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            context.load_cert_chain(self.ssl_cert_path, self.ssl_key_path)

            # 设置安全的SSL选项
            context.options |= ssl.OP_NO_SSLv2
            context.options |= ssl.OP_NO_SSLv3
            context.options |= ssl.OP_NO_TLSv1
            context.options |= ssl.OP_NO_TLSv1_1
            context.options |= ssl.OP_SINGLE_DH_USE
            context.options |= ssl.OP_SINGLE_ECDH_USE

            # 设置密码套件
            context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')

            return context
        except Exception as e:
            logger.error(f"创建SSL上下文失败: {e}")
            return None


def setup_security_middleware(app: FastAPI, config: Optional[SecurityConfig] = None) -> None:
    """
    设置安全中间件

    Args:
        app: FastAPI应用实例
        config: 安全配置对象
    """
    if config is None:
        config = SecurityConfig()

    # 1. 信任主机中间件（最外层）
    if config.trusted_hosts:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=config.trusted_hosts
        )
        logger.info(f"已启用信任主机中间件: {config.trusted_hosts}")

    # 2. HTTPS重定向中间件
    if config.force_https:
        app.add_middleware(HTTPSRedirectMiddleware)
        logger.info("已启用HTTPS重定向中间件")

    # 3. 安全头中间件
    if config.security_headers_enabled:
        app.add_middleware(SecurityHeadersMiddleware)
        logger.info("已启用安全头中间件")

    # 4. 速率限制中间件
    if config.rate_limit_enabled:
        app.add_middleware(
            RateLimitMiddleware,
            max_requests=config.rate_limit_requests,
            window_seconds=config.rate_limit_window
        )
        logger.info(f"已启用速率限制中间件: {config.rate_limit_requests}请求/{config.rate_limit_window}秒")

    # 5. CORS中间件
    if config.cors_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=config.cors_origins,
            allow_credentials=config.cors_credentials,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
            allow_headers=["*"],
        )
        logger.info(f"已启用CORS中间件: {config.cors_origins}")

    # 6. GZip压缩中间件（最内层）
    if config.gzip_enabled:
        app.add_middleware(
            GZipMiddleware,
            minimum_size=config.gzip_minimum_size
        )
        logger.info(f"已启用GZip压缩中间件: 最小大小 {config.gzip_minimum_size} 字节")


# 全局安全配置实例
security_config = SecurityConfig()


def get_security_config() -> SecurityConfig:
    """
    获取全局安全配置实例

    Returns:
        安全配置实例
    """
    return security_config
