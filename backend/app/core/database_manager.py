"""
数据库管理器
提供数据库连接池管理、迁移、备份等功能
"""

import os
import logging
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from datetime import datetime

from sqlalchemy import create_engine, text, MetaData, inspect
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from alembic.config import Config
from alembic import command
from alembic.runtime.migration import MigrationContext
from alembic.script import ScriptDirectory

from app.core.database import Base

logger = logging.getLogger(__name__)


class DatabaseConfig:
    """数据库配置"""
    
    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL", "postgresql://user:password@localhost:5432/ai_legal_assistant")
        self.pool_size = int(os.getenv("DB_POOL_SIZE", "10"))
        self.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", "20"))
        self.pool_timeout = int(os.getenv("DB_POOL_TIMEOUT", "30"))
        self.pool_recycle = int(os.getenv("DB_POOL_RECYCLE", "3600"))
        self.echo = os.getenv("DB_ECHO", "false").lower() == "true"
        
        # SSL配置
        self.ssl_mode = os.getenv("DB_SSL_MODE", "prefer")
        self.ssl_cert = os.getenv("DB_SSL_CERT")
        self.ssl_key = os.getenv("DB_SSL_KEY")
        self.ssl_root_cert = os.getenv("DB_SSL_ROOT_CERT")


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        """
        初始化数据库管理器
        
        Args:
            config: 数据库配置
        """
        self.config = config or DatabaseConfig()
        self.engine = None
        self.session_factory = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化数据库引擎"""
        try:
            # 构建连接参数
            connect_args = {}
            
            # SSL配置
            if self.config.ssl_mode != "disable":
                ssl_args = {"sslmode": self.config.ssl_mode}
                
                if self.config.ssl_cert:
                    ssl_args["sslcert"] = self.config.ssl_cert
                if self.config.ssl_key:
                    ssl_args["sslkey"] = self.config.ssl_key
                if self.config.ssl_root_cert:
                    ssl_args["sslrootcert"] = self.config.ssl_root_cert
                
                connect_args.update(ssl_args)
            
            # 创建引擎
            self.engine = create_engine(
                self.config.database_url,
                poolclass=QueuePool,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                echo=self.config.echo,
                connect_args=connect_args
            )
            
            # 创建会话工厂
            self.session_factory = sessionmaker(bind=self.engine)
            
            logger.info("数据库引擎初始化成功")
            
        except Exception as e:
            logger.error(f"数据库引擎初始化失败: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """获取数据库会话上下文管理器"""
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            session.close()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            logger.info("数据库连接测试成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        try:
            with self.engine.connect() as conn:
                # 获取数据库版本
                version_result = conn.execute(text("SELECT version()"))
                version = version_result.fetchone()[0]
                
                # 获取数据库大小
                size_result = conn.execute(text("""
                    SELECT pg_size_pretty(pg_database_size(current_database()))
                """))
                size = size_result.fetchone()[0]
                
                # 获取连接数
                connections_result = conn.execute(text("""
                    SELECT count(*) FROM pg_stat_activity
                """))
                connections = connections_result.fetchone()[0]
                
                # 获取表数量
                tables_result = conn.execute(text("""
                    SELECT count(*) FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """))
                tables_count = tables_result.fetchone()[0]
                
                return {
                    "version": version,
                    "size": size,
                    "active_connections": connections,
                    "tables_count": tables_count,
                    "engine_info": {
                        "pool_size": self.engine.pool.size(),
                        "checked_in": self.engine.pool.checkedin(),
                        "checked_out": self.engine.pool.checkedout(),
                        "overflow": self.engine.pool.overflow(),
                        "invalid": self.engine.pool.invalid()
                    }
                }
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {"error": str(e)}
    
    def create_all_tables(self):
        """创建所有表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("所有表创建成功")
        except Exception as e:
            logger.error(f"创建表失败: {e}")
            raise
    
    def drop_all_tables(self):
        """删除所有表（谨慎使用）"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.warning("所有表已删除")
        except Exception as e:
            logger.error(f"删除表失败: {e}")
            raise
    
    def get_table_list(self) -> List[str]:
        """获取表列表"""
        try:
            inspector = inspect(self.engine)
            tables = inspector.get_table_names()
            return tables
        except Exception as e:
            logger.error(f"获取表列表失败: {e}")
            return []
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        try:
            inspector = inspect(self.engine)
            
            # 获取列信息
            columns = inspector.get_columns(table_name)
            
            # 获取索引信息
            indexes = inspector.get_indexes(table_name)
            
            # 获取外键信息
            foreign_keys = inspector.get_foreign_keys(table_name)
            
            # 获取表大小
            with self.engine.connect() as conn:
                size_result = conn.execute(text(f"""
                    SELECT pg_size_pretty(pg_total_relation_size('{table_name}'))
                """))
                size = size_result.fetchone()[0]
                
                # 获取行数
                count_result = conn.execute(text(f"SELECT count(*) FROM {table_name}"))
                row_count = count_result.fetchone()[0]
            
            return {
                "table_name": table_name,
                "columns": columns,
                "indexes": indexes,
                "foreign_keys": foreign_keys,
                "size": size,
                "row_count": row_count
            }
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            return {"error": str(e)}
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """执行查询"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                
                # 转换为字典列表
                columns = result.keys()
                rows = []
                for row in result:
                    rows.append(dict(zip(columns, row)))
                
                return rows
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            raise
    
    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            import subprocess
            
            # 解析数据库URL
            from urllib.parse import urlparse
            parsed = urlparse(self.config.database_url)
            
            # 构建pg_dump命令
            cmd = [
                "pg_dump",
                "-h", parsed.hostname,
                "-p", str(parsed.port or 5432),
                "-U", parsed.username,
                "-d", parsed.path[1:],  # 移除开头的斜杠
                "-f", backup_path,
                "--verbose",
                "--no-password"
            ]
            
            # 设置密码环境变量
            env = os.environ.copy()
            env["PGPASSWORD"] = parsed.password
            
            # 执行备份
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"数据库备份成功: {backup_path}")
                return True
            else:
                logger.error(f"数据库备份失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"数据库备份异常: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库"""
        try:
            import subprocess
            
            # 解析数据库URL
            from urllib.parse import urlparse
            parsed = urlparse(self.config.database_url)
            
            # 构建psql命令
            cmd = [
                "psql",
                "-h", parsed.hostname,
                "-p", str(parsed.port or 5432),
                "-U", parsed.username,
                "-d", parsed.path[1:],
                "-f", backup_path,
                "--no-password"
            ]
            
            # 设置密码环境变量
            env = os.environ.copy()
            env["PGPASSWORD"] = parsed.password
            
            # 执行恢复
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"数据库恢复成功: {backup_path}")
                return True
            else:
                logger.error(f"数据库恢复失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"数据库恢复异常: {e}")
            return False
    
    def get_migration_status(self) -> Dict[str, Any]:
        """获取迁移状态"""
        try:
            # 获取当前数据库版本
            with self.engine.connect() as conn:
                context = MigrationContext.configure(conn)
                current_rev = context.get_current_revision()
            
            # 获取脚本目录
            alembic_cfg = Config("alembic.ini")
            script_dir = ScriptDirectory.from_config(alembic_cfg)
            
            # 获取最新版本
            head_rev = script_dir.get_current_head()
            
            # 获取待执行的迁移
            pending_revisions = []
            if current_rev != head_rev:
                for rev in script_dir.walk_revisions(head_rev, current_rev):
                    if rev.revision != current_rev:
                        pending_revisions.append({
                            "revision": rev.revision,
                            "description": rev.doc,
                            "down_revision": rev.down_revision
                        })
            
            return {
                "current_revision": current_rev,
                "head_revision": head_rev,
                "is_up_to_date": current_rev == head_rev,
                "pending_revisions": pending_revisions
            }
            
        except Exception as e:
            logger.error(f"获取迁移状态失败: {e}")
            return {"error": str(e)}
    
    def run_migrations(self) -> bool:
        """运行数据库迁移"""
        try:
            alembic_cfg = Config("alembic.ini")
            command.upgrade(alembic_cfg, "head")
            logger.info("数据库迁移完成")
            return True
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


def init_database_manager(config: Optional[DatabaseConfig] = None) -> DatabaseManager:
    """初始化数据库管理器"""
    global _db_manager
    _db_manager = DatabaseManager(config)
    return _db_manager
