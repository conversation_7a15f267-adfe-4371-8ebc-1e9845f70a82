"""
统一API响应格式模块
提供标准化的API响应结构和工具函数
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel
from fastapi import status
from fastapi.responses import JSONResponse


class ApiResponse(BaseModel):
    """标准API响应模型"""
    
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    code: int = 200
    timestamp: Optional[str] = None
    request_id: Optional[str] = None
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            # 可以添加自定义编码器
        }


class PaginatedResponse(BaseModel):
    """分页响应模型"""
    
    items: List[Any] = []
    total: int = 0
    page: int = 1
    size: int = 10
    pages: int = 0
    has_next: bool = False
    has_prev: bool = False


class ErrorDetail(BaseModel):
    """错误详情模型"""
    
    field: Optional[str] = None
    message: str
    code: Optional[str] = None


class ErrorResponse(BaseModel):
    """错误响应模型"""
    
    success: bool = False
    message: str = "操作失败"
    code: int = 400
    errors: Optional[List[ErrorDetail]] = None
    timestamp: Optional[str] = None
    request_id: Optional[str] = None


def success_response(
    data: Any = None,
    message: str = "操作成功",
    code: int = status.HTTP_200_OK,
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    创建成功响应
    
    Args:
        data: 响应数据
        message: 响应消息
        code: HTTP状态码
        request_id: 请求ID
        
    Returns:
        JSONResponse: 标准化的成功响应
    """
    from datetime import datetime
    
    response_data = ApiResponse(
        success=True,
        message=message,
        data=data,
        code=code,
        timestamp=datetime.now().isoformat(),
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=code,
        content=response_data.dict()
    )


def error_response(
    message: str = "操作失败",
    code: int = status.HTTP_400_BAD_REQUEST,
    errors: Optional[List[Dict[str, Any]]] = None,
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    创建错误响应
    
    Args:
        message: 错误消息
        code: HTTP状态码
        errors: 详细错误信息列表
        request_id: 请求ID
        
    Returns:
        JSONResponse: 标准化的错误响应
    """
    from datetime import datetime
    
    error_details = None
    if errors:
        error_details = [
            ErrorDetail(**error) if isinstance(error, dict) else error
            for error in errors
        ]
    
    response_data = ErrorResponse(
        success=False,
        message=message,
        code=code,
        errors=error_details,
        timestamp=datetime.now().isoformat(),
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=code,
        content=response_data.dict()
    )


def paginated_response(
    items: List[Any],
    total: int,
    page: int = 1,
    size: int = 10,
    message: str = "查询成功",
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    创建分页响应
    
    Args:
        items: 数据项列表
        total: 总数量
        page: 当前页码
        size: 每页大小
        message: 响应消息
        request_id: 请求ID
        
    Returns:
        JSONResponse: 标准化的分页响应
    """
    import math
    
    pages = math.ceil(total / size) if size > 0 else 0
    has_next = page < pages
    has_prev = page > 1
    
    pagination_data = PaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages,
        has_next=has_next,
        has_prev=has_prev
    )
    
    return success_response(
        data=pagination_data.dict(),
        message=message,
        request_id=request_id
    )


def validation_error_response(
    errors: List[Dict[str, Any]],
    message: str = "数据验证失败",
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    创建数据验证错误响应
    
    Args:
        errors: 验证错误列表
        message: 错误消息
        request_id: 请求ID
        
    Returns:
        JSONResponse: 标准化的验证错误响应
    """
    return error_response(
        message=message,
        code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        errors=errors,
        request_id=request_id
    )


def not_found_response(
    message: str = "资源未找到",
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    创建404错误响应
    
    Args:
        message: 错误消息
        request_id: 请求ID
        
    Returns:
        JSONResponse: 标准化的404响应
    """
    return error_response(
        message=message,
        code=status.HTTP_404_NOT_FOUND,
        request_id=request_id
    )


def unauthorized_response(
    message: str = "未授权访问",
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    创建401错误响应
    
    Args:
        message: 错误消息
        request_id: 请求ID
        
    Returns:
        JSONResponse: 标准化的401响应
    """
    return error_response(
        message=message,
        code=status.HTTP_401_UNAUTHORIZED,
        request_id=request_id
    )


def forbidden_response(
    message: str = "禁止访问",
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    创建403错误响应
    
    Args:
        message: 错误消息
        request_id: 请求ID
        
    Returns:
        JSONResponse: 标准化的403响应
    """
    return error_response(
        message=message,
        code=status.HTTP_403_FORBIDDEN,
        request_id=request_id
    )


def server_error_response(
    message: str = "服务器内部错误",
    request_id: Optional[str] = None
) -> JSONResponse:
    """
    创建500错误响应
    
    Args:
        message: 错误消息
        request_id: 请求ID
        
    Returns:
        JSONResponse: 标准化的500响应
    """
    return error_response(
        message=message,
        code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        request_id=request_id
    )
