"""
消息队列系统
基于Celery和Redis实现异步任务处理和消息队列功能
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
import logging

from celery import Celery, Task
from celery.result import AsyncResult
from celery.signals import task_prerun, task_postrun, task_failure
import redis
from kombu import Queue

logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """任务优先级"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "PENDING"
    STARTED = "STARTED"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    RETRY = "RETRY"
    REVOKED = "REVOKED"


class MessageQueueConfig:
    """消息队列配置"""
    
    def __init__(self):
        """初始化配置"""
        self.broker_url = "redis://localhost:6379/0"
        self.result_backend = "redis://localhost:6379/0"
        self.task_serializer = "json"
        self.result_serializer = "json"
        self.accept_content = ["json"]
        self.timezone = "Asia/Shanghai"
        self.enable_utc = True
        
        # 任务路由配置
        self.task_routes = {
            'app.tasks.ai_tasks.*': {'queue': 'ai_queue'},
            'app.tasks.document_tasks.*': {'queue': 'document_queue'},
            'app.tasks.email_tasks.*': {'queue': 'email_queue'},
            'app.tasks.audit_tasks.*': {'queue': 'audit_queue'},
            'app.tasks.backup_tasks.*': {'queue': 'backup_queue'},
        }
        
        # 队列配置
        self.task_queues = [
            Queue('default', routing_key='default'),
            Queue('ai_queue', routing_key='ai_queue'),
            Queue('document_queue', routing_key='document_queue'),
            Queue('email_queue', routing_key='email_queue'),
            Queue('audit_queue', routing_key='audit_queue'),
            Queue('backup_queue', routing_key='backup_queue'),
        ]
        
        # 任务限制配置
        self.task_annotations = {
            '*': {
                'rate_limit': '100/m',
                'time_limit': 300,  # 5分钟超时
                'soft_time_limit': 240,  # 4分钟软超时
            },
            'app.tasks.ai_tasks.*': {
                'rate_limit': '10/m',
                'time_limit': 600,  # AI任务10分钟超时
            },
            'app.tasks.document_tasks.*': {
                'rate_limit': '50/m',
                'time_limit': 300,
            },
            'app.tasks.email_tasks.*': {
                'rate_limit': '200/m',
                'time_limit': 60,
            }
        }


class BaseTask(Task):
    """基础任务类"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功回调"""
        logger.info(f"任务成功完成: {task_id}, 结果: {retval}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败回调"""
        logger.error(f"任务执行失败: {task_id}, 异常: {exc}, 详情: {einfo}")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试回调"""
        logger.warning(f"任务重试: {task_id}, 异常: {exc}")


class MessageQueue:
    """消息队列管理器"""
    
    def __init__(self, config: Optional[MessageQueueConfig] = None):
        """
        初始化消息队列管理器
        
        Args:
            config: 消息队列配置
        """
        self.config = config or MessageQueueConfig()
        self.celery_app = self._create_celery_app()
        self.redis_client = redis.Redis.from_url(self.config.broker_url)
        
        # 注册信号处理器
        self._register_signals()
    
    def _create_celery_app(self) -> Celery:
        """创建Celery应用"""
        app = Celery('ai_legal_assistant')
        
        # 配置Celery
        app.conf.update(
            broker_url=self.config.broker_url,
            result_backend=self.config.result_backend,
            task_serializer=self.config.task_serializer,
            result_serializer=self.config.result_serializer,
            accept_content=self.config.accept_content,
            timezone=self.config.timezone,
            enable_utc=self.config.enable_utc,
            task_routes=self.config.task_routes,
            task_queues=self.config.task_queues,
            task_annotations=self.config.task_annotations,
            
            # 结果过期时间
            result_expires=3600,
            
            # 任务确认设置
            task_acks_late=True,
            worker_prefetch_multiplier=1,
            
            # 错误处理
            task_reject_on_worker_lost=True,
            
            # 监控设置
            worker_send_task_events=True,
            task_send_sent_event=True,
        )
        
        # 设置基础任务类
        app.Task = BaseTask
        
        return app
    
    def _register_signals(self):
        """注册信号处理器"""
        
        @task_prerun.connect
        def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
            """任务开始前的处理"""
            logger.info(f"任务开始执行: {task_id}, 任务名: {task.name}")
        
        @task_postrun.connect
        def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
            """任务完成后的处理"""
            logger.info(f"任务执行完成: {task_id}, 状态: {state}")
        
        @task_failure.connect
        def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
            """任务失败处理"""
            logger.error(f"任务执行失败: {task_id}, 异常: {exception}")
    
    def send_task(
        self,
        task_name: str,
        args: tuple = (),
        kwargs: Dict[str, Any] = None,
        queue: str = "default",
        priority: TaskPriority = TaskPriority.NORMAL,
        countdown: Optional[int] = None,
        eta: Optional[datetime] = None,
        expires: Optional[datetime] = None,
        retry: bool = True,
        retry_policy: Optional[Dict[str, Any]] = None
    ) -> AsyncResult:
        """
        发送任务到队列
        
        Args:
            task_name: 任务名称
            args: 位置参数
            kwargs: 关键字参数
            queue: 队列名称
            priority: 任务优先级
            countdown: 延迟执行秒数
            eta: 指定执行时间
            expires: 任务过期时间
            retry: 是否允许重试
            retry_policy: 重试策略
            
        Returns:
            AsyncResult: 异步结果对象
        """
        kwargs = kwargs or {}
        
        # 设置默认重试策略
        if retry and not retry_policy:
            retry_policy = {
                'max_retries': 3,
                'interval_start': 0,
                'interval_step': 0.2,
                'interval_max': 0.2,
            }
        
        # 发送任务
        result = self.celery_app.send_task(
            task_name,
            args=args,
            kwargs=kwargs,
            queue=queue,
            priority=self._get_priority_value(priority),
            countdown=countdown,
            eta=eta,
            expires=expires,
            retry=retry,
            retry_policy=retry_policy
        )
        
        logger.info(f"任务已发送: {result.id}, 任务名: {task_name}, 队列: {queue}")
        return result
    
    def _get_priority_value(self, priority: TaskPriority) -> int:
        """获取优先级数值"""
        priority_map = {
            TaskPriority.LOW: 0,
            TaskPriority.NORMAL: 5,
            TaskPriority.HIGH: 8,
            TaskPriority.CRITICAL: 10
        }
        return priority_map.get(priority, 5)
    
    def get_task_result(self, task_id: str) -> AsyncResult:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            AsyncResult: 异步结果对象
        """
        return AsyncResult(task_id, app=self.celery_app)
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        result = self.get_task_result(task_id)
        
        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result if result.successful() else None,
            "error": str(result.result) if result.failed() else None,
            "traceback": result.traceback if result.failed() else None,
            "date_done": result.date_done,
            "successful": result.successful(),
            "failed": result.failed(),
            "ready": result.ready()
        }
    
    def revoke_task(self, task_id: str, terminate: bool = False) -> bool:
        """
        撤销任务
        
        Args:
            task_id: 任务ID
            terminate: 是否强制终止
            
        Returns:
            是否成功撤销
        """
        try:
            self.celery_app.control.revoke(task_id, terminate=terminate)
            logger.info(f"任务已撤销: {task_id}")
            return True
        except Exception as e:
            logger.error(f"撤销任务失败: {task_id}, 错误: {e}")
            return False
    
    def get_queue_length(self, queue_name: str = "default") -> int:
        """
        获取队列长度
        
        Args:
            queue_name: 队列名称
            
        Returns:
            队列中的任务数量
        """
        try:
            return self.redis_client.llen(queue_name)
        except Exception as e:
            logger.error(f"获取队列长度失败: {queue_name}, 错误: {e}")
            return 0
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """
        获取活跃任务列表
        
        Returns:
            活跃任务列表
        """
        try:
            inspect = self.celery_app.control.inspect()
            active_tasks = inspect.active()
            
            if not active_tasks:
                return []
            
            # 合并所有worker的活跃任务
            all_tasks = []
            for worker, tasks in active_tasks.items():
                for task in tasks:
                    task['worker'] = worker
                    all_tasks.append(task)
            
            return all_tasks
        except Exception as e:
            logger.error(f"获取活跃任务失败: {e}")
            return []
    
    def get_worker_stats(self) -> Dict[str, Any]:
        """
        获取worker统计信息
        
        Returns:
            worker统计信息
        """
        try:
            inspect = self.celery_app.control.inspect()
            stats = inspect.stats()
            return stats or {}
        except Exception as e:
            logger.error(f"获取worker统计失败: {e}")
            return {}
    
    def purge_queue(self, queue_name: str = "default") -> int:
        """
        清空队列
        
        Args:
            queue_name: 队列名称
            
        Returns:
            清除的任务数量
        """
        try:
            purged = self.celery_app.control.purge()
            logger.info(f"队列已清空: {queue_name}, 清除任务数: {purged}")
            return purged
        except Exception as e:
            logger.error(f"清空队列失败: {queue_name}, 错误: {e}")
            return 0
    
    def schedule_periodic_task(
        self,
        task_name: str,
        schedule: Dict[str, Any],
        args: tuple = (),
        kwargs: Dict[str, Any] = None,
        options: Dict[str, Any] = None
    ):
        """
        调度周期性任务
        
        Args:
            task_name: 任务名称
            schedule: 调度配置
            args: 位置参数
            kwargs: 关键字参数
            options: 其他选项
        """
        from celery.schedules import crontab
        
        kwargs = kwargs or {}
        options = options or {}
        
        # 添加到beat调度
        self.celery_app.conf.beat_schedule[task_name] = {
            'task': task_name,
            'schedule': crontab(**schedule),
            'args': args,
            'kwargs': kwargs,
            'options': options
        }
        
        logger.info(f"周期性任务已调度: {task_name}")


# 全局消息队列实例
_message_queue: Optional[MessageQueue] = None


def get_message_queue() -> MessageQueue:
    """获取消息队列实例"""
    global _message_queue
    if _message_queue is None:
        _message_queue = MessageQueue()
    return _message_queue


def init_message_queue(config: Optional[MessageQueueConfig] = None):
    """初始化消息队列"""
    global _message_queue
    _message_queue = MessageQueue(config)
    return _message_queue


# 装饰器：将函数注册为Celery任务
def task(
    name: Optional[str] = None,
    queue: str = "default",
    priority: TaskPriority = TaskPriority.NORMAL,
    **kwargs
):
    """
    任务装饰器
    
    Args:
        name: 任务名称
        queue: 队列名称
        priority: 任务优先级
        **kwargs: 其他Celery任务选项
    """
    def decorator(func: Callable):
        mq = get_message_queue()
        
        # 设置任务选项
        task_options = {
            'name': name or f"{func.__module__}.{func.__name__}",
            'queue': queue,
            'priority': mq._get_priority_value(priority),
            **kwargs
        }
        
        # 注册为Celery任务
        return mq.celery_app.task(**task_options)(func)
    
    return decorator
