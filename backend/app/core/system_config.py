"""
系统配置管理
"""

import json
import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)


class SystemConfigManager:
    """系统配置管理器"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self._config_cache = {}
        self._cache_timestamp = None
        self.cache_ttl = 300  # 5分钟缓存
        
        # 默认配置
        self.default_configs = {
            # 系统基础配置
            "system.name": "AI法律助手",
            "system.version": "1.0.0",
            "system.description": "智能法律咨询和案例检索系统",
            "system.maintenance_mode": False,
            "system.debug_mode": False,
            
            # API配置
            "api.rate_limit.enabled": True,
            "api.rate_limit.requests_per_minute": 100,
            "api.rate_limit.burst_size": 20,
            "api.timeout.default": 30,
            "api.timeout.qa": 60,
            "api.timeout.search": 30,
            
            # QA引擎配置
            "qa.confidence_threshold": 0.6,
            "qa.max_question_length": 1000,
            "qa.max_context_length": 2000,
            "qa.enable_learning": True,
            "qa.response_cache_ttl": 3600,
            
            # 搜索配置
            "search.elasticsearch.enabled": False,
            "search.elasticsearch.timeout": 30,
            "search.max_results_per_page": 50,
            "search.default_page_size": 20,
            "search.enable_fuzzy_search": True,
            
            # 合同模板配置
            "contract.max_template_size": 100000,
            "contract.max_variables": 50,
            "contract.enable_validation": True,
            "contract.auto_save_drafts": True,
            
            # 文件上传配置
            "upload.max_file_size": 10485760,  # 10MB
            "upload.allowed_extensions": ["pdf", "doc", "docx", "txt"],
            "upload.scan_for_viruses": False,
            "upload.storage_path": "/tmp/uploads",
            
            # 安全配置
            "security.password_min_length": 8,
            "security.password_require_special": True,
            "security.session_timeout": 3600,
            "security.max_login_attempts": 5,
            "security.lockout_duration": 900,
            "security.enable_2fa": False,
            
            # 审计配置
            "audit.enabled": True,
            "audit.log_level": "INFO",
            "audit.retention_days": 90,
            "audit.log_sensitive_data": False,
            
            # 监控配置
            "monitoring.enabled": True,
            "monitoring.metrics_retention_hours": 168,  # 7天
            "monitoring.alert_email": "",
            "monitoring.health_check_interval": 60,
            
            # 邮件配置
            "email.smtp_host": "",
            "email.smtp_port": 587,
            "email.smtp_username": "",
            "email.smtp_password": "",
            "email.from_address": "",
            "email.enable_tls": True,
            
            # 缓存配置
            "cache.redis.enabled": False,
            "cache.redis.host": "localhost",
            "cache.redis.port": 6379,
            "cache.redis.db": 0,
            "cache.default_ttl": 3600,
            
            # 数据库配置
            "database.pool_size": 10,
            "database.max_overflow": 20,
            "database.pool_timeout": 30,
            "database.pool_recycle": 3600,
            
            # 日志配置
            "logging.level": "INFO",
            "logging.format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "logging.file_enabled": True,
            "logging.file_path": "/var/log/legal_assistant.log",
            "logging.max_file_size": 10485760,
            "logging.backup_count": 5
        }
    
    async def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        
        # 检查缓存
        if self._is_cache_valid():
            if key in self._config_cache:
                return self._config_cache[key]
        else:
            await self._refresh_cache()
        
        # 从缓存获取
        value = self._config_cache.get(key)
        if value is not None:
            return value
        
        # 返回默认值
        if default is not None:
            return default
        
        return self.default_configs.get(key)
    
    async def set_config(self, key: str, value: Any, user_id: uuid.UUID = None) -> bool:
        """设置配置值"""
        
        try:
            # 这里应该保存到数据库的配置表
            # 由于没有配置表模型，暂时保存到缓存
            self._config_cache[key] = value
            
            logger.info(f"配置更新: {key} = {value} (用户: {user_id})")
            return True
            
        except Exception as e:
            logger.error(f"设置配置失败: {e}")
            return False
    
    async def get_configs(self, prefix: str = None) -> Dict[str, Any]:
        """获取配置列表"""
        
        if not self._is_cache_valid():
            await self._refresh_cache()
        
        if prefix:
            return {
                k: v for k, v in self._config_cache.items()
                if k.startswith(prefix)
            }
        
        return self._config_cache.copy()
    
    async def update_configs(self, configs: Dict[str, Any], user_id: uuid.UUID = None) -> bool:
        """批量更新配置"""
        
        try:
            for key, value in configs.items():
                await self.set_config(key, value, user_id)
            
            logger.info(f"批量配置更新: {len(configs)} 项 (用户: {user_id})")
            return True
            
        except Exception as e:
            logger.error(f"批量更新配置失败: {e}")
            return False
    
    async def delete_config(self, key: str, user_id: uuid.UUID = None) -> bool:
        """删除配置"""
        
        try:
            if key in self._config_cache:
                del self._config_cache[key]
            
            logger.info(f"配置删除: {key} (用户: {user_id})")
            return True
            
        except Exception as e:
            logger.error(f"删除配置失败: {e}")
            return False
    
    async def reset_to_defaults(self, user_id: uuid.UUID = None) -> bool:
        """重置为默认配置"""
        
        try:
            self._config_cache = self.default_configs.copy()
            
            logger.info(f"配置重置为默认值 (用户: {user_id})")
            return True
            
        except Exception as e:
            logger.error(f"重置配置失败: {e}")
            return False
    
    async def export_configs(self) -> Dict[str, Any]:
        """导出配置"""
        
        if not self._is_cache_valid():
            await self._refresh_cache()
        
        return {
            "configs": self._config_cache,
            "exported_at": datetime.utcnow().isoformat(),
            "version": await self.get_config("system.version", "1.0.0")
        }
    
    async def import_configs(self, config_data: Dict[str, Any], user_id: uuid.UUID = None) -> bool:
        """导入配置"""
        
        try:
            if "configs" in config_data:
                configs = config_data["configs"]
                await self.update_configs(configs, user_id)
                
                logger.info(f"配置导入成功: {len(configs)} 项 (用户: {user_id})")
                return True
            else:
                logger.error("配置数据格式错误")
                return False
                
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        
        if self._cache_timestamp is None:
            return False
        
        cache_age = (datetime.utcnow() - self._cache_timestamp).total_seconds()
        return cache_age < self.cache_ttl
    
    async def _refresh_cache(self):
        """刷新缓存"""
        
        try:
            # 这里应该从数据库加载配置
            # 由于没有配置表，使用默认配置
            self._config_cache = self.default_configs.copy()
            self._cache_timestamp = datetime.utcnow()
            
            logger.debug("配置缓存已刷新")
            
        except Exception as e:
            logger.error(f"刷新配置缓存失败: {e}")
            # 使用默认配置作为后备
            self._config_cache = self.default_configs.copy()
            self._cache_timestamp = datetime.utcnow()
    
    async def validate_config(self, key: str, value: Any) -> tuple[bool, str]:
        """验证配置值"""
        
        # 配置验证规则
        validation_rules = {
            "api.rate_limit.requests_per_minute": lambda v: isinstance(v, int) and 1 <= v <= 10000,
            "qa.confidence_threshold": lambda v: isinstance(v, (int, float)) and 0 <= v <= 1,
            "qa.max_question_length": lambda v: isinstance(v, int) and 1 <= v <= 10000,
            "upload.max_file_size": lambda v: isinstance(v, int) and v > 0,
            "security.password_min_length": lambda v: isinstance(v, int) and 4 <= v <= 128,
            "security.session_timeout": lambda v: isinstance(v, int) and 60 <= v <= 86400,
            "audit.retention_days": lambda v: isinstance(v, int) and 1 <= v <= 3650,
            "monitoring.health_check_interval": lambda v: isinstance(v, int) and 10 <= v <= 3600,
            "email.smtp_port": lambda v: isinstance(v, int) and 1 <= v <= 65535,
            "cache.redis.port": lambda v: isinstance(v, int) and 1 <= v <= 65535,
            "database.pool_size": lambda v: isinstance(v, int) and 1 <= v <= 100,
            "logging.max_file_size": lambda v: isinstance(v, int) and v > 0,
            "logging.backup_count": lambda v: isinstance(v, int) and 0 <= v <= 100
        }
        
        # 检查是否有验证规则
        if key in validation_rules:
            validator = validation_rules[key]
            if not validator(value):
                return False, f"配置值 {value} 不符合 {key} 的验证规则"
        
        # 特殊验证
        if key.endswith("_enabled") or key.endswith("_mode"):
            if not isinstance(value, bool):
                return False, f"配置 {key} 必须是布尔值"
        
        if key.endswith("_email") and value:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, value):
                return False, f"配置 {key} 必须是有效的邮箱地址"
        
        return True, "验证通过"
    
    async def get_config_schema(self) -> Dict[str, Any]:
        """获取配置模式"""
        
        return {
            "system": {
                "name": {"type": "string", "description": "系统名称"},
                "version": {"type": "string", "description": "系统版本"},
                "maintenance_mode": {"type": "boolean", "description": "维护模式"},
                "debug_mode": {"type": "boolean", "description": "调试模式"}
            },
            "api": {
                "rate_limit": {
                    "enabled": {"type": "boolean", "description": "启用限流"},
                    "requests_per_minute": {"type": "integer", "min": 1, "max": 10000, "description": "每分钟请求数"}
                },
                "timeout": {
                    "default": {"type": "integer", "min": 1, "max": 300, "description": "默认超时时间"}
                }
            },
            "qa": {
                "confidence_threshold": {"type": "number", "min": 0, "max": 1, "description": "置信度阈值"},
                "max_question_length": {"type": "integer", "min": 1, "max": 10000, "description": "最大问题长度"}
            },
            "security": {
                "password_min_length": {"type": "integer", "min": 4, "max": 128, "description": "密码最小长度"},
                "session_timeout": {"type": "integer", "min": 60, "max": 86400, "description": "会话超时时间"}
            }
        }


# 全局配置管理器实例
_config_manager = None


async def get_config_manager(db: AsyncSession) -> SystemConfigManager:
    """获取配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = SystemConfigManager(db)
    return _config_manager
