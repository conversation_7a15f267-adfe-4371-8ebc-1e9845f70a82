"""
隐私保护工具模块
实现数据脱敏、个人信息保护和GDPR合规功能
"""

import re
import uuid
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from enum import Enum
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import Column, String, Text, DateTime, Boolean, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
import logging

from app.core.database import Base
from app.core.security import SecurityUtils, data_encryptor

logger = logging.getLogger(__name__)


class DataCategory(str, Enum):
    """数据分类"""
    PUBLIC = "public"  # 公开数据
    INTERNAL = "internal"  # 内部数据
    CONFIDENTIAL = "confidential"  # 机密数据
    RESTRICTED = "restricted"  # 限制数据
    PERSONAL = "personal"  # 个人数据
    SENSITIVE_PERSONAL = "sensitive_personal"  # 敏感个人数据


class ConsentType(str, Enum):
    """同意类型"""
    NECESSARY = "necessary"  # 必要的
    FUNCTIONAL = "functional"  # 功能性的
    ANALYTICS = "analytics"  # 分析性的
    MARKETING = "marketing"  # 营销性的


class ConsentStatus(str, Enum):
    """同意状态"""
    GRANTED = "granted"  # 已同意
    DENIED = "denied"  # 已拒绝
    WITHDRAWN = "withdrawn"  # 已撤回
    EXPIRED = "expired"  # 已过期


class UserConsent(Base):
    """用户同意记录表"""
    __tablename__ = "user_consents"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 用户信息
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 同意信息
    consent_type = Column(
        SQLEnum(ConsentType),
        nullable=False,
        index=True,
        comment="同意类型"
    )
    status = Column(
        SQLEnum(ConsentStatus),
        nullable=False,
        index=True,
        comment="同意状态"
    )
    
    # 详细信息
    purpose = Column(String(200), comment="数据使用目的")
    data_categories = Column(JSONB, default=list, comment="涉及的数据类别")
    legal_basis = Column(String(100), comment="法律依据")
    
    # 时间信息
    granted_at = Column(DateTime(timezone=True), comment="同意时间")
    withdrawn_at = Column(DateTime(timezone=True), comment="撤回时间")
    expires_at = Column(DateTime(timezone=True), comment="过期时间")
    
    # 元数据
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    consent_version = Column(String(20), comment="同意版本")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    def __repr__(self):
        return f"<UserConsent(id={self.id}, user_id={self.user_id}, type={self.consent_type})>"


class DataRetentionPolicy(Base):
    """数据保留策略表"""
    __tablename__ = "data_retention_policies"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 策略信息
    name = Column(String(100), nullable=False, comment="策略名称")
    description = Column(Text, comment="策略描述")
    data_category = Column(
        SQLEnum(DataCategory),
        nullable=False,
        index=True,
        comment="数据类别"
    )
    
    # 保留规则
    retention_period_days = Column(String, nullable=False, comment="保留期限（天）")
    auto_delete = Column(Boolean, default=False, comment="是否自动删除")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )


class PrivacyManager:
    """隐私管理器"""
    
    def __init__(self, db: AsyncSession = None):
        self.db = db
    
    def anonymize_data(self, data: Dict[str, Any], fields: List[str]) -> Dict[str, Any]:
        """匿名化数据"""
        anonymized = data.copy()
        
        for field in fields:
            if field in anonymized:
                if field.lower() in ['email', 'email_address']:
                    anonymized[field] = self._anonymize_email(anonymized[field])
                elif field.lower() in ['phone', 'phone_number', 'mobile']:
                    anonymized[field] = self._anonymize_phone(anonymized[field])
                elif field.lower() in ['name', 'full_name', 'username']:
                    anonymized[field] = self._anonymize_name(anonymized[field])
                elif field.lower() in ['id_card', 'passport', 'license']:
                    anonymized[field] = self._anonymize_id(anonymized[field])
                else:
                    anonymized[field] = self._anonymize_generic(anonymized[field])
        
        return anonymized
    
    def _anonymize_email(self, email: str) -> str:
        """匿名化邮箱"""
        if not email or '@' not in email:
            return "<EMAIL>"
        
        local, domain = email.split('@', 1)
        anonymized_local = f"user_{SecurityUtils.generate_secure_token(8)}"
        return f"{anonymized_local}@{domain}"
    
    def _anonymize_phone(self, phone: str) -> str:
        """匿名化手机号"""
        if not phone:
            return "***********"
        
        # 保留前3位和后4位
        if len(phone) > 7:
            return phone[:3] + "*" * (len(phone) - 7) + phone[-4:]
        else:
            return "*" * len(phone)
    
    def _anonymize_name(self, name: str) -> str:
        """匿名化姓名"""
        if not name:
            return "匿名用户"
        
        return f"用户{SecurityUtils.generate_secure_token(4)}"
    
    def _anonymize_id(self, id_value: str) -> str:
        """匿名化身份证号等"""
        if not id_value:
            return "***"
        
        if len(id_value) > 8:
            return id_value[:4] + "*" * (len(id_value) - 8) + id_value[-4:]
        else:
            return "*" * len(id_value)
    
    def _anonymize_generic(self, value: Any) -> str:
        """通用匿名化"""
        if isinstance(value, str):
            return SecurityUtils.mask_sensitive_data(value)
        else:
            return "[ANONYMIZED]"
    
    def pseudonymize_data(self, data: Dict[str, Any], fields: List[str]) -> Dict[str, Any]:
        """假名化数据"""
        pseudonymized = data.copy()
        
        for field in fields:
            if field in pseudonymized and pseudonymized[field]:
                # 使用一致性哈希来确保相同输入产生相同输出
                original_value = str(pseudonymized[field])
                pseudonym = SecurityUtils.hash_data(original_value)
                pseudonymized[field] = pseudonym.split(':')[1][:16]  # 取哈希值的前16位
        
        return pseudonymized
    
    def detect_personal_data(self, text: str) -> Dict[str, List[str]]:
        """检测文本中的个人数据"""
        patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'1[3-9]\d{9}',
            'id_card': r'\d{17}[\dXx]',
            'credit_card': r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',
            'ip_address': r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'
        }
        
        detected = {}
        for data_type, pattern in patterns.items():
            matches = re.findall(pattern, text)
            if matches:
                detected[data_type] = matches
        
        return detected
    
    def redact_personal_data(self, text: str) -> str:
        """编辑文本中的个人数据"""
        patterns = {
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b': '[EMAIL]',
            r'1[3-9]\d{9}': '[PHONE]',
            r'\d{17}[\dXx]': '[ID_CARD]',
            r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b': '[CREDIT_CARD]',
            r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b': '[IP_ADDRESS]'
        }
        
        redacted_text = text
        for pattern, replacement in patterns.items():
            redacted_text = re.sub(pattern, replacement, redacted_text)
        
        return redacted_text
    
    async def record_consent(
        self,
        user_id: uuid.UUID,
        consent_type: ConsentType,
        status: ConsentStatus,
        purpose: str,
        data_categories: List[str] = None,
        legal_basis: str = None,
        ip_address: str = None,
        user_agent: str = None,
        expires_days: int = None
    ) -> UserConsent:
        """记录用户同意"""
        
        expires_at = None
        if expires_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_days)
        
        granted_at = datetime.utcnow() if status == ConsentStatus.GRANTED else None
        withdrawn_at = datetime.utcnow() if status == ConsentStatus.WITHDRAWN else None
        
        consent = UserConsent(
            user_id=user_id,
            consent_type=consent_type,
            status=status,
            purpose=purpose,
            data_categories=data_categories or [],
            legal_basis=legal_basis,
            granted_at=granted_at,
            withdrawn_at=withdrawn_at,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent,
            consent_version="1.0"
        )
        
        if self.db:
            self.db.add(consent)
            await self.db.commit()
            await self.db.refresh(consent)
        
        logger.info(f"记录用户同意: user_id={user_id}, type={consent_type}, status={status}")
        return consent
    
    async def check_consent(
        self,
        user_id: uuid.UUID,
        consent_type: ConsentType
    ) -> bool:
        """检查用户同意状态"""
        if not self.db:
            return False
        
        from sqlalchemy import select, and_
        
        stmt = select(UserConsent).where(
            and_(
                UserConsent.user_id == user_id,
                UserConsent.consent_type == consent_type,
                UserConsent.status == ConsentStatus.GRANTED,
                UserConsent.expires_at > datetime.utcnow()
            )
        ).order_by(UserConsent.created_at.desc())
        
        result = await self.db.execute(stmt)
        consent = result.scalar_one_or_none()
        
        return consent is not None
    
    def classify_data_sensitivity(self, data: Dict[str, Any]) -> DataCategory:
        """分类数据敏感性"""
        sensitive_fields = {
            'password', 'token', 'secret', 'key', 'credential',
            'id_card', 'passport', 'social_security', 'credit_card'
        }
        
        personal_fields = {
            'email', 'phone', 'address', 'name', 'birth_date',
            'gender', 'nationality', 'occupation'
        }
        
        # 检查是否包含敏感个人数据
        for key in data.keys():
            key_lower = key.lower()
            if any(sensitive in key_lower for sensitive in sensitive_fields):
                return DataCategory.SENSITIVE_PERSONAL
        
        # 检查是否包含个人数据
        for key in data.keys():
            key_lower = key.lower()
            if any(personal in key_lower for personal in personal_fields):
                return DataCategory.PERSONAL
        
        return DataCategory.INTERNAL


# 全局隐私管理器
privacy_manager = PrivacyManager()


def get_privacy_manager(db: AsyncSession = None) -> PrivacyManager:
    """获取隐私管理器"""
    if db:
        return PrivacyManager(db)
    return privacy_manager
