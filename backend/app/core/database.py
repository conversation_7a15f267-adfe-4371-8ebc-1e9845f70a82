"""
数据库连接和配置模块
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import Static<PERSON>ool
import redis.asyncio as redis
# from motor.motor_asyncio import AsyncIOMotorClient
# from elasticsearch import AsyncElasticsearch
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy基础类
Base = declarative_base()

# 元数据
metadata = MetaData()

# 数据库引擎
engine = None
async_engine = None

# 会话工厂
SessionLocal = None
AsyncSessionLocal = None

# Redis连接
redis_client: Optional[redis.Redis] = None

# MongoDB连接
mongodb_client = None
mongodb_database = None

# Elasticsearch连接
elasticsearch_client = None


def create_database_engine():
    """创建数据库引擎"""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    # 同步引擎 - 转换为同步URL
    sync_database_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
    engine = create_engine(
        sync_database_url,
        pool_size=settings.DB_POOL_SIZE,
        max_overflow=settings.DB_MAX_OVERFLOW,
        pool_timeout=settings.DB_POOL_TIMEOUT,
        pool_recycle=settings.DB_POOL_RECYCLE,
        echo=settings.DEBUG,
    )
    
    # 异步引擎 - 直接使用配置中的异步URL
    async_engine = create_async_engine(
        settings.DATABASE_URL,
        pool_size=settings.DB_POOL_SIZE,
        max_overflow=settings.DB_MAX_OVERFLOW,
        pool_timeout=settings.DB_POOL_TIMEOUT,
        pool_recycle=settings.DB_POOL_RECYCLE,
        echo=settings.DEBUG,
    )
    
    # 会话工厂
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine,
    )
    
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    
    logger.info("✅ 数据库引擎创建完成")


async def create_redis_connection():
    """创建Redis连接"""
    global redis_client

    try:
        # Redis连接池配置
        redis_client = redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True,
            max_connections=getattr(settings, 'REDIS_MAX_CONNECTIONS', 50),
            retry_on_timeout=True,
            socket_timeout=getattr(settings, 'REDIS_SOCKET_TIMEOUT', 5),
            socket_connect_timeout=getattr(settings, 'REDIS_CONNECT_TIMEOUT', 5),
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=getattr(settings, 'REDIS_HEALTH_CHECK_INTERVAL', 30),
        )

        # 测试连接
        await redis_client.ping()
        logger.info(f"✅ Redis连接创建完成 - 最大连接数: {getattr(settings, 'REDIS_MAX_CONNECTIONS', 50)}")

    except Exception as e:
        logger.error(f"❌ Redis连接失败: {e}")
        redis_client = None


async def create_mongodb_connection():
    """创建MongoDB连接"""
    logger.info("MongoDB连接已跳过（未安装motor）")
    pass


async def create_elasticsearch_connection():
    """创建Elasticsearch连接"""
    logger.info("Elasticsearch连接已跳过（未安装elasticsearch）")
    pass


async def init_db():
    """初始化数据库连接"""
    logger.info("🔄 初始化数据库连接...")
    
    # 创建数据库引擎
    create_database_engine()
    
    # 创建异步连接
    await asyncio.gather(
        create_redis_connection(),
        create_mongodb_connection(),
        create_elasticsearch_connection(),
        return_exceptions=True,
    )
    
    logger.info("✅ 数据库连接初始化完成")


async def close_db():
    """关闭数据库连接"""
    logger.info("🔄 关闭数据库连接...")
    
    # 关闭Redis连接
    if redis_client:
        await redis_client.close()
        logger.info("✅ Redis连接已关闭")
    
    # 关闭MongoDB连接
    if mongodb_client:
        mongodb_client.close()
        logger.info("✅ MongoDB连接已关闭")
    
    # 关闭Elasticsearch连接
    if elasticsearch_client:
        await elasticsearch_client.close()
        logger.info("✅ Elasticsearch连接已关闭")
    
    # 关闭SQLAlchemy引擎
    if async_engine:
        await async_engine.dispose()
        logger.info("✅ SQLAlchemy异步引擎已关闭")
    
    if engine:
        engine.dispose()
        logger.info("✅ SQLAlchemy引擎已关闭")


# 依赖注入函数
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_redis() -> redis.Redis:
    """获取Redis客户端"""
    if redis_client is None:
        raise RuntimeError("Redis连接未初始化")
    return redis_client


def get_mongodb():
    """获取MongoDB数据库"""
    if mongodb_database is None:
        raise RuntimeError("MongoDB连接未初始化")
    return mongodb_database


async def get_elasticsearch():
    """获取Elasticsearch客户端"""
    if elasticsearch_client is None:
        raise RuntimeError("Elasticsearch连接未初始化")
    return elasticsearch_client


# 数据库健康检查
async def check_database_health() -> dict:
    """检查数据库健康状态"""
    health_status = {
        "postgresql": False,
        "redis": False,
        "mongodb": False,
        "elasticsearch": False,
    }
    
    # 检查PostgreSQL
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
            health_status["postgresql"] = True
    except Exception as e:
        logger.error(f"PostgreSQL健康检查失败: {e}")
    
    # 检查Redis
    try:
        if redis_client:
            await redis_client.ping()
            health_status["redis"] = True
    except Exception as e:
        logger.error(f"Redis健康检查失败: {e}")
    
    # 检查MongoDB
    try:
        if mongodb_client:
            await mongodb_client.admin.command('ping')
            health_status["mongodb"] = True
    except Exception as e:
        logger.error(f"MongoDB健康检查失败: {e}")
    
    # 检查Elasticsearch
    try:
        if elasticsearch_client:
            await elasticsearch_client.ping()
            health_status["elasticsearch"] = True
    except Exception as e:
        logger.error(f"Elasticsearch健康检查失败: {e}")
    
    return health_status


async def get_connection_pool_stats() -> dict:
    """获取连接池统计信息"""
    stats = {
        "postgresql": {},
        "redis": {},
        "mongodb": {},
        "elasticsearch": {},
    }

    # PostgreSQL连接池统计
    try:
        if async_engine:
            pool = async_engine.pool
            stats["postgresql"] = {
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid(),
                "status": "healthy" if pool.checkedin() > 0 else "warning"
            }
    except Exception as e:
        stats["postgresql"]["error"] = str(e)

    # Redis连接池统计
    try:
        if redis_client and hasattr(redis_client, 'connection_pool'):
            pool = redis_client.connection_pool
            stats["redis"] = {
                "max_connections": getattr(pool, 'max_connections', 'unknown'),
                "created_connections": getattr(pool, 'created_connections', 'unknown'),
                "available_connections": len(getattr(pool, '_available_connections', [])),
                "in_use_connections": len(getattr(pool, '_in_use_connections', [])),
                "status": "healthy"
            }
    except Exception as e:
        stats["redis"]["error"] = str(e)

    # MongoDB连接池统计
    try:
        if mongodb_client:
            # MongoDB的连接池信息较难获取，提供基本状态
            stats["mongodb"] = {
                "max_pool_size": getattr(settings, 'MONGODB_MAX_POOL_SIZE', 100),
                "min_pool_size": getattr(settings, 'MONGODB_MIN_POOL_SIZE', 10),
                "status": "healthy" if mongodb_client else "error"
            }
    except Exception as e:
        stats["mongodb"]["error"] = str(e)

    # Elasticsearch连接统计
    try:
        if elasticsearch_client:
            stats["elasticsearch"] = {
                "max_connections": getattr(settings, 'ELASTICSEARCH_MAX_CONNECTIONS', 25),
                "status": "healthy"
            }
    except Exception as e:
        stats["elasticsearch"]["error"] = str(e)

    return stats


async def optimize_connection_pools():
    """优化连接池配置"""
    logger.info("🔄 开始优化数据库连接池配置...")

    # PostgreSQL连接池优化
    if async_engine:
        pool = async_engine.pool
        current_size = pool.size()
        checked_out = pool.checkedout()

        # 如果连接使用率过高，记录警告
        if checked_out / current_size > 0.8:
            logger.warning(f"PostgreSQL连接池使用率过高: {checked_out}/{current_size} ({checked_out/current_size*100:.1f}%)")

        logger.info(f"PostgreSQL连接池状态: 大小={current_size}, 已用={checked_out}, 可用={pool.checkedin()}")

    # Redis连接池优化
    if redis_client:
        try:
            # 清理空闲连接
            await redis_client.connection_pool.disconnect()
            logger.info("Redis连接池已优化")
        except Exception as e:
            logger.warning(f"Redis连接池优化失败: {e}")

    logger.info("✅ 数据库连接池优化完成")


# 数据库事务装饰器
class DatabaseTransaction:
    """数据库事务上下文管理器"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def __aenter__(self):
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.session.rollback()
        else:
            await self.session.commit()


async def get_db_transaction() -> AsyncGenerator[DatabaseTransaction, None]:
    """获取数据库事务"""
    async with AsyncSessionLocal() as session:
        try:
            yield DatabaseTransaction(session)
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
