"""
审计日志系统
记录用户操作、系统事件和安全相关活动
"""

import uuid
import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import Column, String, Text, DateTime, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
import logging

from app.core.database import Base, get_db
from app.core.security import mask_email, mask_phone, SecurityUtils

logger = logging.getLogger(__name__)


class AuditEventType(str, Enum):
    """审计事件类型"""
    # 用户认证相关
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_REGISTER = "user_register"
    PASSWORD_CHANGE = "password_change"
    PASSWORD_RESET = "password_reset"
    
    # 用户操作相关
    USER_CREATE = "user_create"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"
    USER_VIEW = "user_view"
    
    # 数据操作相关
    DATA_CREATE = "data_create"
    DATA_READ = "data_read"
    DATA_UPDATE = "data_update"
    DATA_DELETE = "data_delete"
    DATA_EXPORT = "data_export"
    DATA_IMPORT = "data_import"
    
    # 系统操作相关
    SYSTEM_CONFIG_CHANGE = "system_config_change"
    SYSTEM_BACKUP = "system_backup"
    SYSTEM_RESTORE = "system_restore"
    
    # 安全相关
    SECURITY_VIOLATION = "security_violation"
    ACCESS_DENIED = "access_denied"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    
    # API相关
    API_CALL = "api_call"
    API_ERROR = "api_error"
    API_RATE_LIMIT = "api_rate_limit"


class AuditLevel(str, Enum):
    """审计级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AuditLog(Base):
    """审计日志表"""
    __tablename__ = "audit_logs"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 事件信息
    event_type = Column(
        SQLEnum(AuditEventType),
        nullable=False,
        index=True,
        comment="事件类型"
    )
    level = Column(
        SQLEnum(AuditLevel),
        default=AuditLevel.INFO,
        nullable=False,
        index=True,
        comment="审计级别"
    )
    
    # 用户信息
    user_id = Column(UUID(as_uuid=True), index=True, comment="用户ID")
    username = Column(String(100), index=True, comment="用户名")
    user_ip = Column(String(45), index=True, comment="用户IP地址")
    user_agent = Column(Text, comment="用户代理")
    
    # 操作信息
    resource_type = Column(String(100), index=True, comment="资源类型")
    resource_id = Column(String(100), index=True, comment="资源ID")
    action = Column(String(100), index=True, comment="操作动作")
    
    # 详细信息
    message = Column(Text, comment="事件描述")
    details = Column(JSONB, default=dict, comment="详细信息")
    
    # 请求信息
    request_id = Column(String(100), index=True, comment="请求ID")
    session_id = Column(String(100), index=True, comment="会话ID")
    
    # 结果信息
    success = Column(String, default=True, comment="操作是否成功")
    error_code = Column(String(50), comment="错误代码")
    error_message = Column(Text, comment="错误信息")
    
    # 时间戳
    timestamp = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="事件时间"
    )
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, event_type={self.event_type}, user_id={self.user_id})>"


class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self, db: AsyncSession = None):
        self.db = db
    
    async def log_event(
        self,
        event_type: AuditEventType,
        message: str,
        user_id: Optional[uuid.UUID] = None,
        username: Optional[str] = None,
        user_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        action: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None,
        session_id: Optional[str] = None,
        success: bool = True,
        error_code: Optional[str] = None,
        error_message: Optional[str] = None,
        level: AuditLevel = AuditLevel.INFO
    ) -> AuditLog:
        """记录审计事件"""
        
        # 清理敏感信息
        if details:
            details = self._sanitize_details(details)
        
        audit_log = AuditLog(
            event_type=event_type,
            level=level,
            user_id=user_id,
            username=username,
            user_ip=user_ip,
            user_agent=user_agent,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            message=message,
            details=details or {},
            request_id=request_id,
            session_id=session_id,
            success=success,
            error_code=error_code,
            error_message=error_message
        )
        
        if self.db:
            self.db.add(audit_log)
            await self.db.commit()
            await self.db.refresh(audit_log)
        
        # 同时记录到应用日志
        log_level = getattr(logger, level.value.lower())
        log_level(f"审计事件: {event_type.value} - {message}")
        
        return audit_log
    
    def _sanitize_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """清理详细信息中的敏感数据"""
        sanitized = {}
        sensitive_fields = {
            'password', 'token', 'secret', 'key', 'credential',
            'authorization', 'cookie', 'session'
        }
        
        for key, value in details.items():
            key_lower = key.lower()
            
            # 检查是否为敏感字段
            if any(sensitive in key_lower for sensitive in sensitive_fields):
                sanitized[key] = "[REDACTED]"
            elif key_lower in ['email', 'email_address']:
                sanitized[key] = mask_email(str(value)) if value else value
            elif key_lower in ['phone', 'phone_number', 'mobile']:
                sanitized[key] = mask_phone(str(value)) if value else value
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_details(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self._sanitize_details(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                sanitized[key] = value
        
        return sanitized
    
    async def log_user_login(
        self,
        user_id: uuid.UUID,
        username: str,
        user_ip: str,
        user_agent: str = None,
        success: bool = True,
        error_message: str = None
    ):
        """记录用户登录事件"""
        message = f"用户 {username} 登录{'成功' if success else '失败'}"
        level = AuditLevel.INFO if success else AuditLevel.WARNING
        
        await self.log_event(
            event_type=AuditEventType.USER_LOGIN,
            message=message,
            user_id=user_id,
            username=username,
            user_ip=user_ip,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
            level=level
        )
    
    async def log_user_logout(
        self,
        user_id: uuid.UUID,
        username: str,
        user_ip: str
    ):
        """记录用户登出事件"""
        await self.log_event(
            event_type=AuditEventType.USER_LOGOUT,
            message=f"用户 {username} 登出",
            user_id=user_id,
            username=username,
            user_ip=user_ip
        )
    
    async def log_data_operation(
        self,
        operation: str,
        resource_type: str,
        resource_id: str,
        user_id: uuid.UUID,
        username: str,
        details: Dict[str, Any] = None,
        success: bool = True,
        error_message: str = None
    ):
        """记录数据操作事件"""
        event_type_map = {
            "create": AuditEventType.DATA_CREATE,
            "read": AuditEventType.DATA_READ,
            "update": AuditEventType.DATA_UPDATE,
            "delete": AuditEventType.DATA_DELETE,
        }
        
        event_type = event_type_map.get(operation.lower(), AuditEventType.DATA_READ)
        message = f"用户 {username} {operation} {resource_type} {resource_id}"
        level = AuditLevel.INFO if success else AuditLevel.ERROR
        
        await self.log_event(
            event_type=event_type,
            message=message,
            user_id=user_id,
            username=username,
            resource_type=resource_type,
            resource_id=resource_id,
            action=operation,
            details=details,
            success=success,
            error_message=error_message,
            level=level
        )
    
    async def log_security_event(
        self,
        event_type: AuditEventType,
        message: str,
        user_ip: str,
        details: Dict[str, Any] = None,
        level: AuditLevel = AuditLevel.WARNING
    ):
        """记录安全事件"""
        await self.log_event(
            event_type=event_type,
            message=message,
            user_ip=user_ip,
            details=details,
            level=level,
            success=False
        )


# 全局审计日志记录器
audit_logger = AuditLogger()


async def get_audit_logger(db: AsyncSession = None) -> AuditLogger:
    """获取审计日志记录器"""
    if db:
        return AuditLogger(db)
    return audit_logger
