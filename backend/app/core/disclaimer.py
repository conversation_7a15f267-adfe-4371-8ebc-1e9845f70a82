"""
免责声明和用户协议系统
提供AI回答免责声明、用户协议和隐私政策管理
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
import logging

from app.core.database import Base

logger = logging.getLogger(__name__)


class DisclaimerType(str, Enum):
    """免责声明类型枚举"""
    AI_RESPONSE = "ai_response"  # AI回答免责
    LEGAL_ADVICE = "legal_advice"  # 法律建议免责
    DATA_ACCURACY = "data_accuracy"  # 数据准确性免责
    SERVICE_AVAILABILITY = "service_availability"  # 服务可用性免责
    THIRD_PARTY_CONTENT = "third_party_content"  # 第三方内容免责


class AgreementType(str, Enum):
    """协议类型枚举"""
    TERMS_OF_SERVICE = "terms_of_service"  # 服务条款
    PRIVACY_POLICY = "privacy_policy"  # 隐私政策
    USER_AGREEMENT = "user_agreement"  # 用户协议
    DATA_PROCESSING = "data_processing"  # 数据处理协议
    COOKIE_POLICY = "cookie_policy"  # Cookie政策


class DisclaimerTemplate(Base):
    """
    免责声明模板表
    存储各种类型的免责声明模板
    """
    __tablename__ = "disclaimer_templates"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 模板信息
    template_name = Column(String(100), nullable=False, comment="模板名称")
    disclaimer_type = Column(String(50), nullable=False, index=True, comment="免责声明类型")
    version = Column(String(20), nullable=False, comment="版本号")
    
    # 内容
    title = Column(String(200), nullable=False, comment="标题")
    content = Column(Text, nullable=False, comment="内容")
    summary = Column(Text, nullable=True, comment="摘要")
    
    # 显示设置
    is_mandatory = Column(Boolean, default=True, comment="是否强制显示")
    display_order = Column(Integer, default=0, comment="显示顺序")
    auto_accept_seconds = Column(Integer, nullable=True, comment="自动接受秒数")
    
    # 状态
    is_active = Column(Boolean, default=True, index=True, comment="是否激活")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    def __repr__(self):
        return f"<DisclaimerTemplate(id={self.id}, name={self.template_name})>"


class UserAgreement(Base):
    """
    用户协议表
    存储用户协议和政策文档
    """
    __tablename__ = "user_agreements"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 协议信息
    agreement_name = Column(String(100), nullable=False, comment="协议名称")
    agreement_type = Column(String(50), nullable=False, index=True, comment="协议类型")
    version = Column(String(20), nullable=False, comment="版本号")
    
    # 内容
    title = Column(String(200), nullable=False, comment="标题")
    content = Column(Text, nullable=False, comment="内容")
    summary = Column(Text, nullable=True, comment="摘要")
    
    # 生效信息
    effective_date = Column(DateTime(timezone=True), nullable=False, comment="生效日期")
    expiry_date = Column(DateTime(timezone=True), nullable=True, comment="失效日期")
    
    # 状态
    is_active = Column(Boolean, default=True, index=True, comment="是否激活")
    requires_acceptance = Column(Boolean, default=True, comment="是否需要用户接受")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    def __repr__(self):
        return f"<UserAgreement(id={self.id}, name={self.agreement_name})>"


class DisclaimerManager:
    """
    免责声明管理器
    提供免责声明和用户协议的管理功能
    """
    
    def __init__(self, db_session: Session):
        """
        初始化免责声明管理器
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    def create_disclaimer_template(
        self,
        template_name: str,
        disclaimer_type: DisclaimerType,
        title: str,
        content: str,
        version: str = "1.0",
        summary: Optional[str] = None,
        is_mandatory: bool = True,
        display_order: int = 0,
        auto_accept_seconds: Optional[int] = None
    ) -> DisclaimerTemplate:
        """
        创建免责声明模板
        
        Args:
            template_name: 模板名称
            disclaimer_type: 免责声明类型
            title: 标题
            content: 内容
            version: 版本号
            summary: 摘要
            is_mandatory: 是否强制显示
            display_order: 显示顺序
            auto_accept_seconds: 自动接受秒数
            
        Returns:
            免责声明模板
        """
        try:
            template = DisclaimerTemplate(
                template_name=template_name,
                disclaimer_type=disclaimer_type.value,
                version=version,
                title=title,
                content=content,
                summary=summary,
                is_mandatory=is_mandatory,
                display_order=display_order,
                auto_accept_seconds=auto_accept_seconds
            )
            
            self.db_session.add(template)
            self.db_session.commit()
            
            logger.info(f"免责声明模板已创建: {template_name}")
            return template
            
        except Exception as e:
            logger.error(f"创建免责声明模板失败: {e}")
            self.db_session.rollback()
            raise
    
    def create_user_agreement(
        self,
        agreement_name: str,
        agreement_type: AgreementType,
        title: str,
        content: str,
        version: str = "1.0",
        summary: Optional[str] = None,
        effective_date: Optional[datetime] = None,
        expiry_date: Optional[datetime] = None,
        requires_acceptance: bool = True
    ) -> UserAgreement:
        """
        创建用户协议
        
        Args:
            agreement_name: 协议名称
            agreement_type: 协议类型
            title: 标题
            content: 内容
            version: 版本号
            summary: 摘要
            effective_date: 生效日期
            expiry_date: 失效日期
            requires_acceptance: 是否需要用户接受
            
        Returns:
            用户协议
        """
        try:
            if effective_date is None:
                effective_date = datetime.utcnow()
            
            agreement = UserAgreement(
                agreement_name=agreement_name,
                agreement_type=agreement_type.value,
                version=version,
                title=title,
                content=content,
                summary=summary,
                effective_date=effective_date,
                expiry_date=expiry_date,
                requires_acceptance=requires_acceptance
            )
            
            self.db_session.add(agreement)
            self.db_session.commit()
            
            logger.info(f"用户协议已创建: {agreement_name}")
            return agreement
            
        except Exception as e:
            logger.error(f"创建用户协议失败: {e}")
            self.db_session.rollback()
            raise
    
    def get_active_disclaimers(
        self,
        disclaimer_type: Optional[DisclaimerType] = None
    ) -> List[DisclaimerTemplate]:
        """
        获取激活的免责声明
        
        Args:
            disclaimer_type: 免责声明类型过滤
            
        Returns:
            免责声明列表
        """
        query = (
            self.db_session.query(DisclaimerTemplate)
            .filter(DisclaimerTemplate.is_active == True)
        )
        
        if disclaimer_type:
            query = query.filter(DisclaimerTemplate.disclaimer_type == disclaimer_type.value)
        
        return query.order_by(DisclaimerTemplate.display_order).all()
    
    def get_active_agreements(
        self,
        agreement_type: Optional[AgreementType] = None
    ) -> List[UserAgreement]:
        """
        获取激活的用户协议
        
        Args:
            agreement_type: 协议类型过滤
            
        Returns:
            用户协议列表
        """
        current_time = datetime.utcnow()
        
        query = (
            self.db_session.query(UserAgreement)
            .filter(
                UserAgreement.is_active == True,
                UserAgreement.effective_date <= current_time
            )
        )
        
        # 过滤未过期的协议
        query = query.filter(
            (UserAgreement.expiry_date.is_(None)) |
            (UserAgreement.expiry_date > current_time)
        )
        
        if agreement_type:
            query = query.filter(UserAgreement.agreement_type == agreement_type.value)
        
        return query.all()
    
    def get_ai_response_disclaimer(self) -> Optional[str]:
        """
        获取AI回答免责声明
        
        Returns:
            免责声明文本
        """
        disclaimers = self.get_active_disclaimers(DisclaimerType.AI_RESPONSE)
        if disclaimers:
            return disclaimers[0].content
        return None
    
    def get_legal_risk_warning(self) -> str:
        """
        获取法律风险提示
        
        Returns:
            法律风险提示文本
        """
        default_warning = (
            "⚠️ 重要提示：本AI助手提供的信息仅供参考，不构成正式的法律建议。"
            "具体法律问题请咨询专业律师。我们不对因使用本服务而产生的任何后果承担责任。"
        )
        
        disclaimers = self.get_active_disclaimers(DisclaimerType.LEGAL_ADVICE)
        if disclaimers:
            return disclaimers[0].content
        
        return default_warning


def get_disclaimer_manager(db_session: Session) -> DisclaimerManager:
    """
    获取免责声明管理器实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        免责声明管理器
    """
    return DisclaimerManager(db_session)


# 默认免责声明模板
DEFAULT_DISCLAIMERS = [
    {
        "template_name": "AI回答免责声明",
        "disclaimer_type": DisclaimerType.AI_RESPONSE,
        "title": "AI助手免责声明",
        "content": (
            "本AI法律助手基于人工智能技术提供信息服务，具有以下重要限制：\n\n"
            "1. 信息仅供参考：AI提供的所有信息、建议和分析仅供参考，不构成正式的法律建议。\n"
            "2. 不替代专业咨询：对于具体的法律问题，请务必咨询具有执业资格的专业律师。\n"
            "3. 准确性限制：虽然我们努力确保信息的准确性，但AI可能存在理解偏差或信息过时的情况。\n"
            "4. 责任限制：我们不对因使用本服务而产生的任何直接或间接损失承担责任。\n"
            "5. 持续改进：我们会不断改进AI的准确性和可靠性，但无法保证100%的准确性。\n\n"
            "使用本服务即表示您理解并接受上述限制。"
        ),
        "summary": "AI助手提供的信息仅供参考，不构成法律建议，具体问题请咨询专业律师。",
        "is_mandatory": True,
        "display_order": 1
    },
    {
        "template_name": "法律建议免责声明",
        "disclaimer_type": DisclaimerType.LEGAL_ADVICE,
        "title": "法律建议免责声明",
        "content": (
            "⚠️ 重要法律声明：\n\n"
            "本平台提供的所有内容，包括但不限于法律条文解释、案例分析、合同模板等，"
            "均不构成正式的法律建议或法律服务。\n\n"
            "法律事务具有高度的专业性和复杂性，每个案件都有其独特性。"
            "本平台的AI助手无法替代专业律师的判断和建议。\n\n"
            "对于任何具体的法律问题，我们强烈建议您：\n"
            "• 咨询具有执业资格的专业律师\n"
            "• 获取针对您具体情况的专业法律意见\n"
            "• 在做出重要法律决定前进行充分的专业咨询\n\n"
            "本平台及其运营方不对用户因使用本服务而产生的任何法律后果承担责任。"
        ),
        "summary": "本平台内容不构成法律建议，具体法律问题请咨询专业律师。",
        "is_mandatory": True,
        "display_order": 2
    }
]

# 默认用户协议
DEFAULT_AGREEMENTS = [
    {
        "agreement_name": "用户服务协议",
        "agreement_type": AgreementType.TERMS_OF_SERVICE,
        "title": "AI法律助手用户服务协议",
        "content": (
            "欢迎使用AI法律助手服务！\n\n"
            "1. 服务说明\n"
            "本服务为用户提供AI驱动的法律信息查询和分析功能，包括但不限于：\n"
            "• 法律条文查询和解释\n"
            "• 合同条款分析\n"
            "• 法律文书模板提供\n"
            "• 案例检索和分析\n\n"
            "2. 用户义务\n"
            "用户在使用本服务时应当：\n"
            "• 遵守相关法律法规\n"
            "• 不得利用本服务从事违法活动\n"
            "• 保护个人账户安全\n"
            "• 尊重他人隐私和知识产权\n\n"
            "3. 服务限制\n"
            "• 本服务不提供正式的法律建议\n"
            "• 不保证服务的持续可用性\n"
            "• 保留随时修改或终止服务的权利\n\n"
            "4. 免责条款\n"
            "用户理解并同意，本服务提供方不对因使用本服务而产生的任何损失承担责任。\n\n"
            "本协议的解释和争议解决适用中华人民共和国法律。"
        ),
        "summary": "规定用户使用AI法律助手服务的权利义务和服务限制。",
        "requires_acceptance": True
    },
    {
        "agreement_name": "隐私政策",
        "agreement_type": AgreementType.PRIVACY_POLICY,
        "title": "AI法律助手隐私政策",
        "content": (
            "我们重视您的隐私保护，本政策说明我们如何收集、使用和保护您的个人信息。\n\n"
            "1. 信息收集\n"
            "我们可能收集以下信息：\n"
            "• 注册信息：用户名、邮箱、手机号等\n"
            "• 使用信息：查询记录、操作日志等\n"
            "• 设备信息：IP地址、浏览器类型等\n\n"
            "2. 信息使用\n"
            "收集的信息用于：\n"
            "• 提供和改进服务\n"
            "• 用户身份验证\n"
            "• 安全监控和风险防控\n"
            "• 法律法规要求的其他用途\n\n"
            "3. 信息保护\n"
            "我们采取以下措施保护您的信息：\n"
            "• 数据加密存储和传输\n"
            "• 严格的访问权限控制\n"
            "• 定期安全审计和漏洞修复\n"
            "• 员工隐私保护培训\n\n"
            "4. 信息共享\n"
            "除法律法规要求外，我们不会向第三方共享您的个人信息。\n\n"
            "5. 用户权利\n"
            "您有权：\n"
            "• 查询、更正、删除个人信息\n"
            "• 撤回同意\n"
            "• 投诉和举报\n\n"
            "如有隐私相关问题，请联系我们的客服团队。"
        ),
        "summary": "说明个人信息的收集、使用、保护和用户权利。",
        "requires_acceptance": True
    }
]
