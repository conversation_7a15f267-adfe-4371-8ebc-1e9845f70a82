"""
中间件模块
包含请求ID生成、CORS处理、日志记录等中间件
"""

import uuid
import time
import logging
from typing import Callable
from fastapi import Request, Response
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)


class RequestIDMiddleware(BaseHTTPMiddleware):
    """
    请求ID中间件
    为每个请求生成唯一的请求ID，用于日志追踪和响应标识
    """
    
    def __init__(self, app: ASGIApp, header_name: str = "X-Request-ID"):
        super().__init__(app)
        self.header_name = header_name
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求，生成请求ID
        
        Args:
            request: 请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: 响应对象
        """
        # 生成或获取请求ID
        request_id = request.headers.get(self.header_name) or str(uuid.uuid4())
        
        # 将请求ID添加到请求状态中
        request.state.request_id = request_id
        
        # 记录请求开始日志
        logger.info(
            f"请求开始 - ID: {request_id}, 方法: {request.method}, "
            f"路径: {request.url.path}, 客户端: {request.client.host if request.client else 'unknown'}"
        )
        
        start_time = time.time()
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 添加请求ID到响应头
            response.headers[self.header_name] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            # 记录请求完成日志
            logger.info(
                f"请求完成 - ID: {request_id}, 状态码: {response.status_code}, "
                f"处理时间: {process_time:.3f}s"
            )
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录请求错误日志
            logger.error(
                f"请求异常 - ID: {request_id}, 错误: {str(e)}, "
                f"处理时间: {process_time:.3f}s"
            )
            
            raise


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    日志记录中间件
    记录请求和响应的详细信息
    """
    
    def __init__(self, app: ASGIApp, log_request_body: bool = False, log_response_body: bool = False):
        super().__init__(app)
        self.log_request_body = log_request_body
        self.log_response_body = log_response_body
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求，记录详细日志
        
        Args:
            request: 请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: 响应对象
        """
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        # 记录请求详情
        log_data = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "headers": dict(request.headers),
            "client": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
        }
        
        # 可选记录请求体
        if self.log_request_body and request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    log_data["request_body"] = body.decode("utf-8")[:1000]  # 限制长度
            except Exception as e:
                log_data["request_body_error"] = str(e)
        
        logger.debug(f"请求详情: {log_data}")
        
        # 处理请求
        response = await call_next(request)
        
        # 记录响应详情
        response_log_data = {
            "request_id": request_id,
            "status_code": response.status_code,
            "response_headers": dict(response.headers),
        }
        
        logger.debug(f"响应详情: {response_log_data}")
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    安全头中间件
    添加安全相关的HTTP头
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求，添加安全头
        
        Args:
            request: 请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: 响应对象
        """
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )
        
        return response


def setup_cors_middleware(app, origins: list = None):
    """
    设置CORS中间件
    
    Args:
        app: FastAPI应用实例
        origins: 允许的源列表
    """
    if origins is None:
        origins = [
            "http://localhost:3000",  # React开发服务器
            "http://localhost:8080",  # Vue开发服务器
            "http://127.0.0.1:3000",
            "http://127.0.0.1:8080",
        ]
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["X-Request-ID", "X-Process-Time"],
    )


def setup_middlewares(app, config=None):
    """
    设置所有中间件
    
    Args:
        app: FastAPI应用实例
        config: 配置对象
    """
    # 安全头中间件
    app.add_middleware(SecurityHeadersMiddleware)
    
    # 日志中间件
    app.add_middleware(
        LoggingMiddleware,
        log_request_body=getattr(config, 'LOG_REQUEST_BODY', False),
        log_response_body=getattr(config, 'LOG_RESPONSE_BODY', False)
    )
    
    # 请求ID中间件
    app.add_middleware(RequestIDMiddleware)
    
    # CORS中间件
    cors_origins = getattr(config, 'CORS_ORIGINS', None)
    setup_cors_middleware(app, cors_origins)
    
    logger.info("所有中间件已设置完成")
