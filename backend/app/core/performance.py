"""
性能监控和优化模块
提供系统性能监控、缓存管理、负载均衡等功能
"""

import time
import psutil
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass
from contextlib import asynccontextmanager
import logging

import redis
from sqlalchemy import text
from sqlalchemy.orm import Session
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, int]
    database_connections: int
    redis_connections: int
    response_time: float
    request_count: int
    error_count: int


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        """初始化指标收集器"""
        self.registry = CollectorRegistry()
        
        # 请求指标
        self.request_count = Counter(
            'http_requests_total',
            'HTTP请求总数',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
        
        self.request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP请求持续时间',
            ['method', 'endpoint'],
            registry=self.registry
        )
        
        # 系统指标
        self.cpu_usage = Gauge(
            'system_cpu_usage_percent',
            'CPU使用率',
            registry=self.registry
        )
        
        self.memory_usage = Gauge(
            'system_memory_usage_percent',
            '内存使用率',
            registry=self.registry
        )
        
        self.disk_usage = Gauge(
            'system_disk_usage_percent',
            '磁盘使用率',
            registry=self.registry
        )
        
        # 数据库指标
        self.db_connections = Gauge(
            'database_connections_active',
            '活跃数据库连接数',
            registry=self.registry
        )
        
        self.db_query_duration = Histogram(
            'database_query_duration_seconds',
            '数据库查询持续时间',
            ['query_type'],
            registry=self.registry
        )
        
        # Redis指标
        self.redis_connections = Gauge(
            'redis_connections_active',
            '活跃Redis连接数',
            registry=self.registry
        )
        
        self.cache_hit_rate = Gauge(
            'cache_hit_rate_percent',
            '缓存命中率',
            registry=self.registry
        )
    
    def record_request(self, method: str, endpoint: str, status: int, duration: float):
        """记录HTTP请求指标"""
        self.request_count.labels(method=method, endpoint=endpoint, status=status).inc()
        self.request_duration.labels(method=method, endpoint=endpoint).observe(duration)
    
    def update_system_metrics(self):
        """更新系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        self.cpu_usage.set(cpu_percent)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        self.memory_usage.set(memory.percent)
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        self.disk_usage.set(disk_percent)
    
    def update_database_metrics(self, db_session: Session):
        """更新数据库指标"""
        try:
            # 获取活跃连接数
            result = db_session.execute(text("SELECT count(*) FROM pg_stat_activity"))
            connection_count = result.scalar()
            self.db_connections.set(connection_count)
        except Exception as e:
            logger.error(f"更新数据库指标失败: {e}")
    
    def update_redis_metrics(self, redis_client):
        """更新Redis指标"""
        try:
            info = redis_client.info()
            self.redis_connections.set(info.get('connected_clients', 0))
            
            # 计算缓存命中率
            hits = info.get('keyspace_hits', 0)
            misses = info.get('keyspace_misses', 0)
            total = hits + misses
            hit_rate = (hits / total * 100) if total > 0 else 0
            self.cache_hit_rate.set(hit_rate)
        except Exception as e:
            logger.error(f"更新Redis指标失败: {e}")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, redis_client=None, db_session=None):
        """
        初始化性能监控器
        
        Args:
            redis_client: Redis客户端
            db_session: 数据库会话
        """
        self.redis_client = redis_client
        self.db_session = db_session
        self.metrics_collector = MetricsCollector()
        self.performance_history: List[PerformanceMetrics] = []
        self.max_history_size = 1000
    
    def collect_system_metrics(self) -> PerformanceMetrics:
        """收集系统性能指标"""
        # 获取系统信息
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        network = psutil.net_io_counters()
        
        # 获取数据库连接数
        db_connections = 0
        if self.db_session:
            try:
                result = self.db_session.execute(text("SELECT count(*) FROM pg_stat_activity"))
                db_connections = result.scalar()
            except Exception as e:
                logger.error(f"获取数据库连接数失败: {e}")
        
        # 获取Redis连接数
        redis_connections = 0
        if self.redis_client:
            try:
                info = self.redis_client.info()
                redis_connections = info.get('connected_clients', 0)
            except Exception as e:
                logger.error(f"获取Redis连接数失败: {e}")
        
        # 创建性能指标对象
        metrics = PerformanceMetrics(
            timestamp=datetime.utcnow(),
            cpu_usage=cpu_usage,
            memory_usage=memory.percent,
            disk_usage=(disk.used / disk.total) * 100,
            network_io={
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            },
            database_connections=db_connections,
            redis_connections=redis_connections,
            response_time=0.0,  # 将在中间件中设置
            request_count=0,    # 将在中间件中设置
            error_count=0       # 将在中间件中设置
        )
        
        # 添加到历史记录
        self.performance_history.append(metrics)
        
        # 限制历史记录大小
        if len(self.performance_history) > self.max_history_size:
            self.performance_history.pop(0)
        
        # 更新Prometheus指标
        self.metrics_collector.update_system_metrics()
        if self.db_session:
            self.metrics_collector.update_database_metrics(self.db_session)
        if self.redis_client:
            self.metrics_collector.update_redis_metrics(self.redis_client)
        
        return metrics
    
    def get_performance_summary(self, minutes: int = 60) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Args:
            minutes: 统计时间范围（分钟）
            
        Returns:
            性能摘要数据
        """
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        recent_metrics = [
            m for m in self.performance_history 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {"message": "暂无性能数据"}
        
        # 计算平均值和最大值
        avg_cpu = sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics)
        max_cpu = max(m.cpu_usage for m in recent_metrics)
        
        avg_memory = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
        max_memory = max(m.memory_usage for m in recent_metrics)
        
        avg_disk = sum(m.disk_usage for m in recent_metrics) / len(recent_metrics)
        max_disk = max(m.disk_usage for m in recent_metrics)
        
        avg_db_conn = sum(m.database_connections for m in recent_metrics) / len(recent_metrics)
        max_db_conn = max(m.database_connections for m in recent_metrics)
        
        return {
            "time_range_minutes": minutes,
            "data_points": len(recent_metrics),
            "cpu_usage": {
                "average": round(avg_cpu, 2),
                "maximum": round(max_cpu, 2),
                "status": "正常" if avg_cpu < 80 else "警告" if avg_cpu < 95 else "危险"
            },
            "memory_usage": {
                "average": round(avg_memory, 2),
                "maximum": round(max_memory, 2),
                "status": "正常" if avg_memory < 80 else "警告" if avg_memory < 95 else "危险"
            },
            "disk_usage": {
                "average": round(avg_disk, 2),
                "maximum": round(max_disk, 2),
                "status": "正常" if avg_disk < 80 else "警告" if avg_disk < 95 else "危险"
            },
            "database_connections": {
                "average": round(avg_db_conn, 2),
                "maximum": max_db_conn,
                "status": "正常" if max_db_conn < 50 else "警告" if max_db_conn < 80 else "危险"
            },
            "generated_at": datetime.utcnow()
        }
    
    def check_performance_alerts(self) -> List[Dict[str, Any]]:
        """检查性能告警"""
        alerts = []
        
        if not self.performance_history:
            return alerts
        
        latest_metrics = self.performance_history[-1]
        
        # CPU告警
        if latest_metrics.cpu_usage > 90:
            alerts.append({
                "type": "cpu_high",
                "severity": "critical",
                "message": f"CPU使用率过高: {latest_metrics.cpu_usage:.1f}%",
                "value": latest_metrics.cpu_usage,
                "threshold": 90,
                "timestamp": latest_metrics.timestamp
            })
        elif latest_metrics.cpu_usage > 80:
            alerts.append({
                "type": "cpu_high",
                "severity": "warning",
                "message": f"CPU使用率较高: {latest_metrics.cpu_usage:.1f}%",
                "value": latest_metrics.cpu_usage,
                "threshold": 80,
                "timestamp": latest_metrics.timestamp
            })
        
        # 内存告警
        if latest_metrics.memory_usage > 90:
            alerts.append({
                "type": "memory_high",
                "severity": "critical",
                "message": f"内存使用率过高: {latest_metrics.memory_usage:.1f}%",
                "value": latest_metrics.memory_usage,
                "threshold": 90,
                "timestamp": latest_metrics.timestamp
            })
        elif latest_metrics.memory_usage > 80:
            alerts.append({
                "type": "memory_high",
                "severity": "warning",
                "message": f"内存使用率较高: {latest_metrics.memory_usage:.1f}%",
                "value": latest_metrics.memory_usage,
                "threshold": 80,
                "timestamp": latest_metrics.timestamp
            })
        
        # 磁盘告警
        if latest_metrics.disk_usage > 90:
            alerts.append({
                "type": "disk_high",
                "severity": "critical",
                "message": f"磁盘使用率过高: {latest_metrics.disk_usage:.1f}%",
                "value": latest_metrics.disk_usage,
                "threshold": 90,
                "timestamp": latest_metrics.timestamp
            })
        elif latest_metrics.disk_usage > 80:
            alerts.append({
                "type": "disk_high",
                "severity": "warning",
                "message": f"磁盘使用率较高: {latest_metrics.disk_usage:.1f}%",
                "value": latest_metrics.disk_usage,
                "threshold": 80,
                "timestamp": latest_metrics.timestamp
            })
        
        # 数据库连接告警
        if latest_metrics.database_connections > 80:
            alerts.append({
                "type": "db_connections_high",
                "severity": "critical",
                "message": f"数据库连接数过多: {latest_metrics.database_connections}",
                "value": latest_metrics.database_connections,
                "threshold": 80,
                "timestamp": latest_metrics.timestamp
            })
        elif latest_metrics.database_connections > 50:
            alerts.append({
                "type": "db_connections_high",
                "severity": "warning",
                "message": f"数据库连接数较多: {latest_metrics.database_connections}",
                "value": latest_metrics.database_connections,
                "threshold": 50,
                "timestamp": latest_metrics.timestamp
            })
        
        return alerts


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, redis_client):
        """
        初始化缓存管理器
        
        Args:
            redis_client: Redis客户端
        """
        self.redis_client = redis_client
        self.default_ttl = 3600  # 默认1小时过期
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            value = await self.redis_client.get(key)
            if value:
                import json
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        try:
            import json
            ttl = ttl or self.default_ttl
            await self.redis_client.setex(key, ttl, json.dumps(value, default=str))
            return True
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            await self.redis_client.delete(key)
            return True
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                await self.redis_client.delete(*keys)
                return len(keys)
            return 0
        except Exception as e:
            logger.error(f"清除缓存模式失败 {pattern}: {e}")
            return 0
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            info = await self.redis_client.info()
            return {
                "connected_clients": info.get('connected_clients', 0),
                "used_memory": info.get('used_memory', 0),
                "used_memory_human": info.get('used_memory_human', '0B'),
                "keyspace_hits": info.get('keyspace_hits', 0),
                "keyspace_misses": info.get('keyspace_misses', 0),
                "hit_rate": self._calculate_hit_rate(
                    info.get('keyspace_hits', 0),
                    info.get('keyspace_misses', 0)
                ),
                "total_commands_processed": info.get('total_commands_processed', 0),
                "uptime_in_seconds": info.get('uptime_in_seconds', 0)
            }
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """计算缓存命中率"""
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0.0


@asynccontextmanager
async def performance_timer():
    """性能计时器上下文管理器"""
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"操作耗时: {duration:.3f}秒")


# 全局性能监控器实例
_performance_monitor: Optional[PerformanceMonitor] = None
_cache_manager: Optional[CacheManager] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def get_cache_manager() -> CacheManager:
    """获取缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        # 这里应该从配置中获取Redis客户端
        redis_client = redis.Redis(host='localhost', port=6379, db=0)
        _cache_manager = CacheManager(redis_client)
    return _cache_manager


def init_performance_monitoring(redis_client=None, db_session=None):
    """初始化性能监控"""
    global _performance_monitor, _cache_manager
    _performance_monitor = PerformanceMonitor(redis_client, db_session)
    if redis_client:
        _cache_manager = CacheManager(redis_client)
