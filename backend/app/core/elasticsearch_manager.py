"""
Elasticsearch管理器
提供Elasticsearch连接、索引管理、搜索等功能
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from elasticsearch import Elasticsearch, helpers
from elasticsearch.exceptions import NotFoundError, RequestError

logger = logging.getLogger(__name__)


class ElasticsearchConfig:
    """Elasticsearch配置"""
    
    def __init__(self):
        self.hosts = ["http://localhost:9200"]
        self.username = None
        self.password = None
        self.use_ssl = False
        self.verify_certs = False
        self.ca_certs = None
        self.timeout = 30
        self.max_retries = 3
        self.retry_on_timeout = True


class ElasticsearchManager:
    """Elasticsearch管理器"""
    
    def __init__(self, config: Optional[ElasticsearchConfig] = None):
        """
        初始化Elasticsearch管理器
        
        Args:
            config: Elasticsearch配置
        """
        self.config = config or ElasticsearchConfig()
        self.client = self._create_client()
        
        # 索引配置
        self.indices = {
            "legal_cases": "legal_cases",
            "legal_documents": "legal_documents",
            "legal_knowledge": "legal_knowledge"
        }
    
    def _create_client(self) -> Elasticsearch:
        """创建Elasticsearch客户端"""
        try:
            client_config = {
                "hosts": self.config.hosts,
                "timeout": self.config.timeout,
                "max_retries": self.config.max_retries,
                "retry_on_timeout": self.config.retry_on_timeout
            }
            
            # 认证配置
            if self.config.username and self.config.password:
                client_config["http_auth"] = (self.config.username, self.config.password)
            
            # SSL配置
            if self.config.use_ssl:
                client_config["use_ssl"] = True
                client_config["verify_certs"] = self.config.verify_certs
                if self.config.ca_certs:
                    client_config["ca_certs"] = self.config.ca_certs
            
            client = Elasticsearch(**client_config)
            
            # 测试连接
            if client.ping():
                logger.info("Elasticsearch连接成功")
            else:
                logger.error("Elasticsearch连接失败")
                
            return client
            
        except Exception as e:
            logger.error(f"创建Elasticsearch客户端失败: {e}")
            raise
    
    def test_connection(self) -> bool:
        """测试Elasticsearch连接"""
        try:
            return self.client.ping()
        except Exception as e:
            logger.error(f"Elasticsearch连接测试失败: {e}")
            return False
    
    def get_cluster_info(self) -> Dict[str, Any]:
        """获取集群信息"""
        try:
            info = self.client.info()
            health = self.client.cluster.health()
            stats = self.client.cluster.stats()
            
            return {
                "cluster_name": info.get("cluster_name"),
                "version": info.get("version", {}).get("number"),
                "status": health.get("status"),
                "number_of_nodes": health.get("number_of_nodes"),
                "number_of_data_nodes": health.get("number_of_data_nodes"),
                "active_primary_shards": health.get("active_primary_shards"),
                "active_shards": health.get("active_shards"),
                "indices_count": stats.get("indices", {}).get("count", 0),
                "docs_count": stats.get("indices", {}).get("docs", {}).get("count", 0),
                "store_size": stats.get("indices", {}).get("store", {}).get("size_in_bytes", 0)
            }
        except Exception as e:
            logger.error(f"获取集群信息失败: {e}")
            return {"error": str(e)}
    
    def create_index(self, index_name: str, mapping: Dict[str, Any], settings: Optional[Dict[str, Any]] = None) -> bool:
        """
        创建索引
        
        Args:
            index_name: 索引名称
            mapping: 字段映射
            settings: 索引设置
            
        Returns:
            是否创建成功
        """
        try:
            if self.client.indices.exists(index=index_name):
                logger.info(f"索引已存在: {index_name}")
                return True
            
            body = {"mappings": mapping}
            if settings:
                body["settings"] = settings
            
            self.client.indices.create(index=index_name, body=body)
            logger.info(f"索引创建成功: {index_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建索引失败: {index_name}, 错误: {e}")
            return False
    
    def delete_index(self, index_name: str) -> bool:
        """
        删除索引
        
        Args:
            index_name: 索引名称
            
        Returns:
            是否删除成功
        """
        try:
            if not self.client.indices.exists(index=index_name):
                logger.info(f"索引不存在: {index_name}")
                return True
            
            self.client.indices.delete(index=index_name)
            logger.info(f"索引删除成功: {index_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除索引失败: {index_name}, 错误: {e}")
            return False
    
    def get_index_info(self, index_name: str) -> Dict[str, Any]:
        """
        获取索引信息
        
        Args:
            index_name: 索引名称
            
        Returns:
            索引信息
        """
        try:
            if not self.client.indices.exists(index=index_name):
                return {"error": "索引不存在"}
            
            # 获取索引统计信息
            stats = self.client.indices.stats(index=index_name)
            mapping = self.client.indices.get_mapping(index=index_name)
            settings = self.client.indices.get_settings(index=index_name)
            
            index_stats = stats["indices"][index_name]
            
            return {
                "index_name": index_name,
                "docs_count": index_stats["total"]["docs"]["count"],
                "docs_deleted": index_stats["total"]["docs"]["deleted"],
                "store_size": index_stats["total"]["store"]["size_in_bytes"],
                "primary_shards": len(index_stats["shards"]),
                "mapping": mapping[index_name]["mappings"],
                "settings": settings[index_name]["settings"]
            }
            
        except Exception as e:
            logger.error(f"获取索引信息失败: {index_name}, 错误: {e}")
            return {"error": str(e)}
    
    def index_document(self, index_name: str, doc_id: str, document: Dict[str, Any]) -> bool:
        """
        索引单个文档
        
        Args:
            index_name: 索引名称
            doc_id: 文档ID
            document: 文档内容
            
        Returns:
            是否索引成功
        """
        try:
            # 添加时间戳
            document["indexed_at"] = datetime.utcnow().isoformat()
            
            response = self.client.index(
                index=index_name,
                id=doc_id,
                body=document
            )
            
            logger.debug(f"文档索引成功: {index_name}/{doc_id}")
            return response["result"] in ["created", "updated"]
            
        except Exception as e:
            logger.error(f"索引文档失败: {index_name}/{doc_id}, 错误: {e}")
            return False
    
    def bulk_index_documents(self, index_name: str, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量索引文档
        
        Args:
            index_name: 索引名称
            documents: 文档列表，每个文档需包含_id字段
            
        Returns:
            批量索引结果
        """
        try:
            actions = []
            for doc in documents:
                doc_id = doc.pop("_id", None)
                if not doc_id:
                    continue
                
                # 添加时间戳
                doc["indexed_at"] = datetime.utcnow().isoformat()
                
                action = {
                    "_index": index_name,
                    "_id": doc_id,
                    "_source": doc
                }
                actions.append(action)
            
            if not actions:
                return {"success": 0, "failed": 0, "errors": []}
            
            # 执行批量索引
            success_count, failed_items = helpers.bulk(
                self.client,
                actions,
                chunk_size=1000,
                request_timeout=60
            )
            
            logger.info(f"批量索引完成: {index_name}, 成功: {success_count}, 失败: {len(failed_items)}")
            
            return {
                "success": success_count,
                "failed": len(failed_items),
                "errors": failed_items
            }
            
        except Exception as e:
            logger.error(f"批量索引失败: {index_name}, 错误: {e}")
            return {"success": 0, "failed": len(documents), "errors": [str(e)]}
    
    def search_documents(
        self,
        index_name: str,
        query: Dict[str, Any],
        size: int = 10,
        from_: int = 0,
        sort: Optional[List[Dict[str, Any]]] = None,
        highlight: Optional[Dict[str, Any]] = None,
        aggregations: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        搜索文档
        
        Args:
            index_name: 索引名称
            query: 查询条件
            size: 返回结果数量
            from_: 起始位置
            sort: 排序条件
            highlight: 高亮设置
            aggregations: 聚合查询
            
        Returns:
            搜索结果
        """
        try:
            body = {
                "query": query,
                "size": size,
                "from": from_
            }
            
            if sort:
                body["sort"] = sort
            
            if highlight:
                body["highlight"] = highlight
            
            if aggregations:
                body["aggs"] = aggregations
            
            response = self.client.search(index=index_name, body=body)
            
            # 处理搜索结果
            hits = response["hits"]
            results = []
            
            for hit in hits["hits"]:
                result = {
                    "id": hit["_id"],
                    "score": hit["_score"],
                    "source": hit["_source"]
                }
                
                # 添加高亮结果
                if "highlight" in hit:
                    result["highlight"] = hit["highlight"]
                
                results.append(result)
            
            return {
                "total": hits["total"]["value"],
                "max_score": hits["max_score"],
                "results": results,
                "aggregations": response.get("aggregations", {}),
                "took": response["took"]
            }
            
        except Exception as e:
            logger.error(f"搜索失败: {index_name}, 错误: {e}")
            return {
                "total": 0,
                "max_score": 0,
                "results": [],
                "aggregations": {},
                "took": 0,
                "error": str(e)
            }
    
    def get_document(self, index_name: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        获取单个文档
        
        Args:
            index_name: 索引名称
            doc_id: 文档ID
            
        Returns:
            文档内容
        """
        try:
            response = self.client.get(index=index_name, id=doc_id)
            return {
                "id": response["_id"],
                "source": response["_source"],
                "version": response["_version"]
            }
        except NotFoundError:
            return None
        except Exception as e:
            logger.error(f"获取文档失败: {index_name}/{doc_id}, 错误: {e}")
            return None
    
    def delete_document(self, index_name: str, doc_id: str) -> bool:
        """
        删除文档
        
        Args:
            index_name: 索引名称
            doc_id: 文档ID
            
        Returns:
            是否删除成功
        """
        try:
            response = self.client.delete(index=index_name, id=doc_id)
            logger.debug(f"文档删除成功: {index_name}/{doc_id}")
            return response["result"] == "deleted"
        except NotFoundError:
            logger.warning(f"文档不存在: {index_name}/{doc_id}")
            return True
        except Exception as e:
            logger.error(f"删除文档失败: {index_name}/{doc_id}, 错误: {e}")
            return False
    
    def update_document(self, index_name: str, doc_id: str, update_doc: Dict[str, Any]) -> bool:
        """
        更新文档
        
        Args:
            index_name: 索引名称
            doc_id: 文档ID
            update_doc: 更新内容
            
        Returns:
            是否更新成功
        """
        try:
            # 添加更新时间戳
            update_doc["updated_at"] = datetime.utcnow().isoformat()
            
            response = self.client.update(
                index=index_name,
                id=doc_id,
                body={"doc": update_doc}
            )
            
            logger.debug(f"文档更新成功: {index_name}/{doc_id}")
            return response["result"] in ["updated", "noop"]
            
        except Exception as e:
            logger.error(f"更新文档失败: {index_name}/{doc_id}, 错误: {e}")
            return False


# 全局Elasticsearch管理器实例
_es_manager: Optional[ElasticsearchManager] = None


def get_elasticsearch_manager() -> ElasticsearchManager:
    """获取Elasticsearch管理器实例"""
    global _es_manager
    if _es_manager is None:
        _es_manager = ElasticsearchManager()
    return _es_manager


def init_elasticsearch_manager(config: Optional[ElasticsearchConfig] = None) -> ElasticsearchManager:
    """初始化Elasticsearch管理器"""
    global _es_manager
    _es_manager = ElasticsearchManager(config)
    return _es_manager
