"""
性能监控中间件
自动收集API请求性能指标和系统资源使用情况
"""

import time
import asyncio
from typing import Dict, Any, Optional
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse
import logging

from app.core.performance import get_performance_monitor, get_cache_manager

logger = logging.getLogger(__name__)


class PerformanceMiddleware(BaseHTTPMiddleware):
    """
    性能监控中间件
    收集请求响应时间、系统资源使用等性能指标
    """
    
    def __init__(self, app, **kwargs):
        """
        初始化性能监控中间件
        
        Args:
            app: FastAPI应用实例
            **kwargs: 其他参数
        """
        super().__init__(app)
        self.config = {
            # 是否启用性能监控
            'enable_monitoring': kwargs.get('enable_monitoring', True),
            # 慢请求阈值（秒）
            'slow_request_threshold': kwargs.get('slow_request_threshold', 1.0),
            # 是否记录请求详情
            'log_request_details': kwargs.get('log_request_details', True),
            # 排除的路径
            'exclude_paths': kwargs.get('exclude_paths', [
                '/health', '/metrics', '/docs', '/openapi.json', '/favicon.ico'
            ]),
            # 系统指标收集间隔（秒）
            'system_metrics_interval': kwargs.get('system_metrics_interval', 60),
            # 是否启用缓存
            'enable_caching': kwargs.get('enable_caching', True),
            # 缓存TTL（秒）
            'cache_ttl': kwargs.get('cache_ttl', 300)
        }
        
        self.performance_monitor = get_performance_monitor()
        self.cache_manager = get_cache_manager() if self.config['enable_caching'] else None
        self.last_system_metrics_time = 0
        
        # 请求统计
        self.request_stats = {
            'total_requests': 0,
            'slow_requests': 0,
            'error_requests': 0,
            'total_response_time': 0.0
        }
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        处理请求并收集性能指标
        
        Args:
            request: 请求对象
            call_next: 下一个中间件
            
        Returns:
            响应对象
        """
        # 检查是否需要监控此路径
        if not self._should_monitor_path(request.url.path):
            return await call_next(request)
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 生成请求ID
        request_id = self._generate_request_id()
        
        # 尝试从缓存获取响应
        cached_response = None
        if self.cache_manager and request.method == "GET":
            cache_key = self._generate_cache_key(request)
            cached_response = await self.cache_manager.get(cache_key)
        
        if cached_response:
            # 返回缓存的响应
            response_time = time.time() - start_time
            await self._record_performance_metrics(
                request, None, response_time, True, request_id
            )
            
            return StarletteResponse(
                content=cached_response['content'],
                status_code=cached_response['status_code'],
                headers=cached_response['headers'],
                media_type=cached_response['media_type']
            )
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            # 记录性能指标
            await self._record_performance_metrics(
                request, response, response_time, False, request_id
            )
            
            # 缓存GET请求的成功响应
            if (self.cache_manager and 
                request.method == "GET" and 
                response.status_code == 200):
                await self._cache_response(request, response)
            
            # 添加性能头
            response.headers["X-Response-Time"] = f"{response_time:.3f}s"
            response.headers["X-Request-ID"] = request_id
            
            # 收集系统指标
            await self._collect_system_metrics_if_needed()
            
            return response
            
        except Exception as e:
            # 记录错误
            response_time = time.time() - start_time
            await self._record_error_metrics(request, e, response_time, request_id)
            raise
    
    def _should_monitor_path(self, path: str) -> bool:
        """
        检查是否应该监控此路径
        
        Args:
            path: 请求路径
            
        Returns:
            是否监控
        """
        if not self.config['enable_monitoring']:
            return False
        
        for exclude_path in self.config['exclude_paths']:
            if path.startswith(exclude_path):
                return False
        
        return True
    
    def _generate_request_id(self) -> str:
        """生成请求ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _generate_cache_key(self, request: Request) -> str:
        """
        生成缓存键
        
        Args:
            request: 请求对象
            
        Returns:
            缓存键
        """
        # 基于URL和查询参数生成缓存键
        url_with_params = str(request.url)
        import hashlib
        return f"api_cache:{hashlib.md5(url_with_params.encode()).hexdigest()}"
    
    async def _cache_response(self, request: Request, response: Response):
        """
        缓存响应
        
        Args:
            request: 请求对象
            response: 响应对象
        """
        try:
            # 读取响应体
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            
            # 构建缓存数据
            cache_data = {
                'content': response_body.decode('utf-8'),
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'media_type': response.media_type
            }
            
            # 存储到缓存
            cache_key = self._generate_cache_key(request)
            await self.cache_manager.set(
                cache_key, cache_data, self.config['cache_ttl']
            )
            
            # 重新创建响应对象
            return StarletteResponse(
                content=response_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
            
        except Exception as e:
            logger.error(f"缓存响应失败: {e}")
    
    async def _record_performance_metrics(
        self, 
        request: Request, 
        response: Optional[Response], 
        response_time: float,
        from_cache: bool,
        request_id: str
    ):
        """
        记录性能指标
        
        Args:
            request: 请求对象
            response: 响应对象
            response_time: 响应时间
            from_cache: 是否来自缓存
            request_id: 请求ID
        """
        try:
            # 更新请求统计
            self.request_stats['total_requests'] += 1
            self.request_stats['total_response_time'] += response_time
            
            # 检查慢请求
            if response_time > self.config['slow_request_threshold']:
                self.request_stats['slow_requests'] += 1
                logger.warning(
                    f"慢请求检测 - {request.method} {request.url.path} "
                    f"耗时: {response_time:.3f}s (ID: {request_id})"
                )
            
            # 记录到性能监控器
            if hasattr(self.performance_monitor, 'metrics_collector'):
                status_code = response.status_code if response else 500
                self.performance_monitor.metrics_collector.record_request(
                    method=request.method,
                    endpoint=request.url.path,
                    status=status_code,
                    duration=response_time
                )
            
            # 详细日志记录
            if self.config['log_request_details']:
                status_code = response.status_code if response else 500
                logger.info(
                    f"请求完成 - {request.method} {request.url.path} "
                    f"状态: {status_code} 耗时: {response_time:.3f}s "
                    f"缓存: {'是' if from_cache else '否'} (ID: {request_id})"
                )
            
        except Exception as e:
            logger.error(f"记录性能指标失败: {e}")
    
    async def _record_error_metrics(
        self, 
        request: Request, 
        error: Exception, 
        response_time: float,
        request_id: str
    ):
        """
        记录错误指标
        
        Args:
            request: 请求对象
            error: 异常对象
            response_time: 响应时间
            request_id: 请求ID
        """
        try:
            # 更新错误统计
            self.request_stats['error_requests'] += 1
            
            # 记录错误日志
            logger.error(
                f"请求错误 - {request.method} {request.url.path} "
                f"错误: {str(error)} 耗时: {response_time:.3f}s (ID: {request_id})"
            )
            
            # 记录到性能监控器
            if hasattr(self.performance_monitor, 'metrics_collector'):
                self.performance_monitor.metrics_collector.record_request(
                    method=request.method,
                    endpoint=request.url.path,
                    status=500,
                    duration=response_time
                )
            
        except Exception as e:
            logger.error(f"记录错误指标失败: {e}")
    
    async def _collect_system_metrics_if_needed(self):
        """根据需要收集系统指标"""
        try:
            current_time = time.time()
            if (current_time - self.last_system_metrics_time) >= self.config['system_metrics_interval']:
                # 异步收集系统指标，避免阻塞请求
                asyncio.create_task(self._collect_system_metrics())
                self.last_system_metrics_time = current_time
        except Exception as e:
            logger.error(f"系统指标收集调度失败: {e}")
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 在后台线程中收集系统指标
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, 
                self.performance_monitor.collect_system_metrics
            )
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计数据
        """
        total_requests = self.request_stats['total_requests']
        
        return {
            "total_requests": total_requests,
            "slow_requests": self.request_stats['slow_requests'],
            "error_requests": self.request_stats['error_requests'],
            "slow_request_rate": (
                self.request_stats['slow_requests'] / total_requests * 100
                if total_requests > 0 else 0
            ),
            "error_rate": (
                self.request_stats['error_requests'] / total_requests * 100
                if total_requests > 0 else 0
            ),
            "average_response_time": (
                self.request_stats['total_response_time'] / total_requests
                if total_requests > 0 else 0
            ),
            "slow_request_threshold": self.config['slow_request_threshold'],
            "monitoring_enabled": self.config['enable_monitoring'],
            "caching_enabled": self.config['enable_caching']
        }


def create_performance_middleware(**kwargs) -> PerformanceMiddleware:
    """
    创建性能监控中间件
    
    Args:
        **kwargs: 配置参数
        
    Returns:
        性能监控中间件实例
    """
    return lambda app: PerformanceMiddleware(app, **kwargs)
