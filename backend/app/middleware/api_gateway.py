"""
API网关中间件
实现请求路由、负载均衡、限流、监控等功能
"""

import time
import uuid
import json
import logging
from typing import Callable, Dict, Any, Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import asyncio
from collections import defaultdict, deque
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class APIGatewayMiddleware(BaseHTTPMiddleware):
    """API网关中间件"""
    
    def __init__(
        self,
        app: ASGIApp,
        enable_rate_limiting: bool = True,
        enable_monitoring: bool = True,
        enable_load_balancing: bool = False,
        rate_limit_requests: int = 100,
        rate_limit_window: int = 60
    ):
        super().__init__(app)
        self.enable_rate_limiting = enable_rate_limiting
        self.enable_monitoring = enable_monitoring
        self.enable_load_balancing = enable_load_balancing
        self.rate_limit_requests = rate_limit_requests
        self.rate_limit_window = rate_limit_window
        
        # 限流存储
        self.rate_limit_storage = defaultdict(deque)
        
        # 监控数据存储
        self.metrics = {
            "total_requests": 0,
            "total_errors": 0,
            "response_times": deque(maxlen=1000),
            "status_codes": defaultdict(int),
            "endpoints": defaultdict(lambda: {
                "count": 0,
                "errors": 0,
                "avg_response_time": 0,
                "last_accessed": None
            })
        }
        
        # 服务实例（用于负载均衡）
        self.service_instances = {}
        
        # 健康检查
        self.health_check_interval = 30
        self.last_health_check = time.time()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        
        start_time = time.time()
        request_id = str(uuid.uuid4())
        
        # 添加请求ID到请求头
        request.state.request_id = request_id
        
        try:
            # 1. 请求预处理
            await self._preprocess_request(request)
            
            # 2. 限流检查
            if self.enable_rate_limiting:
                await self._check_rate_limit(request)
            
            # 3. 负载均衡（如果启用）
            if self.enable_load_balancing:
                await self._handle_load_balancing(request)
            
            # 4. 执行请求
            response = await call_next(request)
            
            # 5. 响应后处理
            response = await self._postprocess_response(request, response, start_time)
            
            return response
            
        except HTTPException as e:
            # 处理HTTP异常
            response = JSONResponse(
                status_code=e.status_code,
                content={
                    "error": e.detail,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            await self._record_error(request, e.status_code, str(e.detail), start_time)
            return response
            
        except Exception as e:
            # 处理其他异常
            logger.error(f"API网关异常: {e}", exc_info=True)
            response = JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "内部服务器错误",
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            await self._record_error(request, 500, str(e), start_time)
            return response
    
    async def _preprocess_request(self, request: Request):
        """请求预处理"""
        
        # 记录请求开始
        logger.info(f"API请求: {request.method} {request.url.path} - {request.state.request_id}")
        
        # 添加CORS头（如果需要）
        # 这里可以添加其他预处理逻辑
        pass
    
    async def _check_rate_limit(self, request: Request):
        """检查限流"""
        
        # 获取客户端标识（IP地址或用户ID）
        client_id = self._get_client_id(request)
        
        current_time = time.time()
        window_start = current_time - self.rate_limit_window
        
        # 清理过期的请求记录
        client_requests = self.rate_limit_storage[client_id]
        while client_requests and client_requests[0] < window_start:
            client_requests.popleft()
        
        # 检查是否超过限制
        if len(client_requests) >= self.rate_limit_requests:
            logger.warning(f"限流触发: {client_id} - {len(client_requests)} 请求")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"请求过于频繁，请稍后再试。限制：{self.rate_limit_requests}次/{self.rate_limit_window}秒"
            )
        
        # 记录当前请求
        client_requests.append(current_time)
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        
        # 优先使用用户ID（如果已认证）
        if hasattr(request.state, 'user') and request.state.user:
            return f"user_{request.state.user.id}"
        
        # 使用IP地址
        client_ip = request.client.host if request.client else "unknown"
        
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        return f"ip_{client_ip}"
    
    async def _handle_load_balancing(self, request: Request):
        """处理负载均衡"""
        
        # 这里可以实现负载均衡逻辑
        # 例如：轮询、加权轮询、最少连接等
        
        # 目前是单实例，暂不实现
        pass
    
    async def _postprocess_response(
        self,
        request: Request,
        response: Response,
        start_time: float
    ) -> Response:
        """响应后处理"""
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # 添加响应头
        response.headers["X-Request-ID"] = request.state.request_id
        response.headers["X-Response-Time"] = f"{response_time:.3f}s"
        
        # 记录监控数据
        if self.enable_monitoring:
            await self._record_metrics(request, response, response_time)
        
        logger.info(
            f"API响应: {request.method} {request.url.path} - "
            f"{response.status_code} - {response_time:.3f}s - {request.state.request_id}"
        )
        
        return response
    
    async def _record_metrics(self, request: Request, response: Response, response_time: float):
        """记录监控指标"""
        
        # 总请求数
        self.metrics["total_requests"] += 1
        
        # 响应时间
        self.metrics["response_times"].append(response_time)
        
        # 状态码统计
        self.metrics["status_codes"][response.status_code] += 1
        
        # 错误统计
        if response.status_code >= 400:
            self.metrics["total_errors"] += 1
        
        # 端点统计
        endpoint = f"{request.method} {request.url.path}"
        endpoint_metrics = self.metrics["endpoints"][endpoint]
        endpoint_metrics["count"] += 1
        endpoint_metrics["last_accessed"] = datetime.utcnow().isoformat()
        
        if response.status_code >= 400:
            endpoint_metrics["errors"] += 1
        
        # 计算平均响应时间
        if endpoint_metrics["count"] == 1:
            endpoint_metrics["avg_response_time"] = response_time
        else:
            # 使用移动平均
            current_avg = endpoint_metrics["avg_response_time"]
            endpoint_metrics["avg_response_time"] = (
                (current_avg * (endpoint_metrics["count"] - 1) + response_time) / 
                endpoint_metrics["count"]
            )
    
    async def _record_error(
        self,
        request: Request,
        status_code: int,
        error_message: str,
        start_time: float
    ):
        """记录错误"""
        
        response_time = time.time() - start_time
        
        # 记录监控数据
        if self.enable_monitoring:
            self.metrics["total_requests"] += 1
            self.metrics["total_errors"] += 1
            self.metrics["response_times"].append(response_time)
            self.metrics["status_codes"][status_code] += 1
            
            endpoint = f"{request.method} {request.url.path}"
            endpoint_metrics = self.metrics["endpoints"][endpoint]
            endpoint_metrics["count"] += 1
            endpoint_metrics["errors"] += 1
            endpoint_metrics["last_accessed"] = datetime.utcnow().isoformat()
        
        logger.error(
            f"API错误: {request.method} {request.url.path} - "
            f"{status_code} - {error_message} - {response_time:.3f}s - {request.state.request_id}"
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取监控指标"""
        
        response_times = list(self.metrics["response_times"])
        
        return {
            "total_requests": self.metrics["total_requests"],
            "total_errors": self.metrics["total_errors"],
            "error_rate": (
                self.metrics["total_errors"] / max(self.metrics["total_requests"], 1) * 100
            ),
            "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
            "min_response_time": min(response_times) if response_times else 0,
            "max_response_time": max(response_times) if response_times else 0,
            "status_codes": dict(self.metrics["status_codes"]),
            "endpoints": dict(self.metrics["endpoints"]),
            "rate_limit_storage_size": len(self.rate_limit_storage),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def reset_metrics(self):
        """重置监控指标"""
        
        self.metrics = {
            "total_requests": 0,
            "total_errors": 0,
            "response_times": deque(maxlen=1000),
            "status_codes": defaultdict(int),
            "endpoints": defaultdict(lambda: {
                "count": 0,
                "errors": 0,
                "avg_response_time": 0,
                "last_accessed": None
            })
        }
        
        logger.info("监控指标已重置")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        
        current_time = time.time()
        
        # 检查是否需要执行健康检查
        if current_time - self.last_health_check < self.health_check_interval:
            return {"status": "healthy", "last_check": self.last_health_check}
        
        self.last_health_check = current_time
        
        # 执行健康检查
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "uptime": current_time,
            "metrics": {
                "total_requests": self.metrics["total_requests"],
                "error_rate": (
                    self.metrics["total_errors"] / max(self.metrics["total_requests"], 1) * 100
                ),
                "active_rate_limits": len(self.rate_limit_storage)
            }
        }
        
        # 检查错误率
        error_rate = health_status["metrics"]["error_rate"]
        if error_rate > 50:  # 错误率超过50%
            health_status["status"] = "unhealthy"
            health_status["reason"] = f"高错误率: {error_rate:.2f}%"
        elif error_rate > 20:  # 错误率超过20%
            health_status["status"] = "degraded"
            health_status["reason"] = f"错误率较高: {error_rate:.2f}%"
        
        return health_status
