"""
免责声明中间件
自动为AI回答添加免责声明和法律风险提示
"""

import json
import re
from typing import Optional, Dict, Any, List
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse
import logging

from app.core.database import get_db
from app.core.disclaimer import get_disclaimer_manager, DisclaimerType

logger = logging.getLogger(__name__)


class DisclaimerMiddleware(BaseHTTPMiddleware):
    """
    免责声明中间件
    自动为AI相关的API响应添加免责声明
    """
    
    def __init__(self, app, **kwargs):
        """
        初始化免责声明中间件
        
        Args:
            app: FastAPI应用实例
            **kwargs: 其他参数
        """
        super().__init__(app)
        self.config = {
            # 需要添加免责声明的路径模式
            'ai_paths': kwargs.get('ai_paths', [
                '/api/v1/ai/chat',
                '/api/v1/ai/analyze',
                '/api/v1/ai/question',
                '/api/v1/contracts/analyze',
                '/api/v1/documents/analyze'
            ]),
            # 是否启用自动免责声明
            'enable_auto_disclaimer': kwargs.get('enable_auto_disclaimer', True),
            # 免责声明位置（'header', 'footer', 'both'）
            'disclaimer_position': kwargs.get('disclaimer_position', 'footer'),
            # 是否在响应头中添加免责声明
            'add_disclaimer_header': kwargs.get('add_disclaimer_header', True),
            # 免责声明分隔符
            'disclaimer_separator': kwargs.get('disclaimer_separator', '\n\n---\n\n'),
            # 最大响应体大小（字节）
            'max_response_size': kwargs.get('max_response_size', 1048576)  # 1MB
        }
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        处理请求并添加免责声明
        
        Args:
            request: 请求对象
            call_next: 下一个中间件
            
        Returns:
            响应对象
        """
        # 检查是否需要处理此路径
        if not self._should_add_disclaimer(request.url.path):
            return await call_next(request)
        
        # 处理请求
        response = await call_next(request)
        
        # 检查响应状态码
        if response.status_code != 200:
            return response
        
        # 添加免责声明
        try:
            modified_response = await self._add_disclaimer_to_response(request, response)
            return modified_response
        except Exception as e:
            logger.error(f"添加免责声明失败: {e}")
            return response
    
    def _should_add_disclaimer(self, path: str) -> bool:
        """
        检查是否应该为此路径添加免责声明
        
        Args:
            path: 请求路径
            
        Returns:
            是否添加免责声明
        """
        if not self.config['enable_auto_disclaimer']:
            return False
        
        for ai_path in self.config['ai_paths']:
            if path.startswith(ai_path):
                return True
        
        return False
    
    async def _add_disclaimer_to_response(
        self, 
        request: Request, 
        response: Response
    ) -> Response:
        """
        为响应添加免责声明
        
        Args:
            request: 请求对象
            response: 原始响应
            
        Returns:
            修改后的响应
        """
        # 获取响应体
        response_body = b""
        async for chunk in response.body_iterator:
            response_body += chunk
        
        # 检查响应体大小
        if len(response_body) > self.config['max_response_size']:
            logger.warning(f"响应体过大，跳过免责声明添加: {len(response_body)} bytes")
            return StarletteResponse(
                content=response_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
        
        # 获取免责声明
        disclaimer_text = await self._get_disclaimer_text()
        
        if not disclaimer_text:
            return StarletteResponse(
                content=response_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
        
        # 修改响应体
        modified_body = await self._modify_response_body(
            response_body, 
            disclaimer_text,
            response.media_type
        )
        
        # 创建新的响应
        new_headers = dict(response.headers)
        
        # 添加免责声明头
        if self.config['add_disclaimer_header']:
            new_headers["X-Legal-Disclaimer"] = "AI回答仅供参考，不构成法律建议"
            new_headers["X-Disclaimer-Type"] = "ai_response"
        
        # 更新内容长度
        new_headers["Content-Length"] = str(len(modified_body))
        
        return StarletteResponse(
            content=modified_body,
            status_code=response.status_code,
            headers=new_headers,
            media_type=response.media_type
        )
    
    async def _get_disclaimer_text(self) -> Optional[str]:
        """
        获取免责声明文本
        
        Returns:
            免责声明文本
        """
        try:
            # 获取数据库会话
            db = next(get_db())
            disclaimer_manager = get_disclaimer_manager(db)
            
            # 获取AI回答免责声明
            disclaimer = disclaimer_manager.get_ai_response_disclaimer()
            
            if disclaimer:
                return disclaimer
            
            # 返回默认免责声明
            return (
                "⚠️ 重要提示：本AI助手提供的信息仅供参考，不构成正式的法律建议。"
                "具体法律问题请咨询专业律师。我们不对因使用本服务而产生的任何后果承担责任。"
            )
            
        except Exception as e:
            logger.error(f"获取免责声明失败: {e}")
            return None
    
    async def _modify_response_body(
        self, 
        response_body: bytes, 
        disclaimer_text: str,
        media_type: Optional[str]
    ) -> bytes:
        """
        修改响应体，添加免责声明
        
        Args:
            response_body: 原始响应体
            disclaimer_text: 免责声明文本
            media_type: 媒体类型
            
        Returns:
            修改后的响应体
        """
        try:
            # 解码响应体
            response_text = response_body.decode('utf-8')
            
            # 根据媒体类型处理
            if media_type and 'application/json' in media_type:
                return await self._modify_json_response(response_text, disclaimer_text)
            else:
                return await self._modify_text_response(response_text, disclaimer_text)
                
        except UnicodeDecodeError:
            logger.warning("无法解码响应体，跳过免责声明添加")
            return response_body
        except Exception as e:
            logger.error(f"修改响应体失败: {e}")
            return response_body
    
    async def _modify_json_response(
        self, 
        response_text: str, 
        disclaimer_text: str
    ) -> bytes:
        """
        修改JSON响应，添加免责声明
        
        Args:
            response_text: 响应文本
            disclaimer_text: 免责声明文本
            
        Returns:
            修改后的响应体
        """
        try:
            # 解析JSON
            response_data = json.loads(response_text)
            
            # 添加免责声明到不同的字段
            if isinstance(response_data, dict):
                # 查找可能包含AI回答的字段
                ai_response_fields = [
                    'answer', 'response', 'result', 'content', 'message',
                    'analysis', 'summary', 'explanation', 'advice'
                ]
                
                modified = False
                for field in ai_response_fields:
                    if field in response_data and isinstance(response_data[field], str):
                        response_data[field] = self._add_disclaimer_to_text(
                            response_data[field], disclaimer_text
                        )
                        modified = True
                
                # 如果没有找到合适的字段，添加到根级别
                if not modified:
                    response_data['legal_disclaimer'] = disclaimer_text
                
                # 添加元数据
                response_data['_disclaimer'] = {
                    'type': 'ai_response',
                    'added_at': self._get_current_timestamp(),
                    'version': '1.0'
                }
            
            # 重新编码为JSON
            return json.dumps(response_data, ensure_ascii=False, indent=2).encode('utf-8')
            
        except json.JSONDecodeError:
            logger.warning("无法解析JSON响应，使用文本处理")
            return await self._modify_text_response(response_text, disclaimer_text)
    
    async def _modify_text_response(
        self, 
        response_text: str, 
        disclaimer_text: str
    ) -> bytes:
        """
        修改文本响应，添加免责声明
        
        Args:
            response_text: 响应文本
            disclaimer_text: 免责声明文本
            
        Returns:
            修改后的响应体
        """
        modified_text = self._add_disclaimer_to_text(response_text, disclaimer_text)
        return modified_text.encode('utf-8')
    
    def _add_disclaimer_to_text(self, text: str, disclaimer: str) -> str:
        """
        向文本添加免责声明
        
        Args:
            text: 原始文本
            disclaimer: 免责声明
            
        Returns:
            添加免责声明后的文本
        """
        separator = self.config['disclaimer_separator']
        
        if self.config['disclaimer_position'] == 'header':
            return f"{disclaimer}{separator}{text}"
        elif self.config['disclaimer_position'] == 'footer':
            return f"{text}{separator}{disclaimer}"
        elif self.config['disclaimer_position'] == 'both':
            return f"{disclaimer}{separator}{text}{separator}{disclaimer}"
        else:
            return f"{text}{separator}{disclaimer}"
    
    def _get_current_timestamp(self) -> str:
        """
        获取当前时间戳
        
        Returns:
            ISO格式的时间戳
        """
        from datetime import datetime
        return datetime.utcnow().isoformat()


def create_disclaimer_middleware(**kwargs) -> DisclaimerMiddleware:
    """
    创建免责声明中间件
    
    Args:
        **kwargs: 配置参数
        
    Returns:
        免责声明中间件实例
    """
    return lambda app: DisclaimerMiddleware(app, **kwargs)
