"""
审计日志中间件
自动记录API请求和响应，进行异常检测
"""

import time
import json
import uuid
from typing import Optional, Dict, Any
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse
import logging

from app.core.audit import get_audit_logger, AuditEventType, AuditLevel
from app.core.anomaly_detection import get_anomaly_detector
from app.core.database import get_db

logger = logging.getLogger(__name__)


class AuditMiddleware(BaseHTTPMiddleware):
    """
    审计日志中间件
    记录所有API请求和响应，并进行异常检测
    """
    
    def __init__(self, app, **kwargs):
        """
        初始化审计中间件
        
        Args:
            app: FastAPI应用实例
            **kwargs: 其他参数
        """
        super().__init__(app)
        self.config = {
            # 是否记录请求体
            'log_request_body': kwargs.get('log_request_body', True),
            # 是否记录响应体
            'log_response_body': kwargs.get('log_response_body', False),
            # 最大请求体大小（字节）
            'max_body_size': kwargs.get('max_body_size', 10240),  # 10KB
            # 排除的路径
            'exclude_paths': kwargs.get('exclude_paths', [
                '/health', '/metrics', '/docs', '/openapi.json', '/favicon.ico'
            ]),
            # 敏感字段（不记录）
            'sensitive_fields': kwargs.get('sensitive_fields', [
                'password', 'token', 'secret', 'key', 'authorization'
            ]),
            # 是否启用异常检测
            'enable_anomaly_detection': kwargs.get('enable_anomaly_detection', True),
            # 异常检测间隔（秒）
            'anomaly_detection_interval': kwargs.get('anomaly_detection_interval', 300)  # 5分钟
        }
        self.last_anomaly_check = 0
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        处理请求并记录审计日志
        
        Args:
            request: 请求对象
            call_next: 下一个中间件
            
        Returns:
            响应对象
        """
        # 检查是否需要排除此路径
        if self._should_exclude_path(request.url.path):
            return await call_next(request)
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息
        request_info = await self._extract_request_info(request, request_id)
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 获取响应信息
            response_info = await self._extract_response_info(response, process_time)
            
            # 记录审计日志
            await self._log_request_response(request_info, response_info)
            
            # 异常检测
            if self.config['enable_anomaly_detection']:
                await self._check_anomalies(request_info, response_info)
            
            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # 记录错误
            process_time = time.time() - start_time
            error_info = {
                "error": str(e),
                "error_type": type(e).__name__,
                "process_time": process_time
            }
            
            await self._log_error(request_info, error_info)
            raise
    
    def _should_exclude_path(self, path: str) -> bool:
        """
        检查是否应该排除此路径
        
        Args:
            path: 请求路径
            
        Returns:
            是否排除
        """
        for exclude_path in self.config['exclude_paths']:
            if path.startswith(exclude_path):
                return True
        return False
    
    async def _extract_request_info(self, request: Request, request_id: str) -> Dict[str, Any]:
        """
        提取请求信息
        
        Args:
            request: 请求对象
            request_id: 请求ID
            
        Returns:
            请求信息字典
        """
        # 基本信息
        info = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers),
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("User-Agent"),
            "timestamp": time.time()
        }
        
        # 获取用户信息（如果已认证）
        user_info = self._get_user_info(request)
        if user_info:
            info.update(user_info)
        
        # 获取请求体
        if (self.config['log_request_body'] and 
            request.method in ["POST", "PUT", "PATCH"]):
            try:
                body = await self._get_request_body(request)
                if body:
                    info["body"] = self._sanitize_data(body)
            except Exception as e:
                logger.warning(f"无法读取请求体: {e}")
                info["body_error"] = str(e)
        
        return info
    
    async def _extract_response_info(
        self, 
        response: Response, 
        process_time: float
    ) -> Dict[str, Any]:
        """
        提取响应信息
        
        Args:
            response: 响应对象
            process_time: 处理时间
            
        Returns:
            响应信息字典
        """
        info = {
            "status_code": response.status_code,
            "headers": dict(response.headers),
            "process_time": process_time
        }
        
        # 获取响应体（如果配置允许）
        if self.config['log_response_body'] and hasattr(response, 'body'):
            try:
                body = response.body
                if body and len(body) <= self.config['max_body_size']:
                    try:
                        body_text = body.decode('utf-8')
                        info["body"] = self._sanitize_data(body_text)
                    except UnicodeDecodeError:
                        info["body"] = "[Binary Data]"
            except Exception as e:
                logger.warning(f"无法读取响应体: {e}")
        
        return info
    
    async def _get_request_body(self, request: Request) -> Optional[str]:
        """
        获取请求体
        
        Args:
            request: 请求对象
            
        Returns:
            请求体字符串
        """
        try:
            body = await request.body()
            if body and len(body) <= self.config['max_body_size']:
                return body.decode('utf-8')
        except Exception:
            pass
        return None
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端IP地址
        
        Args:
            request: 请求对象
            
        Returns:
            客户端IP地址
        """
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _get_user_info(self, request: Request) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Args:
            request: 请求对象
            
        Returns:
            用户信息字典
        """
        # 尝试从请求状态中获取用户信息
        if hasattr(request.state, 'user'):
            user = request.state.user
            return {
                "user_id": getattr(user, 'id', None),
                "username": getattr(user, 'username', None),
                "user_type": getattr(user, 'user_type', None)
            }
        
        # 尝试从JWT token中获取用户信息
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                # 这里应该解析JWT token获取用户信息
                # 为了简化，返回None
                pass
            except Exception:
                pass
        
        return None
    
    def _sanitize_data(self, data: Any) -> Any:
        """
        清理敏感数据
        
        Args:
            data: 原始数据
            
        Returns:
            清理后的数据
        """
        if isinstance(data, str):
            try:
                # 尝试解析为JSON
                json_data = json.loads(data)
                return self._sanitize_dict(json_data)
            except (json.JSONDecodeError, TypeError):
                return data
        elif isinstance(data, dict):
            return self._sanitize_dict(data)
        elif isinstance(data, list):
            return [self._sanitize_data(item) for item in data]
        else:
            return data
    
    def _sanitize_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理字典中的敏感数据
        
        Args:
            data: 原始字典
            
        Returns:
            清理后的字典
        """
        sanitized = {}
        for key, value in data.items():
            key_lower = key.lower()
            
            # 检查是否为敏感字段
            if any(sensitive in key_lower for sensitive in self.config['sensitive_fields']):
                sanitized[key] = "[REDACTED]"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_dict(value)
            elif isinstance(value, list):
                sanitized[key] = [self._sanitize_data(item) for item in value]
            else:
                sanitized[key] = value
        
        return sanitized
    
    async def _log_request_response(
        self, 
        request_info: Dict[str, Any], 
        response_info: Dict[str, Any]
    ) -> None:
        """
        记录请求和响应日志
        
        Args:
            request_info: 请求信息
            response_info: 响应信息
        """
        try:
            # 获取数据库会话
            db = next(get_db())
            audit_logger = get_audit_logger(db)
            
            # 确定事件类型和级别
            event_type = AuditEventType.API_CALL
            level = AuditLevel.INFO
            
            if response_info['status_code'] >= 400:
                event_type = AuditEventType.API_ERROR
                level = AuditLevel.ERROR if response_info['status_code'] >= 500 else AuditLevel.WARNING
            
            # 构建消息
            message = f"{request_info['method']} {request_info['path']} - {response_info['status_code']}"
            
            # 合并详细信息
            details = {
                "request": request_info,
                "response": response_info
            }
            
            # 记录审计日志
            await audit_logger.log_event(
                event_type=event_type,
                message=message,
                user_id=request_info.get('user_id'),
                username=request_info.get('username'),
                user_ip=request_info['client_ip'],
                user_agent=request_info.get('user_agent'),
                details=details,
                request_id=request_info['request_id'],
                success=response_info['status_code'] < 400,
                level=level
            )
            
        except Exception as e:
            logger.error(f"记录审计日志失败: {e}")
    
    async def _log_error(
        self, 
        request_info: Dict[str, Any], 
        error_info: Dict[str, Any]
    ) -> None:
        """
        记录错误日志
        
        Args:
            request_info: 请求信息
            error_info: 错误信息
        """
        try:
            # 获取数据库会话
            db = next(get_db())
            audit_logger = get_audit_logger(db)
            
            message = f"{request_info['method']} {request_info['path']} - Error: {error_info['error']}"
            
            details = {
                "request": request_info,
                "error": error_info
            }
            
            await audit_logger.log_event(
                event_type=AuditEventType.API_ERROR,
                message=message,
                user_id=request_info.get('user_id'),
                username=request_info.get('username'),
                user_ip=request_info['client_ip'],
                user_agent=request_info.get('user_agent'),
                details=details,
                request_id=request_info['request_id'],
                success=False,
                error_message=error_info['error'],
                level=AuditLevel.ERROR
            )
            
        except Exception as e:
            logger.error(f"记录错误日志失败: {e}")
    
    async def _check_anomalies(
        self, 
        request_info: Dict[str, Any], 
        response_info: Dict[str, Any]
    ) -> None:
        """
        检查异常行为
        
        Args:
            request_info: 请求信息
            response_info: 响应信息
        """
        try:
            current_time = time.time()
            
            # 检查是否需要进行异常检测
            if (current_time - self.last_anomaly_check) < self.config['anomaly_detection_interval']:
                return
            
            self.last_anomaly_check = current_time
            
            # 获取数据库会话
            db = next(get_db())
            anomaly_detector = get_anomaly_detector(db)
            
            # 分析用户行为
            anomalies = anomaly_detector.analyze_user_behavior(
                user_id=request_info.get('user_id'),
                ip_address=request_info['client_ip'],
                time_window_hours=1
            )
            
            # 创建安全告警
            for anomaly in anomalies:
                anomaly_detector.create_security_alert(
                    anomaly=anomaly,
                    user_id=request_info.get('user_id')
                )
            
        except Exception as e:
            logger.error(f"异常检测失败: {e}")


def create_audit_middleware(**kwargs) -> AuditMiddleware:
    """
    创建审计中间件
    
    Args:
        **kwargs: 配置参数
        
    Returns:
        审计中间件实例
    """
    return lambda app: AuditMiddleware(app, **kwargs)
