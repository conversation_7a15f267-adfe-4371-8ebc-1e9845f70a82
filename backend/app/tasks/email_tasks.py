"""
邮件发送异步任务
处理邮件发送、通知、批量邮件等任务
"""

import smtplib
import uuid
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, Any, Optional, List
import logging

from app.core.message_queue import task, TaskPriority
from app.core.database import get_db
from app.core.audit import get_audit_logger

logger = logging.getLogger(__name__)


class EmailConfig:
    """邮件配置"""
    
    def __init__(self):
        self.smtp_host = "smtp.example.com"
        self.smtp_port = 587
        self.smtp_username = "<EMAIL>"
        self.smtp_password = "your-smtp-password"
        self.use_tls = True
        self.from_email = "<EMAIL>"
        self.from_name = "AI法律助手"


@task(name="email_tasks.send_email", queue="email_queue", priority=TaskPriority.HIGH)
def send_email(
    to_email: str,
    subject: str,
    content: str,
    content_type: str = "html",
    from_email: Optional[str] = None,
    from_name: Optional[str] = None,
    attachments: Optional[List[Dict[str, Any]]] = None,
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    发送单个邮件
    
    Args:
        to_email: 收件人邮箱
        subject: 邮件主题
        content: 邮件内容
        content_type: 内容类型 (html/plain)
        from_email: 发件人邮箱
        from_name: 发件人姓名
        attachments: 附件列表
        user_id: 用户ID（用于审计）
        
    Returns:
        发送结果
    """
    try:
        logger.info(f"开始发送邮件: 收件人={to_email}, 主题={subject}")
        
        # 生成邮件ID
        email_id = str(uuid.uuid4())
        
        # 获取邮件配置
        config = EmailConfig()
        
        # 创建邮件消息
        msg = MIMEMultipart()
        msg['From'] = f"{from_name or config.from_name} <{from_email or config.from_email}>"
        msg['To'] = to_email
        msg['Subject'] = subject
        msg['Message-ID'] = f"<{email_id}@yourdomain.com>"
        
        # 添加邮件内容
        if content_type == "html":
            msg.attach(MIMEText(content, 'html', 'utf-8'))
        else:
            msg.attach(MIMEText(content, 'plain', 'utf-8'))
        
        # 添加附件
        if attachments:
            for attachment in attachments:
                _add_attachment(msg, attachment)
        
        # 发送邮件
        send_result = _send_smtp_email(config, msg, to_email)
        
        # 记录邮件发送记录
        email_record = {
            "email_id": email_id,
            "to_email": to_email,
            "subject": subject,
            "content_type": content_type,
            "content_length": len(content),
            "attachment_count": len(attachments) if attachments else 0,
            "send_timestamp": datetime.utcnow().isoformat(),
            "status": "sent" if send_result["success"] else "failed",
            "error": send_result.get("error"),
            "user_id": user_id
        }
        
        _save_email_record(email_record)
        
        # 记录审计日志
        if user_id:
            db = next(get_db())
            audit_logger = get_audit_logger(db)
            audit_logger.log_action(
                action="EMAIL_SENT",
                user_id=user_id,
                resource_type="EMAIL",
                resource_id=email_id,
                details={
                    "to_email": to_email,
                    "subject": subject,
                    "content_type": content_type,
                    "success": send_result["success"]
                },
                success=send_result["success"]
            )
        
        logger.info(f"邮件发送完成: 邮件ID={email_id}, 成功={send_result['success']}")
        
        return {
            "email_id": email_id,
            "success": send_result["success"],
            "error": send_result.get("error"),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"邮件发送失败: {e}")
        
        # 记录失败的审计日志
        if user_id:
            try:
                db = next(get_db())
                audit_logger = get_audit_logger(db)
                audit_logger.log_action(
                    action="EMAIL_SEND_FAILED",
                    user_id=user_id,
                    resource_type="EMAIL",
                    resource_id="unknown",
                    details={
                        "to_email": to_email,
                        "subject": subject,
                        "error": str(e)
                    },
                    success=False
                )
            except:
                pass
        
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@task(name="email_tasks.send_bulk_email", queue="email_queue", priority=TaskPriority.NORMAL)
def send_bulk_email(
    recipients: List[str],
    subject: str,
    content: str,
    content_type: str = "html",
    batch_size: int = 50,
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    发送批量邮件
    
    Args:
        recipients: 收件人列表
        subject: 邮件主题
        content: 邮件内容
        content_type: 内容类型
        batch_size: 批次大小
        user_id: 用户ID
        
    Returns:
        批量发送结果
    """
    try:
        logger.info(f"开始发送批量邮件: 收件人数量={len(recipients)}, 主题={subject}")
        
        # 生成批次ID
        batch_id = str(uuid.uuid4())
        
        # 分批发送
        total_sent = 0
        total_failed = 0
        failed_emails = []
        
        for i in range(0, len(recipients), batch_size):
            batch_recipients = recipients[i:i + batch_size]
            
            for email in batch_recipients:
                try:
                    # 发送单个邮件
                    result = send_email(
                        to_email=email,
                        subject=subject,
                        content=content,
                        content_type=content_type,
                        user_id=user_id
                    )
                    
                    if result["success"]:
                        total_sent += 1
                    else:
                        total_failed += 1
                        failed_emails.append({
                            "email": email,
                            "error": result.get("error", "未知错误")
                        })
                        
                except Exception as e:
                    total_failed += 1
                    failed_emails.append({
                        "email": email,
                        "error": str(e)
                    })
            
            # 批次间延迟，避免过载
            import time
            time.sleep(1)
        
        # 记录批量发送记录
        bulk_record = {
            "batch_id": batch_id,
            "total_recipients": len(recipients),
            "total_sent": total_sent,
            "total_failed": total_failed,
            "failed_emails": failed_emails,
            "subject": subject,
            "content_type": content_type,
            "send_timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        
        _save_bulk_email_record(bulk_record)
        
        # 记录审计日志
        if user_id:
            db = next(get_db())
            audit_logger = get_audit_logger(db)
            audit_logger.log_action(
                action="BULK_EMAIL_SENT",
                user_id=user_id,
                resource_type="BULK_EMAIL",
                resource_id=batch_id,
                details={
                    "total_recipients": len(recipients),
                    "total_sent": total_sent,
                    "total_failed": total_failed,
                    "subject": subject
                },
                success=total_failed == 0
            )
        
        logger.info(f"批量邮件发送完成: 批次ID={batch_id}, 成功={total_sent}, 失败={total_failed}")
        
        return {
            "batch_id": batch_id,
            "total_recipients": len(recipients),
            "total_sent": total_sent,
            "total_failed": total_failed,
            "failed_emails": failed_emails,
            "success_rate": total_sent / len(recipients) if recipients else 0,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"批量邮件发送失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@task(name="email_tasks.send_notification_email", queue="email_queue", priority=TaskPriority.HIGH)
def send_notification_email(
    notification_type: str,
    recipient_email: str,
    data: Dict[str, Any],
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    发送通知邮件
    
    Args:
        notification_type: 通知类型
        recipient_email: 收件人邮箱
        data: 通知数据
        user_id: 用户ID
        
    Returns:
        发送结果
    """
    try:
        logger.info(f"发送通知邮件: 类型={notification_type}, 收件人={recipient_email}")
        
        # 根据通知类型生成邮件内容
        email_content = _generate_notification_content(notification_type, data)
        
        # 发送邮件
        result = send_email(
            to_email=recipient_email,
            subject=email_content["subject"],
            content=email_content["content"],
            content_type=email_content["content_type"],
            user_id=user_id
        )
        
        logger.info(f"通知邮件发送完成: 类型={notification_type}, 成功={result['success']}")
        
        return result
        
    except Exception as e:
        logger.error(f"通知邮件发送失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


def _add_attachment(msg: MIMEMultipart, attachment: Dict[str, Any]):
    """添加邮件附件"""
    try:
        with open(attachment["file_path"], "rb") as f:
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(f.read())
        
        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            f'attachment; filename= {attachment["filename"]}'
        )
        
        msg.attach(part)
        
    except Exception as e:
        logger.error(f"添加附件失败: {e}")


def _send_smtp_email(config: EmailConfig, msg: MIMEMultipart, to_email: str) -> Dict[str, Any]:
    """通过SMTP发送邮件"""
    try:
        # 创建SMTP连接
        server = smtplib.SMTP(config.smtp_host, config.smtp_port)
        
        if config.use_tls:
            server.starttls()
        
        # 登录
        server.login(config.smtp_username, config.smtp_password)
        
        # 发送邮件
        text = msg.as_string()
        server.sendmail(config.from_email, to_email, text)
        server.quit()
        
        return {"success": True}
        
    except Exception as e:
        logger.error(f"SMTP发送失败: {e}")
        return {"success": False, "error": str(e)}


def _generate_notification_content(notification_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """生成通知邮件内容"""
    templates = {
        "welcome": {
            "subject": "欢迎使用AI法律助手",
            "content": f"""
            <html>
            <body>
                <h2>欢迎使用AI法律助手！</h2>
                <p>亲爱的 {data.get('user_name', '用户')}，</p>
                <p>感谢您注册AI法律助手。我们很高兴为您提供专业的法律服务。</p>
                <p>您可以通过以下方式开始使用我们的服务：</p>
                <ul>
                    <li>AI法律咨询</li>
                    <li>合同分析</li>
                    <li>法律文档处理</li>
                </ul>
                <p>如有任何问题，请随时联系我们。</p>
                <p>祝好，<br>AI法律助手团队</p>
            </body>
            </html>
            """,
            "content_type": "html"
        },
        "password_reset": {
            "subject": "密码重置请求",
            "content": f"""
            <html>
            <body>
                <h2>密码重置请求</h2>
                <p>您好，</p>
                <p>我们收到了您的密码重置请求。请点击以下链接重置您的密码：</p>
                <p><a href="{data.get('reset_link', '#')}">重置密码</a></p>
                <p>此链接将在24小时后失效。</p>
                <p>如果您没有请求重置密码，请忽略此邮件。</p>
                <p>祝好，<br>AI法律助手团队</p>
            </body>
            </html>
            """,
            "content_type": "html"
        },
        "document_processed": {
            "subject": "文档处理完成",
            "content": f"""
            <html>
            <body>
                <h2>文档处理完成</h2>
                <p>您好，</p>
                <p>您上传的文档 "{data.get('document_name', '未知文档')}" 已处理完成。</p>
                <p>处理结果：{data.get('processing_result', '处理成功')}</p>
                <p>您可以登录系统查看详细的分析报告。</p>
                <p>祝好，<br>AI法律助手团队</p>
            </body>
            </html>
            """,
            "content_type": "html"
        }
    }
    
    return templates.get(notification_type, {
        "subject": "系统通知",
        "content": "您有新的系统通知，请登录查看。",
        "content_type": "plain"
    })


def _save_email_record(record: Dict[str, Any]):
    """保存邮件记录"""
    logger.info(f"保存邮件记录: {record['email_id']}")


def _save_bulk_email_record(record: Dict[str, Any]):
    """保存批量邮件记录"""
    logger.info(f"保存批量邮件记录: {record['batch_id']}")
