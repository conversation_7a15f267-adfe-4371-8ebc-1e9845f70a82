"""
权限管理API端点
"""

from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.dependencies.auth import get_current_active_user
from app.dependencies.permissions import (
    get_permission_service,
    require_admin,
    require_permissions
)
from app.models.user import User
from app.services.permission import PermissionService
from app.schemas.permission import (
    PermissionCreate,
    PermissionResponse,
    RoleCreate,
    RoleResponse,
    RoleAssignRequest,
    UserRolesResponse,
    UserPermissionsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/permissions", response_model=PermissionResponse)
async def create_permission(
    permission_data: PermissionCreate,
    current_user: User = Depends(require_admin),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """创建权限（仅管理员）"""
    
    logger.info(f"管理员 {current_user.username} 创建权限: {permission_data.name}")
    
    permission = await permission_service.create_permission(
        name=permission_data.name,
        code=permission_data.code,
        description=permission_data.description,
        category=permission_data.category,
        resource=permission_data.resource,
        action=permission_data.action,
        level=permission_data.level
    )
    
    return PermissionResponse(
        id=str(permission.id),
        name=permission.name,
        code=permission.code,
        description=permission.description,
        category=permission.category,
        resource=permission.resource,
        action=permission.action,
        level=permission.level,
        is_active=permission.is_active,
        created_at=permission.created_at
    )


@router.get("/permissions", response_model=List[PermissionResponse])
async def list_permissions(
    current_user: User = Depends(require_permissions(permissions=["permission:read"])),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """获取权限列表"""
    
    from sqlalchemy import select
    from app.models.permission import Permission
    
    stmt = select(Permission).where(Permission.is_active == True)
    result = await permission_service.db.execute(stmt)
    permissions = result.scalars().all()
    
    return [
        PermissionResponse(
            id=str(permission.id),
            name=permission.name,
            code=permission.code,
            description=permission.description,
            category=permission.category,
            resource=permission.resource,
            action=permission.action,
            level=permission.level,
            is_active=permission.is_active,
            created_at=permission.created_at
        )
        for permission in permissions
    ]


@router.post("/roles", response_model=RoleResponse)
async def create_role(
    role_data: RoleCreate,
    current_user: User = Depends(require_admin),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """创建角色（仅管理员）"""
    
    logger.info(f"管理员 {current_user.username} 创建角色: {role_data.name}")
    
    role = await permission_service.create_role(
        name=role_data.name,
        code=role_data.code,
        description=role_data.description,
        level=role_data.level,
        parent_role_id=role_data.parent_role_id,
        permission_codes=role_data.permission_codes
    )
    
    return RoleResponse(
        id=str(role.id),
        name=role.name,
        code=role.code,
        description=role.description,
        level=role.level,
        parent_role_id=str(role.parent_role_id) if role.parent_role_id else None,
        is_active=role.is_active,
        permission_codes=[p.code for p in role.permissions],
        created_at=role.created_at
    )


@router.get("/roles", response_model=List[RoleResponse])
async def list_roles(
    current_user: User = Depends(require_permissions(permissions=["role:read"])),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """获取角色列表"""
    
    from sqlalchemy import select
    from sqlalchemy.orm import selectinload
    from app.models.permission import Role
    
    stmt = select(Role).where(Role.is_active == True).options(
        selectinload(Role.permissions)
    )
    result = await permission_service.db.execute(stmt)
    roles = result.scalars().all()
    
    return [
        RoleResponse(
            id=str(role.id),
            name=role.name,
            code=role.code,
            description=role.description,
            level=role.level,
            parent_role_id=str(role.parent_role_id) if role.parent_role_id else None,
            is_active=role.is_active,
            permission_codes=[p.code for p in role.permissions],
            created_at=role.created_at
        )
        for role in roles
    ]


@router.post("/users/{user_id}/roles")
async def assign_role_to_user(
    user_id: str,
    role_assign: RoleAssignRequest,
    current_user: User = Depends(require_permissions(permissions=["user:manage"])),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """为用户分配角色"""
    
    logger.info(f"管理员 {current_user.username} 为用户 {user_id} 分配角色")
    
    import uuid
    
    try:
        user_uuid = uuid.UUID(user_id)
        role_uuid = uuid.UUID(role_assign.role_id)
        
        success = await permission_service.assign_role_to_user(
            user_id=user_uuid,
            role_id=role_uuid,
            assigned_by=current_user.id
        )
        
        if success:
            return {"message": "角色分配成功"}
        else:
            return {"message": "用户已拥有该角色"}
            
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的用户ID或角色ID格式"
        )


@router.delete("/users/{user_id}/roles/{role_id}")
async def remove_role_from_user(
    user_id: str,
    role_id: str,
    current_user: User = Depends(require_permissions(permissions=["user:manage"])),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """移除用户角色"""
    
    logger.info(f"管理员 {current_user.username} 移除用户 {user_id} 的角色 {role_id}")
    
    import uuid
    
    try:
        user_uuid = uuid.UUID(user_id)
        role_uuid = uuid.UUID(role_id)
        
        success = await permission_service.remove_role_from_user(
            user_id=user_uuid,
            role_id=role_uuid,
            removed_by=current_user.id
        )
        
        if success:
            return {"message": "角色移除成功"}
        else:
            return {"message": "用户没有该角色"}
            
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的用户ID或角色ID格式"
        )


@router.get("/users/{user_id}/roles", response_model=UserRolesResponse)
async def get_user_roles(
    user_id: str,
    current_user: User = Depends(require_permissions(permissions=["user:read"])),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """获取用户角色"""
    
    import uuid
    
    try:
        user_uuid = uuid.UUID(user_id)
        roles = await permission_service.get_user_roles(user_uuid)
        
        role_responses = [
            RoleResponse(
                id=str(role.id),
                name=role.name,
                code=role.code,
                description=role.description,
                level=role.level,
                parent_role_id=str(role.parent_role_id) if role.parent_role_id else None,
                is_active=role.is_active,
                permission_codes=[p.code for p in role.permissions],
                created_at=role.created_at
            )
            for role in roles
        ]
        
        return UserRolesResponse(
            user_id=user_id,
            roles=role_responses,
            total=len(role_responses)
        )
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的用户ID格式"
        )


@router.get("/users/{user_id}/permissions", response_model=UserPermissionsResponse)
async def get_user_permissions(
    user_id: str,
    current_user: User = Depends(require_permissions(permissions=["user:read"])),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """获取用户权限"""
    
    import uuid
    
    try:
        user_uuid = uuid.UUID(user_id)
        permission_codes = await permission_service.get_user_permissions(user_uuid)
        
        return UserPermissionsResponse(
            user_id=user_id,
            permissions=list(permission_codes),
            total=len(permission_codes)
        )
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的用户ID格式"
        )


@router.get("/my/roles", response_model=UserRolesResponse)
async def get_my_roles(
    current_user: User = Depends(get_current_active_user),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """获取当前用户角色"""
    
    roles = await permission_service.get_user_roles(current_user.id)
    
    role_responses = [
        RoleResponse(
            id=str(role.id),
            name=role.name,
            code=role.code,
            description=role.description,
            level=role.level,
            parent_role_id=str(role.parent_role_id) if role.parent_role_id else None,
            is_active=role.is_active,
            permission_codes=[p.code for p in role.permissions],
            created_at=role.created_at
        )
        for role in roles
    ]
    
    return UserRolesResponse(
        user_id=str(current_user.id),
        roles=role_responses,
        total=len(role_responses)
    )


@router.get("/my/permissions", response_model=UserPermissionsResponse)
async def get_my_permissions(
    current_user: User = Depends(get_current_active_user),
    permission_service: PermissionService = Depends(get_permission_service)
) -> Any:
    """获取当前用户权限"""
    
    permission_codes = await permission_service.get_user_permissions(current_user.id)
    
    return UserPermissionsResponse(
        user_id=str(current_user.id),
        permissions=list(permission_codes),
        total=len(permission_codes)
    )
