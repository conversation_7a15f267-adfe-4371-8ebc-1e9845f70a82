"""
监控API端点
"""

import psutil
import time
from typing import Any, Dict, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text
import logging
from datetime import datetime, timedelta

from app.core.database import get_db
from app.dependencies.auth import get_current_user
from app.dependencies.permissions import require_admin
from app.models.user import User
from app.models.qa import QARecord
from app.models.contract import ContractReview
from app.models.legal_case import LegalCase
from app.core.audit import AuditLog

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/health")
async def health_check() -> Any:
    """健康检查"""
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "environment": "production"
    }


@router.get("/metrics")
async def get_system_metrics(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取系统监控指标"""
    
    logger.info(f"管理员 {current_user.username} 查看系统监控指标")
    
    # 系统资源使用情况
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # 数据库统计
    db_stats = await _get_database_stats(db)
    
    # 业务指标
    business_stats = await _get_business_stats(db)
    
    return {
        "system": {
            "cpu_usage": cpu_percent,
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "uptime": time.time()
        },
        "database": db_stats,
        "business": business_stats,
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/api-metrics")
async def get_api_metrics(
    current_user: User = Depends(require_admin)
) -> Any:
    """获取API监控指标"""
    
    # 这里需要从API网关中间件获取指标
    # 由于中间件实例不容易直接访问，这里返回模拟数据
    
    return {
        "total_requests": 12345,
        "total_errors": 123,
        "error_rate": 1.0,
        "avg_response_time": 0.245,
        "min_response_time": 0.001,
        "max_response_time": 2.345,
        "status_codes": {
            "200": 10000,
            "400": 50,
            "401": 30,
            "403": 20,
            "404": 15,
            "500": 8
        },
        "top_endpoints": [
            {
                "endpoint": "POST /api/v1/qa/ask",
                "count": 3000,
                "errors": 30,
                "avg_response_time": 0.8
            },
            {
                "endpoint": "GET /api/v1/case-search/search",
                "count": 2500,
                "errors": 25,
                "avg_response_time": 0.3
            },
            {
                "endpoint": "POST /api/v1/contract-templates/generate",
                "count": 1500,
                "errors": 15,
                "avg_response_time": 0.5
            }
        ],
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/performance")
async def get_performance_metrics(
    hours: int = 24,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取性能指标"""
    
    logger.info(f"管理员 {current_user.username} 查看性能指标")
    
    # 计算时间范围
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(hours=hours)
    
    # 获取时间段内的统计数据
    performance_stats = await _get_performance_stats(db, start_time, end_time)
    
    return {
        "time_range": {
            "start": start_time.isoformat(),
            "end": end_time.isoformat(),
            "hours": hours
        },
        "performance": performance_stats,
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/alerts")
async def get_system_alerts(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取系统告警"""
    
    alerts = []
    
    # 检查系统资源
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # CPU告警
    if cpu_percent > 80:
        alerts.append({
            "type": "system",
            "level": "warning" if cpu_percent < 90 else "critical",
            "message": f"CPU使用率过高: {cpu_percent}%",
            "value": cpu_percent,
            "threshold": 80,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    # 内存告警
    if memory.percent > 80:
        alerts.append({
            "type": "system",
            "level": "warning" if memory.percent < 90 else "critical",
            "message": f"内存使用率过高: {memory.percent}%",
            "value": memory.percent,
            "threshold": 80,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    # 磁盘告警
    disk_percent = (disk.used / disk.total) * 100
    if disk_percent > 80:
        alerts.append({
            "type": "system",
            "level": "warning" if disk_percent < 90 else "critical",
            "message": f"磁盘使用率过高: {disk_percent:.1f}%",
            "value": disk_percent,
            "threshold": 80,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    # 检查业务指标告警
    business_alerts = await _check_business_alerts(db)
    alerts.extend(business_alerts)
    
    return {
        "alerts": alerts,
        "total": len(alerts),
        "critical_count": len([a for a in alerts if a["level"] == "critical"]),
        "warning_count": len([a for a in alerts if a["level"] == "warning"]),
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/logs")
async def get_system_logs(
    level: str = "INFO",
    limit: int = 100,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取系统日志"""
    
    # 从审计日志中获取最近的日志
    stmt = select(AuditLog).order_by(
        AuditLog.timestamp.desc()
    ).limit(limit)
    
    result = await db.execute(stmt)
    audit_logs = result.scalars().all()
    
    logs = []
    for log in audit_logs:
        logs.append({
            "id": str(log.id),
            "timestamp": log.timestamp.isoformat(),
            "level": log.level.value if log.level else "INFO",
            "event_type": log.event_type.value if log.event_type else "UNKNOWN",
            "message": log.message,
            "user_id": str(log.user_id) if log.user_id else None,
            "username": log.username,
            "user_ip": log.user_ip,
            "resource_type": log.resource_type,
            "action": log.action,
            "success": log.success
        })
    
    return {
        "logs": logs,
        "total": len(logs),
        "level": level,
        "timestamp": datetime.utcnow().isoformat()
    }


async def _get_database_stats(db: AsyncSession) -> Dict[str, Any]:
    """获取数据库统计信息"""
    
    try:
        # 获取数据库连接信息
        result = await db.execute(text("SELECT version()"))
        db_version = result.scalar()
        
        # 获取数据库大小（PostgreSQL）
        result = await db.execute(text(
            "SELECT pg_size_pretty(pg_database_size(current_database()))"
        ))
        db_size = result.scalar()
        
        # 获取连接数
        result = await db.execute(text(
            "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
        ))
        active_connections = result.scalar()
        
        return {
            "version": db_version,
            "size": db_size,
            "active_connections": active_connections,
            "status": "healthy"
        }
        
    except Exception as e:
        logger.error(f"获取数据库统计失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


async def _get_business_stats(db: AsyncSession) -> Dict[str, Any]:
    """获取业务统计信息"""
    
    try:
        # 用户统计
        user_count = await db.execute(select(func.count(User.id)))
        total_users = user_count.scalar()
        
        # 问答统计
        qa_count = await db.execute(select(func.count(QARecord.id)))
        total_qa = qa_count.scalar()
        
        # 合同统计
        contract_count = await db.execute(select(func.count(ContractReview.id)))
        total_contracts = contract_count.scalar()
        
        # 案例统计
        case_count = await db.execute(select(func.count(LegalCase.id)))
        total_cases = case_count.scalar()
        
        # 今日新增统计
        today = datetime.utcnow().date()
        
        today_users = await db.execute(
            select(func.count(User.id)).where(
                func.date(User.created_at) == today
            )
        )
        new_users_today = today_users.scalar()
        
        today_qa = await db.execute(
            select(func.count(QARecord.id)).where(
                func.date(QARecord.created_at) == today
            )
        )
        new_qa_today = today_qa.scalar()
        
        return {
            "users": {
                "total": total_users,
                "new_today": new_users_today
            },
            "qa_records": {
                "total": total_qa,
                "new_today": new_qa_today
            },
            "contracts": {
                "total": total_contracts
            },
            "legal_cases": {
                "total": total_cases
            }
        }
        
    except Exception as e:
        logger.error(f"获取业务统计失败: {e}")
        return {"error": str(e)}


async def _get_performance_stats(
    db: AsyncSession,
    start_time: datetime,
    end_time: datetime
) -> Dict[str, Any]:
    """获取性能统计"""
    
    try:
        # 获取时间段内的QA记录数量
        qa_stmt = select(func.count(QARecord.id)).where(
            QARecord.created_at.between(start_time, end_time)
        )
        qa_result = await db.execute(qa_stmt)
        qa_count = qa_result.scalar()
        
        # 获取平均置信度
        confidence_stmt = select(func.avg(QARecord.confidence)).where(
            QARecord.created_at.between(start_time, end_time)
        )
        confidence_result = await db.execute(confidence_stmt)
        avg_confidence = confidence_result.scalar() or 0
        
        return {
            "qa_requests": qa_count,
            "avg_confidence": float(avg_confidence),
            "throughput": qa_count / max((end_time - start_time).total_seconds() / 3600, 1)
        }
        
    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        return {"error": str(e)}


async def _check_business_alerts(db: AsyncSession) -> List[Dict[str, Any]]:
    """检查业务告警"""
    
    alerts = []
    
    try:
        # 检查最近1小时的错误率
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        
        # 获取失败的QA记录数量
        failed_qa_stmt = select(func.count(QARecord.id)).where(
            QARecord.created_at >= one_hour_ago,
            QARecord.confidence < 0.5  # 假设置信度低于0.5为失败
        )
        failed_qa_result = await db.execute(failed_qa_stmt)
        failed_qa_count = failed_qa_result.scalar()
        
        # 获取总QA记录数量
        total_qa_stmt = select(func.count(QARecord.id)).where(
            QARecord.created_at >= one_hour_ago
        )
        total_qa_result = await db.execute(total_qa_stmt)
        total_qa_count = total_qa_result.scalar()
        
        # 计算错误率
        if total_qa_count > 0:
            error_rate = (failed_qa_count / total_qa_count) * 100
            if error_rate > 20:  # 错误率超过20%
                alerts.append({
                    "type": "business",
                    "level": "warning" if error_rate < 50 else "critical",
                    "message": f"QA服务错误率过高: {error_rate:.1f}%",
                    "value": error_rate,
                    "threshold": 20,
                    "timestamp": datetime.utcnow().isoformat()
                })
        
    except Exception as e:
        logger.error(f"检查业务告警失败: {e}")
    
    return alerts
