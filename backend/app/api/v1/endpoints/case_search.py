"""
案例搜索API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import logging
import uuid

from app.core.database import get_db
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.services.case_search import CaseSearchService
from app.schemas.case_search import (
    CaseSearchRequest,
    CaseSearchResponse,
    CaseDetailResponse,
    SimilarCasesResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/search", response_model=CaseSearchResponse)
async def search_cases(
    q: Optional[str] = Query(None, description="搜索关键词"),
    case_type: Optional[str] = Query(None, description="案件类型"),
    court_name: Optional[str] = Query(None, description="法院名称"),
    precedent_value: Optional[str] = Query(None, description="判例价值"),
    date_from: Optional[str] = Query(None, description="判决日期开始"),
    date_to: Optional[str] = Query(None, description="判决日期结束"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("relevance", description="排序方式"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """搜索案例"""
    
    logger.info(f"用户 {current_user.username} 搜索案例: {q}")
    
    # 构建日期范围
    date_range = None
    if date_from or date_to:
        date_range = {}
        if date_from:
            date_range["gte"] = date_from
        if date_to:
            date_range["lte"] = date_to
    
    # 验证排序参数
    valid_sort_options = ["relevance", "date_desc", "date_asc", "citation_desc"]
    if sort_by not in valid_sort_options:
        sort_by = "relevance"
    
    search_service = CaseSearchService(db)
    result = await search_service.search_cases(
        query=q,
        case_type=case_type,
        court_name=court_name,
        precedent_value=precedent_value,
        date_range=date_range,
        page=page,
        page_size=page_size,
        sort_by=sort_by,
        user_id=current_user.id
    )
    
    return CaseSearchResponse(**result)


@router.post("/search", response_model=CaseSearchResponse)
async def search_cases_post(
    search_request: CaseSearchRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """搜索案例（POST方式，支持复杂查询）"""
    
    logger.info(f"用户 {current_user.username} 高级搜索案例")
    
    search_service = CaseSearchService(db)
    result = await search_service.search_cases(
        query=search_request.query,
        case_type=search_request.case_type,
        court_name=search_request.court_name,
        precedent_value=search_request.precedent_value,
        date_range=search_request.date_range,
        page=search_request.page,
        page_size=search_request.page_size,
        sort_by=search_request.sort_by,
        user_id=current_user.id
    )
    
    return CaseSearchResponse(**result)


@router.get("/{case_id}", response_model=CaseDetailResponse)
async def get_case_detail(
    case_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取案例详情"""
    
    try:
        case_uuid = uuid.UUID(case_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的案例ID格式"
        )
    
    search_service = CaseSearchService(db)
    case_detail = await search_service.get_case_detail(
        case_id=case_uuid,
        user_id=current_user.id
    )
    
    if not case_detail:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="案例不存在"
        )
    
    return CaseDetailResponse(**case_detail)


@router.get("/{case_id}/similar", response_model=SimilarCasesResponse)
async def get_similar_cases(
    case_id: str,
    limit: int = Query(5, ge=1, le=20, description="返回数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取相似案例"""
    
    try:
        case_uuid = uuid.UUID(case_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的案例ID格式"
        )
    
    search_service = CaseSearchService(db)
    similar_cases = await search_service.get_similar_cases(
        case_id=case_uuid,
        limit=limit
    )
    
    return SimilarCasesResponse(
        case_id=case_id,
        similar_cases=similar_cases,
        total=len(similar_cases)
    )


@router.get("/categories/stats")
async def get_case_categories_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取案例分类统计"""
    
    from sqlalchemy import select, func
    from app.models.legal_case import LegalCase
    
    # 按案件类型统计
    stmt = select(
        LegalCase.case_type,
        func.count(LegalCase.id).label('count')
    ).group_by(LegalCase.case_type).order_by(func.count(LegalCase.id).desc())
    
    result = await db.execute(stmt)
    case_type_stats = [
        {"category": row.case_type, "count": row.count}
        for row in result
    ]
    
    # 按法院统计（前10）
    stmt = select(
        LegalCase.court_name,
        func.count(LegalCase.id).label('count')
    ).group_by(LegalCase.court_name).order_by(func.count(LegalCase.id).desc()).limit(10)
    
    result = await db.execute(stmt)
    court_stats = [
        {"court": row.court_name, "count": row.count}
        for row in result
    ]
    
    # 按判例价值统计
    stmt = select(
        LegalCase.precedent_value,
        func.count(LegalCase.id).label('count')
    ).group_by(LegalCase.precedent_value).order_by(func.count(LegalCase.id).desc())
    
    result = await db.execute(stmt)
    precedent_stats = [
        {"value": row.precedent_value, "count": row.count}
        for row in result
    ]
    
    # 总案例数
    stmt = select(func.count(LegalCase.id))
    result = await db.execute(stmt)
    total_cases = result.scalar()
    
    return {
        "total_cases": total_cases,
        "case_type_stats": case_type_stats,
        "court_stats": court_stats,
        "precedent_value_stats": precedent_stats
    }


@router.get("/trending/keywords")
async def get_trending_keywords(
    limit: int = Query(20, ge=1, le=50, description="返回数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取热门关键词"""
    
    # 这里可以实现基于搜索日志的热门关键词统计
    # 目前返回模拟数据
    trending_keywords = [
        {"keyword": "合同纠纷", "count": 156, "trend": "up"},
        {"keyword": "劳动争议", "count": 134, "trend": "up"},
        {"keyword": "交通事故", "count": 98, "trend": "stable"},
        {"keyword": "房屋买卖", "count": 87, "trend": "down"},
        {"keyword": "借款纠纷", "count": 76, "trend": "up"},
        {"keyword": "离婚财产", "count": 65, "trend": "stable"},
        {"keyword": "知识产权", "count": 54, "trend": "up"},
        {"keyword": "公司股权", "count": 43, "trend": "stable"},
        {"keyword": "医疗纠纷", "count": 32, "trend": "down"},
        {"keyword": "环境污染", "count": 21, "trend": "up"}
    ]
    
    return {
        "keywords": trending_keywords[:limit],
        "updated_at": "2024-01-01T12:00:00Z"
    }


@router.get("/suggestions/autocomplete")
async def get_search_suggestions(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=20, description="返回数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取搜索建议（自动完成）"""
    
    from sqlalchemy import select, or_
    from app.models.legal_case import LegalCase
    
    # 基于案例标题和关键词的自动完成
    stmt = select(LegalCase.title).where(
        LegalCase.title.ilike(f"%{q}%")
    ).limit(limit)
    
    result = await db.execute(stmt)
    title_suggestions = [row.title for row in result]
    
    # 也可以基于关键词提供建议
    suggestions = []
    for title in title_suggestions:
        if q.lower() in title.lower():
            suggestions.append({
                "text": title,
                "type": "case_title",
                "highlight": title.replace(q, f"<mark>{q}</mark>")
            })
    
    # 添加一些常见的法律术语建议
    legal_terms = [
        "合同违约", "劳动仲裁", "交通肇事", "房屋租赁", "借贷纠纷",
        "离婚诉讼", "知识产权侵权", "公司法务", "医疗事故", "环境侵权"
    ]
    
    for term in legal_terms:
        if q.lower() in term.lower() and len(suggestions) < limit:
            suggestions.append({
                "text": term,
                "type": "legal_term",
                "highlight": term.replace(q, f"<mark>{q}</mark>")
            })
    
    return {
        "suggestions": suggestions[:limit],
        "query": q
    }
