"""
法律案例管理API端点
"""

from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.core.response import success_response, error_response
from app.core.exceptions import NotFoundException, ValidationException, ConflictException
from app.dependencies.auth import get_current_user, get_current_active_user
from app.models.user import User
from app.services.legal_case import LegalCaseService
from app.schemas.legal_case import (
    LegalCaseCreate,
    LegalCaseUpdate,
    LegalCaseResponse,
    LegalCaseListItem,
    CaseSearchRequest,
    CaseSearchResponse,
    CaseStatisticsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=LegalCaseResponse, status_code=status.HTTP_201_CREATED)
async def create_case(
    case_data: LegalCaseCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """创建法律案例"""
    
    logger.info(f"用户 {current_user.username} 创建法律案例: {case_data.title}")
    
    try:
        case_service = LegalCaseService(db)
        case = await case_service.create_case(
            case_data=case_data.dict(),
            created_by=current_user.id
        )
        
        return LegalCaseResponse(
            id=str(case.id),
            case_number=case.case_number,
            title=case.title,
            court_name=case.court_name,
            case_type=case.case_type.value,
            judgment_date=case.judgment_date,
            trial_procedure=case.trial_procedure,
            parties=case.parties or [],
            case_summary=case.case_summary,
            case_facts=case.case_facts,
            dispute_focus=case.dispute_focus or [],
            court_opinion=case.court_opinion,
            judgment_result=case.judgment_result,
            related_articles=case.related_articles or [],
            legal_basis=case.legal_basis,
            keywords=case.keywords or [],
            tags=case.tags or [],
            precedent_value=case.precedent_value,
            citation_count=case.citation_count,
            status=case.status.value,
            is_public=case.is_public,
            source_url=case.source_url,
            created_by=str(case.created_by) if case.created_by else None,
            created_at=case.created_at,
            updated_at=case.updated_at
        )
        
    except ConflictException as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except ValidationException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/search", response_model=CaseSearchResponse)
async def search_cases(
    keyword: str = Query(None, description="搜索关键词"),
    case_type: str = Query(None, description="案件类型"),
    court_name: str = Query(None, description="法院名称"),
    date_from: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    date_to: str = Query(None, description="结束日期 (YYYY-MM-DD)"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """搜索法律案例"""
    
    logger.info(f"用户 {current_user.username} 搜索案例: keyword={keyword}")
    
    try:
        # 解析日期
        date_from_obj = None
        date_to_obj = None
        
        if date_from:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, "%Y-%m-%d").date()
        
        if date_to:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, "%Y-%m-%d").date()
        
        case_service = LegalCaseService(db)
        cases, total = await case_service.search_cases(
            keyword=keyword,
            case_type=case_type,
            court_name=court_name,
            date_from=date_from_obj,
            date_to=date_to_obj,
            page=page,
            page_size=page_size,
            user_id=current_user.id
        )
        
        # 转换为响应格式
        case_items = []
        for case in cases:
            case_items.append(LegalCaseListItem(
                id=str(case.id),
                case_number=case.case_number,
                title=case.title,
                court_name=case.court_name,
                case_type=case.case_type.value,
                judgment_date=case.judgment_date,
                case_summary=case.case_summary,
                keywords=case.keywords or [],
                precedent_value=case.precedent_value,
                citation_count=case.citation_count,
                created_at=case.created_at
            ))
        
        total_pages = (total + page_size - 1) // page_size if total > 0 else 0
        
        return CaseSearchResponse(
            cases=case_items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"日期格式错误: {str(e)}")


@router.get("/{case_id}", response_model=LegalCaseResponse)
async def get_case(
    case_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取法律案例详情"""
    
    logger.info(f"用户 {current_user.username} 获取案例详情: {case_id}")
    
    try:
        import uuid
        case_uuid = uuid.UUID(case_id)
        
        case_service = LegalCaseService(db)
        case = await case_service.get_case_by_id(case_uuid)
        
        if not case:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="案例不存在")
        
        # 检查访问权限
        if not case.is_public and case.created_by != current_user.id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限访问此案例")
        
        return LegalCaseResponse(
            id=str(case.id),
            case_number=case.case_number,
            title=case.title,
            court_name=case.court_name,
            case_type=case.case_type.value,
            judgment_date=case.judgment_date,
            trial_procedure=case.trial_procedure,
            parties=case.parties or [],
            case_summary=case.case_summary,
            case_facts=case.case_facts,
            dispute_focus=case.dispute_focus or [],
            court_opinion=case.court_opinion,
            judgment_result=case.judgment_result,
            related_articles=case.related_articles or [],
            legal_basis=case.legal_basis,
            keywords=case.keywords or [],
            tags=case.tags or [],
            precedent_value=case.precedent_value,
            citation_count=case.citation_count,
            status=case.status.value,
            is_public=case.is_public,
            source_url=case.source_url,
            created_by=str(case.created_by) if case.created_by else None,
            created_at=case.created_at,
            updated_at=case.updated_at
        )
        
    except ValueError:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的案例ID格式")


@router.put("/{case_id}", response_model=LegalCaseResponse)
async def update_case(
    case_id: str,
    case_data: LegalCaseUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """更新法律案例"""
    
    logger.info(f"用户 {current_user.username} 更新案例: {case_id}")
    
    try:
        import uuid
        case_uuid = uuid.UUID(case_id)
        
        case_service = LegalCaseService(db)
        
        # 只更新非空字段
        update_data = {k: v for k, v in case_data.dict().items() if v is not None}
        
        case = await case_service.update_case(
            case_id=case_uuid,
            case_data=update_data,
            user_id=current_user.id
        )
        
        return LegalCaseResponse(
            id=str(case.id),
            case_number=case.case_number,
            title=case.title,
            court_name=case.court_name,
            case_type=case.case_type.value,
            judgment_date=case.judgment_date,
            trial_procedure=case.trial_procedure,
            parties=case.parties or [],
            case_summary=case.case_summary,
            case_facts=case.case_facts,
            dispute_focus=case.dispute_focus or [],
            court_opinion=case.court_opinion,
            judgment_result=case.judgment_result,
            related_articles=case.related_articles or [],
            legal_basis=case.legal_basis,
            keywords=case.keywords or [],
            tags=case.tags or [],
            precedent_value=case.precedent_value,
            citation_count=case.citation_count,
            status=case.status.value,
            is_public=case.is_public,
            source_url=case.source_url,
            created_by=str(case.created_by) if case.created_by else None,
            created_at=case.created_at,
            updated_at=case.updated_at
        )
        
    except ValueError:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的案例ID格式")
    except NotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValidationException as e:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))


@router.delete("/{case_id}")
async def delete_case(
    case_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """删除法律案例"""
    
    logger.info(f"用户 {current_user.username} 删除案例: {case_id}")
    
    try:
        import uuid
        case_uuid = uuid.UUID(case_id)
        
        case_service = LegalCaseService(db)
        await case_service.soft_delete_case(
            case_id=case_uuid,
            user_id=current_user.id
        )
        
        return {"message": "案例删除成功"}
        
    except ValueError:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的案例ID格式")
    except NotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValidationException as e:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))


@router.get("/statistics/overview", response_model=CaseStatisticsResponse)
async def get_case_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取案例统计信息"""
    
    logger.info(f"用户 {current_user.username} 获取案例统计")
    
    case_service = LegalCaseService(db)
    stats = await case_service.get_case_statistics()
    
    return CaseStatisticsResponse(
        total_cases=stats["total_cases"],
        case_types=stats["case_types"],
        case_years={str(k): v for k, v in stats["case_years"].items()}
    )
