"""
合同模板API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import logging
import uuid

from app.core.database import get_db
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.services.contract_template import ContractTemplateService
from app.schemas.contract_template import (
    ContractTemplateListResponse,
    ContractTemplateDetailResponse,
    ContractGenerateRequest,
    ContractGenerateResponse,
    UserContractListResponse,
    ContractDetailResponse,
    FieldDescriptionsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/templates", response_model=ContractTemplateListResponse)
async def get_template_list(
    category: Optional[str] = Query(None, description="模板分类"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取合同模板列表"""
    
    logger.info(f"用户 {current_user.username} 获取合同模板列表")
    
    # 验证分类参数
    valid_categories = ["labor", "sales", "service", "lease", "partnership", "loan", "other"]
    if category and category not in valid_categories:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的分类，支持的分类: {', '.join(valid_categories)}"
        )
    
    template_service = ContractTemplateService(db)
    templates = await template_service.get_template_list(category=category)
    
    return ContractTemplateListResponse(
        templates=templates,
        total=len(templates)
    )


@router.get("/templates/{template_id}", response_model=ContractTemplateDetailResponse)
async def get_template_detail(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取合同模板详情"""
    
    logger.info(f"用户 {current_user.username} 获取模板详情: {template_id}")
    
    template_service = ContractTemplateService(db)
    template_detail = await template_service.get_template_detail(template_id)
    
    if not template_detail:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在"
        )
    
    return ContractTemplateDetailResponse(**template_detail)


@router.post("/generate", response_model=ContractGenerateResponse)
async def generate_contract(
    generate_request: ContractGenerateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """生成合同"""
    
    logger.info(f"用户 {current_user.username} 生成合同: {generate_request.template_id}")
    
    template_service = ContractTemplateService(db)
    
    try:
        result = await template_service.generate_contract(
            template_id=generate_request.template_id,
            variables=generate_request.variables,
            user_id=current_user.id
        )
        
        return ContractGenerateResponse(**result)
        
    except Exception as e:
        logger.error(f"合同生成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/my-contracts", response_model=UserContractListResponse)
async def get_my_contracts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取我的合同列表"""
    
    logger.info(f"用户 {current_user.username} 获取合同列表")
    
    template_service = ContractTemplateService(db)
    result = await template_service.get_user_contracts(
        user_id=current_user.id,
        page=page,
        page_size=page_size
    )
    
    return UserContractListResponse(**result)


@router.get("/my-contracts/{contract_id}", response_model=ContractDetailResponse)
async def get_my_contract_detail(
    contract_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取我的合同详情"""
    
    try:
        contract_uuid = uuid.UUID(contract_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的合同ID格式"
        )
    
    template_service = ContractTemplateService(db)
    contract_detail = await template_service.get_contract_detail(
        contract_id=contract_uuid,
        user_id=current_user.id
    )
    
    if not contract_detail:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="合同不存在或无权访问"
        )
    
    return ContractDetailResponse(**contract_detail)


@router.get("/field-descriptions", response_model=FieldDescriptionsResponse)
async def get_field_descriptions(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取字段描述"""
    
    template_service = ContractTemplateService(db)
    descriptions = template_service.get_field_descriptions()
    
    return FieldDescriptionsResponse(descriptions=descriptions)


@router.get("/categories")
async def get_template_categories(
    current_user: User = Depends(get_current_user)
) -> Any:
    """获取模板分类"""
    
    categories = [
        {
            "code": "labor",
            "name": "劳动合同",
            "description": "员工聘用、劳动关系相关合同",
            "icon": "team"
        },
        {
            "code": "sales",
            "name": "买卖合同",
            "description": "商品买卖、交易相关合同",
            "icon": "shopping"
        },
        {
            "code": "service",
            "name": "服务合同",
            "description": "服务提供、委托相关合同",
            "icon": "customer-service"
        },
        {
            "code": "lease",
            "name": "租赁合同",
            "description": "房屋、设备租赁相关合同",
            "icon": "home"
        },
        {
            "code": "partnership",
            "name": "合作合同",
            "description": "合作伙伴、联营相关合同",
            "icon": "team"
        },
        {
            "code": "loan",
            "name": "借贷合同",
            "description": "借款、贷款相关合同",
            "icon": "bank"
        },
        {
            "code": "other",
            "name": "其他合同",
            "description": "其他类型合同",
            "icon": "file-text"
        }
    ]
    
    return {"categories": categories}


@router.get("/preview/{template_id}")
async def preview_template(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """预览模板（使用示例数据）"""
    
    template_service = ContractTemplateService(db)
    template_detail = await template_service.get_template_detail(template_id)
    
    if not template_detail:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在"
        )
    
    # 生成示例数据
    sample_data = {}
    if template_id == "labor_contract":
        sample_data = {
            "employer_name": "北京科技有限公司",
            "employer_address": "北京市朝阳区科技园区1号",
            "employer_legal_representative": "张三",
            "employee_name": "李四",
            "employee_id": "110101199001011234",
            "employee_address": "北京市海淀区中关村大街1号",
            "position": "软件工程师",
            "work_location": "北京市朝阳区科技园区1号",
            "contract_period": "固定期限",
            "salary": "15000",
            "start_date": "2024-02-01",
            "end_date": "2027-01-31",
            "probation_period": "3个月"
        }
    elif template_id == "sales_contract":
        sample_data = {
            "buyer_name": "北京贸易有限公司",
            "buyer_address": "北京市东城区商业街1号",
            "buyer_contact": "010-12345678",
            "seller_name": "上海制造有限公司",
            "seller_address": "上海市浦东新区工业园区1号",
            "seller_contact": "021-87654321",
            "product_name": "智能设备",
            "product_specification": "型号：ABC-123，规格：标准版",
            "quantity": "100台",
            "unit_price": "5000",
            "total_amount": "500000",
            "delivery_date": "2024-03-15",
            "payment_method": "银行转账"
        }
    elif template_id == "service_contract":
        sample_data = {
            "client_name": "北京咨询有限公司",
            "client_address": "北京市西城区金融街1号",
            "client_contact": "010-11111111",
            "service_provider_name": "上海技术服务有限公司",
            "service_provider_address": "上海市黄浦区南京路1号",
            "service_provider_contact": "021-22222222",
            "service_description": "提供IT系统开发和维护服务",
            "service_period": "2024年2月1日至2024年12月31日",
            "service_fee": "200000",
            "payment_method": "分期付款"
        }
    
    try:
        # 生成预览内容
        result = await template_service.generate_contract(
            template_id=template_id,
            variables=sample_data,
            user_id=current_user.id
        )
        
        return {
            "template_id": template_id,
            "template_name": template_detail["name"],
            "sample_data": sample_data,
            "preview_content": result["content"]
        }
        
    except Exception as e:
        logger.error(f"模板预览失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="模板预览失败"
        )
