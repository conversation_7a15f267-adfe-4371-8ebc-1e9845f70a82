"""
隐私管理API端点
"""

from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.core.privacy import get_privacy_manager, ConsentType, ConsentStatus
from app.core.audit import get_audit_logger, AuditEventType
from app.dependencies.auth import get_current_user, get_current_active_user
from app.models.user import User
from app.schemas.privacy import (
    ConsentRequest,
    ConsentResponse,
    ConsentListResponse,
    DataExportRequest,
    DataDeletionRequest
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/consent", response_model=ConsentResponse)
async def grant_consent(
    consent_data: ConsentRequest,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """授予或撤回隐私同意"""
    
    logger.info(f"用户 {current_user.username} 更新隐私同意: {consent_data.consent_type}")
    
    privacy_manager = get_privacy_manager(db)
    
    consent = await privacy_manager.record_consent(
        user_id=current_user.id,
        consent_type=consent_data.consent_type,
        status=consent_data.status,
        purpose=consent_data.purpose,
        data_categories=consent_data.data_categories,
        legal_basis=consent_data.legal_basis,
        ip_address=str(request.client.host) if request.client else None,
        user_agent=request.headers.get("user-agent") if request else None,
        expires_days=consent_data.expires_days
    )
    
    # 记录审计日志
    audit_logger = await get_audit_logger(db)
    await audit_logger.log_event(
        event_type=AuditEventType.USER_UPDATE,
        message=f"用户更新隐私同意: {consent_data.consent_type.value} -> {consent_data.status.value}",
        user_id=current_user.id,
        username=current_user.username,
        user_ip=str(request.client.host) if request.client else None,
        resource_type="consent",
        resource_id=str(consent.id),
        action="update_consent",
        details={
            "consent_type": consent_data.consent_type.value,
            "status": consent_data.status.value,
            "purpose": consent_data.purpose
        }
    )
    
    return ConsentResponse(
        id=str(consent.id),
        consent_type=consent.consent_type,
        status=consent.status,
        purpose=consent.purpose,
        data_categories=consent.data_categories,
        legal_basis=consent.legal_basis,
        granted_at=consent.granted_at,
        withdrawn_at=consent.withdrawn_at,
        expires_at=consent.expires_at,
        created_at=consent.created_at
    )


@router.get("/consents", response_model=ConsentListResponse)
async def get_user_consents(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取用户的隐私同意记录"""
    
    from sqlalchemy import select
    from app.core.privacy import UserConsent
    
    stmt = select(UserConsent).where(
        UserConsent.user_id == current_user.id
    ).order_by(UserConsent.created_at.desc())
    
    result = await db.execute(stmt)
    consents = result.scalars().all()
    
    consent_responses = []
    for consent in consents:
        consent_responses.append(ConsentResponse(
            id=str(consent.id),
            consent_type=consent.consent_type,
            status=consent.status,
            purpose=consent.purpose,
            data_categories=consent.data_categories,
            legal_basis=consent.legal_basis,
            granted_at=consent.granted_at,
            withdrawn_at=consent.withdrawn_at,
            expires_at=consent.expires_at,
            created_at=consent.created_at
        ))
    
    return ConsentListResponse(
        consents=consent_responses,
        total=len(consent_responses)
    )


@router.post("/data-export")
async def request_data_export(
    export_request: DataExportRequest,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """请求数据导出（GDPR权利）"""
    
    logger.info(f"用户 {current_user.username} 请求数据导出")
    
    # 记录审计日志
    audit_logger = await get_audit_logger(db)
    await audit_logger.log_event(
        event_type=AuditEventType.DATA_EXPORT,
        message=f"用户请求数据导出",
        user_id=current_user.id,
        username=current_user.username,
        user_ip=str(request.client.host) if request.client else None,
        resource_type="user_data",
        resource_id=str(current_user.id),
        action="export_request",
        details={
            "data_types": export_request.data_types,
            "format": export_request.format
        }
    )
    
    # TODO: 实现实际的数据导出逻辑
    # 这里应该创建一个后台任务来处理数据导出
    
    return {
        "message": "数据导出请求已提交，我们将在30天内处理您的请求",
        "request_id": f"export_{current_user.id}_{int(request.client.host.replace('.', '') if request.client else 0)}",
        "estimated_completion": "30天内"
    }


@router.post("/data-deletion")
async def request_data_deletion(
    deletion_request: DataDeletionRequest,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """请求数据删除（GDPR被遗忘权）"""
    
    logger.info(f"用户 {current_user.username} 请求数据删除")
    
    # 记录审计日志
    audit_logger = await get_audit_logger(db)
    await audit_logger.log_event(
        event_type=AuditEventType.DATA_DELETE,
        message=f"用户请求数据删除",
        user_id=current_user.id,
        username=current_user.username,
        user_ip=str(request.client.host) if request.client else None,
        resource_type="user_data",
        resource_id=str(current_user.id),
        action="deletion_request",
        details={
            "data_types": deletion_request.data_types,
            "reason": deletion_request.reason
        }
    )
    
    # TODO: 实现实际的数据删除逻辑
    # 这里应该创建一个后台任务来处理数据删除
    # 需要考虑法律要求的数据保留期限
    
    return {
        "message": "数据删除请求已提交，我们将在30天内处理您的请求",
        "request_id": f"delete_{current_user.id}_{int(request.client.host.replace('.', '') if request.client else 0)}",
        "estimated_completion": "30天内",
        "note": "某些数据可能因法律要求需要保留一定期限"
    }


@router.get("/privacy-policy")
async def get_privacy_policy() -> Any:
    """获取隐私政策"""
    
    return {
        "version": "1.0",
        "last_updated": "2024-01-01",
        "policy": {
            "data_collection": {
                "personal_data": [
                    "姓名、邮箱地址、电话号码",
                    "账户信息和偏好设置",
                    "使用记录和行为数据"
                ],
                "purpose": [
                    "提供法律服务",
                    "改善用户体验",
                    "安全和欺诈防护"
                ]
            },
            "data_sharing": {
                "third_parties": "我们不会向第三方出售您的个人数据",
                "legal_requirements": "在法律要求的情况下可能会披露数据"
            },
            "data_retention": {
                "active_users": "账户活跃期间",
                "inactive_users": "账户关闭后2年",
                "legal_data": "根据法律要求保留"
            },
            "user_rights": [
                "访问权：您可以请求查看我们持有的关于您的数据",
                "更正权：您可以要求更正不准确的数据",
                "删除权：您可以要求删除您的个人数据",
                "限制处理权：您可以要求限制对您数据的处理",
                "数据可携权：您可以要求以结构化格式获取您的数据"
            ]
        }
    }


@router.get("/data-categories")
async def get_data_categories() -> Any:
    """获取数据分类信息"""
    
    return {
        "categories": {
            "necessary": {
                "name": "必要数据",
                "description": "提供基本服务所必需的数据",
                "examples": ["账户信息", "登录凭据", "基本个人信息"],
                "legal_basis": "合同履行",
                "retention_period": "账户存续期间"
            },
            "functional": {
                "name": "功能性数据",
                "description": "改善服务功能和用户体验的数据",
                "examples": ["偏好设置", "使用历史", "搜索记录"],
                "legal_basis": "合法利益",
                "retention_period": "2年"
            },
            "analytics": {
                "name": "分析数据",
                "description": "用于分析和改进服务的数据",
                "examples": ["访问统计", "性能数据", "错误日志"],
                "legal_basis": "合法利益",
                "retention_period": "1年"
            },
            "marketing": {
                "name": "营销数据",
                "description": "用于营销和推广的数据",
                "examples": ["营销偏好", "推广响应", "活动参与"],
                "legal_basis": "同意",
                "retention_period": "同意撤回后立即删除"
            }
        }
    }
