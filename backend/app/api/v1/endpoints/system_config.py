"""
系统配置API端点
"""

from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
import logging
import json

from app.core.database import get_db
from app.dependencies.auth import get_current_user
from app.dependencies.permissions import require_admin
from app.models.user import User
from app.core.system_config import get_config_manager
from app.schemas.system_config import (
    ConfigResponse,
    ConfigUpdateRequest,
    ConfigBatchUpdateRequest,
    ConfigExportResponse,
    ConfigImportRequest,
    ConfigValidationResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/configs", response_model=Dict[str, Any])
async def get_configs(
    prefix: Optional[str] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取系统配置"""
    
    logger.info(f"管理员 {current_user.username} 获取系统配置")
    
    config_manager = await get_config_manager(db)
    configs = await config_manager.get_configs(prefix=prefix)
    
    return {
        "configs": configs,
        "total": len(configs),
        "prefix": prefix
    }


@router.get("/configs/{key}", response_model=ConfigResponse)
async def get_config(
    key: str,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取单个配置"""
    
    config_manager = await get_config_manager(db)
    value = await config_manager.get_config(key)
    
    if value is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"配置 {key} 不存在"
        )
    
    return ConfigResponse(
        key=key,
        value=value,
        type=type(value).__name__
    )


@router.put("/configs/{key}")
async def update_config(
    key: str,
    config_update: ConfigUpdateRequest,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """更新单个配置"""
    
    logger.info(f"管理员 {current_user.username} 更新配置: {key}")
    
    config_manager = await get_config_manager(db)
    
    # 验证配置值
    is_valid, error_message = await config_manager.validate_config(key, config_update.value)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_message
        )
    
    # 更新配置
    success = await config_manager.set_config(key, config_update.value, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配置更新失败"
        )
    
    return {"message": f"配置 {key} 更新成功", "key": key, "value": config_update.value}


@router.post("/configs/batch")
async def batch_update_configs(
    batch_update: ConfigBatchUpdateRequest,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """批量更新配置"""
    
    logger.info(f"管理员 {current_user.username} 批量更新配置: {len(batch_update.configs)} 项")
    
    config_manager = await get_config_manager(db)
    
    # 验证所有配置
    validation_errors = []
    for key, value in batch_update.configs.items():
        is_valid, error_message = await config_manager.validate_config(key, value)
        if not is_valid:
            validation_errors.append(f"{key}: {error_message}")
    
    if validation_errors:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"配置验证失败: {'; '.join(validation_errors)}"
        )
    
    # 批量更新
    success = await config_manager.update_configs(batch_update.configs, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量配置更新失败"
        )
    
    return {
        "message": f"成功更新 {len(batch_update.configs)} 项配置",
        "updated_count": len(batch_update.configs)
    }


@router.delete("/configs/{key}")
async def delete_config(
    key: str,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """删除配置"""
    
    logger.info(f"管理员 {current_user.username} 删除配置: {key}")
    
    config_manager = await get_config_manager(db)
    success = await config_manager.delete_config(key, current_user.id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配置删除失败"
        )
    
    return {"message": f"配置 {key} 删除成功"}


@router.post("/configs/reset")
async def reset_configs(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """重置配置为默认值"""
    
    logger.info(f"管理员 {current_user.username} 重置系统配置")
    
    config_manager = await get_config_manager(db)
    success = await config_manager.reset_to_defaults(current_user.id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配置重置失败"
        )
    
    return {"message": "系统配置已重置为默认值"}


@router.get("/configs/export", response_model=ConfigExportResponse)
async def export_configs(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """导出系统配置"""
    
    logger.info(f"管理员 {current_user.username} 导出系统配置")
    
    config_manager = await get_config_manager(db)
    config_data = await config_manager.export_configs()
    
    return ConfigExportResponse(**config_data)


@router.post("/configs/import")
async def import_configs(
    config_import: ConfigImportRequest,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """导入系统配置"""
    
    logger.info(f"管理员 {current_user.username} 导入系统配置")
    
    config_manager = await get_config_manager(db)
    
    # 验证导入数据
    if not config_import.configs:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="导入数据不能为空"
        )
    
    # 验证所有配置
    validation_errors = []
    for key, value in config_import.configs.items():
        is_valid, error_message = await config_manager.validate_config(key, value)
        if not is_valid:
            validation_errors.append(f"{key}: {error_message}")
    
    if validation_errors:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"配置验证失败: {'; '.join(validation_errors)}"
        )
    
    # 导入配置
    success = await config_manager.import_configs(
        {"configs": config_import.configs},
        current_user.id
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配置导入失败"
        )
    
    return {
        "message": f"成功导入 {len(config_import.configs)} 项配置",
        "imported_count": len(config_import.configs)
    }


@router.post("/configs/import/file")
async def import_configs_from_file(
    file: UploadFile = File(...),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """从文件导入系统配置"""
    
    logger.info(f"管理员 {current_user.username} 从文件导入系统配置")
    
    # 检查文件类型
    if not file.filename.endswith('.json'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只支持JSON格式的配置文件"
        )
    
    try:
        # 读取文件内容
        content = await file.read()
        config_data = json.loads(content.decode('utf-8'))
        
        config_manager = await get_config_manager(db)
        success = await config_manager.import_configs(config_data, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="配置导入失败"
            )
        
        configs_count = len(config_data.get("configs", {}))
        return {
            "message": f"成功从文件导入 {configs_count} 项配置",
            "filename": file.filename,
            "imported_count": configs_count
        }
        
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="配置文件格式错误，请确保是有效的JSON格式"
        )
    except Exception as e:
        logger.error(f"导入配置文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配置文件导入失败"
        )


@router.post("/configs/validate", response_model=ConfigValidationResponse)
async def validate_config(
    key: str,
    config_update: ConfigUpdateRequest,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """验证配置值"""
    
    config_manager = await get_config_manager(db)
    is_valid, message = await config_manager.validate_config(key, config_update.value)
    
    return ConfigValidationResponse(
        key=key,
        value=config_update.value,
        is_valid=is_valid,
        message=message
    )


@router.get("/configs/schema")
async def get_config_schema(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取配置模式"""
    
    config_manager = await get_config_manager(db)
    schema = await config_manager.get_config_schema()
    
    return {"schema": schema}


@router.get("/system/status")
async def get_system_status(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取系统状态"""
    
    config_manager = await get_config_manager(db)
    
    # 获取关键配置
    maintenance_mode = await config_manager.get_config("system.maintenance_mode", False)
    debug_mode = await config_manager.get_config("system.debug_mode", False)
    system_name = await config_manager.get_config("system.name", "AI法律助手")
    system_version = await config_manager.get_config("system.version", "1.0.0")
    
    return {
        "system_name": system_name,
        "version": system_version,
        "maintenance_mode": maintenance_mode,
        "debug_mode": debug_mode,
        "status": "maintenance" if maintenance_mode else "running",
        "timestamp": "2024-01-01T12:00:00Z"
    }
