"""
用户管理API
提供完整的用户管理功能，包括注册、登录、会话管理等
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user, get_current_active_user, require_permissions
from app.models.user import User
from app.schemas.user import UserCreate, UserResponse, UserUpdate, UserLogin
from app.services.user_service import UserService

router = APIRouter()


@router.post("/register", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    用户注册
    
    创建新用户账户，包括基本信息验证、密码加密、默认设置等
    """
    user_service = UserService(db)
    
    try:
        result = user_service.create_user(
            user_data=user_data,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        return {
            "message": "用户注册成功",
            "user": result,
            "next_steps": [
                "请验证您的邮箱地址",
                "完善个人资料",
                "阅读使用条款和隐私政策"
            ]
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户注册失败，请稍后重试"
        )


@router.post("/login", response_model=Dict[str, Any])
async def login_user(
    login_data: UserLogin,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    用户登录
    
    验证用户凭据并创建访问令牌
    """
    user_service = UserService(db)
    
    try:
        result = user_service.authenticate_user(
            email=login_data.email,
            password=login_data.password,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        return {
            "message": "登录成功",
            **result
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/logout")
async def logout_user(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    用户登出
    
    撤销当前会话令牌
    """
    user_service = UserService(db)
    
    # 从请求头获取令牌（这里需要实现令牌撤销逻辑）
    authorization = request.headers.get("authorization")
    if authorization and authorization.startswith("Bearer "):
        token = authorization.split(" ")[1]
        # 这里应该实现令牌黑名单或会话撤销
        # user_service.revoke_token(token)
    
    return {"message": "登出成功"}


@router.get("/profile", response_model=Dict[str, Any])
async def get_user_profile(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户完整资料
    
    包括基本信息、个人资料、偏好设置等
    """
    user_service = UserService(db)
    
    try:
        profile = user_service.get_user_profile(str(current_user.id))
        return {
            "message": "获取用户资料成功",
            "profile": profile
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户资料失败"
        )


@router.put("/profile", response_model=Dict[str, Any])
async def update_user_profile(
    update_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    更新当前用户资料
    
    支持更新基本信息、个人资料、偏好设置等
    """
    user_service = UserService(db)
    
    try:
        result = user_service.update_user(str(current_user.id), update_data)
        return {
            "message": "用户资料更新成功",
            "profile": result
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户资料更新失败"
        )


@router.get("/sessions", response_model=Dict[str, Any])
async def get_user_sessions(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取用户会话列表
    
    显示所有活跃和历史会话信息
    """
    user_service = UserService(db)
    
    sessions = user_service.get_user_sessions(str(current_user.id))
    return {
        "message": "获取会话列表成功",
        "sessions": sessions,
        "total_count": len(sessions),
        "active_count": len([s for s in sessions if s["is_active"]])
    }


@router.delete("/sessions/{session_id}")
async def revoke_user_session(
    session_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    撤销指定会话
    
    用户可以主动撤销其他设备的登录会话
    """
    user_service = UserService(db)
    
    success = user_service.revoke_session(str(current_user.id), session_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在或撤销失败"
        )
    
    return {"message": "会话已撤销"}


@router.delete("/sessions")
async def revoke_all_sessions(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    撤销所有会话
    
    撤销用户的所有活跃会话（除当前会话外）
    """
    user_service = UserService(db)
    
    sessions = user_service.get_user_sessions(str(current_user.id))
    revoked_count = 0
    
    for session in sessions:
        if session["is_active"]:
            success = user_service.revoke_session(str(current_user.id), session["id"])
            if success:
                revoked_count += 1
    
    return {
        "message": f"已撤销 {revoked_count} 个会话",
        "revoked_count": revoked_count
    }


@router.post("/deactivate")
async def deactivate_account(
    reason: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    停用用户账户
    
    用户主动停用自己的账户
    """
    user_service = UserService(db)
    
    success = user_service.deactivate_user(
        str(current_user.id), 
        reason or "用户主动停用账户"
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="账户停用失败"
        )
    
    return {
        "message": "账户已停用",
        "deactivated_at": datetime.utcnow().isoformat(),
        "note": "账户停用后，您将无法登录系统。如需重新激活，请联系客服。"
    }


# 管理员用户管理接口
@router.get("/admin/users", response_model=Dict[str, Any])
async def get_all_users(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    user_type: Optional[str] = Query(None, description="用户类型筛选"),
    is_active: Optional[bool] = Query(None, description="活跃状态筛选"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户列表（管理员）
    
    支持分页、搜索、筛选等功能
    """
    require_permissions(current_user, ["admin", "user_manager"])
    
    user_service = UserService(db)
    
    # 构建查询条件
    query = db.query(User)
    
    if search:
        query = query.filter(
            User.email.ilike(f"%{search}%") | 
            User.full_name.ilike(f"%{search}%")
        )
    
    if user_type:
        query = query.filter(User.user_type == user_type)
    
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    # 获取总数
    total_count = query.count()
    
    # 分页查询
    users = query.offset(skip).limit(limit).all()
    
    # 转换为响应格式
    user_list = []
    for user in users:
        try:
            profile = user_service.get_user_profile(str(user.id))
            user_list.append(profile)
        except:
            # 如果获取详细资料失败，返回基本信息
            user_list.append({
                "id": str(user.id),
                "email": user.email,
                "full_name": user.full_name,
                "user_type": user.user_type,
                "is_active": user.is_active,
                "created_at": user.created_at.isoformat()
            })
    
    return {
        "message": "获取用户列表成功",
        "users": user_list,
        "pagination": {
            "total_count": total_count,
            "skip": skip,
            "limit": limit,
            "has_more": skip + limit < total_count
        },
        "filters": {
            "search": search,
            "user_type": user_type,
            "is_active": is_active
        }
    }


@router.get("/admin/users/{user_id}", response_model=Dict[str, Any])
async def get_user_detail(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户详情（管理员）
    
    获取指定用户的完整信息
    """
    require_permissions(current_user, ["admin", "user_manager"])
    
    user_service = UserService(db)
    
    try:
        profile = user_service.get_user_profile(user_id)
        sessions = user_service.get_user_sessions(user_id)
        
        return {
            "message": "获取用户详情成功",
            "profile": profile,
            "sessions": {
                "list": sessions,
                "total_count": len(sessions),
                "active_count": len([s for s in sessions if s["is_active"]])
            }
        }
    except ValueError:
        raise HTTPException(status_code=404, detail="用户不存在")
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户详情失败"
        )


@router.put("/admin/users/{user_id}", response_model=Dict[str, Any])
async def update_user_admin(
    user_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新用户信息（管理员）
    
    管理员可以更新任意用户的信息
    """
    require_permissions(current_user, ["admin", "user_manager"])
    
    user_service = UserService(db)
    
    try:
        result = user_service.update_user(user_id, user_data)
        return {
            "message": "用户信息更新成功",
            "profile": result
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户信息更新失败"
        )


@router.post("/admin/users/{user_id}/deactivate")
async def deactivate_user_admin(
    user_id: str,
    reason: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    停用用户（管理员）
    
    管理员可以停用任意用户账户
    """
    require_permissions(current_user, ["admin"])
    
    user_service = UserService(db)
    
    success = user_service.deactivate_user(
        user_id, 
        reason or f"管理员 {current_user.email} 执行停用操作"
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户停用失败"
        )
    
    return {
        "message": "用户已停用",
        "deactivated_by": current_user.email,
        "deactivated_at": datetime.utcnow().isoformat(),
        "reason": reason
    }


@router.delete("/admin/users/{user_id}/sessions/{session_id}")
async def revoke_user_session_admin(
    user_id: str,
    session_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    撤销用户会话（管理员）
    
    管理员可以撤销任意用户的会话
    """
    require_permissions(current_user, ["admin", "user_manager"])
    
    user_service = UserService(db)
    
    success = user_service.revoke_session(user_id, session_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在或撤销失败"
        )
    
    return {
        "message": "会话已撤销",
        "revoked_by": current_user.email,
        "revoked_at": datetime.utcnow().isoformat()
    }
