"""
用户数据隐私保护API
提供个人信息匿名化、数据访问权限控制、数据删除和清理机制
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.privacy import (
    PrivacyManager, ConsentType, DataRetentionType,
    get_privacy_manager, UserConsent, DataRetentionPolicy
)
from app.core.auth import get_current_user, require_permissions
from app.models.user import User

router = APIRouter()


# Pydantic模型
class ConsentRequest(BaseModel):
    """用户同意请求"""
    consent_type: ConsentType = Field(..., description="同意类型")
    consented: bool = Field(..., description="是否同意")
    consent_version: str = Field("1.0", description="同意版本")


class ConsentResponse(BaseModel):
    """用户同意响应"""
    id: uuid.UUID
    user_id: uuid.UUID
    consent_type: str
    consented: bool
    consent_version: str
    ip_address: Optional[str]
    user_agent: Optional[str]
    expires_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DataExportRequest(BaseModel):
    """数据导出请求"""
    include_personal_data: bool = Field(True, description="包含个人数据")
    include_activity_logs: bool = Field(True, description="包含活动日志")
    include_preferences: bool = Field(True, description="包含偏好设置")
    format: str = Field("json", description="导出格式")


class DataDeletionRequest(BaseModel):
    """数据删除请求"""
    deletion_reason: str = Field(..., description="删除原因")
    hard_delete: bool = Field(False, description="是否硬删除")
    retain_legal_data: bool = Field(True, description="保留法律要求的数据")


class AnonymizationRequest(BaseModel):
    """匿名化请求"""
    fields_to_anonymize: List[str] = Field(..., description="要匿名化的字段")
    anonymization_method: str = Field("hash", description="匿名化方法")


class RetentionPolicyCreate(BaseModel):
    """数据保留策略创建"""
    policy_name: str = Field(..., description="策略名称")
    data_type: DataRetentionType = Field(..., description="数据类型")
    retention_days: int = Field(..., description="保留天数")
    description: Optional[str] = Field(None, description="策略描述")
    auto_delete: bool = Field(True, description="是否自动删除")


class RetentionPolicyResponse(BaseModel):
    """数据保留策略响应"""
    id: uuid.UUID
    policy_name: str
    data_type: str
    retention_days: int
    description: Optional[str]
    auto_delete: bool
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


@router.post("/consent", response_model=ConsentResponse)
async def record_user_consent(
    consent_request: ConsentRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    记录用户同意
    用户可以管理自己的同意记录
    """
    # 获取隐私管理器
    privacy_manager = get_privacy_manager(db)
    
    # 记录用户同意
    consent = privacy_manager.record_consent(
        user_id=str(current_user.id),
        consent_type=consent_request.consent_type,
        consented=consent_request.consented,
        consent_version=consent_request.consent_version
    )
    
    return consent


@router.get("/consent", response_model=List[ConsentResponse])
async def get_user_consents(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户的所有同意记录
    用户只能查看自己的同意记录
    """
    consents = (
        db.query(UserConsent)
        .filter(UserConsent.user_id == current_user.id)
        .order_by(UserConsent.created_at.desc())
        .all()
    )
    
    return consents


@router.get("/consent/required")
async def check_consent_required(
    consent_type: ConsentType = Query(..., description="同意类型"),
    version: str = Query("1.0", description="版本号"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    检查是否需要用户重新同意
    """
    # 获取隐私管理器
    privacy_manager = get_privacy_manager(db)
    
    # 检查是否需要同意
    needs_consent = privacy_manager.check_consent_required(
        str(current_user.id), consent_type, version
    )
    
    return {
        "needs_consent": needs_consent,
        "consent_type": consent_type.value,
        "version": version,
        "user_id": str(current_user.id)
    }


@router.post("/export-data")
async def export_user_data(
    export_request: DataExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    导出用户数据（GDPR数据可携带权）
    用户可以导出自己的所有数据
    """
    # 获取隐私管理器
    privacy_manager = get_privacy_manager(db)
    
    # 导出用户数据
    export_data = privacy_manager.export_user_data(
        str(current_user.id),
        include_personal_data=export_request.include_personal_data,
        include_activity_logs=export_request.include_activity_logs,
        include_preferences=export_request.include_preferences
    )
    
    # 记录导出操作
    from app.core.audit import get_audit_logger
    audit_logger = get_audit_logger(db)
    audit_logger.log_action(
        action="DATA_EXPORT",
        user_id=str(current_user.id),
        resource_type="USER_DATA",
        resource_id=str(current_user.id),
        details={
            "export_format": export_request.format,
            "include_personal_data": export_request.include_personal_data,
            "include_activity_logs": export_request.include_activity_logs,
            "include_preferences": export_request.include_preferences
        },
        success=True
    )
    
    return {
        "message": "数据导出完成",
        "export_id": str(uuid.uuid4()),
        "data": export_data,
        "format": export_request.format,
        "exported_at": datetime.utcnow()
    }


@router.post("/delete-data")
async def delete_user_data(
    deletion_request: DataDeletionRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除用户数据（GDPR被遗忘权）
    用户可以请求删除自己的数据
    """
    # 获取隐私管理器
    privacy_manager = get_privacy_manager(db)
    
    # 记录删除请求
    from app.core.audit import get_audit_logger
    audit_logger = get_audit_logger(db)
    audit_logger.log_action(
        action="DATA_DELETION_REQUEST",
        user_id=str(current_user.id),
        resource_type="USER_DATA",
        resource_id=str(current_user.id),
        details={
            "deletion_reason": deletion_request.deletion_reason,
            "hard_delete": deletion_request.hard_delete,
            "retain_legal_data": deletion_request.retain_legal_data
        },
        success=True
    )
    
    # 执行数据删除
    deletion_result = privacy_manager.delete_user_data(
        str(current_user.id),
        hard_delete=deletion_request.hard_delete,
        retain_legal_data=deletion_request.retain_legal_data
    )
    
    return {
        "message": "数据删除请求已处理",
        "deletion_id": str(uuid.uuid4()),
        "hard_delete": deletion_request.hard_delete,
        "deleted_data_types": deletion_result.get("deleted_data_types", []),
        "retained_data_types": deletion_result.get("retained_data_types", []),
        "processed_at": datetime.utcnow()
    }


@router.post("/anonymize-data")
async def anonymize_user_data(
    anonymization_request: AnonymizationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    匿名化用户数据
    用户可以请求匿名化特定字段
    """
    # 获取隐私管理器
    privacy_manager = get_privacy_manager(db)
    
    # 执行数据匿名化
    anonymized_data = privacy_manager.anonymize_user_data(
        str(current_user.id),
        anonymization_request.fields_to_anonymize,
        method=anonymization_request.anonymization_method
    )
    
    # 记录匿名化操作
    from app.core.audit import get_audit_logger
    audit_logger = get_audit_logger(db)
    audit_logger.log_action(
        action="DATA_ANONYMIZATION",
        user_id=str(current_user.id),
        resource_type="USER_DATA",
        resource_id=str(current_user.id),
        details={
            "fields_anonymized": anonymization_request.fields_to_anonymize,
            "anonymization_method": anonymization_request.anonymization_method
        },
        success=True
    )
    
    return {
        "message": "数据匿名化完成",
        "anonymization_id": str(uuid.uuid4()),
        "anonymized_fields": anonymization_request.fields_to_anonymize,
        "method": anonymization_request.anonymization_method,
        "anonymized_data": anonymized_data,
        "processed_at": datetime.utcnow()
    }


@router.post("/retention-policies", response_model=RetentionPolicyResponse)
async def create_retention_policy(
    policy: RetentionPolicyCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建数据保留策略
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "privacy_manager"])
    
    # 获取隐私管理器
    privacy_manager = get_privacy_manager(db)
    
    # 创建保留策略
    retention_policy = privacy_manager.create_retention_policy(
        policy_name=policy.policy_name,
        data_type=policy.data_type,
        retention_days=policy.retention_days,
        description=policy.description,
        auto_delete=policy.auto_delete
    )
    
    return retention_policy


@router.get("/retention-policies", response_model=List[RetentionPolicyResponse])
async def get_retention_policies(
    data_type: Optional[str] = Query(None, description="数据类型"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取数据保留策略列表
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "privacy_manager", "privacy_viewer"])
    
    # 构建查询
    query = db.query(DataRetentionPolicy)
    
    if data_type:
        query = query.filter(DataRetentionPolicy.data_type == data_type)
    
    if is_active is not None:
        query = query.filter(DataRetentionPolicy.is_active == is_active)
    
    policies = query.order_by(DataRetentionPolicy.created_at.desc()).all()
    
    return policies


@router.post("/cleanup-expired-data")
async def cleanup_expired_data(
    background_tasks: BackgroundTasks,
    dry_run: bool = Query(False, description="是否为试运行"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    清理过期数据
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "privacy_manager"])
    
    # 获取隐私管理器
    privacy_manager = get_privacy_manager(db)
    
    # 执行数据清理
    cleanup_result = privacy_manager.cleanup_expired_data(dry_run=dry_run)
    
    # 记录清理操作
    from app.core.audit import get_audit_logger
    audit_logger = get_audit_logger(db)
    audit_logger.log_action(
        action="DATA_CLEANUP",
        user_id=str(current_user.id),
        resource_type="SYSTEM_DATA",
        resource_id="expired_data",
        details={
            "dry_run": dry_run,
            "cleanup_summary": cleanup_result
        },
        success=True
    )
    
    return {
        "message": "数据清理完成" if not dry_run else "数据清理试运行完成",
        "cleanup_id": str(uuid.uuid4()),
        "dry_run": dry_run,
        "cleanup_summary": cleanup_result,
        "processed_at": datetime.utcnow()
    }


@router.get("/privacy-dashboard")
async def get_privacy_dashboard(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户隐私仪表板
    显示用户的隐私设置和数据使用情况
    """
    # 获取用户同意记录
    consents = (
        db.query(UserConsent)
        .filter(UserConsent.user_id == current_user.id)
        .order_by(UserConsent.created_at.desc())
        .all()
    )
    
    # 获取数据访问记录（最近30天）
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    from app.core.audit import AuditLog
    data_access_logs = (
        db.query(AuditLog)
        .filter(
            AuditLog.user_id == current_user.id,
            AuditLog.action.in_(["DATA_ACCESS", "DATA_EXPORT", "DATA_ANONYMIZATION"]),
            AuditLog.timestamp >= thirty_days_ago
        )
        .count()
    )
    
    # 获取数据保留信息
    privacy_manager = get_privacy_manager(db)
    retention_info = privacy_manager.get_user_data_retention_info(str(current_user.id))
    
    return {
        "user_id": str(current_user.id),
        "consents": [
            {
                "consent_type": consent.consent_type,
                "consented": consent.consented,
                "version": consent.consent_version,
                "created_at": consent.created_at,
                "expires_at": consent.expires_at
            }
            for consent in consents
        ],
        "data_access_count_30_days": data_access_logs,
        "retention_info": retention_info,
        "privacy_rights": {
            "data_portability": True,  # 数据可携带权
            "right_to_be_forgotten": True,  # 被遗忘权
            "data_rectification": True,  # 数据更正权
            "access_right": True,  # 数据访问权
            "consent_withdrawal": True  # 同意撤回权
        },
        "generated_at": datetime.utcnow()
    }
