"""
免责声明和用户协议API
提供免责声明管理、用户协议管理和法律风险提示功能
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.disclaimer import (
    DisclaimerManager, DisclaimerTemplate, UserAgreement,
    DisclaimerType, AgreementType, get_disclaimer_manager,
    DEFAULT_DISCLAIMERS, DEFAULT_AGREEMENTS
)
from app.core.auth import get_current_user, require_permissions
from app.models.user import User

router = APIRouter()


# Pydantic模型
class DisclaimerTemplateCreate(BaseModel):
    """免责声明模板创建请求"""
    template_name: str = Field(..., description="模板名称")
    disclaimer_type: DisclaimerType = Field(..., description="免责声明类型")
    title: str = Field(..., description="标题")
    content: str = Field(..., description="内容")
    version: str = Field("1.0", description="版本号")
    summary: Optional[str] = Field(None, description="摘要")
    is_mandatory: bool = Field(True, description="是否强制显示")
    display_order: int = Field(0, description="显示顺序")
    auto_accept_seconds: Optional[int] = Field(None, description="自动接受秒数")


class DisclaimerTemplateResponse(BaseModel):
    """免责声明模板响应模型"""
    id: uuid.UUID
    template_name: str
    disclaimer_type: str
    version: str
    title: str
    content: str
    summary: Optional[str]
    is_mandatory: bool
    display_order: int
    auto_accept_seconds: Optional[int]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserAgreementCreate(BaseModel):
    """用户协议创建请求"""
    agreement_name: str = Field(..., description="协议名称")
    agreement_type: AgreementType = Field(..., description="协议类型")
    title: str = Field(..., description="标题")
    content: str = Field(..., description="内容")
    version: str = Field("1.0", description="版本号")
    summary: Optional[str] = Field(None, description="摘要")
    effective_date: Optional[datetime] = Field(None, description="生效日期")
    expiry_date: Optional[datetime] = Field(None, description="失效日期")
    requires_acceptance: bool = Field(True, description="是否需要用户接受")


class UserAgreementResponse(BaseModel):
    """用户协议响应模型"""
    id: uuid.UUID
    agreement_name: str
    agreement_type: str
    version: str
    title: str
    content: str
    summary: Optional[str]
    effective_date: datetime
    expiry_date: Optional[datetime]
    is_active: bool
    requires_acceptance: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


@router.post("/disclaimer-templates", response_model=DisclaimerTemplateResponse)
async def create_disclaimer_template(
    template: DisclaimerTemplateCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建免责声明模板
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "legal_manager"])
    
    # 获取免责声明管理器
    disclaimer_manager = get_disclaimer_manager(db)
    
    # 创建模板
    try:
        new_template = disclaimer_manager.create_disclaimer_template(
            template_name=template.template_name,
            disclaimer_type=template.disclaimer_type,
            title=template.title,
            content=template.content,
            version=template.version,
            summary=template.summary,
            is_mandatory=template.is_mandatory,
            display_order=template.display_order,
            auto_accept_seconds=template.auto_accept_seconds
        )
        
        return new_template
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建免责声明模板失败: {str(e)}")


@router.get("/disclaimer-templates", response_model=List[DisclaimerTemplateResponse])
async def get_disclaimer_templates(
    disclaimer_type: Optional[str] = Query(None, description="免责声明类型"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    limit: int = Query(100, le=1000, description="限制数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取免责声明模板列表
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "legal_manager", "legal_viewer"])
    
    # 构建查询
    query = db.query(DisclaimerTemplate)
    
    # 应用过滤条件
    if disclaimer_type:
        query = query.filter(DisclaimerTemplate.disclaimer_type == disclaimer_type)
    
    if is_active is not None:
        query = query.filter(DisclaimerTemplate.is_active == is_active)
    
    # 排序和分页
    templates = (
        query.order_by(DisclaimerTemplate.display_order, DisclaimerTemplate.created_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    return templates


@router.get("/disclaimer-templates/{template_id}", response_model=DisclaimerTemplateResponse)
async def get_disclaimer_template(
    template_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取单个免责声明模板详情
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "legal_manager", "legal_viewer"])
    
    template = db.query(DisclaimerTemplate).filter(DisclaimerTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="免责声明模板不存在")
    
    return template


@router.post("/user-agreements", response_model=UserAgreementResponse)
async def create_user_agreement(
    agreement: UserAgreementCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建用户协议
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "legal_manager"])
    
    # 获取免责声明管理器
    disclaimer_manager = get_disclaimer_manager(db)
    
    # 创建协议
    try:
        new_agreement = disclaimer_manager.create_user_agreement(
            agreement_name=agreement.agreement_name,
            agreement_type=agreement.agreement_type,
            title=agreement.title,
            content=agreement.content,
            version=agreement.version,
            summary=agreement.summary,
            effective_date=agreement.effective_date,
            expiry_date=agreement.expiry_date,
            requires_acceptance=agreement.requires_acceptance
        )
        
        return new_agreement
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建用户协议失败: {str(e)}")


@router.get("/user-agreements", response_model=List[UserAgreementResponse])
async def get_user_agreements(
    agreement_type: Optional[str] = Query(None, description="协议类型"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    limit: int = Query(100, le=1000, description="限制数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户协议列表
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "legal_manager", "legal_viewer"])
    
    # 构建查询
    query = db.query(UserAgreement)
    
    # 应用过滤条件
    if agreement_type:
        query = query.filter(UserAgreement.agreement_type == agreement_type)
    
    if is_active is not None:
        query = query.filter(UserAgreement.is_active == is_active)
    
    # 排序和分页
    agreements = (
        query.order_by(UserAgreement.created_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    return agreements


@router.get("/user-agreements/{agreement_id}", response_model=UserAgreementResponse)
async def get_user_agreement(
    agreement_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取单个用户协议详情
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "legal_manager", "legal_viewer"])
    
    agreement = db.query(UserAgreement).filter(UserAgreement.id == agreement_id).first()
    if not agreement:
        raise HTTPException(status_code=404, detail="用户协议不存在")
    
    return agreement


@router.get("/ai-disclaimer")
async def get_ai_disclaimer(
    db: Session = Depends(get_db)
):
    """
    获取AI回答免责声明
    公开接口，无需认证
    """
    # 获取免责声明管理器
    disclaimer_manager = get_disclaimer_manager(db)
    
    # 获取AI回答免责声明
    disclaimer = disclaimer_manager.get_ai_response_disclaimer()
    
    if not disclaimer:
        # 返回默认免责声明
        disclaimer = (
            "⚠️ 重要提示：本AI助手提供的信息仅供参考，不构成正式的法律建议。"
            "具体法律问题请咨询专业律师。我们不对因使用本服务而产生的任何后果承担责任。"
        )
    
    return {
        "disclaimer": disclaimer,
        "type": "ai_response",
        "timestamp": datetime.utcnow()
    }


@router.get("/legal-warning")
async def get_legal_warning(
    db: Session = Depends(get_db)
):
    """
    获取法律风险提示
    公开接口，无需认证
    """
    # 获取免责声明管理器
    disclaimer_manager = get_disclaimer_manager(db)
    
    # 获取法律风险提示
    warning = disclaimer_manager.get_legal_risk_warning()
    
    return {
        "warning": warning,
        "type": "legal_risk",
        "timestamp": datetime.utcnow()
    }


@router.get("/active-disclaimers")
async def get_active_disclaimers(
    disclaimer_type: Optional[str] = Query(None, description="免责声明类型"),
    db: Session = Depends(get_db)
):
    """
    获取激活的免责声明
    公开接口，无需认证
    """
    # 获取免责声明管理器
    disclaimer_manager = get_disclaimer_manager(db)
    
    # 获取激活的免责声明
    disclaimer_type_enum = None
    if disclaimer_type:
        try:
            disclaimer_type_enum = DisclaimerType(disclaimer_type)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的免责声明类型")
    
    disclaimers = disclaimer_manager.get_active_disclaimers(disclaimer_type_enum)
    
    return {
        "disclaimers": [
            {
                "id": str(disclaimer.id),
                "title": disclaimer.title,
                "content": disclaimer.content,
                "summary": disclaimer.summary,
                "type": disclaimer.disclaimer_type,
                "version": disclaimer.version,
                "is_mandatory": disclaimer.is_mandatory,
                "display_order": disclaimer.display_order,
                "auto_accept_seconds": disclaimer.auto_accept_seconds
            }
            for disclaimer in disclaimers
        ],
        "count": len(disclaimers),
        "timestamp": datetime.utcnow()
    }


@router.get("/active-agreements")
async def get_active_agreements(
    agreement_type: Optional[str] = Query(None, description="协议类型"),
    db: Session = Depends(get_db)
):
    """
    获取激活的用户协议
    公开接口，无需认证
    """
    # 获取免责声明管理器
    disclaimer_manager = get_disclaimer_manager(db)
    
    # 获取激活的用户协议
    agreement_type_enum = None
    if agreement_type:
        try:
            agreement_type_enum = AgreementType(agreement_type)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的协议类型")
    
    agreements = disclaimer_manager.get_active_agreements(agreement_type_enum)
    
    return {
        "agreements": [
            {
                "id": str(agreement.id),
                "title": agreement.title,
                "content": agreement.content,
                "summary": agreement.summary,
                "type": agreement.agreement_type,
                "version": agreement.version,
                "effective_date": agreement.effective_date.isoformat(),
                "expiry_date": agreement.expiry_date.isoformat() if agreement.expiry_date else None,
                "requires_acceptance": agreement.requires_acceptance
            }
            for agreement in agreements
        ],
        "count": len(agreements),
        "timestamp": datetime.utcnow()
    }


@router.post("/initialize-defaults")
async def initialize_default_disclaimers_and_agreements(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    初始化默认的免责声明和用户协议
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin"])
    
    # 获取免责声明管理器
    disclaimer_manager = get_disclaimer_manager(db)
    
    created_disclaimers = []
    created_agreements = []
    
    try:
        # 创建默认免责声明
        for disclaimer_data in DEFAULT_DISCLAIMERS:
            # 检查是否已存在
            existing = (
                db.query(DisclaimerTemplate)
                .filter(
                    DisclaimerTemplate.template_name == disclaimer_data["template_name"],
                    DisclaimerTemplate.disclaimer_type == disclaimer_data["disclaimer_type"].value
                )
                .first()
            )
            
            if not existing:
                template = disclaimer_manager.create_disclaimer_template(**disclaimer_data)
                created_disclaimers.append(template.template_name)
        
        # 创建默认用户协议
        for agreement_data in DEFAULT_AGREEMENTS:
            # 检查是否已存在
            existing = (
                db.query(UserAgreement)
                .filter(
                    UserAgreement.agreement_name == agreement_data["agreement_name"],
                    UserAgreement.agreement_type == agreement_data["agreement_type"].value
                )
                .first()
            )
            
            if not existing:
                agreement = disclaimer_manager.create_user_agreement(**agreement_data)
                created_agreements.append(agreement.agreement_name)
        
        return {
            "message": "默认免责声明和用户协议初始化完成",
            "created_disclaimers": created_disclaimers,
            "created_agreements": created_agreements,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")
