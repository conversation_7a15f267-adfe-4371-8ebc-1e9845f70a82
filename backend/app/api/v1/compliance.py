"""
数据来源合规审查API
提供数据源注册、合规检查、使用协议管理等功能
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.compliance import (
    ComplianceManager, DataSource, DataUsageAgreement,
    DataSourceType, LicenseType, ComplianceStatus,
    get_compliance_manager
)
from app.core.auth import get_current_user, require_permissions
from app.models.user import User

router = APIRouter()


# Pydantic模型
class DataSourceCreate(BaseModel):
    """数据源创建请求"""
    source_name: str = Field(..., description="数据源名称")
    source_type: DataSourceType = Field(..., description="数据源类型")
    license_type: LicenseType = Field(..., description="许可证类型")
    source_url: Optional[str] = Field(None, description="数据源URL")
    provider: Optional[str] = Field(None, description="数据提供方")
    license_text: Optional[str] = Field(None, description="许可证文本")
    license_url: Optional[str] = Field(None, description="许可证URL")
    commercial_use_allowed: bool = Field(False, description="是否允许商业使用")
    modification_allowed: bool = Field(False, description="是否允许修改")
    redistribution_allowed: bool = Field(False, description="是否允许再分发")
    attribution_required: bool = Field(True, description="是否需要署名")
    cost_per_use: Optional[float] = Field(None, description="每次使用费用")
    monthly_cost: Optional[float] = Field(None, description="月度费用")
    contact_person: Optional[str] = Field(None, description="联系人")
    contact_email: Optional[str] = Field(None, description="联系邮箱")
    contact_phone: Optional[str] = Field(None, description="联系电话")
    notes: Optional[str] = Field(None, description="备注")


class DataSourceResponse(BaseModel):
    """数据源响应模型"""
    id: uuid.UUID
    source_name: str
    source_type: str
    source_url: Optional[str]
    provider: Optional[str]
    license_type: str
    license_text: Optional[str]
    license_url: Optional[str]
    commercial_use_allowed: bool
    modification_allowed: bool
    redistribution_allowed: bool
    attribution_required: bool
    compliance_status: str
    last_review_date: Optional[datetime]
    next_review_date: Optional[datetime]
    usage_count: int
    last_used_date: Optional[datetime]
    cost_per_use: Optional[float]
    monthly_cost: Optional[float]
    contact_person: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]
    notes: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UsageAgreementCreate(BaseModel):
    """使用协议创建请求"""
    agreement_name: str = Field(..., description="协议名称")
    data_source_id: uuid.UUID = Field(..., description="数据源ID")
    provider_name: str = Field(..., description="提供方名称")
    agreement_content: str = Field(..., description="协议内容")
    effective_date: datetime = Field(..., description="生效日期")
    expiry_date: Optional[datetime] = Field(None, description="到期日期")
    agreement_number: Optional[str] = Field(None, description="协议编号")
    provider_contact: Optional[str] = Field(None, description="提供方联系方式")
    usage_scope: Optional[str] = Field(None, description="使用范围")
    restrictions: Optional[str] = Field(None, description="使用限制")
    auto_renewal: bool = Field(False, description="是否自动续约")
    total_cost: Optional[float] = Field(None, description="总费用")
    payment_terms: Optional[str] = Field(None, description="付款条款")


class UsageAgreementResponse(BaseModel):
    """使用协议响应模型"""
    id: uuid.UUID
    agreement_name: str
    agreement_number: Optional[str]
    data_source_id: uuid.UUID
    provider_name: str
    provider_contact: Optional[str]
    agreement_content: str
    usage_scope: Optional[str]
    restrictions: Optional[str]
    effective_date: datetime
    expiry_date: Optional[datetime]
    auto_renewal: bool
    total_cost: Optional[float]
    payment_terms: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ComplianceCheckRequest(BaseModel):
    """合规检查请求"""
    data_source_id: uuid.UUID = Field(..., description="数据源ID")
    intended_use: str = Field("commercial", description="预期用途")


@router.post("/data-sources", response_model=DataSourceResponse)
async def create_data_source(
    data_source: DataSourceCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    注册新的数据源
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager"])
    
    # 获取合规管理器
    compliance_manager = get_compliance_manager(db)
    
    # 创建数据源
    try:
        new_data_source = compliance_manager.register_data_source(
            source_name=data_source.source_name,
            source_type=data_source.source_type,
            license_type=data_source.license_type,
            source_url=data_source.source_url,
            provider=data_source.provider,
            license_text=data_source.license_text,
            license_url=data_source.license_url,
            commercial_use_allowed=data_source.commercial_use_allowed,
            modification_allowed=data_source.modification_allowed,
            redistribution_allowed=data_source.redistribution_allowed,
            attribution_required=data_source.attribution_required,
            cost_per_use=data_source.cost_per_use,
            monthly_cost=data_source.monthly_cost,
            contact_person=data_source.contact_person,
            contact_email=data_source.contact_email,
            contact_phone=data_source.contact_phone,
            notes=data_source.notes
        )
        
        return new_data_source
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建数据源失败: {str(e)}")


@router.get("/data-sources", response_model=List[DataSourceResponse])
async def get_data_sources(
    source_type: Optional[str] = Query(None, description="数据源类型"),
    license_type: Optional[str] = Query(None, description="许可证类型"),
    compliance_status: Optional[str] = Query(None, description="合规状态"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    limit: int = Query(100, le=1000, description="限制数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取数据源列表
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager", "compliance_viewer"])
    
    # 构建查询
    query = db.query(DataSource)
    
    # 应用过滤条件
    if source_type:
        query = query.filter(DataSource.source_type == source_type)
    
    if license_type:
        query = query.filter(DataSource.license_type == license_type)
    
    if compliance_status:
        query = query.filter(DataSource.compliance_status == compliance_status)
    
    if is_active is not None:
        query = query.filter(DataSource.is_active == is_active)
    
    # 排序和分页
    data_sources = (
        query.order_by(DataSource.created_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    return data_sources


@router.get("/data-sources/{source_id}", response_model=DataSourceResponse)
async def get_data_source(
    source_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取单个数据源详情
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager", "compliance_viewer"])
    
    data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
    if not data_source:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    return data_source


@router.post("/data-sources/{source_id}/check-compliance")
async def check_data_source_compliance(
    source_id: uuid.UUID,
    intended_use: str = Query("commercial", description="预期用途"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    检查数据源合规性
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager"])
    
    # 获取合规管理器
    compliance_manager = get_compliance_manager(db)
    
    # 检查合规性
    result = compliance_manager.check_data_source_compliance(
        data_source_id=str(source_id),
        intended_use=intended_use
    )
    
    return result


@router.post("/data-sources/{source_id}/record-usage")
async def record_data_usage(
    source_id: uuid.UUID,
    usage_context: Optional[str] = Query(None, description="使用上下文"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    记录数据使用
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager", "data_user"])
    
    # 获取合规管理器
    compliance_manager = get_compliance_manager(db)
    
    # 记录使用
    success = compliance_manager.record_data_usage(
        data_source_id=str(source_id),
        usage_context=usage_context
    )
    
    if not success:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    return {"message": "数据使用已记录", "success": True}


@router.post("/usage-agreements", response_model=UsageAgreementResponse)
async def create_usage_agreement(
    agreement: UsageAgreementCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建数据使用协议
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager"])
    
    # 检查数据源是否存在
    data_source = db.query(DataSource).filter(DataSource.id == agreement.data_source_id).first()
    if not data_source:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    # 获取合规管理器
    compliance_manager = get_compliance_manager(db)
    
    # 创建使用协议
    try:
        new_agreement = compliance_manager.create_usage_agreement(
            agreement_name=agreement.agreement_name,
            data_source_id=str(agreement.data_source_id),
            provider_name=agreement.provider_name,
            agreement_content=agreement.agreement_content,
            effective_date=agreement.effective_date,
            expiry_date=agreement.expiry_date,
            agreement_number=agreement.agreement_number,
            provider_contact=agreement.provider_contact,
            usage_scope=agreement.usage_scope,
            restrictions=agreement.restrictions,
            auto_renewal=agreement.auto_renewal,
            total_cost=agreement.total_cost,
            payment_terms=agreement.payment_terms
        )
        
        return new_agreement
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建使用协议失败: {str(e)}")


@router.get("/usage-agreements", response_model=List[UsageAgreementResponse])
async def get_usage_agreements(
    data_source_id: Optional[str] = Query(None, description="数据源ID"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    limit: int = Query(100, le=1000, description="限制数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取使用协议列表
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager", "compliance_viewer"])
    
    # 构建查询
    query = db.query(DataUsageAgreement)
    
    # 应用过滤条件
    if data_source_id:
        try:
            source_uuid = uuid.UUID(data_source_id)
            query = query.filter(DataUsageAgreement.data_source_id == source_uuid)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的数据源ID格式")
    
    if is_active is not None:
        query = query.filter(DataUsageAgreement.is_active == is_active)
    
    # 排序和分页
    agreements = (
        query.order_by(DataUsageAgreement.created_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    return agreements


@router.get("/usage-agreements/{agreement_id}", response_model=UsageAgreementResponse)
async def get_usage_agreement(
    agreement_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取单个使用协议详情
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager", "compliance_viewer"])
    
    agreement = db.query(DataUsageAgreement).filter(DataUsageAgreement.id == agreement_id).first()
    if not agreement:
        raise HTTPException(status_code=404, detail="使用协议不存在")
    
    return agreement


@router.get("/compliance-report")
async def get_compliance_report(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取合规报告
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "compliance_manager"])
    
    # 统计数据源
    total_sources = db.query(DataSource).count()
    active_sources = db.query(DataSource).filter(DataSource.is_active == True).count()
    compliant_sources = db.query(DataSource).filter(
        DataSource.compliance_status == ComplianceStatus.COMPLIANT.value
    ).count()
    
    # 统计使用协议
    total_agreements = db.query(DataUsageAgreement).count()
    active_agreements = db.query(DataUsageAgreement).filter(
        DataUsageAgreement.is_active == True
    ).count()
    
    # 即将到期的协议
    thirty_days_later = datetime.utcnow() + timedelta(days=30)
    expiring_agreements = db.query(DataUsageAgreement).filter(
        DataUsageAgreement.expiry_date <= thirty_days_later,
        DataUsageAgreement.is_active == True
    ).count()
    
    # 需要审查的数据源
    sources_need_review = db.query(DataSource).filter(
        DataSource.next_review_date <= datetime.utcnow(),
        DataSource.is_active == True
    ).count()
    
    return {
        "data_sources": {
            "total": total_sources,
            "active": active_sources,
            "compliant": compliant_sources,
            "compliance_rate": compliant_sources / total_sources if total_sources > 0 else 0,
            "need_review": sources_need_review
        },
        "usage_agreements": {
            "total": total_agreements,
            "active": active_agreements,
            "expiring_soon": expiring_agreements
        },
        "generated_at": datetime.utcnow()
    }
