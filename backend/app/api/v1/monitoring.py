"""
系统监控API
提供性能监控、系统状态检查、缓存管理等功能
"""

import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.performance import get_performance_monitor, get_cache_manager
from app.core.auth import get_current_user, require_permissions
from app.models.user import User

router = APIRouter()


# Pydantic模型
class SystemHealthResponse(BaseModel):
    """系统健康状态响应"""
    status: str = Field(..., description="系统状态")
    timestamp: datetime = Field(..., description="检查时间")
    services: Dict[str, Any] = Field(..., description="服务状态")
    performance: Dict[str, Any] = Field(..., description="性能指标")
    alerts: List[Dict[str, Any]] = Field(..., description="告警信息")


class PerformanceMetricsResponse(BaseModel):
    """性能指标响应"""
    cpu_usage: float = Field(..., description="CPU使用率")
    memory_usage: float = Field(..., description="内存使用率")
    disk_usage: float = Field(..., description="磁盘使用率")
    database_connections: int = Field(..., description="数据库连接数")
    redis_connections: int = Field(..., description="Redis连接数")
    timestamp: datetime = Field(..., description="采集时间")


class CacheStatsResponse(BaseModel):
    """缓存统计响应"""
    connected_clients: int = Field(..., description="连接客户端数")
    used_memory: int = Field(..., description="已使用内存")
    used_memory_human: str = Field(..., description="已使用内存（人类可读）")
    keyspace_hits: int = Field(..., description="缓存命中次数")
    keyspace_misses: int = Field(..., description="缓存未命中次数")
    hit_rate: float = Field(..., description="缓存命中率")
    total_commands_processed: int = Field(..., description="总处理命令数")
    uptime_in_seconds: int = Field(..., description="运行时间（秒）")


@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(
    db: Session = Depends(get_db)
):
    """
    获取系统健康状态
    公开接口，用于健康检查
    """
    try:
        # 检查数据库连接
        db_status = "healthy"
        try:
            db.execute("SELECT 1")
        except Exception as e:
            db_status = f"unhealthy: {str(e)}"
        
        # 检查Redis连接
        redis_status = "healthy"
        try:
            cache_manager = get_cache_manager()
            if cache_manager.redis_client:
                await cache_manager.redis_client.ping()
        except Exception as e:
            redis_status = f"unhealthy: {str(e)}"
        
        # 获取性能指标
        performance_monitor = get_performance_monitor()
        latest_metrics = None
        if performance_monitor.performance_history:
            latest_metrics = performance_monitor.performance_history[-1]
        
        # 检查性能告警
        alerts = performance_monitor.check_performance_alerts()
        
        # 确定整体状态
        overall_status = "healthy"
        if db_status != "healthy" or redis_status != "healthy":
            overall_status = "degraded"
        if alerts and any(alert["severity"] == "critical" for alert in alerts):
            overall_status = "unhealthy"
        
        return SystemHealthResponse(
            status=overall_status,
            timestamp=datetime.utcnow(),
            services={
                "database": db_status,
                "redis": redis_status,
                "application": "healthy"
            },
            performance={
                "cpu_usage": latest_metrics.cpu_usage if latest_metrics else 0,
                "memory_usage": latest_metrics.memory_usage if latest_metrics else 0,
                "disk_usage": latest_metrics.disk_usage if latest_metrics else 0,
                "database_connections": latest_metrics.database_connections if latest_metrics else 0
            },
            alerts=alerts
        )
        
    except Exception as e:
        return SystemHealthResponse(
            status="error",
            timestamp=datetime.utcnow(),
            services={"error": str(e)},
            performance={},
            alerts=[{
                "type": "system_error",
                "severity": "critical",
                "message": f"健康检查失败: {str(e)}",
                "timestamp": datetime.utcnow()
            }]
        )


@router.get("/metrics/current")
async def get_current_metrics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前性能指标
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "monitor_viewer"])
    
    # 获取性能监控器
    performance_monitor = get_performance_monitor()
    
    # 收集最新指标
    current_metrics = performance_monitor.collect_system_metrics()
    
    return {
        "cpu_usage": current_metrics.cpu_usage,
        "memory_usage": current_metrics.memory_usage,
        "disk_usage": current_metrics.disk_usage,
        "network_io": current_metrics.network_io,
        "database_connections": current_metrics.database_connections,
        "redis_connections": current_metrics.redis_connections,
        "timestamp": current_metrics.timestamp
    }


@router.get("/metrics/summary")
async def get_metrics_summary(
    minutes: int = Query(60, ge=1, le=1440, description="统计时间范围（分钟）"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取性能指标摘要
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "monitor_viewer"])
    
    # 获取性能监控器
    performance_monitor = get_performance_monitor()
    
    # 获取性能摘要
    summary = performance_monitor.get_performance_summary(minutes)
    
    return summary


@router.get("/metrics/alerts")
async def get_performance_alerts(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取性能告警
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "monitor_viewer"])
    
    # 获取性能监控器
    performance_monitor = get_performance_monitor()
    
    # 获取告警信息
    alerts = performance_monitor.check_performance_alerts()
    
    return {
        "alerts": alerts,
        "alert_count": len(alerts),
        "critical_count": len([a for a in alerts if a["severity"] == "critical"]),
        "warning_count": len([a for a in alerts if a["severity"] == "warning"]),
        "generated_at": datetime.utcnow()
    }


@router.get("/cache/stats", response_model=CacheStatsResponse)
async def get_cache_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取缓存统计信息
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "monitor_viewer"])
    
    # 获取缓存管理器
    cache_manager = get_cache_manager()
    
    # 获取缓存统计
    stats = await cache_manager.get_cache_stats()
    
    if not stats:
        raise HTTPException(status_code=503, detail="无法获取缓存统计信息")
    
    return CacheStatsResponse(**stats)


@router.post("/cache/clear")
async def clear_cache(
    pattern: str = Query("*", description="清除模式"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    清除缓存
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "cache_manager"])
    
    # 获取缓存管理器
    cache_manager = get_cache_manager()
    
    # 清除缓存
    cleared_count = await cache_manager.clear_pattern(pattern)
    
    # 记录操作
    from app.core.audit import get_audit_logger
    audit_logger = get_audit_logger(db)
    audit_logger.log_action(
        action="CACHE_CLEAR",
        user_id=str(current_user.id),
        resource_type="CACHE",
        resource_id=pattern,
        details={
            "pattern": pattern,
            "cleared_count": cleared_count
        },
        success=True
    )
    
    return {
        "message": f"缓存清除完成",
        "pattern": pattern,
        "cleared_count": cleared_count,
        "cleared_at": datetime.utcnow()
    }


@router.get("/performance/history")
async def get_performance_history(
    hours: int = Query(24, ge=1, le=168, description="历史时间范围（小时）"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取性能历史数据
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "monitor_viewer"])
    
    # 获取性能监控器
    performance_monitor = get_performance_monitor()
    
    # 计算时间范围
    cutoff_time = datetime.utcnow() - timedelta(hours=hours)
    
    # 过滤历史数据
    history_data = [
        {
            "timestamp": metrics.timestamp,
            "cpu_usage": metrics.cpu_usage,
            "memory_usage": metrics.memory_usage,
            "disk_usage": metrics.disk_usage,
            "database_connections": metrics.database_connections,
            "redis_connections": metrics.redis_connections,
            "network_io": metrics.network_io
        }
        for metrics in performance_monitor.performance_history
        if metrics.timestamp >= cutoff_time
    ]
    
    return {
        "time_range_hours": hours,
        "data_points": len(history_data),
        "history": history_data,
        "generated_at": datetime.utcnow()
    }


@router.post("/system/optimize")
async def optimize_system(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    系统优化
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin"])
    
    # 添加后台优化任务
    background_tasks.add_task(run_system_optimization, str(current_user.id), db)
    
    return {
        "message": "系统优化任务已启动",
        "optimization_id": f"opt_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
        "started_at": datetime.utcnow()
    }


@router.get("/system/status")
async def get_system_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取系统状态概览
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "monitor_viewer"])
    
    # 获取各种统计信息
    performance_monitor = get_performance_monitor()
    cache_manager = get_cache_manager()
    
    # 获取最新性能指标
    latest_metrics = None
    if performance_monitor.performance_history:
        latest_metrics = performance_monitor.performance_history[-1]
    
    # 获取缓存统计
    cache_stats = await cache_manager.get_cache_stats()
    
    # 获取告警信息
    alerts = performance_monitor.check_performance_alerts()
    
    # 获取数据库统计
    try:
        db_stats = db.execute("SELECT count(*) as active_connections FROM pg_stat_activity").fetchone()
        active_connections = db_stats[0] if db_stats else 0
    except Exception:
        active_connections = 0
    
    return {
        "system_info": {
            "status": "运行中",
            "uptime": "计算中...",  # 可以添加系统启动时间计算
            "version": "1.0.0"
        },
        "performance": {
            "cpu_usage": latest_metrics.cpu_usage if latest_metrics else 0,
            "memory_usage": latest_metrics.memory_usage if latest_metrics else 0,
            "disk_usage": latest_metrics.disk_usage if latest_metrics else 0
        },
        "database": {
            "active_connections": active_connections,
            "status": "正常"
        },
        "cache": {
            "hit_rate": cache_stats.get("hit_rate", 0),
            "used_memory": cache_stats.get("used_memory_human", "0B"),
            "connected_clients": cache_stats.get("connected_clients", 0)
        },
        "alerts": {
            "total": len(alerts),
            "critical": len([a for a in alerts if a["severity"] == "critical"]),
            "warning": len([a for a in alerts if a["severity"] == "warning"])
        },
        "generated_at": datetime.utcnow()
    }


async def run_system_optimization(user_id: str, db: Session):
    """
    运行系统优化任务
    
    Args:
        user_id: 用户ID
        db: 数据库会话
    """
    try:
        # 记录优化开始
        from app.core.audit import get_audit_logger
        audit_logger = get_audit_logger(db)
        
        optimization_tasks = []
        
        # 1. 清理过期缓存
        cache_manager = get_cache_manager()
        cleared_cache = await cache_manager.clear_pattern("expired:*")
        optimization_tasks.append(f"清理过期缓存: {cleared_cache}个键")
        
        # 2. 数据库连接池优化
        # 这里可以添加数据库优化逻辑
        optimization_tasks.append("数据库连接池优化: 完成")
        
        # 3. 内存清理
        import gc
        collected = gc.collect()
        optimization_tasks.append(f"内存垃圾回收: 清理{collected}个对象")
        
        # 记录优化完成
        audit_logger.log_action(
            action="SYSTEM_OPTIMIZATION",
            user_id=user_id,
            resource_type="SYSTEM",
            resource_id="optimization",
            details={
                "optimization_tasks": optimization_tasks,
                "completed_at": datetime.utcnow()
            },
            success=True
        )
        
    except Exception as e:
        # 记录优化失败
        audit_logger.log_action(
            action="SYSTEM_OPTIMIZATION",
            user_id=user_id,
            resource_type="SYSTEM",
            resource_id="optimization",
            details={"error": str(e)},
            success=False
        )
