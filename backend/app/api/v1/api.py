"""
API v1路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, qa, cases, contracts, documents, disputes, favorites, history, legal_cases, privacy, permissions, case_search, contract_templates, monitoring, system_config

# 创建API路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(qa.router, prefix="/qa", tags=["AI问答"])
api_router.include_router(cases.router, prefix="/cases", tags=["案例检索"])
api_router.include_router(legal_cases.router, prefix="/legal-cases", tags=["法律案例管理"])
api_router.include_router(contracts.router, prefix="/contracts", tags=["合同工具"])
api_router.include_router(documents.router, prefix="/documents", tags=["文书工具"])
api_router.include_router(disputes.router, prefix="/disputes", tags=["纠纷解决"])
api_router.include_router(favorites.router, prefix="/favorites", tags=["收藏功能"])
api_router.include_router(history.router, prefix="/history", tags=["历史记录"])
api_router.include_router(privacy.router, prefix="/privacy", tags=["隐私管理"])
api_router.include_router(permissions.router, prefix="/permissions", tags=["权限管理"])
api_router.include_router(case_search.router, prefix="/case-search", tags=["案例搜索"])
api_router.include_router(contract_templates.router, prefix="/contract-templates", tags=["合同模板"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["系统监控"])
api_router.include_router(system_config.router, prefix="/system", tags=["系统配置"])
