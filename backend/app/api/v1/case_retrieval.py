"""
案例检索API
提供完整的法律案例检索功能
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user, get_current_active_user
from app.models.user import User
from app.services.case_search_service import CaseSearchService

router = APIRouter()


@router.get("/search")
async def search_cases(
    q: str = Query(..., description="搜索关键词", min_length=1),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    sort_by: str = Query("relevance", description="排序方式: relevance, date_desc, date_asc, importance, citation"),
    court_level: Optional[str] = Query(None, description="法院级别筛选"),
    case_type: Optional[str] = Query(None, description="案例类型筛选"),
    legal_area: Optional[str] = Query(None, description="法律领域筛选"),
    court: Optional[str] = Query(None, description="法院名称筛选"),
    judgment_date_from: Optional[str] = Query(None, description="判决日期起始"),
    judgment_date_to: Optional[str] = Query(None, description="判决日期结束"),
    keywords: Optional[str] = Query(None, description="关键词筛选，多个用逗号分隔"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    搜索法律案例
    
    支持全文搜索、多维度筛选、多种排序方式
    """
    try:
        # 构建过滤条件
        filters = {}
        if court_level:
            filters["court_level"] = court_level
        if case_type:
            filters["case_type"] = case_type
        if legal_area:
            filters["legal_area"] = legal_area
        if court:
            filters["court"] = court
        if judgment_date_from:
            filters["judgment_date_from"] = judgment_date_from
        if judgment_date_to:
            filters["judgment_date_to"] = judgment_date_to
        if keywords:
            filters["keywords"] = [kw.strip() for kw in keywords.split(",") if kw.strip()]
        
        # 执行搜索
        case_search_service = CaseSearchService(db)
        result = case_search_service.search_cases(
            query=q,
            filters=filters,
            sort_by=sort_by,
            page=page,
            page_size=page_size,
            user_id=str(current_user.id)
        )
        
        return {
            "message": "搜索完成",
            "search_info": {
                "query": result["query"],
                "filters": result["filters"],
                "sort_by": sort_by,
                "took": result["took"]
            },
            "pagination": {
                "page": result["page"],
                "page_size": result["page_size"],
                "total_count": result["total_count"],
                "total_pages": result["total_pages"],
                "has_next": result["page"] < result["total_pages"],
                "has_prev": result["page"] > 1
            },
            "results": result["results"],
            "max_score": result["max_score"]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )


@router.get("/case/{case_id}")
async def get_case_detail(
    case_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取案例详情
    
    返回指定案例的完整信息
    """
    try:
        case_search_service = CaseSearchService(db)
        case_detail = case_search_service.get_case_by_id(
            case_id=case_id,
            user_id=str(current_user.id)
        )
        
        if not case_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="案例不存在"
            )
        
        return {
            "message": "获取案例详情成功",
            "case": case_detail
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取案例详情失败: {str(e)}"
        )


@router.get("/case/{case_id}/similar")
async def get_similar_cases(
    case_id: str,
    limit: int = Query(10, ge=1, le=50, description="返回数量限制"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取相似案例
    
    基于案例内容推荐相似的法律案例
    """
    try:
        case_search_service = CaseSearchService(db)
        similar_cases = case_search_service.get_similar_cases(
            case_id=case_id,
            limit=limit
        )
        
        return {
            "message": "获取相似案例成功",
            "case_id": case_id,
            "similar_cases": similar_cases,
            "count": len(similar_cases)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取相似案例失败: {str(e)}"
        )


@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., description="查询词", min_length=1),
    limit: int = Query(10, ge=1, le=20, description="建议数量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取搜索建议
    
    基于用户输入提供搜索关键词建议
    """
    try:
        case_search_service = CaseSearchService(db)
        suggestions = case_search_service.get_search_suggestions(
            query=q,
            limit=limit
        )
        
        return {
            "message": "获取搜索建议成功",
            "query": q,
            "suggestions": suggestions
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取搜索建议失败: {str(e)}"
        )


@router.get("/filters")
async def get_search_filters(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取搜索过滤器选项
    
    返回可用的筛选条件选项
    """
    try:
        # 这里应该从数据库或Elasticsearch聚合查询获取实际的选项
        filters = {
            "court_levels": [
                {"value": "supreme", "label": "最高人民法院", "count": 1250},
                {"value": "high", "label": "高级人民法院", "count": 8500},
                {"value": "intermediate", "label": "中级人民法院", "count": 45000},
                {"value": "basic", "label": "基层人民法院", "count": 120000}
            ],
            "case_types": [
                {"value": "civil", "label": "民事案件", "count": 85000},
                {"value": "criminal", "label": "刑事案件", "count": 45000},
                {"value": "administrative", "label": "行政案件", "count": 25000},
                {"value": "commercial", "label": "商事案件", "count": 35000}
            ],
            "legal_areas": [
                {"value": "contract", "label": "合同纠纷", "count": 35000},
                {"value": "tort", "label": "侵权责任", "count": 28000},
                {"value": "property", "label": "物权纠纷", "count": 15000},
                {"value": "labor", "label": "劳动争议", "count": 22000},
                {"value": "marriage", "label": "婚姻家庭", "count": 18000},
                {"value": "intellectual_property", "label": "知识产权", "count": 12000}
            ],
            "sort_options": [
                {"value": "relevance", "label": "相关性"},
                {"value": "date_desc", "label": "日期降序"},
                {"value": "date_asc", "label": "日期升序"},
                {"value": "importance", "label": "重要性"},
                {"value": "citation", "label": "引用次数"}
            ]
        }
        
        return {
            "message": "获取筛选选项成功",
            "filters": filters
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取筛选选项失败: {str(e)}"
        )


@router.get("/statistics")
async def get_search_statistics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取搜索统计信息
    
    返回案例库的统计数据
    """
    try:
        # 这里应该从Elasticsearch获取实际的统计数据
        statistics = {
            "total_cases": 175000,
            "recent_cases": 2500,  # 最近30天新增
            "court_distribution": {
                "supreme": 1250,
                "high": 8500,
                "intermediate": 45000,
                "basic": 120250
            },
            "case_type_distribution": {
                "civil": 85000,
                "criminal": 45000,
                "administrative": 25000,
                "commercial": 20000
            },
            "update_info": {
                "last_update": "2024-08-26T10:00:00Z",
                "update_frequency": "daily",
                "data_sources": ["最高人民法院", "各级人民法院", "法律数据库"]
            }
        }
        
        return {
            "message": "获取统计信息成功",
            "statistics": statistics
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.post("/index/case")
async def index_single_case(
    case_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    索引单个案例（管理员功能）
    
    将案例数据添加到搜索索引中
    """
    # 检查管理员权限
    if current_user.user_type not in ["admin", "data_manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        case_search_service = CaseSearchService(db)
        success = case_search_service.index_case(case_data)
        
        if success:
            return {
                "message": "案例索引成功",
                "case_id": case_data.get("case_id")
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="案例索引失败"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"案例索引失败: {str(e)}"
        )


@router.post("/index/bulk")
async def bulk_index_cases(
    cases_data: List[Dict[str, Any]],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    批量索引案例（管理员功能）
    
    批量将案例数据添加到搜索索引中
    """
    # 检查管理员权限
    if current_user.user_type not in ["admin", "data_manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        case_search_service = CaseSearchService(db)
        result = case_search_service.bulk_index_cases(cases_data)
        
        return {
            "message": "批量索引完成",
            "total_cases": len(cases_data),
            "success_count": result["success"],
            "failed_count": result["failed"],
            "errors": result["errors"][:10] if result["errors"] else []  # 只返回前10个错误
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量索引失败: {str(e)}"
        )
