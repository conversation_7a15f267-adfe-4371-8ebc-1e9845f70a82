"""
审计日志管理API
提供审计日志查询、安全告警管理等功能
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.audit import AuditLog, AuditEventType, AuditLevel, get_audit_logger
from app.core.anomaly_detection import (
    SecurityAlert, AnomalyType, SeverityLevel, AlertStatus,
    get_anomaly_detector
)
from app.core.auth import get_current_user, require_permissions
from app.models.user import User

router = APIRouter()


# Pydantic模型
class AuditLogResponse(BaseModel):
    """审计日志响应模型"""
    id: uuid.UUID
    event_type: str
    level: str
    user_id: Optional[uuid.UUID]
    username: Optional[str]
    user_ip: Optional[str]
    user_agent: Optional[str]
    resource_type: Optional[str]
    resource_id: Optional[str]
    action: Optional[str]
    message: str
    details: Dict[str, Any]
    request_id: Optional[str]
    session_id: Optional[str]
    success: bool
    error_code: Optional[str]
    error_message: Optional[str]
    timestamp: datetime

    class Config:
        from_attributes = True


class SecurityAlertResponse(BaseModel):
    """安全告警响应模型"""
    id: uuid.UUID
    alert_title: str
    anomaly_type: str
    severity: str
    description: str
    user_id: Optional[uuid.UUID]
    ip_address: Optional[str]
    rule_id: str
    threshold_value: Optional[float]
    actual_value: Optional[float]
    evidence: Dict[str, Any]
    status: str
    assigned_to: Optional[str]
    resolution_notes: Optional[str]
    first_detected_at: datetime
    last_detected_at: datetime
    resolved_at: Optional[datetime]
    occurrence_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AuditLogQuery(BaseModel):
    """审计日志查询参数"""
    event_type: Optional[str] = None
    level: Optional[str] = None
    user_id: Optional[uuid.UUID] = None
    user_ip: Optional[str] = None
    resource_type: Optional[str] = None
    action: Optional[str] = None
    success: Optional[bool] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(default=100, le=1000)
    offset: int = Field(default=0, ge=0)


class SecurityAlertQuery(BaseModel):
    """安全告警查询参数"""
    anomaly_type: Optional[str] = None
    severity: Optional[str] = None
    status: Optional[str] = None
    user_id: Optional[uuid.UUID] = None
    ip_address: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(default=100, le=1000)
    offset: int = Field(default=0, ge=0)


class AlertUpdateRequest(BaseModel):
    """告警更新请求"""
    status: Optional[str] = None
    assigned_to: Optional[str] = None
    resolution_notes: Optional[str] = None


@router.get("/logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    event_type: Optional[str] = Query(None, description="事件类型"),
    level: Optional[str] = Query(None, description="日志级别"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    user_ip: Optional[str] = Query(None, description="用户IP"),
    resource_type: Optional[str] = Query(None, description="资源类型"),
    action: Optional[str] = Query(None, description="操作动作"),
    success: Optional[bool] = Query(None, description="是否成功"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, le=1000, description="限制数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取审计日志列表
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "audit_viewer"])
    
    # 构建查询
    query = db.query(AuditLog)
    
    # 应用过滤条件
    if event_type:
        query = query.filter(AuditLog.event_type == event_type)
    
    if level:
        query = query.filter(AuditLog.level == level)
    
    if user_id:
        try:
            user_uuid = uuid.UUID(user_id)
            query = query.filter(AuditLog.user_id == user_uuid)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的用户ID格式")
    
    if user_ip:
        query = query.filter(AuditLog.user_ip == user_ip)
    
    if resource_type:
        query = query.filter(AuditLog.resource_type == resource_type)
    
    if action:
        query = query.filter(AuditLog.action == action)
    
    if success is not None:
        query = query.filter(AuditLog.success == success)
    
    if start_time:
        query = query.filter(AuditLog.timestamp >= start_time)
    
    if end_time:
        query = query.filter(AuditLog.timestamp <= end_time)
    
    # 排序和分页
    logs = (
        query.order_by(AuditLog.timestamp.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    return logs


@router.get("/logs/{log_id}", response_model=AuditLogResponse)
async def get_audit_log(
    log_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取单个审计日志详情
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "audit_viewer"])
    
    log = db.query(AuditLog).filter(AuditLog.id == log_id).first()
    if not log:
        raise HTTPException(status_code=404, detail="审计日志不存在")
    
    return log


@router.get("/alerts", response_model=List[SecurityAlertResponse])
async def get_security_alerts(
    anomaly_type: Optional[str] = Query(None, description="异常类型"),
    severity: Optional[str] = Query(None, description="严重程度"),
    status: Optional[str] = Query(None, description="告警状态"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    ip_address: Optional[str] = Query(None, description="IP地址"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, le=1000, description="限制数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取安全告警列表
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "security_analyst"])
    
    # 构建查询
    query = db.query(SecurityAlert)
    
    # 应用过滤条件
    if anomaly_type:
        query = query.filter(SecurityAlert.anomaly_type == anomaly_type)
    
    if severity:
        query = query.filter(SecurityAlert.severity == severity)
    
    if status:
        query = query.filter(SecurityAlert.status == status)
    
    if user_id:
        try:
            user_uuid = uuid.UUID(user_id)
            query = query.filter(SecurityAlert.user_id == user_uuid)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的用户ID格式")
    
    if ip_address:
        query = query.filter(SecurityAlert.ip_address == ip_address)
    
    if start_time:
        query = query.filter(SecurityAlert.created_at >= start_time)
    
    if end_time:
        query = query.filter(SecurityAlert.created_at <= end_time)
    
    # 排序和分页
    alerts = (
        query.order_by(SecurityAlert.created_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    return alerts


@router.get("/alerts/{alert_id}", response_model=SecurityAlertResponse)
async def get_security_alert(
    alert_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取单个安全告警详情
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "security_analyst"])
    
    alert = db.query(SecurityAlert).filter(SecurityAlert.id == alert_id).first()
    if not alert:
        raise HTTPException(status_code=404, detail="安全告警不存在")
    
    return alert


@router.put("/alerts/{alert_id}", response_model=SecurityAlertResponse)
async def update_security_alert(
    alert_id: uuid.UUID,
    update_data: AlertUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新安全告警
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "security_analyst"])
    
    alert = db.query(SecurityAlert).filter(SecurityAlert.id == alert_id).first()
    if not alert:
        raise HTTPException(status_code=404, detail="安全告警不存在")
    
    # 更新字段
    if update_data.status:
        if update_data.status not in [status.value for status in AlertStatus]:
            raise HTTPException(status_code=400, detail="无效的告警状态")
        alert.status = update_data.status
        
        # 如果状态为已解决，设置解决时间
        if update_data.status == AlertStatus.RESOLVED.value:
            alert.resolved_at = datetime.utcnow()
    
    if update_data.assigned_to is not None:
        alert.assigned_to = update_data.assigned_to
    
    if update_data.resolution_notes is not None:
        alert.resolution_notes = update_data.resolution_notes
    
    # 保存更改
    db.commit()
    db.refresh(alert)
    
    return alert


@router.post("/analyze-behavior")
async def analyze_user_behavior(
    user_id: Optional[str] = Query(None, description="用户ID"),
    ip_address: Optional[str] = Query(None, description="IP地址"),
    time_window_hours: int = Query(24, ge=1, le=168, description="时间窗口（小时）"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    分析用户行为并检测异常
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "security_analyst"])
    
    # 获取异常检测器
    anomaly_detector = get_anomaly_detector(db)
    
    # 分析用户行为
    anomalies = anomaly_detector.analyze_user_behavior(
        user_id=user_id,
        ip_address=ip_address,
        time_window_hours=time_window_hours
    )
    
    # 创建安全告警
    created_alerts = []
    for anomaly in anomalies:
        alert = anomaly_detector.create_security_alert(
            anomaly=anomaly,
            user_id=user_id
        )
        created_alerts.append(alert)
    
    return {
        "message": f"行为分析完成，检测到 {len(anomalies)} 个异常",
        "anomalies_count": len(anomalies),
        "alerts_created": len(created_alerts),
        "anomalies": anomalies
    }


@router.get("/statistics")
async def get_audit_statistics(
    days: int = Query(7, ge=1, le=90, description="统计天数"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取审计统计信息
    需要管理员权限
    """
    # 检查权限
    require_permissions(current_user, ["admin", "audit_viewer"])
    
    # 计算时间范围
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(days=days)
    
    # 审计日志统计
    total_logs = (
        db.query(AuditLog)
        .filter(AuditLog.timestamp >= start_time)
        .count()
    )
    
    error_logs = (
        db.query(AuditLog)
        .filter(
            AuditLog.timestamp >= start_time,
            AuditLog.success == False
        )
        .count()
    )
    
    # 安全告警统计
    total_alerts = (
        db.query(SecurityAlert)
        .filter(SecurityAlert.created_at >= start_time)
        .count()
    )
    
    open_alerts = (
        db.query(SecurityAlert)
        .filter(
            SecurityAlert.created_at >= start_time,
            SecurityAlert.status == AlertStatus.OPEN.value
        )
        .count()
    )
    
    critical_alerts = (
        db.query(SecurityAlert)
        .filter(
            SecurityAlert.created_at >= start_time,
            SecurityAlert.severity == SeverityLevel.CRITICAL.value
        )
        .count()
    )
    
    return {
        "time_range": {
            "start_time": start_time,
            "end_time": end_time,
            "days": days
        },
        "audit_logs": {
            "total": total_logs,
            "errors": error_logs,
            "success_rate": (total_logs - error_logs) / total_logs if total_logs > 0 else 0
        },
        "security_alerts": {
            "total": total_alerts,
            "open": open_alerts,
            "critical": critical_alerts
        }
    }
