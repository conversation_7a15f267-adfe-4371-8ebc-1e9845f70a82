"""
NLP服务测试
"""

import pytest
from app.services.nlp import LegalNLPProcessor, process_legal_text


class TestLegalNLPProcessor:
    """法律NLP处理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.nlp = LegalNLPProcessor()
    
    def test_tokenize(self):
        """测试分词功能"""
        text = "我想咨询一下劳动合同的相关问题"
        tokens = self.nlp.tokenize(text)
        
        assert isinstance(tokens, list)
        assert len(tokens) > 0
        assert "劳动合同" in tokens
        assert "咨询" in tokens
        
        # 测试停用词过滤
        assert "的" not in tokens
        assert "一下" not in tokens
    
    def test_extract_keywords(self):
        """测试关键词提取"""
        text = """
        我是一名员工，最近公司要求我签署新的劳动合同，
        但是合同中有一些条款我不太理解，比如违约金的设定，
        还有加班费的计算方式。请问这些条款是否合理？
        """
        
        # 测试TF-IDF方法
        keywords_tfidf = self.nlp.extract_keywords(text, top_k=5, method="tfidf")
        assert isinstance(keywords_tfidf, list)
        assert len(keywords_tfidf) <= 5
        assert all(isinstance(kw, tuple) and len(kw) == 2 for kw in keywords_tfidf)
        
        # 测试TextRank方法
        keywords_textrank = self.nlp.extract_keywords(text, top_k=5, method="textrank")
        assert isinstance(keywords_textrank, list)
        assert len(keywords_textrank) <= 5
        
        # 测试混合方法
        keywords_hybrid = self.nlp.extract_keywords(text, top_k=5, method="hybrid")
        assert isinstance(keywords_hybrid, list)
        assert len(keywords_hybrid) <= 5
    
    def test_classify_question(self):
        """测试问题分类"""
        # 测试劳动法问题
        labor_question = "我的工资被拖欠了，应该怎么办？"
        result = self.nlp.classify_question(labor_question)
        
        assert isinstance(result, dict)
        assert "category" in result
        assert "confidence" in result
        assert "scores" in result
        assert result["category"] == "劳动"
        assert 0 <= result["confidence"] <= 1
        
        # 测试合同问题
        contract_question = "合同违约金条款是否有效？"
        result = self.nlp.classify_question(contract_question)
        assert result["category"] == "合同"
    
    def test_extract_entities(self):
        """测试实体提取"""
        text = """
        根据《劳动合同法》第39条规定，用人单位可以解除劳动合同。
        违约金为5000元，合同期限为2年，
        联系电话：13800138000，邮箱：<EMAIL>
        """
        
        entities = self.nlp.extract_entities(text)
        
        assert isinstance(entities, dict)
        
        # 检查法条提取
        if "法条" in entities:
            assert "《劳动合同法》第39条" in entities["法条"]
        
        # 检查金额提取
        if "金额" in entities:
            assert "5000元" in entities["金额"]
        
        # 检查时间期限提取
        if "时间期限" in entities:
            assert "2年" in entities["时间期限"]
        
        # 检查电话提取
        if "电话" in entities:
            assert "13800138000" in entities["电话"]
        
        # 检查邮箱提取
        if "邮箱" in entities:
            assert "<EMAIL>" in entities["邮箱"]
    
    def test_analyze_sentiment(self):
        """测试情感分析"""
        # 测试积极情感
        positive_text = "非常感谢您的帮助，这个解答很好"
        result = self.nlp.analyze_sentiment(positive_text)
        
        assert isinstance(result, dict)
        assert "sentiment" in result
        assert "confidence" in result
        assert result["sentiment"] == "positive"
        
        # 测试消极情感
        negative_text = "这个服务太差了，完全不满意"
        result = self.nlp.analyze_sentiment(negative_text)
        assert result["sentiment"] == "negative"
        
        # 测试中性情感
        neutral_text = "请问劳动合同的期限是多久？"
        result = self.nlp.analyze_sentiment(neutral_text)
        assert result["sentiment"] == "neutral"
    
    def test_extract_intent(self):
        """测试意图识别"""
        # 测试咨询意图
        consult_text = "请问如何申请劳动仲裁？"
        result = self.nlp.extract_intent(consult_text)
        
        assert isinstance(result, dict)
        assert "intent" in result
        assert "confidence" in result
        assert result["intent"] in ["咨询", "申请"]
        
        # 测试投诉意图
        complaint_text = "我要投诉这家公司违法用工"
        result = self.nlp.extract_intent(complaint_text)
        assert result["intent"] == "投诉"
    
    def test_similarity(self):
        """测试文本相似度"""
        text1 = "劳动合同纠纷处理"
        text2 = "处理劳动合同争议"
        text3 = "房屋买卖合同"
        
        # 测试Jaccard相似度
        sim1 = self.nlp.similarity(text1, text2, method="jaccard")
        sim2 = self.nlp.similarity(text1, text3, method="jaccard")
        
        assert 0 <= sim1 <= 1
        assert 0 <= sim2 <= 1
        assert sim1 > sim2  # text1和text2更相似
        
        # 测试余弦相似度
        sim3 = self.nlp.similarity(text1, text2, method="cosine")
        assert 0 <= sim3 <= 1
        
        # 测试混合相似度
        sim4 = self.nlp.similarity(text1, text2, method="hybrid")
        assert 0 <= sim4 <= 1
    
    def test_summarize(self):
        """测试文本摘要"""
        long_text = """
        劳动合同是劳动者与用人单位确立劳动关系、明确双方权利和义务的协议。
        建立劳动关系，应当订立书面劳动合同。
        已建立劳动关系，未同时订立书面劳动合同的，应当自用工之日起一个月内订立书面劳动合同。
        用人单位与劳动者在用工前订立劳动合同的，劳动关系自用工之日起建立。
        用人单位应当建立职工名册备查。
        劳动合同由用人单位和劳动者协商一致，并经用人单位和劳动者在劳动合同文本上签字或者盖章生效。
        """
        
        # 测试基于关键词的摘要
        summary1 = self.nlp.summarize(long_text, max_sentences=2, method="keyword_based")
        assert isinstance(summary1, str)
        assert len(summary1) < len(long_text)
        
        # 测试基于位置的摘要
        summary2 = self.nlp.summarize(long_text, max_sentences=2, method="position_based")
        assert isinstance(summary2, str)
        
        # 测试混合摘要
        summary3 = self.nlp.summarize(long_text, max_sentences=2, method="hybrid")
        assert isinstance(summary3, str)
    
    def test_find_similar_cases(self):
        """测试相似案例查找"""
        query = "劳动合同违约金纠纷"
        cases = [
            "员工违反劳动合同约定，公司要求支付违约金",
            "房屋租赁合同纠纷案例",
            "劳动者提前解除合同，用人单位主张违约责任",
            "交通事故赔偿案例"
        ]
        
        similar_cases = self.nlp.find_similar_cases(query, cases, top_k=2)
        
        assert isinstance(similar_cases, list)
        assert len(similar_cases) <= 2
        assert all(isinstance(case, tuple) and len(case) == 2 for case in similar_cases)
        
        # 检查相似度排序
        if len(similar_cases) > 1:
            assert similar_cases[0][1] >= similar_cases[1][1]
    
    def test_extract_legal_concepts(self):
        """测试法律概念提取"""
        text = "根据劳动法规定，用人单位应当支付加班费，违约责任由双方协商确定"
        
        concepts = self.nlp.extract_legal_concepts(text)
        
        assert isinstance(concepts, dict)
        # 应该能提取到劳动相关概念
        if "劳动" in concepts:
            assert len(concepts["劳动"]) > 0
    
    def test_analyze_text_complexity(self):
        """测试文本复杂度分析"""
        simple_text = "这是一个简单的问题。"
        complex_text = """
        根据《中华人民共和国劳动合同法》第三十九条之规定，
        劳动者有下列情形之一的，用人单位可以解除劳动合同：
        在试用期间被证明不符合录用条件的；严重违反用人单位的规章制度的；
        严重失职，营私舞弊，给用人单位造成重大损害的。
        """
        
        simple_analysis = self.nlp.analyze_text_complexity(simple_text)
        complex_analysis = self.nlp.analyze_text_complexity(complex_text)
        
        assert isinstance(simple_analysis, dict)
        assert isinstance(complex_analysis, dict)
        
        # 检查必要字段
        required_fields = ["complexity_score", "complexity_level", "word_count", "legal_term_density"]
        for field in required_fields:
            assert field in simple_analysis
            assert field in complex_analysis
        
        # 复杂文本的复杂度应该更高
        assert complex_analysis["complexity_score"] > simple_analysis["complexity_score"]


def test_process_legal_text():
    """测试法律文本处理便捷函数"""
    text = "我想咨询劳动合同违约金的相关法律问题"
    
    result = process_legal_text(text)
    
    assert isinstance(result, dict)
    
    # 检查所有必要字段
    required_fields = [
        "tokens", "keywords", "classification", "entities", 
        "sentiment", "intent", "summary", "legal_concepts", "complexity"
    ]
    
    for field in required_fields:
        assert field in result
    
    # 检查分类结果
    assert result["classification"]["category"] in ["劳动", "合同"]
    
    # 检查意图识别
    assert result["intent"]["intent"] == "咨询"


if __name__ == "__main__":
    pytest.main([__file__])
