"""
系统配置相关的Pydantic模式
"""

from typing import Any, Dict, Optional, List, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator


class ConfigResponse(BaseModel):
    """配置响应"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")
    type: str = Field(..., description="值类型")
    description: Optional[str] = Field(None, description="配置描述")


class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    value: Any = Field(..., description="配置值")
    description: Optional[str] = Field(None, description="配置描述")


class ConfigBatchUpdateRequest(BaseModel):
    """批量配置更新请求"""
    configs: Dict[str, Any] = Field(..., description="配置键值对")
    
    @validator('configs')
    def validate_configs(cls, v):
        if not v:
            raise ValueError("配置不能为空")
        if len(v) > 100:
            raise ValueError("一次最多只能更新100项配置")
        return v


class ConfigExportResponse(BaseModel):
    """配置导出响应"""
    configs: Dict[str, Any] = Field(..., description="配置数据")
    exported_at: str = Field(..., description="导出时间")
    version: str = Field(..., description="系统版本")
    total: int = Field(..., description="配置总数")


class ConfigImportRequest(BaseModel):
    """配置导入请求"""
    configs: Dict[str, Any] = Field(..., description="配置数据")
    overwrite: bool = Field(True, description="是否覆盖现有配置")
    
    @validator('configs')
    def validate_configs(cls, v):
        if not v:
            raise ValueError("配置数据不能为空")
        return v


class ConfigValidationResponse(BaseModel):
    """配置验证响应"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")
    is_valid: bool = Field(..., description="是否有效")
    message: str = Field(..., description="验证消息")


class SystemStatus(BaseModel):
    """系统状态"""
    system_name: str = Field(..., description="系统名称")
    version: str = Field(..., description="系统版本")
    maintenance_mode: bool = Field(..., description="维护模式")
    debug_mode: bool = Field(..., description="调试模式")
    status: str = Field(..., description="运行状态")
    timestamp: str = Field(..., description="状态时间")


class ConfigCategory(BaseModel):
    """配置分类"""
    name: str = Field(..., description="分类名称")
    key: str = Field(..., description="分类键")
    description: str = Field(..., description="分类描述")
    icon: Optional[str] = Field(None, description="图标")
    configs: List[ConfigResponse] = Field(default_factory=list, description="配置列表")


class ConfigCategoriesResponse(BaseModel):
    """配置分类响应"""
    categories: List[ConfigCategory] = Field(..., description="分类列表")
    total: int = Field(..., description="总分类数")


class ConfigHistory(BaseModel):
    """配置历史"""
    id: str = Field(..., description="历史记录ID")
    key: str = Field(..., description="配置键")
    old_value: Any = Field(..., description="旧值")
    new_value: Any = Field(..., description="新值")
    changed_by: str = Field(..., description="修改者")
    changed_at: str = Field(..., description="修改时间")
    action: str = Field(..., description="操作类型")  # create, update, delete


class ConfigHistoryResponse(BaseModel):
    """配置历史响应"""
    history: List[ConfigHistory] = Field(..., description="历史记录")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")


class ConfigBackup(BaseModel):
    """配置备份"""
    id: str = Field(..., description="备份ID")
    name: str = Field(..., description="备份名称")
    description: Optional[str] = Field(None, description="备份描述")
    configs: Dict[str, Any] = Field(..., description="配置数据")
    created_by: str = Field(..., description="创建者")
    created_at: str = Field(..., description="创建时间")
    size: int = Field(..., description="备份大小")


class ConfigBackupListResponse(BaseModel):
    """配置备份列表响应"""
    backups: List[ConfigBackup] = Field(..., description="备份列表")
    total: int = Field(..., description="总备份数")


class ConfigBackupCreateRequest(BaseModel):
    """配置备份创建请求"""
    name: str = Field(..., min_length=1, max_length=100, description="备份名称")
    description: Optional[str] = Field(None, max_length=500, description="备份描述")
    include_sensitive: bool = Field(False, description="是否包含敏感配置")


class ConfigRestoreRequest(BaseModel):
    """配置恢复请求"""
    backup_id: str = Field(..., description="备份ID")
    selective_keys: Optional[List[str]] = Field(None, description="选择性恢复的配置键")
    confirm: bool = Field(False, description="确认恢复")
    
    @validator('confirm')
    def validate_confirm(cls, v):
        if not v:
            raise ValueError("必须确认恢复操作")
        return v


class ConfigTemplate(BaseModel):
    """配置模板"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    category: str = Field(..., description="模板分类")
    configs: Dict[str, Any] = Field(..., description="配置数据")
    is_system: bool = Field(False, description="是否系统模板")
    created_at: str = Field(..., description="创建时间")


class ConfigTemplateListResponse(BaseModel):
    """配置模板列表响应"""
    templates: List[ConfigTemplate] = Field(..., description="模板列表")
    total: int = Field(..., description="总模板数")


class ConfigTemplateCreateRequest(BaseModel):
    """配置模板创建请求"""
    name: str = Field(..., min_length=1, max_length=100, description="模板名称")
    description: str = Field(..., min_length=1, max_length=500, description="模板描述")
    category: str = Field(..., description="模板分类")
    configs: Dict[str, Any] = Field(..., description="配置数据")
    
    @validator('configs')
    def validate_configs(cls, v):
        if not v:
            raise ValueError("配置数据不能为空")
        return v


class ConfigDiff(BaseModel):
    """配置差异"""
    key: str = Field(..., description="配置键")
    action: str = Field(..., description="操作类型")  # added, removed, modified
    old_value: Optional[Any] = Field(None, description="旧值")
    new_value: Optional[Any] = Field(None, description="新值")


class ConfigCompareResponse(BaseModel):
    """配置比较响应"""
    differences: List[ConfigDiff] = Field(..., description="差异列表")
    total_differences: int = Field(..., description="总差异数")
    added_count: int = Field(..., description="新增数量")
    removed_count: int = Field(..., description="删除数量")
    modified_count: int = Field(..., description="修改数量")


class ConfigEnvironment(BaseModel):
    """配置环境"""
    name: str = Field(..., description="环境名称")
    key: str = Field(..., description="环境键")
    description: str = Field(..., description="环境描述")
    is_active: bool = Field(..., description="是否激活")
    configs: Dict[str, Any] = Field(default_factory=dict, description="环境配置")


class ConfigEnvironmentListResponse(BaseModel):
    """配置环境列表响应"""
    environments: List[ConfigEnvironment] = Field(..., description="环境列表")
    current_environment: str = Field(..., description="当前环境")
    total: int = Field(..., description="总环境数")


class ConfigSyncRequest(BaseModel):
    """配置同步请求"""
    source_environment: str = Field(..., description="源环境")
    target_environment: str = Field(..., description="目标环境")
    config_keys: Optional[List[str]] = Field(None, description="同步的配置键")
    overwrite: bool = Field(True, description="是否覆盖")


class ConfigSyncResponse(BaseModel):
    """配置同步响应"""
    synced_configs: List[str] = Field(..., description="已同步的配置")
    skipped_configs: List[str] = Field(default_factory=list, description="跳过的配置")
    failed_configs: List[str] = Field(default_factory=list, description="失败的配置")
    total_synced: int = Field(..., description="同步总数")


class ConfigMonitoring(BaseModel):
    """配置监控"""
    key: str = Field(..., description="配置键")
    enabled: bool = Field(..., description="是否启用监控")
    alert_on_change: bool = Field(False, description="变更时告警")
    alert_recipients: List[str] = Field(default_factory=list, description="告警接收者")
    check_interval: int = Field(300, description="检查间隔（秒）")


class ConfigMonitoringListResponse(BaseModel):
    """配置监控列表响应"""
    monitoring: List[ConfigMonitoring] = Field(..., description="监控配置")
    total: int = Field(..., description="总监控数")


class ConfigAlert(BaseModel):
    """配置告警"""
    id: str = Field(..., description="告警ID")
    config_key: str = Field(..., description="配置键")
    alert_type: str = Field(..., description="告警类型")
    message: str = Field(..., description="告警消息")
    old_value: Any = Field(..., description="旧值")
    new_value: Any = Field(..., description="新值")
    triggered_at: str = Field(..., description="触发时间")
    acknowledged: bool = Field(False, description="是否已确认")
    acknowledged_by: Optional[str] = Field(None, description="确认者")
    acknowledged_at: Optional[str] = Field(None, description="确认时间")


class ConfigAlertListResponse(BaseModel):
    """配置告警列表响应"""
    alerts: List[ConfigAlert] = Field(..., description="告警列表")
    total: int = Field(..., description="总告警数")
    unacknowledged_count: int = Field(..., description="未确认数量")
