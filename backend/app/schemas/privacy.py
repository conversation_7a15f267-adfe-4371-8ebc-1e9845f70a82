"""
隐私管理相关的Pydantic模式
"""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

from app.core.privacy import ConsentType, ConsentStatus


class ConsentRequest(BaseModel):
    """隐私同意请求"""
    consent_type: ConsentType = Field(..., description="同意类型")
    status: ConsentStatus = Field(..., description="同意状态")
    purpose: str = Field(..., min_length=1, max_length=200, description="数据使用目的")
    data_categories: Optional[List[str]] = Field(default_factory=list, description="涉及的数据类别")
    legal_basis: Optional[str] = Field(None, max_length=100, description="法律依据")
    expires_days: Optional[int] = Field(None, ge=1, le=3650, description="过期天数")


class ConsentResponse(BaseModel):
    """隐私同意响应"""
    id: str = Field(..., description="同意记录ID")
    consent_type: ConsentType = Field(..., description="同意类型")
    status: ConsentStatus = Field(..., description="同意状态")
    purpose: str = Field(..., description="数据使用目的")
    data_categories: List[str] = Field(default_factory=list, description="涉及的数据类别")
    legal_basis: Optional[str] = Field(None, description="法律依据")
    granted_at: Optional[datetime] = Field(None, description="同意时间")
    withdrawn_at: Optional[datetime] = Field(None, description="撤回时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class ConsentListResponse(BaseModel):
    """隐私同意列表响应"""
    consents: List[ConsentResponse] = Field(..., description="同意记录列表")
    total: int = Field(..., description="总数量")


class DataExportFormat(str, Enum):
    """数据导出格式"""
    JSON = "json"
    CSV = "csv"
    PDF = "pdf"


class DataExportRequest(BaseModel):
    """数据导出请求"""
    data_types: List[str] = Field(..., description="要导出的数据类型")
    format: DataExportFormat = Field(DataExportFormat.JSON, description="导出格式")
    include_metadata: bool = Field(True, description="是否包含元数据")
    
    class Config:
        use_enum_values = True


class DataDeletionRequest(BaseModel):
    """数据删除请求"""
    data_types: List[str] = Field(..., description="要删除的数据类型")
    reason: Optional[str] = Field(None, max_length=500, description="删除原因")
    confirm_deletion: bool = Field(..., description="确认删除")
    
    def __init__(self, **data):
        super().__init__(**data)
        if not self.confirm_deletion:
            raise ValueError("必须确认删除操作")


class PrivacySettings(BaseModel):
    """隐私设置"""
    data_processing_consent: bool = Field(False, description="数据处理同意")
    marketing_consent: bool = Field(False, description="营销同意")
    analytics_consent: bool = Field(False, description="分析同意")
    third_party_sharing: bool = Field(False, description="第三方共享同意")
    
    # 数据可见性设置
    profile_visibility: str = Field("private", description="个人资料可见性")  # public, private, friends
    activity_visibility: str = Field("private", description="活动可见性")
    
    # 通知设置
    email_notifications: bool = Field(True, description="邮件通知")
    sms_notifications: bool = Field(False, description="短信通知")
    push_notifications: bool = Field(True, description="推送通知")
    
    # 数据保留设置
    auto_delete_inactive_data: bool = Field(False, description="自动删除非活跃数据")
    data_retention_period: int = Field(365, description="数据保留期限（天）")


class PrivacySettingsUpdate(BaseModel):
    """隐私设置更新"""
    data_processing_consent: Optional[bool] = Field(None, description="数据处理同意")
    marketing_consent: Optional[bool] = Field(None, description="营销同意")
    analytics_consent: Optional[bool] = Field(None, description="分析同意")
    third_party_sharing: Optional[bool] = Field(None, description="第三方共享同意")
    profile_visibility: Optional[str] = Field(None, description="个人资料可见性")
    activity_visibility: Optional[str] = Field(None, description="活动可见性")
    email_notifications: Optional[bool] = Field(None, description="邮件通知")
    sms_notifications: Optional[bool] = Field(None, description="短信通知")
    push_notifications: Optional[bool] = Field(None, description="推送通知")
    auto_delete_inactive_data: Optional[bool] = Field(None, description="自动删除非活跃数据")
    data_retention_period: Optional[int] = Field(None, ge=30, le=3650, description="数据保留期限（天）")


class DataPortabilityRequest(BaseModel):
    """数据可携性请求"""
    data_types: List[str] = Field(..., description="要导出的数据类型")
    destination_service: Optional[str] = Field(None, description="目标服务")
    format: DataExportFormat = Field(DataExportFormat.JSON, description="导出格式")
    include_relationships: bool = Field(True, description="是否包含关联数据")


class AuditLogResponse(BaseModel):
    """审计日志响应"""
    id: str = Field(..., description="日志ID")
    event_type: str = Field(..., description="事件类型")
    message: str = Field(..., description="事件描述")
    timestamp: datetime = Field(..., description="事件时间")
    user_ip: Optional[str] = Field(None, description="用户IP")
    resource_type: Optional[str] = Field(None, description="资源类型")
    action: Optional[str] = Field(None, description="操作动作")
    success: bool = Field(..., description="操作是否成功")
    
    class Config:
        from_attributes = True


class AuditLogListResponse(BaseModel):
    """审计日志列表响应"""
    logs: List[AuditLogResponse] = Field(..., description="审计日志列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class SecurityEventResponse(BaseModel):
    """安全事件响应"""
    event_type: str = Field(..., description="事件类型")
    message: str = Field(..., description="事件描述")
    timestamp: datetime = Field(..., description="事件时间")
    severity: str = Field(..., description="严重程度")
    user_ip: Optional[str] = Field(None, description="用户IP")
    details: dict = Field(default_factory=dict, description="事件详情")


class DataBreachNotification(BaseModel):
    """数据泄露通知"""
    incident_id: str = Field(..., description="事件ID")
    incident_type: str = Field(..., description="事件类型")
    description: str = Field(..., description="事件描述")
    affected_data_types: List[str] = Field(..., description="受影响的数据类型")
    affected_users_count: int = Field(..., description="受影响用户数量")
    discovery_date: datetime = Field(..., description="发现时间")
    notification_date: datetime = Field(..., description="通知时间")
    mitigation_measures: List[str] = Field(..., description="缓解措施")
    user_actions_required: List[str] = Field(default_factory=list, description="用户需要采取的行动")
    contact_information: dict = Field(..., description="联系信息")


class ComplianceReport(BaseModel):
    """合规报告"""
    report_id: str = Field(..., description="报告ID")
    report_type: str = Field(..., description="报告类型")
    period_start: datetime = Field(..., description="报告期间开始")
    period_end: datetime = Field(..., description="报告期间结束")
    
    # 数据处理统计
    total_users: int = Field(..., description="总用户数")
    active_users: int = Field(..., description="活跃用户数")
    new_registrations: int = Field(..., description="新注册用户数")
    account_deletions: int = Field(..., description="账户删除数")
    
    # 同意统计
    consent_granted: dict = Field(..., description="同意授予统计")
    consent_withdrawn: dict = Field(..., description="同意撤回统计")
    
    # 数据请求统计
    data_export_requests: int = Field(..., description="数据导出请求数")
    data_deletion_requests: int = Field(..., description="数据删除请求数")
    data_rectification_requests: int = Field(..., description="数据更正请求数")
    
    # 安全事件统计
    security_incidents: int = Field(..., description="安全事件数")
    data_breaches: int = Field(..., description="数据泄露数")
    
    generated_at: datetime = Field(..., description="生成时间")
