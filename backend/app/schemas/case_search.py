"""
案例搜索相关的Pydantic模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class CaseSearchRequest(BaseModel):
    """案例搜索请求"""
    query: Optional[str] = Field(None, description="搜索关键词")
    case_type: Optional[str] = Field(None, description="案件类型")
    court_name: Optional[str] = Field(None, description="法院名称")
    precedent_value: Optional[str] = Field(None, description="判例价值")
    date_range: Optional[Dict[str, str]] = Field(None, description="日期范围")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    sort_by: str = Field("relevance", description="排序方式")
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        valid_options = ["relevance", "date_desc", "date_asc", "citation_desc"]
        if v not in valid_options:
            return "relevance"
        return v
    
    @validator('case_type')
    def validate_case_type(cls, v):
        if v is None:
            return v
        valid_types = [
            "civil", "criminal", "administrative", "commercial", 
            "labor", "intellectual_property", "environmental", "other"
        ]
        if v not in valid_types:
            raise ValueError(f"案件类型必须是以下之一: {', '.join(valid_types)}")
        return v
    
    @validator('precedent_value')
    def validate_precedent_value(cls, v):
        if v is None:
            return v
        valid_values = ["高", "中", "低"]
        if v not in valid_values:
            raise ValueError(f"判例价值必须是以下之一: {', '.join(valid_values)}")
        return v


class CaseSearchResult(BaseModel):
    """案例搜索结果项"""
    id: str = Field(..., description="案例ID")
    case_number: str = Field(..., description="案件编号")
    title: str = Field(..., description="案例标题")
    court_name: str = Field(..., description="审理法院")
    case_type: str = Field(..., description="案件类型")
    judgment_date: Optional[str] = Field(None, description="判决日期")
    case_summary: Optional[str] = Field(None, description="案例摘要")
    keywords: Optional[List[str]] = Field(default_factory=list, description="关键词")
    precedent_value: Optional[str] = Field(None, description="判例价值")
    citation_count: int = Field(0, description="引用次数")
    score: float = Field(0.0, description="相关性分数")
    highlight: Optional[Dict[str, List[str]]] = Field(default_factory=dict, description="高亮信息")
    created_at: str = Field(..., description="创建时间")


class CaseSearchResponse(BaseModel):
    """案例搜索响应"""
    cases: List[CaseSearchResult] = Field(..., description="搜索结果")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class CaseDetailResponse(BaseModel):
    """案例详情响应"""
    id: str = Field(..., description="案例ID")
    case_number: str = Field(..., description="案件编号")
    title: str = Field(..., description="案例标题")
    court_name: str = Field(..., description="审理法院")
    case_type: str = Field(..., description="案件类型")
    judgment_date: Optional[str] = Field(None, description="判决日期")
    case_summary: Optional[str] = Field(None, description="案例摘要")
    full_text: Optional[str] = Field(None, description="全文内容")
    keywords: Optional[List[str]] = Field(default_factory=list, description="关键词")
    precedent_value: Optional[str] = Field(None, description="判例价值")
    citation_count: int = Field(0, description="引用次数")
    legal_provisions: Optional[List[str]] = Field(default_factory=list, description="法律条文")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class SimilarCase(BaseModel):
    """相似案例"""
    id: str = Field(..., description="案例ID")
    case_number: str = Field(..., description="案件编号")
    title: str = Field(..., description="案例标题")
    court_name: str = Field(..., description="审理法院")
    case_type: str = Field(..., description="案件类型")
    judgment_date: Optional[str] = Field(None, description="判决日期")
    case_summary: Optional[str] = Field(None, description="案例摘要")
    precedent_value: Optional[str] = Field(None, description="判例价值")
    citation_count: int = Field(0, description="引用次数")
    similarity_score: float = Field(..., description="相似度分数")
    created_at: str = Field(..., description="创建时间")


class SimilarCasesResponse(BaseModel):
    """相似案例响应"""
    case_id: str = Field(..., description="原案例ID")
    similar_cases: List[SimilarCase] = Field(..., description="相似案例列表")
    total: int = Field(..., description="相似案例总数")


class SearchSuggestion(BaseModel):
    """搜索建议"""
    text: str = Field(..., description="建议文本")
    type: str = Field(..., description="建议类型")
    highlight: Optional[str] = Field(None, description="高亮显示")


class SearchSuggestionsResponse(BaseModel):
    """搜索建议响应"""
    suggestions: List[SearchSuggestion] = Field(..., description="建议列表")
    query: str = Field(..., description="查询关键词")


class CategoryStats(BaseModel):
    """分类统计"""
    category: str = Field(..., description="分类名称")
    count: int = Field(..., description="数量")


class CourtStats(BaseModel):
    """法院统计"""
    court: str = Field(..., description="法院名称")
    count: int = Field(..., description="案例数量")


class PrecedentValueStats(BaseModel):
    """判例价值统计"""
    value: str = Field(..., description="判例价值")
    count: int = Field(..., description="数量")


class CaseStatsResponse(BaseModel):
    """案例统计响应"""
    total_cases: int = Field(..., description="总案例数")
    case_type_stats: List[CategoryStats] = Field(..., description="案件类型统计")
    court_stats: List[CourtStats] = Field(..., description="法院统计")
    precedent_value_stats: List[PrecedentValueStats] = Field(..., description="判例价值统计")


class TrendingKeyword(BaseModel):
    """热门关键词"""
    keyword: str = Field(..., description="关键词")
    count: int = Field(..., description="搜索次数")
    trend: str = Field(..., description="趋势")  # up, down, stable


class TrendingKeywordsResponse(BaseModel):
    """热门关键词响应"""
    keywords: List[TrendingKeyword] = Field(..., description="热门关键词列表")
    updated_at: str = Field(..., description="更新时间")


class CaseFilter(BaseModel):
    """案例过滤器"""
    case_types: List[str] = Field(default_factory=list, description="案件类型列表")
    courts: List[str] = Field(default_factory=list, description="法院列表")
    precedent_values: List[str] = Field(default_factory=list, description="判例价值列表")
    date_ranges: List[Dict[str, str]] = Field(default_factory=list, description="日期范围选项")


class SearchHistory(BaseModel):
    """搜索历史"""
    id: str = Field(..., description="搜索记录ID")
    query: str = Field(..., description="搜索关键词")
    filters: Dict[str, Any] = Field(default_factory=dict, description="过滤条件")
    result_count: int = Field(..., description="结果数量")
    created_at: str = Field(..., description="搜索时间")


class SearchHistoryResponse(BaseModel):
    """搜索历史响应"""
    history: List[SearchHistory] = Field(..., description="搜索历史列表")
    total: int = Field(..., description="总数量")


class CaseExportRequest(BaseModel):
    """案例导出请求"""
    case_ids: List[str] = Field(..., description="案例ID列表")
    format: str = Field("pdf", description="导出格式")  # pdf, word, excel
    include_full_text: bool = Field(False, description="是否包含全文")
    
    @validator('format')
    def validate_format(cls, v):
        valid_formats = ["pdf", "word", "excel"]
        if v not in valid_formats:
            raise ValueError(f"导出格式必须是以下之一: {', '.join(valid_formats)}")
        return v
    
    @validator('case_ids')
    def validate_case_ids(cls, v):
        if not v:
            raise ValueError("至少需要选择一个案例")
        if len(v) > 100:
            raise ValueError("一次最多只能导出100个案例")
        return v


class CaseExportResponse(BaseModel):
    """案例导出响应"""
    export_id: str = Field(..., description="导出任务ID")
    status: str = Field(..., description="导出状态")  # pending, processing, completed, failed
    download_url: Optional[str] = Field(None, description="下载链接")
    created_at: str = Field(..., description="创建时间")
    expires_at: Optional[str] = Field(None, description="过期时间")
