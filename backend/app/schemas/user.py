"""
用户相关数据模式
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, EmailStr, validator
from uuid import UUID

from app.models.user import UserType, UserStatus


class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    phone: Optional[str] = None
    user_type: UserType = UserType.INDIVIDUAL


class UserCreate(UserBase):
    """用户创建模式"""
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        return v
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('用户名长度至少3位')
        if not v.isalnum():
            raise ValueError('用户名只能包含字母和数字')
        return v


class UserUpdate(BaseModel):
    """用户更新模式"""
    full_name: Optional[str] = None
    phone: Optional[str] = None
    
    class Config:
        extra = "forbid"


class UserPasswordUpdate(BaseModel):
    """用户密码更新模式"""
    current_password: str
    new_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('新密码长度至少8位')
        return v


class UserResponse(BaseModel):
    """用户响应模式"""
    id: UUID
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    phone: Optional[str] = None
    user_type: UserType
    status: UserStatus
    email_verified: bool = False
    phone_verified: bool = False
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserProfileBase(BaseModel):
    """用户配置基础模式"""
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    specialization: Optional[str] = None
    license_number: Optional[str] = None
    company_name: Optional[str] = None
    company_position: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = {}


class UserProfileCreate(UserProfileBase):
    """用户配置创建模式"""
    pass


class UserProfileUpdate(UserProfileBase):
    """用户配置更新模式"""
    pass


class UserProfileResponse(UserProfileBase):
    """用户配置响应模式"""
    id: UUID
    user_id: UUID
    total_questions: str = "0"
    total_contracts: str = "0"
    total_documents: str = "0"
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserWithProfile(UserResponse):
    """包含配置的用户响应模式"""
    profile: Optional[UserProfileResponse] = None


# 为了向后兼容，添加别名
UserProfile = UserProfileResponse


class UserListResponse(BaseModel):
    """用户列表响应模式"""
    users: list[UserResponse]
    total: int
    page: int
    size: int
    pages: int


class UserStatistics(BaseModel):
    """用户统计模式"""
    total_users: int
    active_users: int
    new_users_today: int
    new_users_this_week: int
    new_users_this_month: int
    user_type_distribution: Dict[str, int]
    status_distribution: Dict[str, int]


class UserActivityLog(BaseModel):
    """用户活动日志模式"""
    id: UUID
    action: str
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    details: Dict[str, Any] = {}
    ip_address: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class UserSession(BaseModel):
    """用户会话模式"""
    id: UUID
    session_token: str
    expires_at: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    device_info: Dict[str, Any] = {}
    is_active: bool = True
    created_at: datetime
    last_accessed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserSessionList(BaseModel):
    """用户会话列表模式"""
    sessions: list[UserSession]
    total: int
