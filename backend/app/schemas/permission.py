"""
权限管理相关的Pydantic模式
"""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, validator
import uuid


class PermissionBase(BaseModel):
    """权限基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="权限名称")
    code: str = Field(..., min_length=1, max_length=100, description="权限代码")
    description: Optional[str] = Field(None, description="权限描述")
    category: Optional[str] = Field(None, max_length=50, description="权限分类")
    resource: Optional[str] = Field(None, max_length=100, description="资源类型")
    action: Optional[str] = Field(None, max_length=50, description="操作动作")
    level: int = Field(1, ge=1, le=5, description="权限级别")
    
    @validator('code')
    def validate_code(cls, v):
        """验证权限代码格式"""
        import re
        if not re.match(r'^[a-z][a-z0-9_]*:[a-z][a-z0-9_]*$', v):
            raise ValueError('权限代码格式应为 resource:action，如 user:read')
        return v


class PermissionCreate(PermissionBase):
    """创建权限请求"""
    pass


class PermissionUpdate(BaseModel):
    """更新权限请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    category: Optional[str] = Field(None, max_length=50, description="权限分类")
    level: Optional[int] = Field(None, ge=1, le=5, description="权限级别")
    is_active: Optional[bool] = Field(None, description="是否激活")


class PermissionResponse(PermissionBase):
    """权限响应"""
    id: str = Field(..., description="权限ID")
    is_active: bool = Field(..., description="是否激活")
    is_system: bool = Field(False, description="是否系统权限")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class RoleBase(BaseModel):
    """角色基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="角色名称")
    code: str = Field(..., min_length=1, max_length=100, description="角色代码")
    description: Optional[str] = Field(None, description="角色描述")
    level: int = Field(1, ge=1, le=5, description="角色级别")
    
    @validator('code')
    def validate_code(cls, v):
        """验证角色代码格式"""
        import re
        if not re.match(r'^[a-z][a-z0-9_]*$', v):
            raise ValueError('角色代码只能包含小写字母、数字和下划线，且以字母开头')
        return v


class RoleCreate(RoleBase):
    """创建角色请求"""
    parent_role_id: Optional[str] = Field(None, description="上级角色ID")
    permission_codes: Optional[List[str]] = Field(default_factory=list, description="权限代码列表")
    
    @validator('parent_role_id')
    def validate_parent_role_id(cls, v):
        """验证上级角色ID格式"""
        if v:
            try:
                uuid.UUID(v)
            except ValueError:
                raise ValueError('无效的角色ID格式')
        return v


class RoleUpdate(BaseModel):
    """更新角色请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    level: Optional[int] = Field(None, ge=1, le=5, description="角色级别")
    parent_role_id: Optional[str] = Field(None, description="上级角色ID")
    permission_codes: Optional[List[str]] = Field(None, description="权限代码列表")
    is_active: Optional[bool] = Field(None, description="是否激活")


class RoleResponse(RoleBase):
    """角色响应"""
    id: str = Field(..., description="角色ID")
    parent_role_id: Optional[str] = Field(None, description="上级角色ID")
    is_active: bool = Field(..., description="是否激活")
    is_system: bool = Field(False, description="是否系统角色")
    is_default: bool = Field(False, description="是否默认角色")
    permission_codes: List[str] = Field(default_factory=list, description="权限代码列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class RoleAssignRequest(BaseModel):
    """角色分配请求"""
    role_id: str = Field(..., description="角色ID")
    
    @validator('role_id')
    def validate_role_id(cls, v):
        """验证角色ID格式"""
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError('无效的角色ID格式')
        return v


class UserRolesResponse(BaseModel):
    """用户角色响应"""
    user_id: str = Field(..., description="用户ID")
    roles: List[RoleResponse] = Field(..., description="角色列表")
    total: int = Field(..., description="角色总数")


class UserPermissionsResponse(BaseModel):
    """用户权限响应"""
    user_id: str = Field(..., description="用户ID")
    permissions: List[str] = Field(..., description="权限代码列表")
    total: int = Field(..., description="权限总数")


class ResourcePermissionBase(BaseModel):
    """资源权限基础模式"""
    subject_type: str = Field(..., description="主体类型")  # user, role
    subject_id: str = Field(..., description="主体ID")
    resource_type: str = Field(..., description="资源类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    actions: List[str] = Field(..., description="允许的动作列表")
    effect: str = Field("allow", description="权限效果")  # allow, deny
    priority: int = Field(0, description="优先级")
    
    @validator('subject_type')
    def validate_subject_type(cls, v):
        """验证主体类型"""
        if v not in ['user', 'role']:
            raise ValueError('主体类型必须是 user 或 role')
        return v
    
    @validator('effect')
    def validate_effect(cls, v):
        """验证权限效果"""
        if v not in ['allow', 'deny']:
            raise ValueError('权限效果必须是 allow 或 deny')
        return v


class ResourcePermissionCreate(ResourcePermissionBase):
    """创建资源权限请求"""
    conditions: Optional[dict] = Field(default_factory=dict, description="权限条件")
    valid_from: Optional[datetime] = Field(None, description="生效时间")
    valid_until: Optional[datetime] = Field(None, description="失效时间")


class ResourcePermissionResponse(ResourcePermissionBase):
    """资源权限响应"""
    id: str = Field(..., description="权限ID")
    conditions: dict = Field(default_factory=dict, description="权限条件")
    valid_from: Optional[datetime] = Field(None, description="生效时间")
    valid_until: Optional[datetime] = Field(None, description="失效时间")
    is_active: bool = Field(..., description="是否激活")
    created_by: Optional[str] = Field(None, description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class PermissionCheckRequest(BaseModel):
    """权限检查请求"""
    user_id: str = Field(..., description="用户ID")
    permissions: Optional[List[str]] = Field(None, description="权限代码列表")
    roles: Optional[List[str]] = Field(None, description="角色代码列表")
    resource_type: Optional[str] = Field(None, description="资源类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    action: Optional[str] = Field(None, description="操作动作")
    
    @validator('user_id')
    def validate_user_id(cls, v):
        """验证用户ID格式"""
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError('无效的用户ID格式')
        return v


class PermissionCheckResponse(BaseModel):
    """权限检查响应"""
    user_id: str = Field(..., description="用户ID")
    has_permission: bool = Field(..., description="是否有权限")
    matched_permissions: List[str] = Field(default_factory=list, description="匹配的权限")
    matched_roles: List[str] = Field(default_factory=list, description="匹配的角色")
    reason: Optional[str] = Field(None, description="权限检查结果说明")


class PermissionGroupBase(BaseModel):
    """权限组基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="权限组名称")
    code: str = Field(..., min_length=1, max_length=100, description="权限组代码")
    description: Optional[str] = Field(None, description="权限组描述")
    group_type: Optional[str] = Field(None, max_length=50, description="权限组类型")
    permission_codes: List[str] = Field(default_factory=list, description="权限代码列表")


class PermissionGroupCreate(PermissionGroupBase):
    """创建权限组请求"""
    pass


class PermissionGroupResponse(PermissionGroupBase):
    """权限组响应"""
    id: str = Field(..., description="权限组ID")
    is_active: bool = Field(..., description="是否激活")
    is_system: bool = Field(False, description="是否系统权限组")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class RoleHierarchyResponse(BaseModel):
    """角色层级响应"""
    role: RoleResponse = Field(..., description="角色信息")
    children: List['RoleHierarchyResponse'] = Field(default_factory=list, description="子角色")
    
    class Config:
        from_attributes = True


# 更新前向引用
RoleHierarchyResponse.model_rebuild()
