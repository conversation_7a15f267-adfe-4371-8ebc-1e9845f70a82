"""
合同模板相关的Pydantic模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class ContractTemplate(BaseModel):
    """合同模板"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    category: str = Field(..., description="模板分类")
    description: Optional[str] = Field(None, description="模板描述")
    difficulty_level: str = Field(..., description="难度级别")
    usage_count: int = Field(0, description="使用次数")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    is_premium: bool = Field(False, description="是否为高级模板")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")


class ContractTemplateListResponse(BaseModel):
    """合同模板列表响应"""
    templates: List[ContractTemplate] = Field(..., description="模板列表")
    total: int = Field(..., description="总数量")


class ContractTemplateDetail(ContractTemplate):
    """合同模板详情"""
    required_fields: List[str] = Field(..., description="必填字段")
    optional_fields: List[str] = Field(default_factory=list, description="可选字段")
    template_content: str = Field(..., description="模板内容")


class ContractTemplateDetailResponse(ContractTemplateDetail):
    """合同模板详情响应"""
    pass


class ContractGenerateRequest(BaseModel):
    """合同生成请求"""
    template_id: str = Field(..., description="模板ID")
    variables: Dict[str, Any] = Field(..., description="变量值")
    
    @validator('variables')
    def validate_variables(cls, v):
        if not v:
            raise ValueError("变量不能为空")
        return v


class ContractGenerateResponse(BaseModel):
    """合同生成响应"""
    id: str = Field(..., description="合同ID")
    template_id: str = Field(..., description="模板ID")
    template_name: str = Field(..., description="模板名称")
    content: str = Field(..., description="生成的合同内容")
    variables: Dict[str, Any] = Field(..., description="使用的变量")
    created_at: str = Field(..., description="生成时间")


class UserContract(BaseModel):
    """用户合同"""
    id: str = Field(..., description="合同ID")
    template_id: Optional[str] = Field(None, description="模板ID")
    template_name: Optional[str] = Field(None, description="模板名称")
    contract_type: str = Field(..., description="合同类型")
    review_status: str = Field(..., description="审核状态")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class UserContractListResponse(BaseModel):
    """用户合同列表响应"""
    contracts: List[UserContract] = Field(..., description="合同列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class ContractDetailResponse(BaseModel):
    """合同详情响应"""
    id: str = Field(..., description="合同ID")
    template_id: Optional[str] = Field(None, description="模板ID")
    template_name: Optional[str] = Field(None, description="模板名称")
    contract_type: str = Field(..., description="合同类型")
    content: str = Field(..., description="合同内容")
    variables: Dict[str, Any] = Field(default_factory=dict, description="变量值")
    review_status: str = Field(..., description="审核状态")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class FieldDescriptionsResponse(BaseModel):
    """字段描述响应"""
    descriptions: Dict[str, str] = Field(..., description="字段描述映射")


class TemplateCategory(BaseModel):
    """模板分类"""
    code: str = Field(..., description="分类代码")
    name: str = Field(..., description="分类名称")
    description: str = Field(..., description="分类描述")
    icon: str = Field(..., description="图标")


class TemplateCategoriesResponse(BaseModel):
    """模板分类响应"""
    categories: List[TemplateCategory] = Field(..., description="分类列表")


class TemplatePreviewRequest(BaseModel):
    """模板预览请求"""
    template_id: str = Field(..., description="模板ID")
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict, description="预览变量")


class TemplatePreviewResponse(BaseModel):
    """模板预览响应"""
    template_id: str = Field(..., description="模板ID")
    template_name: str = Field(..., description="模板名称")
    sample_data: Dict[str, Any] = Field(..., description="示例数据")
    preview_content: str = Field(..., description="预览内容")


class ContractValidationRequest(BaseModel):
    """合同验证请求"""
    content: str = Field(..., description="合同内容")
    template_id: Optional[str] = Field(None, description="模板ID")


class ContractValidationIssue(BaseModel):
    """合同验证问题"""
    type: str = Field(..., description="问题类型")  # error, warning, suggestion
    message: str = Field(..., description="问题描述")
    line_number: Optional[int] = Field(None, description="行号")
    severity: str = Field(..., description="严重程度")  # high, medium, low


class ContractValidationResponse(BaseModel):
    """合同验证响应"""
    is_valid: bool = Field(..., description="是否有效")
    issues: List[ContractValidationIssue] = Field(default_factory=list, description="问题列表")
    suggestions: List[str] = Field(default_factory=list, description="改进建议")
    score: float = Field(..., description="质量评分")


class ContractExportRequest(BaseModel):
    """合同导出请求"""
    contract_id: str = Field(..., description="合同ID")
    format: str = Field("pdf", description="导出格式")  # pdf, word, html
    include_metadata: bool = Field(False, description="是否包含元数据")
    
    @validator('format')
    def validate_format(cls, v):
        valid_formats = ["pdf", "word", "html"]
        if v not in valid_formats:
            raise ValueError(f"导出格式必须是以下之一: {', '.join(valid_formats)}")
        return v


class ContractExportResponse(BaseModel):
    """合同导出响应"""
    export_id: str = Field(..., description="导出任务ID")
    download_url: str = Field(..., description="下载链接")
    format: str = Field(..., description="导出格式")
    file_size: int = Field(..., description="文件大小（字节）")
    expires_at: str = Field(..., description="链接过期时间")


class ContractShareRequest(BaseModel):
    """合同分享请求"""
    contract_id: str = Field(..., description="合同ID")
    share_type: str = Field("view", description="分享类型")  # view, edit
    expires_days: int = Field(7, ge=1, le=30, description="有效天数")
    password: Optional[str] = Field(None, description="访问密码")


class ContractShareResponse(BaseModel):
    """合同分享响应"""
    share_id: str = Field(..., description="分享ID")
    share_url: str = Field(..., description="分享链接")
    share_type: str = Field(..., description="分享类型")
    expires_at: str = Field(..., description="过期时间")
    has_password: bool = Field(..., description="是否设置密码")


class ContractVersionRequest(BaseModel):
    """合同版本请求"""
    contract_id: str = Field(..., description="合同ID")
    content: str = Field(..., description="新版本内容")
    change_description: Optional[str] = Field(None, description="变更说明")


class ContractVersion(BaseModel):
    """合同版本"""
    id: str = Field(..., description="版本ID")
    version_number: int = Field(..., description="版本号")
    content: str = Field(..., description="版本内容")
    change_description: Optional[str] = Field(None, description="变更说明")
    created_at: str = Field(..., description="创建时间")
    created_by: str = Field(..., description="创建者")


class ContractVersionListResponse(BaseModel):
    """合同版本列表响应"""
    contract_id: str = Field(..., description="合同ID")
    versions: List[ContractVersion] = Field(..., description="版本列表")
    current_version: int = Field(..., description="当前版本号")
    total: int = Field(..., description="总版本数")


class ContractCompareRequest(BaseModel):
    """合同对比请求"""
    contract_id: str = Field(..., description="合同ID")
    version1: int = Field(..., description="版本1")
    version2: int = Field(..., description="版本2")


class ContractDifference(BaseModel):
    """合同差异"""
    type: str = Field(..., description="差异类型")  # added, deleted, modified
    line_number: int = Field(..., description="行号")
    old_content: Optional[str] = Field(None, description="原内容")
    new_content: Optional[str] = Field(None, description="新内容")


class ContractCompareResponse(BaseModel):
    """合同对比响应"""
    contract_id: str = Field(..., description="合同ID")
    version1: int = Field(..., description="版本1")
    version2: int = Field(..., description="版本2")
    differences: List[ContractDifference] = Field(..., description="差异列表")
    similarity_score: float = Field(..., description="相似度分数")
