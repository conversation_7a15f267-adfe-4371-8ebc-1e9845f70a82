"""
法律案例相关的Pydantic模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.models.legal_case import CaseType, CaseStatus


class CaseTypeEnum(str, Enum):
    """案例类型枚举"""
    CIVIL = "civil"
    CRIMINAL = "criminal"
    ADMINISTRATIVE = "administrative"
    COMMERCIAL = "commercial"
    LABOR = "labor"
    INTELLECTUAL_PROPERTY = "intellectual_property"
    ENVIRONMENTAL = "environmental"
    OTHER = "other"


class CaseStatusEnum(str, Enum):
    """案例状态枚举"""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"


class PartyInfo(BaseModel):
    """当事人信息"""
    name: str = Field(..., description="当事人姓名/名称")
    type: str = Field(..., description="当事人类型")  # 原告、被告、第三人等
    role: Optional[str] = Field(None, description="角色描述")
    lawyer: Optional[str] = Field(None, description="代理律师")


class LegalCaseBase(BaseModel):
    """法律案例基础模式"""
    case_number: str = Field(..., min_length=1, max_length=100, description="案件编号")
    title: str = Field(..., min_length=1, max_length=500, description="案件标题")
    court_name: str = Field(..., min_length=1, max_length=200, description="审理法院")
    case_type: CaseTypeEnum = Field(..., description="案件类型")
    judgment_date: Optional[date] = Field(None, description="判决日期")
    trial_procedure: Optional[str] = Field(None, max_length=50, description="审理程序")
    
    @validator('case_number')
    def validate_case_number(cls, v):
        """验证案件编号格式"""
        import re
        if not re.match(r'^[\w\-\(\)（）]+$', v):
            raise ValueError('案件编号格式不正确，只能包含字母、数字、连字符和括号')
        return v


class LegalCaseCreate(LegalCaseBase):
    """创建法律案例请求"""
    parties: Optional[List[PartyInfo]] = Field(default_factory=list, description="当事人信息")
    case_summary: Optional[str] = Field(None, description="案件摘要")
    case_facts: Optional[str] = Field(None, description="案件事实")
    dispute_focus: Optional[List[str]] = Field(default_factory=list, description="争议焦点")
    court_opinion: Optional[str] = Field(None, description="法院观点")
    judgment_result: Optional[str] = Field(None, description="判决结果")
    related_articles: Optional[List[str]] = Field(default_factory=list, description="相关法条ID")
    legal_basis: Optional[str] = Field(None, description="法律依据")
    keywords: Optional[List[str]] = Field(default_factory=list, description="关键词")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")
    precedent_value: Optional[str] = Field(None, description="判例价值")
    is_public: bool = Field(True, description="是否公开")
    source_url: Optional[str] = Field(None, max_length=500, description="来源URL")
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外数据")


class LegalCaseUpdate(BaseModel):
    """更新法律案例请求"""
    title: Optional[str] = Field(None, min_length=1, max_length=500, description="案件标题")
    court_name: Optional[str] = Field(None, min_length=1, max_length=200, description="审理法院")
    case_type: Optional[CaseTypeEnum] = Field(None, description="案件类型")
    judgment_date: Optional[date] = Field(None, description="判决日期")
    trial_procedure: Optional[str] = Field(None, max_length=50, description="审理程序")
    parties: Optional[List[PartyInfo]] = Field(None, description="当事人信息")
    case_summary: Optional[str] = Field(None, description="案件摘要")
    case_facts: Optional[str] = Field(None, description="案件事实")
    dispute_focus: Optional[List[str]] = Field(None, description="争议焦点")
    court_opinion: Optional[str] = Field(None, description="法院观点")
    judgment_result: Optional[str] = Field(None, description="判决结果")
    related_articles: Optional[List[str]] = Field(None, description="相关法条ID")
    legal_basis: Optional[str] = Field(None, description="法律依据")
    keywords: Optional[List[str]] = Field(None, description="关键词")
    tags: Optional[List[str]] = Field(None, description="标签")
    precedent_value: Optional[str] = Field(None, description="判例价值")
    is_public: Optional[bool] = Field(None, description="是否公开")
    source_url: Optional[str] = Field(None, max_length=500, description="来源URL")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="额外数据")


class LegalCaseResponse(LegalCaseBase):
    """法律案例响应"""
    id: str = Field(..., description="案例ID")
    parties: List[Dict[str, Any]] = Field(default_factory=list, description="当事人信息")
    case_summary: Optional[str] = Field(None, description="案件摘要")
    case_facts: Optional[str] = Field(None, description="案件事实")
    dispute_focus: List[str] = Field(default_factory=list, description="争议焦点")
    court_opinion: Optional[str] = Field(None, description="法院观点")
    judgment_result: Optional[str] = Field(None, description="判决结果")
    related_articles: List[str] = Field(default_factory=list, description="相关法条ID")
    legal_basis: Optional[str] = Field(None, description="法律依据")
    keywords: List[str] = Field(default_factory=list, description="关键词")
    tags: List[str] = Field(default_factory=list, description="标签")
    precedent_value: Optional[str] = Field(None, description="判例价值")
    citation_count: str = Field("0", description="引用次数")
    status: CaseStatusEnum = Field(..., description="案例状态")
    is_public: bool = Field(..., description="是否公开")
    source_url: Optional[str] = Field(None, description="来源URL")
    created_by: Optional[str] = Field(None, description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class LegalCaseListItem(BaseModel):
    """法律案例列表项"""
    id: str = Field(..., description="案例ID")
    case_number: str = Field(..., description="案件编号")
    title: str = Field(..., description="案件标题")
    court_name: str = Field(..., description="审理法院")
    case_type: CaseTypeEnum = Field(..., description="案件类型")
    judgment_date: Optional[date] = Field(None, description="判决日期")
    case_summary: Optional[str] = Field(None, description="案件摘要")
    keywords: List[str] = Field(default_factory=list, description="关键词")
    precedent_value: Optional[str] = Field(None, description="判例价值")
    citation_count: str = Field("0", description="引用次数")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class CaseSearchRequest(BaseModel):
    """案例搜索请求"""
    keyword: Optional[str] = Field(None, max_length=200, description="搜索关键词")
    case_type: Optional[CaseTypeEnum] = Field(None, description="案件类型")
    court_name: Optional[str] = Field(None, max_length=200, description="法院名称")
    date_from: Optional[date] = Field(None, description="开始日期")
    date_to: Optional[date] = Field(None, description="结束日期")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    
    @validator('date_to')
    def validate_date_range(cls, v, values):
        """验证日期范围"""
        if v and values.get('date_from') and v < values['date_from']:
            raise ValueError('结束日期不能早于开始日期')
        return v
    
    @validator('keyword')
    def validate_keyword(cls, v):
        """验证关键词"""
        if v and len(v.strip()) < 2:
            raise ValueError('搜索关键词至少需要2个字符')
        return v.strip() if v else None


class CaseSearchResponse(BaseModel):
    """案例搜索响应"""
    cases: List[LegalCaseListItem] = Field(..., description="案例列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")
    
    @validator('total_pages', pre=True, always=True)
    def calculate_total_pages(cls, v, values):
        """计算总页数"""
        total = values.get('total', 0)
        page_size = values.get('page_size', 20)
        return (total + page_size - 1) // page_size if total > 0 else 0


class CaseStatisticsResponse(BaseModel):
    """案例统计响应"""
    total_cases: int = Field(..., description="总案例数")
    case_types: Dict[str, int] = Field(..., description="按类型统计")
    case_years: Dict[str, int] = Field(..., description="按年份统计")


class LegalArticleBase(BaseModel):
    """法条基础模式"""
    article_number: Optional[str] = Field(None, max_length=50, description="法条编号")
    title: str = Field(..., min_length=1, max_length=500, description="法条标题")
    content: str = Field(..., min_length=1, description="法条内容")
    law_name: str = Field(..., min_length=1, max_length=200, description="法律名称")
    law_category: Optional[str] = Field(None, max_length=100, description="法律分类")
    chapter: Optional[str] = Field(None, max_length=100, description="章节")
    section: Optional[str] = Field(None, max_length=100, description="节")
    effective_date: Optional[date] = Field(None, description="生效日期")
    expiry_date: Optional[date] = Field(None, description="失效日期")


class LegalArticleCreate(LegalArticleBase):
    """创建法条请求"""
    parent_article_id: Optional[str] = Field(None, description="上级法条ID")
    keywords: Optional[List[str]] = Field(default_factory=list, description="关键词")
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外数据")


class LegalArticleResponse(LegalArticleBase):
    """法条响应"""
    id: str = Field(..., description="法条ID")
    status: CaseStatusEnum = Field(..., description="法条状态")
    parent_article_id: Optional[str] = Field(None, description="上级法条ID")
    keywords: List[str] = Field(default_factory=list, description="关键词")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True
