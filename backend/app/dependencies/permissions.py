"""
权限相关的依赖注入
"""

from typing import List, Optional, Callable, Any
from functools import wraps
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.dependencies.auth import get_current_user, get_current_active_user
from app.models.user import User
from app.services.permission import PermissionService
from app.core.audit import get_audit_logger, AuditEventType, AuditLevel
import logging

logger = logging.getLogger(__name__)


async def get_permission_service(db: AsyncSession = Depends(get_db)) -> PermissionService:
    """获取权限服务"""
    return PermissionService(db)


class PermissionChecker:
    """权限检查器"""
    
    def __init__(
        self,
        permissions: List[str] = None,
        roles: List[str] = None,
        resource_type: str = None,
        resource_action: str = None,
        require_all_permissions: bool = True,
        require_all_roles: bool = False
    ):
        self.permissions = permissions or []
        self.roles = roles or []
        self.resource_type = resource_type
        self.resource_action = resource_action
        self.require_all_permissions = require_all_permissions
        self.require_all_roles = require_all_roles
    
    async def __call__(
        self,
        current_user: User = Depends(get_current_active_user),
        permission_service: PermissionService = Depends(get_permission_service),
        db: AsyncSession = Depends(get_db)
    ) -> User:
        """检查权限"""
        
        # 检查权限
        if self.permissions:
            has_permission = await permission_service.check_user_permissions(
                user_id=current_user.id,
                permission_codes=self.permissions,
                require_all=self.require_all_permissions
            )
            
            if not has_permission:
                # 记录权限拒绝的审计日志
                audit_logger = await get_audit_logger(db)
                await audit_logger.log_event(
                    event_type=AuditEventType.ACCESS_DENIED,
                    message=f"权限不足: 需要权限 {', '.join(self.permissions)}",
                    user_id=current_user.id,
                    username=current_user.username,
                    level=AuditLevel.WARNING,
                    success=False,
                    details={
                        "required_permissions": self.permissions,
                        "require_all": self.require_all_permissions
                    }
                )
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {', '.join(self.permissions)}"
                )
        
        # 检查角色
        if self.roles:
            user_roles = await permission_service.get_user_roles(current_user.id)
            user_role_codes = {role.code for role in user_roles}
            
            if self.require_all_roles:
                has_role = all(role in user_role_codes for role in self.roles)
            else:
                has_role = any(role in user_role_codes for role in self.roles)
            
            if not has_role:
                # 记录角色拒绝的审计日志
                audit_logger = await get_audit_logger(db)
                await audit_logger.log_event(
                    event_type=AuditEventType.ACCESS_DENIED,
                    message=f"角色不足: 需要角色 {', '.join(self.roles)}",
                    user_id=current_user.id,
                    username=current_user.username,
                    level=AuditLevel.WARNING,
                    success=False,
                    details={
                        "required_roles": self.roles,
                        "user_roles": list(user_role_codes),
                        "require_all": self.require_all_roles
                    }
                )
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"角色不足，需要角色: {', '.join(self.roles)}"
                )
        
        return current_user


class ResourcePermissionChecker:
    """资源权限检查器"""
    
    def __init__(
        self,
        resource_type: str,
        action: str,
        resource_id_param: str = "resource_id"
    ):
        self.resource_type = resource_type
        self.action = action
        self.resource_id_param = resource_id_param
    
    async def __call__(
        self,
        resource_id: Optional[str] = None,
        current_user: User = Depends(get_current_active_user),
        permission_service: PermissionService = Depends(get_permission_service),
        db: AsyncSession = Depends(get_db)
    ) -> User:
        """检查资源权限"""
        
        has_permission = await permission_service.check_resource_permission(
            user_id=current_user.id,
            resource_type=self.resource_type,
            resource_id=resource_id,
            action=self.action
        )
        
        if not has_permission:
            # 记录资源权限拒绝的审计日志
            audit_logger = await get_audit_logger(db)
            await audit_logger.log_event(
                event_type=AuditEventType.ACCESS_DENIED,
                message=f"资源权限不足: {self.resource_type}:{resource_id} - {self.action}",
                user_id=current_user.id,
                username=current_user.username,
                resource_type=self.resource_type,
                resource_id=resource_id,
                action=self.action,
                level=AuditLevel.WARNING,
                success=False
            )
            
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"对资源 {self.resource_type} 没有 {self.action} 权限"
            )
        
        return current_user


# 常用权限检查器实例
require_admin = PermissionChecker(roles=["admin"])
require_lawyer = PermissionChecker(roles=["lawyer", "admin"], require_all_roles=False)
require_user_management = PermissionChecker(permissions=["user:manage"])
require_case_read = PermissionChecker(permissions=["case:read"])
require_case_write = PermissionChecker(permissions=["case:write"])
require_case_delete = PermissionChecker(permissions=["case:delete"])
require_contract_read = PermissionChecker(permissions=["contract:read"])
require_contract_write = PermissionChecker(permissions=["contract:write"])
require_document_read = PermissionChecker(permissions=["document:read"])
require_document_write = PermissionChecker(permissions=["document:write"])
require_system_config = PermissionChecker(permissions=["system:config"])
require_audit_read = PermissionChecker(permissions=["audit:read"])


def require_permissions(
    permissions: List[str] = None,
    roles: List[str] = None,
    require_all_permissions: bool = True,
    require_all_roles: bool = False
):
    """权限装饰器工厂"""
    return PermissionChecker(
        permissions=permissions,
        roles=roles,
        require_all_permissions=require_all_permissions,
        require_all_roles=require_all_roles
    )


def require_resource_permission(
    resource_type: str,
    action: str,
    resource_id_param: str = "resource_id"
):
    """资源权限装饰器工厂"""
    return ResourcePermissionChecker(
        resource_type=resource_type,
        action=action,
        resource_id_param=resource_id_param
    )


# 权限装饰器（用于非FastAPI函数）
def permission_required(
    permissions: List[str] = None,
    roles: List[str] = None,
    require_all_permissions: bool = True,
    require_all_roles: bool = False
):
    """权限装饰器（用于普通函数）"""
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取用户和权限服务
            user = kwargs.get('current_user')
            permission_service = kwargs.get('permission_service')
            
            if not user or not permission_service:
                raise ValueError("权限装饰器需要 current_user 和 permission_service 参数")
            
            # 检查权限
            if permissions:
                has_permission = await permission_service.check_user_permissions(
                    user_id=user.id,
                    permission_codes=permissions,
                    require_all=require_all_permissions
                )
                
                if not has_permission:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"权限不足，需要权限: {', '.join(permissions)}"
                    )
            
            # 检查角色
            if roles:
                user_roles = await permission_service.get_user_roles(user.id)
                user_role_codes = {role.code for role in user_roles}
                
                if require_all_roles:
                    has_role = all(role in user_role_codes for role in roles)
                else:
                    has_role = any(role in user_role_codes for role in roles)
                
                if not has_role:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"角色不足，需要角色: {', '.join(roles)}"
                    )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


async def check_user_can_access_resource(
    user_id: str,
    resource_type: str,
    resource_id: str,
    action: str,
    permission_service: PermissionService
) -> bool:
    """检查用户是否可以访问特定资源"""
    
    import uuid
    
    try:
        user_uuid = uuid.UUID(user_id)
        return await permission_service.check_resource_permission(
            user_id=user_uuid,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action
        )
    except (ValueError, TypeError):
        return False


async def get_user_accessible_resources(
    user_id: str,
    resource_type: str,
    action: str,
    permission_service: PermissionService
) -> List[str]:
    """获取用户可访问的资源列表"""
    
    # TODO: 实现获取用户可访问资源的逻辑
    # 这需要根据具体的业务需求来实现
    return []
