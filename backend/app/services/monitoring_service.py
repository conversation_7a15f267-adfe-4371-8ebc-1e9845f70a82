"""
系统监控服务
提供应用性能监控、健康检查、告警等功能
"""

import psutil
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json
import aioredis
from sqlalchemy.orm import Session

from app.core.database import get_db, get_database_manager
from app.core.elasticsearch_manager import get_elasticsearch_manager
from app.core.performance import get_performance_monitor, MetricsCollector

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_available: int
    disk_usage: Dict[str, float]
    network_io: Dict[str, int]
    process_count: int
    load_average: List[float]


@dataclass
class ApplicationMetrics:
    """应用指标"""
    timestamp: datetime
    active_connections: int
    request_count: int
    response_time_avg: float
    response_time_p95: float
    error_rate: float
    database_connections: int
    cache_hit_rate: float
    queue_size: int


@dataclass
class Alert:
    """告警信息"""
    id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: datetime
    source: str
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class MonitoringService:
    """监控服务"""
    
    def __init__(self, db: Session):
        """
        初始化监控服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.db_manager = get_database_manager()
        self.es_manager = get_elasticsearch_manager()
        self.performance_monitor = get_performance_monitor()
        self.metrics_collector = MetricsCollector()
        
        # 告警配置
        self.alert_thresholds = {
            "cpu_percent": {"warning": 70, "critical": 90},
            "memory_percent": {"warning": 80, "critical": 95},
            "disk_usage": {"warning": 80, "critical": 95},
            "response_time_avg": {"warning": 1000, "critical": 3000},  # ms
            "error_rate": {"warning": 5, "critical": 10},  # %
            "database_connections": {"warning": 80, "critical": 95}  # % of max
        }
        
        # 告警历史
        self.alerts: List[Alert] = []
        self.alert_callbacks: List[Callable] = []
        
        # 监控状态
        self.monitoring_active = False
        self.monitoring_interval = 30  # 秒
    
    async def start_monitoring(self):
        """启动监控"""
        if self.monitoring_active:
            logger.warning("监控已在运行中")
            return
        
        self.monitoring_active = True
        logger.info("启动系统监控")
        
        # 启动监控任务
        asyncio.create_task(self._monitoring_loop())
    
    async def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        logger.info("停止系统监控")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集系统指标
                system_metrics = await self._collect_system_metrics()
                
                # 收集应用指标
                app_metrics = await self._collect_application_metrics()
                
                # 检查告警条件
                await self._check_alerts(system_metrics, app_metrics)
                
                # 存储指标数据
                await self._store_metrics(system_metrics, app_metrics)
                
                # 等待下次监控
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            
            # 磁盘使用率
            disk_usage = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage[partition.mountpoint] = (usage.used / usage.total) * 100
                except PermissionError:
                    continue
            
            # 网络IO
            network_io = psutil.net_io_counters()._asdict()
            
            # 进程数量
            process_count = len(psutil.pids())
            
            # 负载平均值
            load_average = list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else [0, 0, 0]
            
            return SystemMetrics(
                timestamp=datetime.utcnow(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_available=memory.available,
                disk_usage=disk_usage,
                network_io=network_io,
                process_count=process_count,
                load_average=load_average
            )
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return SystemMetrics(
                timestamp=datetime.utcnow(),
                cpu_percent=0,
                memory_percent=0,
                memory_available=0,
                disk_usage={},
                network_io={},
                process_count=0,
                load_average=[0, 0, 0]
            )
    
    async def _collect_application_metrics(self) -> ApplicationMetrics:
        """收集应用指标"""
        try:
            # 获取性能摘要
            perf_summary = self.performance_monitor.get_performance_summary(300)  # 5分钟
            
            # 数据库连接信息
            db_info = self.db_manager.get_database_info()
            db_connections = db_info.get("engine_info", {}).get("checked_out", 0)
            
            # 缓存命中率（模拟）
            cache_hit_rate = 85.0  # 实际应该从Redis获取
            
            # 队列大小（模拟）
            queue_size = 0  # 实际应该从消息队列获取
            
            return ApplicationMetrics(
                timestamp=datetime.utcnow(),
                active_connections=perf_summary.get("active_connections", 0),
                request_count=perf_summary.get("total_requests", 0),
                response_time_avg=perf_summary.get("avg_response_time", 0),
                response_time_p95=perf_summary.get("p95_response_time", 0),
                error_rate=perf_summary.get("error_rate", 0),
                database_connections=db_connections,
                cache_hit_rate=cache_hit_rate,
                queue_size=queue_size
            )
            
        except Exception as e:
            logger.error(f"收集应用指标失败: {e}")
            return ApplicationMetrics(
                timestamp=datetime.utcnow(),
                active_connections=0,
                request_count=0,
                response_time_avg=0,
                response_time_p95=0,
                error_rate=0,
                database_connections=0,
                cache_hit_rate=0,
                queue_size=0
            )
    
    async def _check_alerts(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """检查告警条件"""
        try:
            # 检查CPU使用率
            await self._check_threshold_alert(
                "cpu_percent",
                system_metrics.cpu_percent,
                "CPU使用率",
                f"CPU使用率: {system_metrics.cpu_percent:.1f}%"
            )
            
            # 检查内存使用率
            await self._check_threshold_alert(
                "memory_percent",
                system_metrics.memory_percent,
                "内存使用率",
                f"内存使用率: {system_metrics.memory_percent:.1f}%"
            )
            
            # 检查磁盘使用率
            for mount_point, usage in system_metrics.disk_usage.items():
                await self._check_threshold_alert(
                    "disk_usage",
                    usage,
                    f"磁盘使用率 ({mount_point})",
                    f"磁盘使用率 {mount_point}: {usage:.1f}%"
                )
            
            # 检查响应时间
            await self._check_threshold_alert(
                "response_time_avg",
                app_metrics.response_time_avg,
                "平均响应时间",
                f"平均响应时间: {app_metrics.response_time_avg:.1f}ms"
            )
            
            # 检查错误率
            await self._check_threshold_alert(
                "error_rate",
                app_metrics.error_rate,
                "错误率",
                f"错误率: {app_metrics.error_rate:.1f}%"
            )
            
        except Exception as e:
            logger.error(f"检查告警失败: {e}")
    
    async def _check_threshold_alert(
        self,
        metric_name: str,
        current_value: float,
        alert_title: str,
        alert_message: str
    ):
        """检查阈值告警"""
        try:
            thresholds = self.alert_thresholds.get(metric_name, {})
            
            # 检查临界告警
            if "critical" in thresholds and current_value >= thresholds["critical"]:
                await self._create_alert(
                    AlertLevel.CRITICAL,
                    f"{alert_title}临界告警",
                    f"{alert_message} (阈值: {thresholds['critical']})",
                    f"system.{metric_name}"
                )
            # 检查警告告警
            elif "warning" in thresholds and current_value >= thresholds["warning"]:
                await self._create_alert(
                    AlertLevel.WARNING,
                    f"{alert_title}警告",
                    f"{alert_message} (阈值: {thresholds['warning']})",
                    f"system.{metric_name}"
                )
                
        except Exception as e:
            logger.error(f"检查阈值告警失败: {metric_name}, 错误: {e}")
    
    async def _create_alert(
        self,
        level: AlertLevel,
        title: str,
        message: str,
        source: str
    ):
        """创建告警"""
        try:
            import uuid
            
            alert = Alert(
                id=str(uuid.uuid4()),
                level=level,
                title=title,
                message=message,
                timestamp=datetime.utcnow(),
                source=source
            )
            
            # 检查是否为重复告警
            recent_alerts = [
                a for a in self.alerts
                if a.source == source and a.level == level and not a.resolved
                and (datetime.utcnow() - a.timestamp).total_seconds() < 300  # 5分钟内
            ]
            
            if recent_alerts:
                logger.debug(f"跳过重复告警: {title}")
                return
            
            self.alerts.append(alert)
            
            # 记录告警日志
            logger.warning(f"告警: [{level.value.upper()}] {title} - {message}")
            
            # 触发告警回调
            for callback in self.alert_callbacks:
                try:
                    await callback(alert)
                except Exception as e:
                    logger.error(f"告警回调失败: {e}")
                    
        except Exception as e:
            logger.error(f"创建告警失败: {e}")
    
    async def _store_metrics(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """存储指标数据"""
        try:
            # 这里应该将指标数据存储到时序数据库（如InfluxDB）或Elasticsearch
            # 目前只记录日志
            logger.debug(f"系统指标: CPU={system_metrics.cpu_percent:.1f}%, "
                        f"内存={system_metrics.memory_percent:.1f}%")
            logger.debug(f"应用指标: 响应时间={app_metrics.response_time_avg:.1f}ms, "
                        f"错误率={app_metrics.error_rate:.1f}%")
            
        except Exception as e:
            logger.error(f"存储指标数据失败: {e}")
    
    def add_alert_callback(self, callback: Callable):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        try:
            # 同步获取当前指标
            system_metrics = psutil.cpu_percent(), psutil.virtual_memory().percent
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "system": {
                    "cpu_percent": system_metrics[0],
                    "memory_percent": system_metrics[1],
                    "disk_usage": {
                        partition.mountpoint: (psutil.disk_usage(partition.mountpoint).used / 
                                             psutil.disk_usage(partition.mountpoint).total) * 100
                        for partition in psutil.disk_partitions()
                        if partition.mountpoint != '/snap'  # 跳过snap分区
                    },
                    "process_count": len(psutil.pids())
                },
                "application": {
                    "database_status": self.db_manager.test_connection(),
                    "elasticsearch_status": self.es_manager.test_connection(),
                    "monitoring_active": self.monitoring_active
                }
            }
            
        except Exception as e:
            logger.error(f"获取当前指标失败: {e}")
            return {"error": str(e)}
    
    def get_alerts(
        self,
        level: Optional[AlertLevel] = None,
        resolved: Optional[bool] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取告警列表"""
        try:
            filtered_alerts = self.alerts
            
            # 按级别过滤
            if level:
                filtered_alerts = [a for a in filtered_alerts if a.level == level]
            
            # 按解决状态过滤
            if resolved is not None:
                filtered_alerts = [a for a in filtered_alerts if a.resolved == resolved]
            
            # 按时间排序并限制数量
            filtered_alerts.sort(key=lambda x: x.timestamp, reverse=True)
            filtered_alerts = filtered_alerts[:limit]
            
            # 转换为字典格式
            return [
                {
                    "id": alert.id,
                    "level": alert.level.value,
                    "title": alert.title,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat(),
                    "source": alert.source,
                    "resolved": alert.resolved,
                    "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None
                }
                for alert in filtered_alerts
            ]
            
        except Exception as e:
            logger.error(f"获取告警列表失败: {e}")
            return []
    
    def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        try:
            for alert in self.alerts:
                if alert.id == alert_id and not alert.resolved:
                    alert.resolved = True
                    alert.resolved_at = datetime.utcnow()
                    logger.info(f"告警已解决: {alert.title}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"解决告警失败: {e}")
            return False
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            # 检查各个组件的健康状态
            health_status = {
                "overall": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "components": {
                    "database": {
                        "status": "healthy" if self.db_manager.test_connection() else "unhealthy",
                        "details": self.db_manager.get_database_info()
                    },
                    "elasticsearch": {
                        "status": "healthy" if self.es_manager.test_connection() else "unhealthy",
                        "details": self.es_manager.get_cluster_info()
                    },
                    "monitoring": {
                        "status": "healthy" if self.monitoring_active else "stopped",
                        "details": {
                            "active": self.monitoring_active,
                            "interval": self.monitoring_interval,
                            "alerts_count": len([a for a in self.alerts if not a.resolved])
                        }
                    }
                }
            }
            
            # 检查是否有未解决的临界告警
            critical_alerts = [
                a for a in self.alerts
                if a.level == AlertLevel.CRITICAL and not a.resolved
                and (datetime.utcnow() - a.timestamp).total_seconds() < 3600  # 1小时内
            ]
            
            if critical_alerts:
                health_status["overall"] = "critical"
            elif any(comp["status"] == "unhealthy" for comp in health_status["components"].values()):
                health_status["overall"] = "degraded"
            
            return health_status
            
        except Exception as e:
            logger.error(f"获取健康状态失败: {e}")
            return {
                "overall": "unknown",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }


# 全局监控服务实例
_monitoring_service: Optional[MonitoringService] = None


def get_monitoring_service() -> MonitoringService:
    """获取监控服务实例"""
    global _monitoring_service
    if _monitoring_service is None:
        db = next(get_db())
        _monitoring_service = MonitoringService(db)
    return _monitoring_service


def init_monitoring_service(db: Session) -> MonitoringService:
    """初始化监控服务"""
    global _monitoring_service
    _monitoring_service = MonitoringService(db)
    return _monitoring_service
