"""
法律知识图谱构建与查询系统
基于内存的简化版本，支持实体关系建模和图谱查询
"""

import logging
import re
from typing import Dict, List, Tuple, Set, Any, Optional
from collections import defaultdict, deque
import json
import jieba
import jieba.posseg as pseg

logger = logging.getLogger(__name__)


class LegalEntity:
    """法律实体类"""
    
    def __init__(self, entity_id: str, name: str, entity_type: str, properties: Dict[str, Any] = None):
        """初始化法律实体
        
        Args:
            entity_id: 实体唯一标识
            name: 实体名称
            entity_type: 实体类型
            properties: 实体属性
        """
        self.entity_id = entity_id
        self.name = name
        self.entity_type = entity_type
        self.properties = properties or {}
        self.relationships = []  # 关系列表
    
    def add_relationship(self, relation_type: str, target_entity: str, properties: Dict[str, Any] = None):
        """添加关系"""
        relationship = {
            "type": relation_type,
            "target": target_entity,
            "properties": properties or {}
        }
        self.relationships.append(relationship)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "entity_id": self.entity_id,
            "name": self.name,
            "type": self.entity_type,
            "properties": self.properties,
            "relationships": self.relationships
        }


class LegalKnowledgeGraph:
    """法律知识图谱"""
    
    def __init__(self):
        """初始化知识图谱"""
        self.entities = {}  # 实体存储 {entity_id: LegalEntity}
        self.entity_index = defaultdict(list)  # 实体索引 {entity_type: [entity_ids]}
        self.name_index = {}  # 名称索引 {name: entity_id}
        self.relationship_index = defaultdict(list)  # 关系索引 {relation_type: [(source, target)]}
        
        # 初始化基础法律知识
        self._initialize_legal_knowledge()
        
        logger.info("法律知识图谱初始化完成")
    
    def _initialize_legal_knowledge(self):
        """初始化基础法律知识"""
        # 法律法规
        laws = [
            ("民法典", "民法", {"颁布时间": "2020年", "生效时间": "2021年1月1日"}),
            ("劳动合同法", "劳动法", {"颁布时间": "2007年", "修订时间": "2012年"}),
            ("公司法", "商法", {"颁布时间": "1993年", "最新修订": "2018年"}),
            ("刑法", "刑法", {"颁布时间": "1979年", "最新修订": "2020年"}),
            ("行政诉讼法", "行政法", {"颁布时间": "1989年", "修订时间": "2017年"})
        ]
        
        for name, category, properties in laws:
            entity_id = f"law_{len(self.entities)}"
            self.add_entity(entity_id, name, "法律法规", properties)
        
        # 法律概念
        concepts = [
            ("合同", "民法概念", {"定义": "当事人之间设立、变更、终止民事权利义务关系的协议"}),
            ("侵权", "民法概念", {"定义": "侵害他人合法权益的行为"}),
            ("犯罪", "刑法概念", {"定义": "危害社会，依法应受刑罚处罚的行为"}),
            ("行政行为", "行政法概念", {"定义": "行政机关行使行政职权的行为"}),
            ("劳动关系", "劳动法概念", {"定义": "劳动者与用人单位之间的法律关系"})
        ]
        
        for name, category, properties in concepts:
            entity_id = f"concept_{len(self.entities)}"
            self.add_entity(entity_id, name, "法律概念", properties)
        
        # 法律主体
        subjects = [
            ("自然人", "民事主体", {"权利能力": "出生至死亡", "行为能力": "根据年龄和精神状态"}),
            ("法人", "民事主体", {"类型": "营利法人、非营利法人", "责任": "有限责任"}),
            ("用人单位", "劳动主体", {"义务": "支付工资、缴纳社保", "权利": "管理权、解除权"}),
            ("劳动者", "劳动主体", {"权利": "获得报酬、休息休假", "义务": "履行劳动义务"})
        ]
        
        for name, category, properties in subjects:
            entity_id = f"subject_{len(self.entities)}"
            self.add_entity(entity_id, name, "法律主体", properties)
        
        # 建立关系
        self._build_initial_relationships()
    
    def _build_initial_relationships(self):
        """建立初始关系"""
        # 法律与概念的关系
        relationships = [
            ("民法典", "规定", "合同"),
            ("民法典", "规定", "侵权"),
            ("刑法", "规定", "犯罪"),
            ("劳动合同法", "规定", "劳动关系"),
            ("合同", "涉及", "自然人"),
            ("合同", "涉及", "法人"),
            ("劳动关系", "涉及", "用人单位"),
            ("劳动关系", "涉及", "劳动者")
        ]
        
        for source_name, relation_type, target_name in relationships:
            source_id = self.find_entity_by_name(source_name)
            target_id = self.find_entity_by_name(target_name)
            
            if source_id and target_id:
                self.add_relationship(source_id, relation_type, target_id)
    
    def add_entity(self, entity_id: str, name: str, entity_type: str, properties: Dict[str, Any] = None) -> bool:
        """添加实体"""
        try:
            entity = LegalEntity(entity_id, name, entity_type, properties)
            self.entities[entity_id] = entity
            self.entity_index[entity_type].append(entity_id)
            self.name_index[name] = entity_id
            
            logger.debug(f"添加实体: {name} ({entity_type})")
            return True
        
        except Exception as e:
            logger.error(f"添加实体失败: {e}")
            return False
    
    def add_relationship(self, source_id: str, relation_type: str, target_id: str, properties: Dict[str, Any] = None) -> bool:
        """添加关系"""
        try:
            if source_id not in self.entities or target_id not in self.entities:
                logger.warning(f"关系中的实体不存在: {source_id} -> {target_id}")
                return False
            
            self.entities[source_id].add_relationship(relation_type, target_id, properties)
            self.relationship_index[relation_type].append((source_id, target_id))
            
            logger.debug(f"添加关系: {source_id} -{relation_type}-> {target_id}")
            return True
        
        except Exception as e:
            logger.error(f"添加关系失败: {e}")
            return False
    
    def find_entity_by_name(self, name: str) -> Optional[str]:
        """根据名称查找实体ID"""
        return self.name_index.get(name)
    
    def get_entity(self, entity_id: str) -> Optional[LegalEntity]:
        """获取实体"""
        return self.entities.get(entity_id)
    
    def get_entities_by_type(self, entity_type: str) -> List[LegalEntity]:
        """根据类型获取实体列表"""
        entity_ids = self.entity_index.get(entity_type, [])
        return [self.entities[eid] for eid in entity_ids if eid in self.entities]
    
    def find_relationships(self, source_id: str = None, relation_type: str = None, target_id: str = None) -> List[Tuple[str, str, str]]:
        """查找关系"""
        results = []
        
        if relation_type:
            # 按关系类型查找
            candidates = self.relationship_index.get(relation_type, [])
        else:
            # 查找所有关系
            candidates = []
            for relations in self.relationship_index.values():
                candidates.extend(relations)
        
        for src, tgt in candidates:
            if (source_id is None or src == source_id) and (target_id is None or tgt == target_id):
                # 找到关系类型
                for rel_type, relations in self.relationship_index.items():
                    if (src, tgt) in relations:
                        results.append((src, rel_type, tgt))
                        break
        
        return results
    
    def get_neighbors(self, entity_id: str, relation_type: str = None, direction: str = "out") -> List[Tuple[str, str]]:
        """获取邻居节点
        
        Args:
            entity_id: 实体ID
            relation_type: 关系类型过滤
            direction: 方向 ("out", "in", "both")
        
        Returns:
            邻居列表 [(neighbor_id, relation_type), ...]
        """
        neighbors = []
        
        if direction in ["out", "both"]:
            # 出边
            entity = self.get_entity(entity_id)
            if entity:
                for rel in entity.relationships:
                    if relation_type is None or rel["type"] == relation_type:
                        neighbors.append((rel["target"], rel["type"]))
        
        if direction in ["in", "both"]:
            # 入边
            for rel_type, relations in self.relationship_index.items():
                if relation_type is None or rel_type == relation_type:
                    for src, tgt in relations:
                        if tgt == entity_id:
                            neighbors.append((src, rel_type))
        
        return neighbors
    
    def shortest_path(self, source_id: str, target_id: str, max_depth: int = 5) -> Optional[List[str]]:
        """查找最短路径"""
        if source_id == target_id:
            return [source_id]
        
        if source_id not in self.entities or target_id not in self.entities:
            return None
        
        # BFS查找最短路径
        queue = deque([(source_id, [source_id])])
        visited = {source_id}
        
        while queue:
            current_id, path = queue.popleft()
            
            if len(path) > max_depth:
                continue
            
            neighbors = self.get_neighbors(current_id, direction="both")
            
            for neighbor_id, _ in neighbors:
                if neighbor_id == target_id:
                    return path + [neighbor_id]
                
                if neighbor_id not in visited:
                    visited.add(neighbor_id)
                    queue.append((neighbor_id, path + [neighbor_id]))
        
        return None
    
    def query_by_pattern(self, pattern: Dict[str, Any]) -> List[Dict[str, Any]]:
        """模式查询
        
        Args:
            pattern: 查询模式，如 {"entity_type": "法律概念", "relation": "规定"}
        
        Returns:
            匹配结果列表
        """
        results = []
        
        # 根据实体类型过滤
        if "entity_type" in pattern:
            candidates = self.get_entities_by_type(pattern["entity_type"])
        else:
            candidates = list(self.entities.values())
        
        for entity in candidates:
            match = True
            
            # 检查属性匹配
            if "properties" in pattern:
                for key, value in pattern["properties"].items():
                    if key not in entity.properties or entity.properties[key] != value:
                        match = False
                        break
            
            # 检查关系匹配
            if match and "relation" in pattern:
                relation_type = pattern["relation"]
                has_relation = any(rel["type"] == relation_type for rel in entity.relationships)
                if not has_relation:
                    match = False
            
            if match:
                results.append(entity.to_dict())
        
        return results
    
    def extract_entities_from_text(self, text: str) -> List[Tuple[str, str, str]]:
        """从文本中提取实体
        
        Args:
            text: 输入文本
        
        Returns:
            实体列表 [(entity_name, entity_type, entity_id), ...]
        """
        extracted_entities = []
        
        # 使用词性标注提取潜在实体
        words = pseg.cut(text)
        
        for word, flag in words:
            # 检查是否为已知实体
            entity_id = self.find_entity_by_name(word)
            if entity_id:
                entity = self.get_entity(entity_id)
                if entity:
                    extracted_entities.append((word, entity.entity_type, entity_id))
            
            # 基于词性和模式识别新实体
            elif self._is_potential_legal_entity(word, flag):
                entity_type = self._classify_entity_type(word, flag)
                extracted_entities.append((word, entity_type, None))
        
        return extracted_entities
    
    def _is_potential_legal_entity(self, word: str, pos_tag: str) -> bool:
        """判断是否为潜在的法律实体"""
        # 法律相关词汇模式
        legal_patterns = [
            r'.*法$',  # 以"法"结尾
            r'.*条例$',  # 以"条例"结尾
            r'.*规定$',  # 以"规定"结尾
            r'.*办法$',  # 以"办法"结尾
            r'.*权$',   # 以"权"结尾
            r'.*责任$', # 以"责任"结尾
        ]
        
        # 检查词性（名词类）
        if not pos_tag.startswith('n'):
            return False
        
        # 检查是否匹配法律模式
        for pattern in legal_patterns:
            if re.match(pattern, word):
                return True
        
        return False
    
    def _classify_entity_type(self, word: str, pos_tag: str) -> str:
        """分类实体类型"""
        if word.endswith('法') or word.endswith('条例') or word.endswith('规定'):
            return "法律法规"
        elif word.endswith('权') or word.endswith('责任'):
            return "法律概念"
        elif word.endswith('人') or word.endswith('方'):
            return "法律主体"
        else:
            return "其他"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取图谱统计信息"""
        entity_type_counts = {}
        for entity_type, entity_ids in self.entity_index.items():
            entity_type_counts[entity_type] = len(entity_ids)
        
        relation_type_counts = {}
        for relation_type, relations in self.relationship_index.items():
            relation_type_counts[relation_type] = len(relations)
        
        return {
            "total_entities": len(self.entities),
            "total_relationships": sum(len(relations) for relations in self.relationship_index.values()),
            "entity_types": entity_type_counts,
            "relation_types": relation_type_counts
        }
    
    def export_to_json(self) -> str:
        """导出为JSON格式"""
        data = {
            "entities": [entity.to_dict() for entity in self.entities.values()],
            "statistics": self.get_statistics()
        }
        return json.dumps(data, ensure_ascii=False, indent=2)


# 全局知识图谱实例
knowledge_graph = LegalKnowledgeGraph()


def query_legal_knowledge(query: str) -> Dict[str, Any]:
    """查询法律知识的便捷函数"""
    # 提取实体
    entities = knowledge_graph.extract_entities_from_text(query)
    
    # 查找相关信息
    results = []
    for entity_name, entity_type, entity_id in entities:
        if entity_id:
            entity = knowledge_graph.get_entity(entity_id)
            if entity:
                neighbors = knowledge_graph.get_neighbors(entity_id, direction="both")
                results.append({
                    "entity": entity.to_dict(),
                    "neighbors": neighbors
                })
    
    return {
        "extracted_entities": entities,
        "knowledge_results": results,
        "statistics": knowledge_graph.get_statistics()
    }


def find_legal_relationships(entity1: str, entity2: str) -> List[str]:
    """查找两个法律实体之间的关系"""
    entity1_id = knowledge_graph.find_entity_by_name(entity1)
    entity2_id = knowledge_graph.find_entity_by_name(entity2)
    
    if not entity1_id or not entity2_id:
        return []
    
    path = knowledge_graph.shortest_path(entity1_id, entity2_id)
    if path:
        return [knowledge_graph.get_entity(eid).name for eid in path]
    else:
        return []
