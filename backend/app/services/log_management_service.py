"""
日志管理服务
提供日志收集、分析、查询、告警等功能
"""

import os
import json
import logging
import gzip
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import re
import asyncio
from concurrent.futures import ThreadPoolExecutor

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.elasticsearch_manager import get_elasticsearch_manager

logger = logging.getLogger(__name__)


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogSource(Enum):
    """日志来源"""
    APPLICATION = "application"
    ACCESS = "access"
    ERROR = "error"
    SECURITY = "security"
    AUDIT = "audit"
    SYSTEM = "system"


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: datetime
    level: LogLevel
    source: LogSource
    message: str
    module: Optional[str] = None
    function: Optional[str] = None
    line_number: Optional[int] = None
    user_id: Optional[str] = None
    ip_address: Optional[str] = None
    request_id: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None


class LogManagementService:
    """日志管理服务"""
    
    def __init__(self, db: Session):
        """
        初始化日志管理服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.es_manager = get_elasticsearch_manager()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 日志配置
        self.log_config = {
            "log_directory": "logs",
            "max_file_size": 100 * 1024 * 1024,  # 100MB
            "max_files": 30,  # 保留30个文件
            "compression": True,
            "index_logs": True,
            "retention_days": 90
        }
        
        # 确保日志目录存在
        self._ensure_log_directory()
        
        # 初始化Elasticsearch索引
        if self.log_config["index_logs"]:
            self._ensure_log_index()
    
    def _ensure_log_directory(self):
        """确保日志目录存在"""
        log_dir = Path(self.log_config["log_directory"])
        log_dir.mkdir(exist_ok=True)
        
        # 创建各种日志子目录
        for source in LogSource:
            (log_dir / source.value).mkdir(exist_ok=True)
    
    def _ensure_log_index(self):
        """确保日志索引存在"""
        index_name = "application_logs"
        mapping = {
            "properties": {
                "timestamp": {"type": "date"},
                "level": {"type": "keyword"},
                "source": {"type": "keyword"},
                "message": {
                    "type": "text",
                    "analyzer": "standard",
                    "fields": {
                        "keyword": {"type": "keyword", "ignore_above": 256}
                    }
                },
                "module": {"type": "keyword"},
                "function": {"type": "keyword"},
                "line_number": {"type": "integer"},
                "user_id": {"type": "keyword"},
                "ip_address": {"type": "ip"},
                "request_id": {"type": "keyword"},
                "extra_data": {"type": "object"},
                "indexed_at": {"type": "date"}
            }
        }
        
        settings = {
            "number_of_shards": 2,
            "number_of_replicas": 1,
            "index.lifecycle.name": "logs_policy",
            "index.lifecycle.rollover_alias": "logs"
        }
        
        self.es_manager.create_index(index_name, mapping, settings)
    
    async def log_entry(self, log_entry: LogEntry):
        """
        记录日志条目
        
        Args:
            log_entry: 日志条目
        """
        try:
            # 异步写入文件
            await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._write_log_to_file,
                log_entry
            )
            
            # 异步索引到Elasticsearch
            if self.log_config["index_logs"]:
                await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    self._index_log_entry,
                    log_entry
                )
            
        except Exception as e:
            logger.error(f"记录日志失败: {e}")
    
    def _write_log_to_file(self, log_entry: LogEntry):
        """写入日志到文件"""
        try:
            # 构建日志文件路径
            log_dir = Path(self.log_config["log_directory"]) / log_entry.source.value
            log_file = log_dir / f"{datetime.now().strftime('%Y-%m-%d')}.log"
            
            # 构建日志行
            log_line = self._format_log_entry(log_entry)
            
            # 写入文件
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_line + '\n')
            
            # 检查文件大小并轮转
            self._rotate_log_file_if_needed(log_file)
            
        except Exception as e:
            logger.error(f"写入日志文件失败: {e}")
    
    def _format_log_entry(self, log_entry: LogEntry) -> str:
        """格式化日志条目"""
        # 基本格式
        formatted = f"{log_entry.timestamp.isoformat()} [{log_entry.level.value}] {log_entry.message}"
        
        # 添加模块信息
        if log_entry.module:
            formatted += f" - {log_entry.module}"
            if log_entry.function:
                formatted += f".{log_entry.function}"
            if log_entry.line_number:
                formatted += f":{log_entry.line_number}"
        
        # 添加用户信息
        if log_entry.user_id:
            formatted += f" [user:{log_entry.user_id}]"
        
        # 添加IP地址
        if log_entry.ip_address:
            formatted += f" [ip:{log_entry.ip_address}]"
        
        # 添加请求ID
        if log_entry.request_id:
            formatted += f" [req:{log_entry.request_id}]"
        
        # 添加额外数据
        if log_entry.extra_data:
            formatted += f" [extra:{json.dumps(log_entry.extra_data, ensure_ascii=False)}]"
        
        return formatted
    
    def _rotate_log_file_if_needed(self, log_file: Path):
        """如果需要则轮转日志文件"""
        try:
            if log_file.stat().st_size > self.log_config["max_file_size"]:
                # 生成轮转文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                rotated_file = log_file.with_suffix(f'.{timestamp}.log')
                
                # 重命名当前文件
                log_file.rename(rotated_file)
                
                # 压缩文件
                if self.log_config["compression"]:
                    self._compress_log_file(rotated_file)
                
                # 清理旧文件
                self._cleanup_old_log_files(log_file.parent)
                
        except Exception as e:
            logger.error(f"轮转日志文件失败: {e}")
    
    def _compress_log_file(self, log_file: Path):
        """压缩日志文件"""
        try:
            compressed_file = log_file.with_suffix(log_file.suffix + '.gz')
            
            with open(log_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            # 删除原文件
            log_file.unlink()
            
        except Exception as e:
            logger.error(f"压缩日志文件失败: {e}")
    
    def _cleanup_old_log_files(self, log_dir: Path):
        """清理旧日志文件"""
        try:
            # 获取所有日志文件
            log_files = list(log_dir.glob('*.log*'))
            
            # 按修改时间排序
            log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除超过限制的文件
            for old_file in log_files[self.log_config["max_files"]:]:
                old_file.unlink()
                logger.info(f"删除旧日志文件: {old_file}")
                
        except Exception as e:
            logger.error(f"清理旧日志文件失败: {e}")
    
    def _index_log_entry(self, log_entry: LogEntry):
        """索引日志条目到Elasticsearch"""
        try:
            doc = {
                "timestamp": log_entry.timestamp.isoformat(),
                "level": log_entry.level.value,
                "source": log_entry.source.value,
                "message": log_entry.message,
                "module": log_entry.module,
                "function": log_entry.function,
                "line_number": log_entry.line_number,
                "user_id": log_entry.user_id,
                "ip_address": log_entry.ip_address,
                "request_id": log_entry.request_id,
                "extra_data": log_entry.extra_data,
                "indexed_at": datetime.utcnow().isoformat()
            }
            
            # 生成文档ID
            doc_id = f"{log_entry.timestamp.isoformat()}_{hash(log_entry.message)}"
            
            # 索引文档
            self.es_manager.index_document("application_logs", doc_id, doc)
            
        except Exception as e:
            logger.error(f"索引日志条目失败: {e}")
    
    async def search_logs(
        self,
        query: Optional[str] = None,
        level: Optional[LogLevel] = None,
        source: Optional[LogSource] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        page: int = 1,
        page_size: int = 100
    ) -> Dict[str, Any]:
        """
        搜索日志
        
        Args:
            query: 搜索查询
            level: 日志级别
            source: 日志来源
            start_time: 开始时间
            end_time: 结束时间
            user_id: 用户ID
            ip_address: IP地址
            page: 页码
            page_size: 每页大小
            
        Returns:
            搜索结果
        """
        try:
            # 构建搜索查询
            must_clauses = []
            filter_clauses = []
            
            # 文本查询
            if query:
                must_clauses.append({
                    "multi_match": {
                        "query": query,
                        "fields": ["message^2", "module", "function"],
                        "type": "best_fields"
                    }
                })
            else:
                must_clauses.append({"match_all": {}})
            
            # 过滤条件
            if level:
                filter_clauses.append({"term": {"level": level.value}})
            
            if source:
                filter_clauses.append({"term": {"source": source.value}})
            
            if user_id:
                filter_clauses.append({"term": {"user_id": user_id}})
            
            if ip_address:
                filter_clauses.append({"term": {"ip_address": ip_address}})
            
            # 时间范围
            if start_time or end_time:
                time_range = {}
                if start_time:
                    time_range["gte"] = start_time.isoformat()
                if end_time:
                    time_range["lte"] = end_time.isoformat()
                filter_clauses.append({"range": {"timestamp": time_range}})
            
            # 构建最终查询
            es_query = {
                "bool": {
                    "must": must_clauses,
                    "filter": filter_clauses
                }
            }
            
            # 排序条件
            sort_conditions = [{"timestamp": {"order": "desc"}}]
            
            # 高亮设置
            highlight = {
                "fields": {
                    "message": {},
                    "module": {},
                    "function": {}
                },
                "pre_tags": ["<mark>"],
                "post_tags": ["</mark>"]
            }
            
            # 计算分页参数
            from_index = (page - 1) * page_size
            
            # 执行搜索
            search_result = self.es_manager.search_documents(
                index_name="application_logs",
                query=es_query,
                size=page_size,
                from_=from_index,
                sort=sort_conditions,
                highlight=highlight
            )
            
            return {
                "query": query,
                "filters": {
                    "level": level.value if level else None,
                    "source": source.value if source else None,
                    "user_id": user_id,
                    "ip_address": ip_address,
                    "start_time": start_time.isoformat() if start_time else None,
                    "end_time": end_time.isoformat() if end_time else None
                },
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": search_result["total"],
                    "total_pages": (search_result["total"] + page_size - 1) // page_size
                },
                "results": search_result["results"],
                "took": search_result["took"]
            }
            
        except Exception as e:
            logger.error(f"搜索日志失败: {e}")
            return {
                "error": str(e),
                "results": [],
                "pagination": {"page": page, "page_size": page_size, "total_count": 0}
            }
    
    async def get_log_statistics(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        获取日志统计信息
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            统计信息
        """
        try:
            # 默认时间范围为最近24小时
            if not end_time:
                end_time = datetime.utcnow()
            if not start_time:
                start_time = end_time - timedelta(hours=24)
            
            # 构建聚合查询
            aggs = {
                "levels": {
                    "terms": {"field": "level", "size": 10}
                },
                "sources": {
                    "terms": {"field": "source", "size": 10}
                },
                "hourly_distribution": {
                    "date_histogram": {
                        "field": "timestamp",
                        "calendar_interval": "hour",
                        "min_doc_count": 0,
                        "extended_bounds": {
                            "min": start_time.isoformat(),
                            "max": end_time.isoformat()
                        }
                    }
                },
                "top_modules": {
                    "terms": {"field": "module", "size": 10}
                },
                "error_messages": {
                    "filter": {"term": {"level": "ERROR"}},
                    "aggs": {
                        "top_errors": {
                            "terms": {"field": "message.keyword", "size": 10}
                        }
                    }
                }
            }
            
            # 时间过滤
            query = {
                "bool": {
                    "filter": [
                        {
                            "range": {
                                "timestamp": {
                                    "gte": start_time.isoformat(),
                                    "lte": end_time.isoformat()
                                }
                            }
                        }
                    ]
                }
            }
            
            # 执行搜索
            search_result = self.es_manager.search_documents(
                index_name="application_logs",
                query=query,
                size=0,
                aggregations=aggs
            )
            
            # 处理聚合结果
            aggregations = search_result.get("aggregations", {})
            
            return {
                "time_range": {
                    "start": start_time.isoformat(),
                    "end": end_time.isoformat()
                },
                "total_logs": search_result["total"],
                "level_distribution": {
                    bucket["key"]: bucket["doc_count"]
                    for bucket in aggregations.get("levels", {}).get("buckets", [])
                },
                "source_distribution": {
                    bucket["key"]: bucket["doc_count"]
                    for bucket in aggregations.get("sources", {}).get("buckets", [])
                },
                "hourly_distribution": [
                    {
                        "time": bucket["key_as_string"],
                        "count": bucket["doc_count"]
                    }
                    for bucket in aggregations.get("hourly_distribution", {}).get("buckets", [])
                ],
                "top_modules": [
                    {"module": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggregations.get("top_modules", {}).get("buckets", [])
                ],
                "top_errors": [
                    {"message": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggregations.get("error_messages", {}).get("top_errors", {}).get("buckets", [])
                ]
            }
            
        except Exception as e:
            logger.error(f"获取日志统计失败: {e}")
            return {"error": str(e)}
    
    async def cleanup_old_logs(self):
        """清理过期日志"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=self.log_config["retention_days"])
            
            # 清理文件系统中的日志
            log_dir = Path(self.log_config["log_directory"])
            for source_dir in log_dir.iterdir():
                if source_dir.is_dir():
                    for log_file in source_dir.glob("*.log*"):
                        if datetime.fromtimestamp(log_file.stat().st_mtime) < cutoff_date:
                            log_file.unlink()
                            logger.info(f"删除过期日志文件: {log_file}")
            
            # 清理Elasticsearch中的日志
            if self.log_config["index_logs"]:
                delete_query = {
                    "range": {
                        "timestamp": {
                            "lt": cutoff_date.isoformat()
                        }
                    }
                }
                
                # 这里应该使用delete_by_query API
                logger.info(f"清理Elasticsearch中 {cutoff_date} 之前的日志")
            
        except Exception as e:
            logger.error(f"清理过期日志失败: {e}")
    
    def get_log_files_info(self) -> List[Dict[str, Any]]:
        """获取日志文件信息"""
        try:
            files_info = []
            log_dir = Path(self.log_config["log_directory"])
            
            for source_dir in log_dir.iterdir():
                if source_dir.is_dir():
                    for log_file in source_dir.glob("*.log*"):
                        stat = log_file.stat()
                        files_info.append({
                            "path": str(log_file),
                            "source": source_dir.name,
                            "size": stat.st_size,
                            "size_mb": round(stat.st_size / (1024 * 1024), 2),
                            "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            "compressed": log_file.suffix == '.gz'
                        })
            
            # 按修改时间排序
            files_info.sort(key=lambda x: x["modified"], reverse=True)
            
            return files_info
            
        except Exception as e:
            logger.error(f"获取日志文件信息失败: {e}")
            return []


# 全局日志管理服务实例
_log_management_service: Optional[LogManagementService] = None


def get_log_management_service() -> LogManagementService:
    """获取日志管理服务实例"""
    global _log_management_service
    if _log_management_service is None:
        db = next(get_db())
        _log_management_service = LogManagementService(db)
    return _log_management_service


def init_log_management_service(db: Session) -> LogManagementService:
    """初始化日志管理服务"""
    global _log_management_service
    _log_management_service = LogManagementService(db)
    return _log_management_service
