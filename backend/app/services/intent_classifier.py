"""
法律问题意图识别与分类系统
基于规则和机器学习的混合方法
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, Counter
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split
import jieba

logger = logging.getLogger(__name__)


class LegalIntentClassifier:
    """法律意图分类器"""
    
    def __init__(self):
        """初始化意图分类器"""
        self.intent_patterns = self._load_intent_patterns()
        self.intent_keywords = self._load_intent_keywords()
        self.ml_classifier = None
        self.is_trained = False
        
        logger.info("法律意图分类器初始化完成")
    
    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """加载意图识别模式"""
        return {
            "咨询": [
                r"请问|咨询|想了解|想知道|如何|怎么|什么是|能否|可以吗",
                r"帮我|告诉我|解释|说明|介绍",
                r"法律规定|相关法律|法条|条款"
            ],
            "投诉": [
                r"投诉|举报|控告|告发",
                r"违法|违规|不合理|不公平|欺骗|诈骗",
                r"要求赔偿|索赔|维权"
            ],
            "申请": [
                r"申请|办理|提交|递交",
                r"如何申请|申请流程|申请条件|申请材料",
                r"仲裁申请|诉讼申请|复议申请"
            ],
            "查询": [
                r"查询|查看|查找|搜索",
                r"案件进度|处理结果|审理情况",
                r"法条查询|判例查询|案例查询"
            ],
            "求助": [
                r"求助|帮助|救助|援助",
                r"紧急|急需|马上|立即",
                r"不知道怎么办|该怎么办|怎么处理"
            ],
            "建议": [
                r"建议|意见|看法|观点",
                r"应该|最好|推荐|建议",
                r"处理方案|解决方案|应对策略"
            ],
            "确认": [
                r"确认|核实|验证|是否",
                r"对不对|正确吗|是这样吗",
                r"法律效力|是否有效|是否合法"
            ],
            "分析": [
                r"分析|评估|判断|研究",
                r"风险分析|法律分析|案例分析",
                r"胜诉可能|败诉风险|法律后果"
            ]
        }
    
    def _load_intent_keywords(self) -> Dict[str, List[str]]:
        """加载意图关键词"""
        return {
            "咨询": [
                "咨询", "请教", "了解", "知道", "解释", "说明", "介绍",
                "法律", "法规", "条例", "规定", "办法"
            ],
            "投诉": [
                "投诉", "举报", "控告", "告发", "违法", "违规", "欺骗",
                "诈骗", "不合理", "不公平", "维权", "赔偿", "索赔"
            ],
            "申请": [
                "申请", "办理", "提交", "递交", "流程", "条件", "材料",
                "仲裁", "诉讼", "复议", "执行"
            ],
            "查询": [
                "查询", "查看", "查找", "搜索", "进度", "结果", "情况",
                "法条", "判例", "案例", "先例"
            ],
            "求助": [
                "求助", "帮助", "救助", "援助", "紧急", "急需", "马上",
                "立即", "怎么办", "如何处理"
            ],
            "建议": [
                "建议", "意见", "看法", "观点", "应该", "最好", "推荐",
                "方案", "策略", "对策"
            ],
            "确认": [
                "确认", "核实", "验证", "是否", "对不对", "正确",
                "有效", "合法", "效力"
            ],
            "分析": [
                "分析", "评估", "判断", "研究", "风险", "可能性",
                "胜诉", "败诉", "后果", "影响"
            ]
        }
    
    def classify_intent(self, text: str) -> Dict[str, Any]:
        """分类意图
        
        Args:
            text: 输入文本
        
        Returns:
            意图分类结果
        """
        # 规则基础分类
        rule_scores = self._rule_based_classification(text)
        
        # 关键词基础分类
        keyword_scores = self._keyword_based_classification(text)
        
        # 机器学习分类（如果模型已训练）
        ml_scores = {}
        if self.is_trained and self.ml_classifier:
            ml_scores = self._ml_based_classification(text)
        
        # 综合评分
        final_scores = self._combine_scores(rule_scores, keyword_scores, ml_scores)
        
        # 确定最终意图
        if not final_scores:
            intent = "未知"
            confidence = 0.0
        else:
            intent = max(final_scores, key=final_scores.get)
            confidence = final_scores[intent]
        
        return {
            "intent": intent,
            "confidence": confidence,
            "scores": final_scores,
            "rule_scores": rule_scores,
            "keyword_scores": keyword_scores,
            "ml_scores": ml_scores
        }
    
    def _rule_based_classification(self, text: str) -> Dict[str, float]:
        """基于规则的分类"""
        scores = defaultdict(float)
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    # 根据匹配数量和模式复杂度计算分数
                    pattern_score = len(matches) * (len(pattern) / 100.0)
                    scores[intent] += pattern_score
        
        # 归一化分数
        if scores:
            max_score = max(scores.values())
            scores = {k: v / max_score for k, v in scores.items()}
        
        return dict(scores)
    
    def _keyword_based_classification(self, text: str) -> Dict[str, float]:
        """基于关键词的分类"""
        scores = defaultdict(float)
        
        # 使用jieba分词
        tokens = list(jieba.cut(text))
        
        for intent, keywords in self.intent_keywords.items():
            for keyword in keywords:
                if keyword in tokens:
                    # 根据关键词长度和重要性计算权重
                    weight = len(keyword) / 5.0 + 0.5
                    scores[intent] += weight
        
        # 归一化分数
        if scores:
            max_score = max(scores.values())
            scores = {k: v / max_score for k, v in scores.items()}
        
        return dict(scores)
    
    def _ml_based_classification(self, text: str) -> Dict[str, float]:
        """基于机器学习的分类"""
        try:
            if not self.ml_classifier:
                return {}
            
            # 预测概率
            probabilities = self.ml_classifier.predict_proba([text])[0]
            classes = self.ml_classifier.classes_
            
            return dict(zip(classes, probabilities))
        
        except Exception as e:
            logger.warning(f"机器学习分类失败: {e}")
            return {}
    
    def _combine_scores(self, rule_scores: Dict[str, float], 
                       keyword_scores: Dict[str, float],
                       ml_scores: Dict[str, float]) -> Dict[str, float]:
        """综合评分"""
        all_intents = set(rule_scores.keys()) | set(keyword_scores.keys()) | set(ml_scores.keys())
        combined_scores = {}
        
        for intent in all_intents:
            rule_score = rule_scores.get(intent, 0.0)
            keyword_score = keyword_scores.get(intent, 0.0)
            ml_score = ml_scores.get(intent, 0.0)
            
            # 加权平均
            if ml_scores:  # 如果有ML分数，给予更高权重
                combined_score = 0.3 * rule_score + 0.3 * keyword_score + 0.4 * ml_score
            else:
                combined_score = 0.6 * rule_score + 0.4 * keyword_score
            
            combined_scores[intent] = combined_score
        
        return combined_scores
    
    def train_ml_classifier(self, training_data: List[Tuple[str, str]]) -> None:
        """训练机器学习分类器
        
        Args:
            training_data: 训练数据，格式为[(text, intent), ...]
        """
        try:
            if len(training_data) < 10:
                logger.warning("训练数据太少，跳过机器学习模型训练")
                return
            
            texts, labels = zip(*training_data)
            
            # 创建管道
            self.ml_classifier = Pipeline([
                ('tfidf', TfidfVectorizer(
                    tokenizer=lambda x: list(jieba.cut(x)),
                    lowercase=False,
                    token_pattern=None,
                    max_features=1000
                )),
                ('classifier', MultinomialNB(alpha=0.1))
            ])
            
            # 训练模型
            self.ml_classifier.fit(texts, labels)
            self.is_trained = True
            
            logger.info(f"机器学习分类器训练完成，使用{len(training_data)}个样本")
            
        except Exception as e:
            logger.error(f"训练机器学习分类器失败: {e}")
    
    def get_intent_explanation(self, intent: str) -> str:
        """获取意图解释"""
        explanations = {
            "咨询": "用户想要了解法律知识、法规条文或寻求法律建议",
            "投诉": "用户要举报违法行为或投诉不当处理",
            "申请": "用户想要申请某项法律服务或办理相关手续",
            "查询": "用户要查询案件进度、法条内容或相关信息",
            "求助": "用户遇到紧急情况需要立即帮助或指导",
            "建议": "用户寻求处理问题的建议或解决方案",
            "确认": "用户要确认某个法律事实或验证信息的准确性",
            "分析": "用户需要对法律问题进行深入分析或风险评估",
            "未知": "无法确定用户的具体意图，需要进一步澄清"
        }
        
        return explanations.get(intent, f"关于{intent}的意图")
    
    def extract_intent_entities(self, text: str, intent: str) -> Dict[str, List[str]]:
        """提取意图相关的实体
        
        Args:
            text: 输入文本
            intent: 识别的意图
        
        Returns:
            实体字典
        """
        entities = defaultdict(list)
        
        # 根据不同意图提取不同类型的实体
        if intent == "咨询":
            # 提取法律概念
            legal_concepts = re.findall(r'《[^》]+》|[^，。！？]*法[^，。！？]*', text)
            entities["法律概念"].extend(legal_concepts)
        
        elif intent == "投诉":
            # 提取被投诉对象和违法行为
            subjects = re.findall(r'[^，。！？]*(?:公司|企业|单位|个人)[^，。！？]*', text)
            entities["投诉对象"].extend(subjects)
            
            violations = re.findall(r'[^，。！？]*(?:违法|违规|欺骗|诈骗)[^，。！？]*', text)
            entities["违法行为"].extend(violations)
        
        elif intent == "申请":
            # 提取申请类型
            applications = re.findall(r'申请[^，。！？]*', text)
            entities["申请类型"].extend(applications)
        
        elif intent == "查询":
            # 提取查询对象
            queries = re.findall(r'查询[^，。！？]*|[^，。！？]*进度[^，。！？]*', text)
            entities["查询对象"].extend(queries)
        
        # 通用实体提取
        # 金额
        amounts = re.findall(r'\d+(?:\.\d+)?(?:万|千|百)?元', text)
        entities["金额"].extend(amounts)
        
        # 时间
        times = re.findall(r'\d{4}年\d{1,2}月\d{1,2}日|\d{1,2}(?:个)?(?:月|年|日|天)', text)
        entities["时间"].extend(times)
        
        # 清理空值
        entities = {k: list(set(v)) for k, v in entities.items() if v}
        
        return dict(entities)
    
    def get_intent_suggestions(self, intent: str) -> List[str]:
        """获取意图相关的建议
        
        Args:
            intent: 识别的意图
        
        Returns:
            建议列表
        """
        suggestions = {
            "咨询": [
                "请详细描述您的具体问题",
                "您可以提供相关的法律条文或案例",
                "建议说明事件的时间、地点和涉及的人员"
            ],
            "投诉": [
                "请保留相关证据材料",
                "建议先尝试协商解决",
                "可以向相关监管部门举报"
            ],
            "申请": [
                "请准备相关申请材料",
                "了解申请的条件和流程",
                "建议咨询专业律师"
            ],
            "查询": [
                "请提供准确的查询信息",
                "可以通过官方渠道查询",
                "注意保护个人隐私信息"
            ],
            "求助": [
                "如遇紧急情况请立即报警",
                "可以寻求法律援助",
                "建议联系专业律师"
            ],
            "建议": [
                "建议详细分析具体情况",
                "可以考虑多种解决方案",
                "必要时寻求专业法律意见"
            ],
            "确认": [
                "建议查阅相关法律条文",
                "可以咨询专业律师确认",
                "注意法律条文的时效性"
            ],
            "分析": [
                "建议提供完整的案例信息",
                "可以参考类似案例",
                "建议寻求专业法律分析"
            ]
        }
        
        return suggestions.get(intent, ["建议提供更多详细信息"])


# 全局意图分类器实例
intent_classifier = LegalIntentClassifier()


def classify_legal_intent(text: str) -> Dict[str, Any]:
    """分类法律意图的便捷函数"""
    return intent_classifier.classify_intent(text)


def get_intent_suggestions(intent: str) -> List[str]:
    """获取意图建议的便捷函数"""
    return intent_classifier.get_intent_suggestions(intent)
