"""
案例搜索服务
"""

import uuid
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text
from sqlalchemy.orm import selectinload
import logging
from datetime import datetime

from app.models.legal_case import LegalCase, CaseSimilarity
from app.core.elasticsearch import get_elasticsearch_client
from app.core.exceptions import ValidationException
from app.core.audit import get_audit_logger, AuditEventType

logger = logging.getLogger(__name__)


class CaseSearchService:
    """案例搜索服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def search_cases(
        self,
        query: str = None,
        case_type: str = None,
        court_name: str = None,
        precedent_value: str = None,
        date_range: Dict[str, str] = None,
        page: int = 1,
        page_size: int = 20,
        sort_by: str = "relevance",
        user_id: uuid.UUID = None
    ) -> Dict[str, Any]:
        """搜索案例"""
        
        logger.info(f"搜索案例: query={query}, type={case_type}, page={page}")
        
        # 构建过滤条件
        filters = {}
        if case_type:
            filters["case_type"] = case_type
        if court_name:
            filters["court_name"] = court_name
        if precedent_value:
            filters["precedent_value"] = precedent_value
        if date_range:
            filters["date_range"] = date_range
        
        # 构建排序条件
        sort_options = self._build_sort_options(sort_by)
        
        # 计算分页参数
        from_ = (page - 1) * page_size
        
        try:
            # 使用Elasticsearch搜索
            es_client = await get_elasticsearch_client()
            es_result = await es_client.search_cases(
                query=query or "",
                filters=filters,
                size=page_size,
                from_=from_,
                sort=sort_options
            )
            
            # 如果Elasticsearch搜索成功，使用ES结果
            if es_result["hits"]:
                cases = await self._enrich_es_results(es_result["hits"])
                total = es_result["total"]
            else:
                # 回退到数据库搜索
                cases, total = await self._database_search(
                    query, filters, page, page_size, sort_by
                )
            
        except Exception as e:
            logger.warning(f"Elasticsearch搜索失败，回退到数据库搜索: {e}")
            # 回退到数据库搜索
            cases, total = await self._database_search(
                query, filters, page, page_size, sort_by
            )
        
        # 记录搜索日志
        if user_id:
            await self._log_search_activity(user_id, query, filters, total)
        
        return {
            "cases": cases,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        }
    
    async def _database_search(
        self,
        query: str,
        filters: Dict[str, Any],
        page: int,
        page_size: int,
        sort_by: str
    ) -> Tuple[List[Dict[str, Any]], int]:
        """数据库搜索（回退方案）"""
        
        # 构建查询条件
        conditions = []
        
        if query:
            # 简单的文本搜索
            search_condition = or_(
                LegalCase.title.ilike(f"%{query}%"),
                LegalCase.case_summary.ilike(f"%{query}%"),
                LegalCase.keywords.contains([query])
            )
            conditions.append(search_condition)
        
        if filters.get("case_type"):
            conditions.append(LegalCase.case_type == filters["case_type"])
        
        if filters.get("court_name"):
            conditions.append(LegalCase.court_name.ilike(f"%{filters['court_name']}%"))
        
        if filters.get("precedent_value"):
            conditions.append(LegalCase.precedent_value == filters["precedent_value"])
        
        if filters.get("date_range"):
            date_range = filters["date_range"]
            if date_range.get("gte"):
                conditions.append(LegalCase.judgment_date >= date_range["gte"])
            if date_range.get("lte"):
                conditions.append(LegalCase.judgment_date <= date_range["lte"])
        
        # 构建基础查询
        base_query = select(LegalCase)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 添加排序
        if sort_by == "date_desc":
            base_query = base_query.order_by(LegalCase.judgment_date.desc())
        elif sort_by == "date_asc":
            base_query = base_query.order_by(LegalCase.judgment_date.asc())
        elif sort_by == "citation_desc":
            base_query = base_query.order_by(LegalCase.citation_count.desc())
        else:  # relevance or default
            base_query = base_query.order_by(LegalCase.created_at.desc())
        
        # 获取总数
        count_query = select(func.count(LegalCase.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()
        
        # 分页查询
        paginated_query = base_query.limit(page_size).offset((page - 1) * page_size)
        result = await self.db.execute(paginated_query)
        cases = result.scalars().all()
        
        # 转换为字典格式
        case_dicts = []
        for case in cases:
            case_dict = {
                "id": str(case.id),
                "case_number": case.case_number,
                "title": case.title,
                "court_name": case.court_name,
                "case_type": case.case_type,
                "judgment_date": case.judgment_date.isoformat() if case.judgment_date else None,
                "case_summary": case.case_summary,
                "keywords": case.keywords,
                "precedent_value": case.precedent_value,
                "citation_count": case.citation_count,
                "created_at": case.created_at.isoformat(),
                "score": 1.0  # 数据库搜索没有相关性分数
            }
            case_dicts.append(case_dict)
        
        return case_dicts, total
    
    async def _enrich_es_results(self, es_hits: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """丰富Elasticsearch搜索结果"""
        
        # 提取案例ID
        case_ids = [hit["id"] for hit in es_hits]
        
        # 从数据库获取完整信息（如果需要）
        stmt = select(LegalCase).where(LegalCase.id.in_(case_ids))
        result = await self.db.execute(stmt)
        db_cases = {str(case.id): case for case in result.scalars().all()}
        
        # 合并ES结果和数据库结果
        enriched_cases = []
        for hit in es_hits:
            case_id = hit["id"]
            db_case = db_cases.get(case_id)
            
            if db_case:
                # 使用数据库中的最新数据，保留ES的搜索相关信息
                case_dict = {
                    "id": str(db_case.id),
                    "case_number": db_case.case_number,
                    "title": db_case.title,
                    "court_name": db_case.court_name,
                    "case_type": db_case.case_type,
                    "judgment_date": db_case.judgment_date.isoformat() if db_case.judgment_date else None,
                    "case_summary": db_case.case_summary,
                    "keywords": db_case.keywords,
                    "precedent_value": db_case.precedent_value,
                    "citation_count": db_case.citation_count,
                    "created_at": db_case.created_at.isoformat(),
                    "score": hit.get("score", 0),
                    "highlight": hit.get("highlight", {})
                }
            else:
                # 如果数据库中没有找到，使用ES中的数据
                case_dict = hit
            
            enriched_cases.append(case_dict)
        
        return enriched_cases
    
    def _build_sort_options(self, sort_by: str) -> List[Dict[str, Any]]:
        """构建排序选项"""
        
        if sort_by == "date_desc":
            return [{"judgment_date": {"order": "desc"}}]
        elif sort_by == "date_asc":
            return [{"judgment_date": {"order": "asc"}}]
        elif sort_by == "citation_desc":
            return [{"citation_count": {"order": "desc"}}]
        elif sort_by == "relevance":
            return [{"_score": {"order": "desc"}}]
        else:
            # 默认排序：相关性 + 引用次数 + 日期
            return [
                {"_score": {"order": "desc"}},
                {"citation_count": {"order": "desc"}},
                {"judgment_date": {"order": "desc"}}
            ]
    
    async def get_case_detail(self, case_id: uuid.UUID, user_id: uuid.UUID = None) -> Optional[Dict[str, Any]]:
        """获取案例详情"""
        
        stmt = select(LegalCase).where(LegalCase.id == case_id)
        result = await self.db.execute(stmt)
        case = result.scalar_one_or_none()
        
        if not case:
            return None
        
        # 记录查看日志
        if user_id:
            audit_logger = await get_audit_logger(self.db)
            await audit_logger.log_event(
                event_type=AuditEventType.DATA_READ,
                message=f"查看案例详情: {case.title}",
                user_id=user_id,
                resource_type="legal_case",
                resource_id=str(case_id),
                action="view_detail"
            )
        
        # 增加查看次数（如果有该字段）
        # case.view_count = (case.view_count or 0) + 1
        # await self.db.commit()
        
        return {
            "id": str(case.id),
            "case_number": case.case_number,
            "title": case.title,
            "court_name": case.court_name,
            "case_type": case.case_type,
            "judgment_date": case.judgment_date.isoformat() if case.judgment_date else None,
            "case_summary": case.case_summary,
            "full_text": case.full_text,
            "keywords": case.keywords,
            "precedent_value": case.precedent_value,
            "citation_count": case.citation_count,
            "legal_provisions": case.legal_provisions,
            "created_at": case.created_at.isoformat(),
            "updated_at": case.updated_at.isoformat() if case.updated_at else None
        }
    
    async def get_similar_cases(self, case_id: uuid.UUID, limit: int = 5) -> List[Dict[str, Any]]:
        """获取相似案例"""
        
        # 首先尝试从相似度表中获取
        stmt = select(CaseSimilarity).where(
            CaseSimilarity.case_id == case_id
        ).order_by(
            CaseSimilarity.similarity_score.desc()
        ).limit(limit)
        
        result = await self.db.execute(stmt)
        similarities = result.scalars().all()
        
        if similarities:
            # 获取相似案例的详细信息
            similar_case_ids = [sim.similar_case_id for sim in similarities]
            stmt = select(LegalCase).where(LegalCase.id.in_(similar_case_ids))
            result = await self.db.execute(stmt)
            similar_cases = result.scalars().all()
            
            # 构建结果
            similar_case_dict = {str(case.id): case for case in similar_cases}
            results = []
            
            for sim in similarities:
                similar_case = similar_case_dict.get(str(sim.similar_case_id))
                if similar_case:
                    results.append({
                        "id": str(similar_case.id),
                        "case_number": similar_case.case_number,
                        "title": similar_case.title,
                        "court_name": similar_case.court_name,
                        "case_type": similar_case.case_type,
                        "judgment_date": similar_case.judgment_date.isoformat() if similar_case.judgment_date else None,
                        "case_summary": similar_case.case_summary,
                        "precedent_value": similar_case.precedent_value,
                        "citation_count": similar_case.citation_count,
                        "similarity_score": float(sim.similarity_score),
                        "created_at": similar_case.created_at.isoformat()
                    })
            
            return results
        
        # 如果没有预计算的相似度，使用简单的关键词匹配
        return await self._find_similar_by_keywords(case_id, limit)
    
    async def _find_similar_by_keywords(self, case_id: uuid.UUID, limit: int) -> List[Dict[str, Any]]:
        """基于关键词查找相似案例"""
        
        # 获取原案例
        stmt = select(LegalCase).where(LegalCase.id == case_id)
        result = await self.db.execute(stmt)
        original_case = result.scalar_one_or_none()
        
        if not original_case or not original_case.keywords:
            return []
        
        # 基于关键词查找相似案例
        stmt = select(LegalCase).where(
            and_(
                LegalCase.id != case_id,
                LegalCase.keywords.overlap(original_case.keywords)
            )
        ).order_by(
            LegalCase.citation_count.desc()
        ).limit(limit)
        
        result = await self.db.execute(stmt)
        similar_cases = result.scalars().all()
        
        results = []
        for case in similar_cases:
            # 计算简单的相似度（共同关键词数量）
            common_keywords = set(original_case.keywords) & set(case.keywords)
            similarity_score = len(common_keywords) / max(len(original_case.keywords), 1)
            
            results.append({
                "id": str(case.id),
                "case_number": case.case_number,
                "title": case.title,
                "court_name": case.court_name,
                "case_type": case.case_type,
                "judgment_date": case.judgment_date.isoformat() if case.judgment_date else None,
                "case_summary": case.case_summary,
                "precedent_value": case.precedent_value,
                "citation_count": case.citation_count,
                "similarity_score": similarity_score,
                "created_at": case.created_at.isoformat()
            })
        
        return results
    
    async def _log_search_activity(
        self,
        user_id: uuid.UUID,
        query: str,
        filters: Dict[str, Any],
        result_count: int
    ):
        """记录搜索活动"""
        
        audit_logger = await get_audit_logger(self.db)
        await audit_logger.log_event(
            event_type=AuditEventType.DATA_READ,
            message=f"搜索案例: {query or '全部'}",
            user_id=user_id,
            resource_type="legal_case",
            action="search",
            details={
                "query": query,
                "filters": filters,
                "result_count": result_count
            }
        )
