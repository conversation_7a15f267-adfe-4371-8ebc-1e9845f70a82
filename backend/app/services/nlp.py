"""
自然语言处理服务
"""

import re
import jieba
import jieba.posseg as pseg
import jieba.analyse
from typing import List, Dict, Any, Optional, Tuple, Set
from collections import Counter, defaultdict
import logging
import math
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

logger = logging.getLogger(__name__)

# 加载自定义词典
import os
legal_dict_path = os.path.join(os.path.dirname(__file__), "../../../data/legal_dict.txt")
if os.path.exists(legal_dict_path):
    jieba.load_userdict(legal_dict_path)  # 法律专业词典
    logger.info("法律专业词典加载成功")
else:
    logger.warning(f"法律专业词典文件不存在: {legal_dict_path}")


class LegalNLPProcessor:
    """法律领域NLP处理器"""
    
    def __init__(self):
        # 法律领域关键词（扩展版）
        self.legal_keywords = {
            "合同": ["合同", "协议", "约定", "条款", "违约", "履行", "解除", "终止", "要约", "承诺", "违约金", "定金", "格式条款"],
            "劳动": ["劳动", "工作", "雇佣", "工资", "加班", "辞职", "辞退", "社保", "劳动合同", "用人单位", "劳动者", "工伤", "年休假"],
            "婚姻": ["婚姻", "离婚", "结婚", "财产", "抚养", "赡养", "分割", "夫妻", "共同财产", "个人财产", "抚养费", "探视权"],
            "房产": ["房屋", "房产", "买卖", "租赁", "物业", "装修", "产权", "土地使用权", "房屋所有权", "不动产登记", "房产证"],
            "交通": ["交通", "车祸", "事故", "撞车", "保险", "赔偿", "责任", "交通事故", "机动车", "道路交通安全法", "驾驶证"],
            "刑事": ["犯罪", "刑事", "盗窃", "诈骗", "故意", "过失", "判决", "犯罪构成", "正当防卫", "缓刑", "假释", "累犯"],
            "民事": ["民事", "侵权", "损害", "赔偿", "诉讼", "仲裁", "调解", "民事责任", "过错责任", "无过错责任", "精神损害"],
            "公司": ["公司", "企业", "股东", "董事", "经营", "破产", "清算", "公司法", "股权", "法定代表人", "公司章程"],
            "知识产权": ["专利", "商标", "著作权", "版权", "侵权", "许可", "专利权", "商标权", "发明专利", "商业秘密"],
            "税务": ["税务", "纳税", "发票", "税收", "申报", "减免", "增值税", "所得税", "税务登记", "纳税义务"],
            "继承": ["继承", "遗产", "遗嘱", "继承人", "被继承人", "法定继承", "遗嘱继承", "遗赠", "继承权"],
            "行政": ["行政", "行政机关", "行政行为", "行政许可", "行政处罚", "行政复议", "行政诉讼", "行政赔偿"],
            "证据": ["证据", "举证", "质证", "认证", "物证", "书证", "证人证言", "鉴定意见", "电子数据"],
            "执行": ["执行", "强制执行", "查封", "扣押", "冻结", "拍卖", "变卖", "执行异议", "执行和解"]
        }
        
        # 停用词
        self.stop_words = self._load_stop_words()
        
        # 法律实体识别模式（扩展版）
        self.entity_patterns = {
            "法条": re.compile(r'《[^》]+》第?\d+条?(?:第?\d+款)?(?:第?\d+项)?'),
            "金额": re.compile(r'\d+(?:\.\d+)?(?:万|千|百|十)?元'),
            "日期": re.compile(r'\d{4}年\d{1,2}月\d{1,2}日|\d{4}-\d{1,2}-\d{1,2}|\d{4}/\d{1,2}/\d{1,2}'),
            "时间期限": re.compile(r'\d{1,2}(?:个)?(?:月|年|日|天|小时|分钟|工作日)(?:内|以内|之内)?'),
            "法院": re.compile(r'[^，。！？]*(?:人民法院|高级人民法院|中级人民法院|基层人民法院|最高人民法院|仲裁委员会|仲裁院)'),
            "当事人": re.compile(r'(?:原告|被告|第三人|申请人|被申请人|上诉人|被上诉人|甲方|乙方|丙方|委托人|受托人|出租人|承租人|买方|卖方)'),
            "案件编号": re.compile(r'\(\d{4}\)[^）]*\d+号'),
            "百分比": re.compile(r'\d+(?:\.\d+)?%'),
            "证件号码": re.compile(r'(?:身份证号|营业执照号|组织机构代码|统一社会信用代码)[:：]?\s*[A-Z0-9]{15,20}'),
            "地址": re.compile(r'[^，。！？]*(?:省|市|区|县|街道|路|号|室|楼)[^，。！？]*'),
            "电话": re.compile(r'1[3-9]\d{9}|\d{3,4}-\d{7,8}'),
            "邮箱": re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'),
            "公司名称": re.compile(r'[^，。！？]*(?:有限公司|股份有限公司|合伙企业|个人独资企业|公司)[^，。！？]*'),
            "职务": re.compile(r'(?:董事长|总经理|董事|监事|经理|法定代表人|股东|合伙人)')
        }
    
    def _load_stop_words(self) -> set:
        """加载停用词"""
        stop_words = {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个",
            "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好",
            "自己", "这", "那", "什么", "可以", "怎么", "如何", "为什么", "吗", "呢"
        }
        return stop_words
    
    def tokenize(self, text: str) -> List[str]:
        """分词"""
        # 清理文本
        text = self._clean_text(text)
        
        # 使用jieba分词
        tokens = jieba.lcut(text)
        
        # 过滤停用词和短词
        filtered_tokens = [
            token for token in tokens 
            if token not in self.stop_words and len(token) > 1
        ]
        
        return filtered_tokens
    
    def extract_keywords(self, text: str, top_k: int = 10, method: str = "hybrid") -> List[Tuple[str, float]]:
        """提取关键词

        Args:
            text: 输入文本
            top_k: 返回关键词数量
            method: 提取方法 ("tfidf", "textrank", "hybrid")

        Returns:
            关键词列表，每个元素为(词语, 权重)
        """
        if method == "tfidf":
            return self._extract_keywords_tfidf(text, top_k)
        elif method == "textrank":
            return self._extract_keywords_textrank(text, top_k)
        elif method == "hybrid":
            # 混合方法：结合TF-IDF和TextRank
            tfidf_keywords = dict(self._extract_keywords_tfidf(text, top_k * 2))
            textrank_keywords = dict(self._extract_keywords_textrank(text, top_k * 2))

            # 合并并重新排序
            combined_keywords = {}
            all_keywords = set(tfidf_keywords.keys()) | set(textrank_keywords.keys())

            for keyword in all_keywords:
                tfidf_score = tfidf_keywords.get(keyword, 0)
                textrank_score = textrank_keywords.get(keyword, 0)
                combined_keywords[keyword] = 0.6 * tfidf_score + 0.4 * textrank_score

            # 按权重排序并返回前top_k个
            sorted_keywords = sorted(combined_keywords.items(), key=lambda x: x[1], reverse=True)
            return sorted_keywords[:top_k]
        else:
            raise ValueError(f"不支持的关键词提取方法: {method}")

    def _extract_keywords_tfidf(self, text: str, top_k: int) -> List[Tuple[str, float]]:
        """使用TF-IDF提取关键词"""
        keywords = jieba.analyse.extract_tags(
            text,
            topK=top_k,
            withWeight=True,
            allowPOS=('n', 'nr', 'ns', 'nt', 'nz', 'v', 'vn', 'a', 'ad')
        )
        return keywords

    def _extract_keywords_textrank(self, text: str, top_k: int) -> List[Tuple[str, float]]:
        """使用TextRank提取关键词"""
        keywords = jieba.analyse.textrank(
            text,
            topK=top_k,
            withWeight=True,
            allowPOS=('n', 'nr', 'ns', 'nt', 'nz', 'v', 'vn', 'a', 'ad')
        )
        return keywords
    
    def classify_question(self, question: str) -> Dict[str, Any]:
        """问题分类"""
        question_clean = self._clean_text(question)
        tokens = self.tokenize(question_clean)
        
        # 计算每个类别的匹配分数
        category_scores = {}
        for category, keywords in self.legal_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in question_clean:
                    score += 2  # 完全匹配得分更高
                for token in tokens:
                    if keyword in token or token in keyword:
                        score += 1
            category_scores[category] = score
        
        # 找出最高分的类别
        if category_scores:
            best_category = max(category_scores, key=category_scores.get)
            confidence = category_scores[best_category] / sum(category_scores.values()) if sum(category_scores.values()) > 0 else 0
        else:
            best_category = "general"
            confidence = 0.5
        
        return {
            "category": best_category,
            "confidence": confidence,
            "scores": category_scores
        }
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取法律实体"""
        entities = {}
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = pattern.findall(text)
            if matches:
                entities[entity_type] = list(set(matches))  # 去重
        
        return entities
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """情感分析"""
        # 简单的基于词典的情感分析
        positive_words = ["满意", "好", "赞", "支持", "同意", "感谢", "帮助"]
        negative_words = ["不满", "差", "糟糕", "反对", "不同意", "投诉", "问题"]
        
        text_clean = self._clean_text(text)
        tokens = self.tokenize(text_clean)
        
        positive_count = sum(1 for token in tokens if token in positive_words)
        negative_count = sum(1 for token in tokens if token in negative_words)
        
        total_count = positive_count + negative_count
        if total_count == 0:
            sentiment = "neutral"
            confidence = 0.5
        elif positive_count > negative_count:
            sentiment = "positive"
            confidence = positive_count / total_count
        else:
            sentiment = "negative"
            confidence = negative_count / total_count
        
        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "positive_count": positive_count,
            "negative_count": negative_count
        }
    
    def extract_intent(self, question: str) -> Dict[str, Any]:
        """意图识别"""
        question_clean = self._clean_text(question).lower()
        
        # 定义意图模式
        intent_patterns = {
            "咨询": ["怎么", "如何", "什么", "为什么", "是否", "可以", "能否"],
            "求助": ["帮助", "帮忙", "救助", "支援", "协助"],
            "投诉": ["投诉", "举报", "控告", "起诉", "告发"],
            "查询": ["查询", "查找", "搜索", "寻找", "了解"],
            "申请": ["申请", "办理", "提交", "递交", "登记"],
            "咨询费用": ["费用", "价格", "收费", "多少钱", "成本"]
        }
        
        intent_scores = {}
        for intent, patterns in intent_patterns.items():
            score = sum(1 for pattern in patterns if pattern in question_clean)
            if score > 0:
                intent_scores[intent] = score
        
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            confidence = intent_scores[best_intent] / sum(intent_scores.values())
        else:
            best_intent = "咨询"  # 默认意图
            confidence = 0.5
        
        return {
            "intent": best_intent,
            "confidence": confidence,
            "scores": intent_scores
        }
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中文、英文、数字和基本标点）
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9，。！？；：""''（）【】\s]', '', text)
        
        return text.strip()
    
    def similarity(self, text1: str, text2: str, method: str = "hybrid") -> float:
        """计算文本相似度

        Args:
            text1: 第一个文本
            text2: 第二个文本
            method: 相似度计算方法 ("jaccard", "cosine", "hybrid")

        Returns:
            相似度分数 (0-1)
        """
        if method == "jaccard":
            return self._jaccard_similarity(text1, text2)
        elif method == "cosine":
            return self._cosine_similarity(text1, text2)
        elif method == "hybrid":
            # 混合方法：结合Jaccard和余弦相似度
            jaccard_sim = self._jaccard_similarity(text1, text2)
            cosine_sim = self._cosine_similarity(text1, text2)
            return 0.6 * cosine_sim + 0.4 * jaccard_sim
        else:
            raise ValueError(f"不支持的相似度计算方法: {method}")

    def _jaccard_similarity(self, text1: str, text2: str) -> float:
        """Jaccard相似度"""
        tokens1 = set(self.tokenize(text1))
        tokens2 = set(self.tokenize(text2))

        if not tokens1 and not tokens2:
            return 1.0
        if not tokens1 or not tokens2:
            return 0.0

        intersection = len(tokens1.intersection(tokens2))
        union = len(tokens1.union(tokens2))

        return intersection / union if union > 0 else 0.0

    def _cosine_similarity(self, text1: str, text2: str) -> float:
        """余弦相似度"""
        try:
            # 使用TF-IDF向量化
            vectorizer = TfidfVectorizer(
                tokenizer=self.tokenize,
                lowercase=False,
                token_pattern=None
            )

            tfidf_matrix = vectorizer.fit_transform([text1, text2])
            similarity_matrix = cosine_similarity(tfidf_matrix)

            return float(similarity_matrix[0, 1])
        except Exception as e:
            logger.warning(f"余弦相似度计算失败: {e}")
            # 降级到Jaccard相似度
            return self._jaccard_similarity(text1, text2)
    
    def summarize(self, text: str, max_sentences: int = 3, method: str = "keyword_based") -> str:
        """文本摘要

        Args:
            text: 输入文本
            max_sentences: 最大句子数
            method: 摘要方法 ("keyword_based", "position_based", "hybrid")

        Returns:
            摘要文本
        """
        sentences = self._split_sentences(text)

        if len(sentences) <= max_sentences:
            return text

        if method == "keyword_based":
            return self._summarize_keyword_based(sentences, max_sentences)
        elif method == "position_based":
            return self._summarize_position_based(sentences, max_sentences)
        elif method == "hybrid":
            return self._summarize_hybrid(sentences, max_sentences)
        else:
            raise ValueError(f"不支持的摘要方法: {method}")

    def _split_sentences(self, text: str) -> List[str]:
        """分割句子"""
        sentences = re.split(r'[。！？；]', text)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 5]
        return sentences

    def _summarize_keyword_based(self, sentences: List[str], max_sentences: int) -> str:
        """基于关键词的摘要"""
        full_text = '。'.join(sentences)
        keywords = [kw[0] for kw in self.extract_keywords(full_text, top_k=15)]

        sentence_scores = []
        for sentence in sentences:
            # 计算句子中关键词的权重和
            score = 0
            sentence_tokens = self.tokenize(sentence)
            for token in sentence_tokens:
                if token in keywords:
                    score += keywords.index(token) + 1  # 排名越前权重越高

            # 归一化分数
            score = score / len(sentence_tokens) if sentence_tokens else 0
            sentence_scores.append((sentence, score))

        # 选择得分最高的句子
        sentence_scores.sort(key=lambda x: x[1], reverse=True)
        top_sentences = [s[0] for s in sentence_scores[:max_sentences]]

        # 按原文顺序排列
        ordered_sentences = []
        for sentence in sentences:
            if sentence in top_sentences:
                ordered_sentences.append(sentence)

        return '。'.join(ordered_sentences) + '。'

    def _summarize_position_based(self, sentences: List[str], max_sentences: int) -> str:
        """基于位置的摘要（首尾句子权重更高）"""
        sentence_scores = []
        total_sentences = len(sentences)

        for i, sentence in enumerate(sentences):
            # 位置权重：首尾句子权重更高
            if i == 0 or i == total_sentences - 1:
                position_weight = 1.0
            elif i < total_sentences * 0.3:  # 前30%
                position_weight = 0.8
            elif i > total_sentences * 0.7:  # 后30%
                position_weight = 0.8
            else:
                position_weight = 0.5

            # 句子长度权重（适中长度的句子权重更高）
            length_weight = min(len(sentence) / 50, 1.0) if len(sentence) > 10 else 0.3

            total_score = position_weight * length_weight
            sentence_scores.append((sentence, total_score))

        sentence_scores.sort(key=lambda x: x[1], reverse=True)
        top_sentences = [s[0] for s in sentence_scores[:max_sentences]]

        # 按原文顺序排列
        ordered_sentences = []
        for sentence in sentences:
            if sentence in top_sentences:
                ordered_sentences.append(sentence)

        return '。'.join(ordered_sentences) + '。'

    def _summarize_hybrid(self, sentences: List[str], max_sentences: int) -> str:
        """混合摘要方法"""
        # 结合关键词和位置权重
        full_text = '。'.join(sentences)
        keywords = [kw[0] for kw in self.extract_keywords(full_text, top_k=15)]
        total_sentences = len(sentences)

        sentence_scores = []
        for i, sentence in enumerate(sentences):
            # 关键词权重
            keyword_score = 0
            sentence_tokens = self.tokenize(sentence)
            for token in sentence_tokens:
                if token in keywords:
                    keyword_score += (len(keywords) - keywords.index(token)) / len(keywords)
            keyword_score = keyword_score / len(sentence_tokens) if sentence_tokens else 0

            # 位置权重
            if i == 0 or i == total_sentences - 1:
                position_weight = 1.0
            elif i < total_sentences * 0.3:
                position_weight = 0.8
            elif i > total_sentences * 0.7:
                position_weight = 0.8
            else:
                position_weight = 0.5

            # 句子长度权重
            length_weight = min(len(sentence) / 50, 1.0) if len(sentence) > 10 else 0.3

            # 综合得分
            total_score = 0.5 * keyword_score + 0.3 * position_weight + 0.2 * length_weight
            sentence_scores.append((sentence, total_score))

        sentence_scores.sort(key=lambda x: x[1], reverse=True)
        top_sentences = [s[0] for s in sentence_scores[:max_sentences]]

        # 按原文顺序排列
        ordered_sentences = []
        for sentence in sentences:
            if sentence in top_sentences:
                ordered_sentences.append(sentence)

        return '。'.join(ordered_sentences) + '。'


# 全局NLP处理器实例
nlp_processor = LegalNLPProcessor()


    def find_similar_cases(self, query_text: str, case_texts: List[str], top_k: int = 5) -> List[Tuple[int, float]]:
        """查找相似案例

        Args:
            query_text: 查询文本
            case_texts: 案例文本列表
            top_k: 返回最相似的案例数量

        Returns:
            相似案例列表，每个元素为(案例索引, 相似度分数)
        """
        similarities = []

        for i, case_text in enumerate(case_texts):
            similarity_score = self.similarity(query_text, case_text, method="hybrid")
            similarities.append((i, similarity_score))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        return similarities[:top_k]

    def extract_legal_concepts(self, text: str) -> Dict[str, List[str]]:
        """提取法律概念

        Args:
            text: 输入文本

        Returns:
            法律概念字典，按类别分组
        """
        concepts = defaultdict(list)

        # 使用词性标注提取名词性法律概念
        words = pseg.cut(text)

        for word, flag in words:
            # 过滤法律相关的名词
            if flag.startswith('n') and len(word) > 1:
                # 检查是否为法律概念
                for category, keywords in self.legal_keywords.items():
                    if word in keywords or any(keyword in word for keyword in keywords):
                        concepts[category].append(word)
                        break
                else:
                    # 通用法律概念
                    if any(legal_char in word for legal_char in ['法', '律', '权', '责', '诉', '判', '审']):
                        concepts['通用法律概念'].append(word)

        # 去重
        for category in concepts:
            concepts[category] = list(set(concepts[category]))

        return dict(concepts)

    def analyze_text_complexity(self, text: str) -> Dict[str, Any]:
        """分析文本复杂度

        Args:
            text: 输入文本

        Returns:
            复杂度分析结果
        """
        sentences = self._split_sentences(text)
        tokens = self.tokenize(text)

        # 基本统计
        char_count = len(text)
        sentence_count = len(sentences)
        word_count = len(tokens)

        # 平均句长
        avg_sentence_length = char_count / sentence_count if sentence_count > 0 else 0

        # 词汇丰富度（不重复词汇比例）
        unique_words = set(tokens)
        lexical_diversity = len(unique_words) / word_count if word_count > 0 else 0

        # 法律术语密度
        legal_terms = 0
        for token in tokens:
            for keywords in self.legal_keywords.values():
                if token in keywords:
                    legal_terms += 1
                    break

        legal_term_density = legal_terms / word_count if word_count > 0 else 0

        # 复杂度评分（0-1，越高越复杂）
        complexity_score = min(
            (avg_sentence_length / 50) * 0.3 +
            (1 - lexical_diversity) * 0.3 +
            legal_term_density * 0.4,
            1.0
        )

        return {
            "char_count": char_count,
            "sentence_count": sentence_count,
            "word_count": word_count,
            "avg_sentence_length": avg_sentence_length,
            "lexical_diversity": lexical_diversity,
            "legal_term_density": legal_term_density,
            "complexity_score": complexity_score,
            "complexity_level": self._get_complexity_level(complexity_score)
        }

    def _get_complexity_level(self, score: float) -> str:
        """获取复杂度等级"""
        if score < 0.3:
            return "简单"
        elif score < 0.6:
            return "中等"
        elif score < 0.8:
            return "复杂"
        else:
            return "非常复杂"


def process_legal_text(text: str) -> Dict[str, Any]:
    """处理法律文本的便捷函数"""
    return {
        "tokens": nlp_processor.tokenize(text),
        "keywords": nlp_processor.extract_keywords(text),
        "classification": nlp_processor.classify_question(text),
        "entities": nlp_processor.extract_entities(text),
        "sentiment": nlp_processor.analyze_sentiment(text),
        "intent": nlp_processor.extract_intent(text),
        "summary": nlp_processor.summarize(text),
        "legal_concepts": nlp_processor.extract_legal_concepts(text),
        "complexity": nlp_processor.analyze_text_complexity(text)
    }
