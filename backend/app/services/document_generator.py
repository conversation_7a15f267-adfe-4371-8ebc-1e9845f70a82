"""
智能文书生成器
基于模板和用户输入，自动生成法律文书，支持参数化和个性化定制
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import jieba

logger = logging.getLogger(__name__)


class GeneratedDocument:
    """生成的文档类"""
    
    def __init__(self, document_id: str, template_id: str, content: str):
        """初始化生成的文档
        
        Args:
            document_id: 文档唯一标识
            template_id: 使用的模板ID
            content: 文档内容
        """
        self.document_id = document_id
        self.template_id = template_id
        self.content = content
        self.generated_time = datetime.now().isoformat()
        self.variables_used = {}
        self.customizations = []
        self.validation_results = {}
        self.export_formats = ["txt", "docx", "pdf"]
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "document_id": self.document_id,
            "template_id": self.template_id,
            "content": self.content,
            "generated_time": self.generated_time,
            "variables_used": self.variables_used,
            "customizations": self.customizations,
            "validation_results": self.validation_results,
            "export_formats": self.export_formats
        }


class DocumentGenerator:
    """智能文书生成器"""
    
    def __init__(self, template_manager=None):
        """初始化文书生成器
        
        Args:
            template_manager: 模板管理器实例
        """
        self.template_manager = template_manager
        self.generated_documents = {}
        self.generation_rules = self._load_generation_rules()
        self.validation_rules = self._load_validation_rules()
        self.customization_options = self._load_customization_options()
        
        logger.info("智能文书生成器初始化完成")
    
    def _load_generation_rules(self) -> Dict[str, Any]:
        """加载生成规则"""
        return {
            "auto_fill_rules": {
                "当前日期": lambda: datetime.now().strftime("%Y年%m月%d日"),
                "当前年份": lambda: str(datetime.now().year),
                "当前月份": lambda: str(datetime.now().month),
                "当前日": lambda: str(datetime.now().day)
            },
            "format_rules": {
                "金额大写": self._convert_amount_to_chinese,
                "日期格式化": self._format_date,
                "电话格式化": self._format_phone,
                "身份证格式化": self._format_id_card
            },
            "validation_rules": {
                "必填字段": ["甲方名称", "乙方名称"],
                "格式验证": {
                    "电话": r"^1[3-9]\d{9}$",
                    "邮箱": r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
                    "身份证": r"^\d{17}[\dXx]$"
                }
            }
        }
    
    def _load_validation_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载验证规则"""
        return {
            "内容验证": [
                {
                    "rule": "必填字段检查",
                    "description": "检查必填字段是否完整",
                    "severity": "error"
                },
                {
                    "rule": "格式验证",
                    "description": "检查字段格式是否正确",
                    "severity": "warning"
                }
            ],
            "法律合规": [
                {
                    "rule": "违法内容检查",
                    "description": "检查是否包含违法内容",
                    "severity": "error"
                },
                {
                    "rule": "格式条款检查",
                    "description": "检查格式条款是否合规",
                    "severity": "warning"
                }
            ]
        }
    
    def _load_customization_options(self) -> Dict[str, List[str]]:
        """加载定制选项"""
        return {
            "字体样式": ["宋体", "黑体", "楷体", "仿宋"],
            "字号大小": ["小四", "四号", "小三", "三号"],
            "页面设置": ["A4纵向", "A4横向", "A3纵向"],
            "页眉页脚": ["标准", "简洁", "详细", "无"],
            "签名位置": ["右下角", "居中", "左下角"]
        }
    
    def generate_document(self, template_id: str, variables: Dict[str, str], 
                         customizations: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成法律文书
        
        Args:
            template_id: 模板ID
            variables: 变量值字典
            customizations: 定制选项
        
        Returns:
            生成结果
        """
        try:
            # 1. 获取模板
            if not self.template_manager:
                return {"error": "模板管理器未初始化"}
            
            template = self.template_manager.get_template(template_id)
            if not template:
                return {"error": f"模板不存在: {template_id}"}
            
            # 2. 预处理变量
            processed_variables = self._preprocess_variables(variables, template)
            
            # 3. 验证变量
            validation_result = self._validate_variables(processed_variables, template)
            if validation_result["has_errors"]:
                return {
                    "error": "变量验证失败",
                    "validation_result": validation_result
                }
            
            # 4. 渲染文档
            content = template.render(processed_variables)
            
            # 5. 应用定制选项
            if customizations:
                content = self._apply_customizations(content, customizations)
            
            # 6. 后处理
            content = self._post_process_content(content)
            
            # 7. 创建生成的文档对象
            document_id = f"doc_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.generated_documents)}"
            generated_doc = GeneratedDocument(document_id, template_id, content)
            generated_doc.variables_used = processed_variables
            generated_doc.customizations = customizations or {}
            generated_doc.validation_results = validation_result
            
            # 8. 存储生成的文档
            self.generated_documents[document_id] = generated_doc
            
            # 9. 更新模板使用统计
            template.usage_count += 1
            
            return {
                "success": True,
                "document_id": document_id,
                "content": content,
                "template_name": template.name,
                "variables_used": processed_variables,
                "validation_result": validation_result,
                "generation_time": generated_doc.generated_time
            }
            
        except Exception as e:
            logger.error(f"生成文书失败: {e}")
            return {"error": f"生成文书失败: {str(e)}"}
    
    def _preprocess_variables(self, variables: Dict[str, str], template) -> Dict[str, str]:
        """预处理变量"""
        processed = variables.copy()
        
        # 自动填充规则
        auto_fill_rules = self.generation_rules["auto_fill_rules"]
        for var_name in template.variables:
            if var_name not in processed or not processed[var_name]:
                if var_name in auto_fill_rules:
                    processed[var_name] = auto_fill_rules[var_name]()
        
        # 格式化规则
        format_rules = self.generation_rules["format_rules"]
        for var_name, var_value in processed.items():
            if var_value:
                # 金额大写转换
                if "金额" in var_name and var_value.replace(".", "").replace(",", "").isdigit():
                    amount_key = var_name + "_大写"
                    if amount_key in template.variables:
                        processed[amount_key] = format_rules["金额大写"](var_value)
                
                # 日期格式化
                if "日期" in var_name:
                    processed[var_name] = format_rules["日期格式化"](var_value)
                
                # 电话格式化
                if "电话" in var_name:
                    processed[var_name] = format_rules["电话格式化"](var_value)
        
        return processed
    
    def _validate_variables(self, variables: Dict[str, str], template) -> Dict[str, Any]:
        """验证变量"""
        result = {
            "has_errors": False,
            "has_warnings": False,
            "errors": [],
            "warnings": [],
            "missing_variables": [],
            "format_issues": []
        }
        
        # 检查缺失变量
        missing_vars = template.validate_variables(variables)
        if missing_vars:
            result["has_errors"] = True
            result["missing_variables"] = missing_vars
            result["errors"].append(f"缺少必填变量: {', '.join(missing_vars)}")
        
        # 格式验证
        format_rules = self.generation_rules["validation_rules"]["格式验证"]
        for var_name, var_value in variables.items():
            if var_value:
                for format_type, pattern in format_rules.items():
                    if format_type in var_name.lower():
                        if not re.match(pattern, var_value):
                            result["has_warnings"] = True
                            result["format_issues"].append(f"{var_name}格式可能不正确")
                            result["warnings"].append(f"{var_name}格式验证失败")
        
        return result
    
    def _apply_customizations(self, content: str, customizations: Dict[str, Any]) -> str:
        """应用定制选项"""
        # 这里是简化实现，实际应用中可能需要更复杂的格式化逻辑
        customized_content = content
        
        # 添加定制信息注释
        if customizations:
            customization_info = "\n<!-- 定制选项: " + str(customizations) + " -->\n"
            customized_content = customization_info + customized_content
        
        return customized_content
    
    def _post_process_content(self, content: str) -> str:
        """后处理内容"""
        # 清理多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 去除首尾空白
        content = content.strip()
        
        # 确保段落格式正确
        content = re.sub(r'([。！？])\s*\n\s*([^\n])', r'\1\n\n\2', content)
        
        return content
    
    def _convert_amount_to_chinese(self, amount: str) -> str:
        """将金额转换为中文大写"""
        try:
            # 简化的中文数字转换
            num = float(amount.replace(",", ""))
            
            digits = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"]
            units = ["", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿"]
            
            if num == 0:
                return "零元整"
            
            # 简化实现，实际应用中需要更完整的转换逻辑
            int_part = int(num)
            if int_part < 10:
                return f"{digits[int_part]}元整"
            else:
                return f"{amount}元（大写转换需要完整实现）"
                
        except ValueError:
            return amount
    
    def _format_date(self, date_str: str) -> str:
        """格式化日期"""
        # 简化的日期格式化
        if re.match(r'\d{4}-\d{1,2}-\d{1,2}', date_str):
            parts = date_str.split('-')
            return f"{parts[0]}年{int(parts[1])}月{int(parts[2])}日"
        return date_str
    
    def _format_phone(self, phone: str) -> str:
        """格式化电话号码"""
        # 去除非数字字符
        digits = re.sub(r'\D', '', phone)
        
        # 手机号格式化
        if len(digits) == 11 and digits.startswith('1'):
            return f"{digits[:3]}-{digits[3:7]}-{digits[7:]}"
        
        return phone
    
    def _format_id_card(self, id_card: str) -> str:
        """格式化身份证号"""
        # 去除空格和特殊字符
        cleaned = re.sub(r'[^\dXx]', '', id_card)
        
        if len(cleaned) == 18:
            return f"{cleaned[:6]} {cleaned[6:14]} {cleaned[14:]}"
        
        return id_card
    
    def get_generated_document(self, document_id: str) -> Optional[GeneratedDocument]:
        """获取生成的文档
        
        Args:
            document_id: 文档ID
        
        Returns:
            生成的文档对象或None
        """
        return self.generated_documents.get(document_id)
    
    def list_generated_documents(self, limit: int = 10) -> List[Dict[str, Any]]:
        """列出生成的文档
        
        Args:
            limit: 返回数量限制
        
        Returns:
            文档列表
        """
        documents = list(self.generated_documents.values())
        documents.sort(key=lambda d: d.generated_time, reverse=True)
        
        return [
            {
                "document_id": doc.document_id,
                "template_id": doc.template_id,
                "generated_time": doc.generated_time,
                "content_preview": doc.content[:100] + "..." if len(doc.content) > 100 else doc.content
            }
            for doc in documents[:limit]
        ]
    
    def export_document(self, document_id: str, format_type: str = "txt") -> Dict[str, Any]:
        """导出文档
        
        Args:
            document_id: 文档ID
            format_type: 导出格式 (txt, docx, pdf)
        
        Returns:
            导出结果
        """
        try:
            document = self.get_generated_document(document_id)
            if not document:
                return {"error": "文档不存在"}
            
            if format_type not in document.export_formats:
                return {"error": f"不支持的导出格式: {format_type}"}
            
            # 简化的导出实现
            export_content = document.content
            
            if format_type == "txt":
                filename = f"{document_id}.txt"
            elif format_type == "docx":
                filename = f"{document_id}.docx"
                export_content = f"[Word格式]\n{export_content}"
            elif format_type == "pdf":
                filename = f"{document_id}.pdf"
                export_content = f"[PDF格式]\n{export_content}"
            
            return {
                "success": True,
                "filename": filename,
                "content": export_content,
                "format": format_type,
                "size": len(export_content)
            }
            
        except Exception as e:
            logger.error(f"导出文档失败: {e}")
            return {"error": f"导出文档失败: {str(e)}"}
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        stats = {
            "total_generated": len(self.generated_documents),
            "template_usage": defaultdict(int),
            "recent_generations": [],
            "popular_templates": []
        }
        
        # 统计模板使用情况
        for doc in self.generated_documents.values():
            stats["template_usage"][doc.template_id] += 1
        
        # 最近生成的文档
        recent_docs = sorted(
            self.generated_documents.values(),
            key=lambda d: d.generated_time,
            reverse=True
        )[:5]
        
        stats["recent_generations"] = [
            {
                "document_id": doc.document_id,
                "template_id": doc.template_id,
                "generated_time": doc.generated_time
            }
            for doc in recent_docs
        ]
        
        # 热门模板
        popular_templates = sorted(
            stats["template_usage"].items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        stats["popular_templates"] = [
            {"template_id": tid, "usage_count": count}
            for tid, count in popular_templates
        ]
        
        return dict(stats)


# 全局文书生成器实例（需要在使用时注入模板管理器）
document_generator = None


def initialize_document_generator(template_manager):
    """初始化文书生成器"""
    global document_generator
    document_generator = DocumentGenerator(template_manager)
    return document_generator


def generate_document(template_id: str, variables: Dict[str, str], 
                     customizations: Dict[str, Any] = None) -> Dict[str, Any]:
    """生成文书的便捷函数"""
    if not document_generator:
        return {"error": "文书生成器未初始化"}
    
    return document_generator.generate_document(template_id, variables, customizations)
