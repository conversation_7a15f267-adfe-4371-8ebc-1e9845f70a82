"""
合同条款分析器
智能分析合同条款的合法性、完整性和合理性，提供专业的法律建议
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from datetime import datetime
import jieba

logger = logging.getLogger(__name__)


class ClauseAnalysisResult:
    """条款分析结果类"""
    
    def __init__(self, clause_id: str, clause_title: str):
        """初始化条款分析结果
        
        Args:
            clause_id: 条款ID
            clause_title: 条款标题
        """
        self.clause_id = clause_id
        self.clause_title = clause_title
        self.legality_score = 0.0  # 合法性得分 (0-100)
        self.completeness_score = 0.0  # 完整性得分 (0-100)
        self.reasonableness_score = 0.0  # 合理性得分 (0-100)
        self.overall_score = 0.0  # 综合得分 (0-100)
        
        self.legality_issues = []  # 合法性问题
        self.completeness_issues = []  # 完整性问题
        self.reasonableness_issues = []  # 合理性问题
        
        self.suggestions = []  # 改进建议
        self.legal_references = []  # 法律依据
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "clause_id": self.clause_id,
            "clause_title": self.clause_title,
            "scores": {
                "legality": self.legality_score,
                "completeness": self.completeness_score,
                "reasonableness": self.reasonableness_score,
                "overall": self.overall_score
            },
            "issues": {
                "legality": self.legality_issues,
                "completeness": self.completeness_issues,
                "reasonableness": self.reasonableness_issues
            },
            "suggestions": self.suggestions,
            "legal_references": self.legal_references
        }


class ContractClauseAnalyzer:
    """合同条款分析器"""
    
    def __init__(self):
        """初始化条款分析器"""
        self.legality_rules = self._load_legality_rules()
        self.completeness_rules = self._load_completeness_rules()
        self.reasonableness_rules = self._load_reasonableness_rules()
        self.legal_references = self._load_legal_references()
        
        logger.info("合同条款分析器初始化完成")
    
    def _load_legality_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载合法性检查规则"""
        return {
            "违法内容": [
                {
                    "pattern": r"规避.*税收|避税|逃税",
                    "severity": "critical",
                    "description": "涉嫌税收规避",
                    "legal_basis": "税收征收管理法"
                },
                {
                    "pattern": r"垄断.*市场|限制.*竞争|价格.*联盟",
                    "severity": "high",
                    "description": "可能违反反垄断法",
                    "legal_basis": "反垄断法"
                },
                {
                    "pattern": r"洗钱|资金.*来源不明|非法.*资金",
                    "severity": "critical",
                    "description": "涉嫌洗钱风险",
                    "legal_basis": "反洗钱法"
                }
            ],
            "无效条款": [
                {
                    "pattern": r"排除.*法律适用|不受.*法律约束",
                    "severity": "high",
                    "description": "试图排除法律适用",
                    "legal_basis": "合同法第52条"
                },
                {
                    "pattern": r"损害.*国家利益|损害.*社会公共利益",
                    "severity": "critical",
                    "description": "损害国家或社会公共利益",
                    "legal_basis": "合同法第52条"
                }
            ],
            "格式条款": [
                {
                    "pattern": r"本条款.*最终解释权|解释权.*归.*所有",
                    "severity": "medium",
                    "description": "格式条款解释权条款",
                    "legal_basis": "合同法第41条"
                }
            ]
        }
    
    def _load_completeness_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载完整性检查规则"""
        return {
            "必要要素": [
                {
                    "element": "当事人信息",
                    "patterns": [r"甲方[:：]", r"乙方[:：]", r"当事人"],
                    "required": True,
                    "description": "合同当事人信息"
                },
                {
                    "element": "标的物",
                    "patterns": [r"标的", r"服务内容", r"商品", r"工程"],
                    "required": True,
                    "description": "合同标的物或服务内容"
                },
                {
                    "element": "价款或报酬",
                    "patterns": [r"价格", r"费用", r"报酬", r"金额", r"\d+元"],
                    "required": True,
                    "description": "价款或报酬条款"
                },
                {
                    "element": "履行期限",
                    "patterns": [r"期限", r"时间", r"日期", r"\d+年|\d+月|\d+日"],
                    "required": True,
                    "description": "履行期限条款"
                }
            ],
            "重要条款": [
                {
                    "element": "违约责任",
                    "patterns": [r"违约", r"责任", r"赔偿"],
                    "required": False,
                    "description": "违约责任条款"
                },
                {
                    "element": "争议解决",
                    "patterns": [r"争议", r"仲裁", r"诉讼", r"管辖"],
                    "required": False,
                    "description": "争议解决条款"
                }
            ]
        }
    
    def _load_reasonableness_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载合理性检查规则"""
        return {
            "权利义务平衡": [
                {
                    "pattern": r"甲方.*权利.*乙方.*义务",
                    "check_type": "balance",
                    "description": "检查权利义务是否平衡"
                },
                {
                    "pattern": r"单方.*决定|单方.*变更",
                    "severity": "medium",
                    "description": "存在单方决定权，可能不够合理"
                }
            ],
            "经济条款": [
                {
                    "pattern": r"违约金.*(\d+)%",
                    "check_type": "percentage",
                    "max_reasonable": 30,
                    "description": "违约金比例检查"
                },
                {
                    "pattern": r"利率.*(\d+(?:\.\d+)?)%",
                    "check_type": "percentage", 
                    "max_reasonable": 24,
                    "description": "利率水平检查"
                }
            ],
            "时间条款": [
                {
                    "pattern": r"通知.*(\d+)(?:小时|日|天)",
                    "check_type": "time_limit",
                    "min_reasonable": 3,
                    "description": "通知期限检查"
                }
            ]
        }
    
    def _load_legal_references(self) -> Dict[str, str]:
        """加载法律依据"""
        return {
            "合同法第52条": "有下列情形之一的，合同无效：(一)一方以欺诈、胁迫的手段订立合同，损害国家利益；(二)恶意串通，损害国家、集体或者第三人利益；(三)以合法形式掩盖非法目的；(四)损害社会公共利益；(五)违反法律、行政法规的强制性规定。",
            "合同法第41条": "对格式条款的理解发生争议的，应当按照通常理解予以解释。对格式条款有两种以上解释的，应当作出不利于提供格式条款一方的解释。",
            "反垄断法": "禁止经营者达成垄断协议、滥用市场支配地位、具有或者可能具有排除、限制竞争效果的经营者集中。",
            "税收征收管理法": "纳税人、扣缴义务人必须依照法律、行政法规规定或者税务机关依照法律、行政法规的规定确定的申报期限、申报内容如实办理纳税申报。"
        }
    
    def analyze_clause(self, clause_content: str, clause_type: str = "", clause_title: str = "") -> ClauseAnalysisResult:
        """分析单个条款
        
        Args:
            clause_content: 条款内容
            clause_type: 条款类型
            clause_title: 条款标题
        
        Returns:
            条款分析结果
        """
        result = ClauseAnalysisResult(
            clause_id=f"clause_{hash(clause_content) % 10000}",
            clause_title=clause_title or clause_content[:30] + "..."
        )
        
        try:
            # 1. 合法性分析
            result.legality_score, result.legality_issues = self._analyze_legality(clause_content)
            
            # 2. 完整性分析
            result.completeness_score, result.completeness_issues = self._analyze_completeness(
                clause_content, clause_type
            )
            
            # 3. 合理性分析
            result.reasonableness_score, result.reasonableness_issues = self._analyze_reasonableness(
                clause_content
            )
            
            # 4. 计算综合得分
            result.overall_score = (
                result.legality_score * 0.4 +
                result.completeness_score * 0.3 +
                result.reasonableness_score * 0.3
            )
            
            # 5. 生成改进建议
            result.suggestions = self._generate_suggestions(result)
            
            # 6. 添加法律依据
            result.legal_references = self._get_relevant_legal_references(result)
            
            return result
            
        except Exception as e:
            logger.error(f"条款分析失败: {e}")
            result.overall_score = 0.0
            result.legality_issues.append(f"分析过程出错: {str(e)}")
            return result
    
    def _analyze_legality(self, content: str) -> Tuple[float, List[str]]:
        """分析合法性"""
        issues = []
        base_score = 100.0
        
        for category, rules in self.legality_rules.items():
            for rule in rules:
                pattern = rule["pattern"]
                if re.search(pattern, content, re.IGNORECASE):
                    severity = rule["severity"]
                    description = rule["description"]
                    
                    # 根据严重程度扣分
                    if severity == "critical":
                        base_score -= 40
                    elif severity == "high":
                        base_score -= 25
                    elif severity == "medium":
                        base_score -= 15
                    else:
                        base_score -= 5
                    
                    issues.append(f"[{severity.upper()}] {description}")
        
        return max(0.0, base_score), issues
    
    def _analyze_completeness(self, content: str, clause_type: str) -> Tuple[float, List[str]]:
        """分析完整性"""
        issues = []
        total_elements = 0
        present_elements = 0
        
        for category, elements in self.completeness_rules.items():
            for element in elements:
                total_elements += 1
                element_name = element["element"]
                patterns = element["patterns"]
                required = element["required"]
                
                # 检查是否包含该要素
                found = any(re.search(pattern, content, re.IGNORECASE) for pattern in patterns)
                
                if found:
                    present_elements += 1
                elif required:
                    issues.append(f"缺少必要要素: {element_name}")
                else:
                    issues.append(f"建议添加: {element_name}")
        
        # 计算完整性得分
        if total_elements > 0:
            completeness_score = (present_elements / total_elements) * 100
        else:
            completeness_score = 100.0
        
        return completeness_score, issues
    
    def _analyze_reasonableness(self, content: str) -> Tuple[float, List[str]]:
        """分析合理性"""
        issues = []
        base_score = 100.0
        
        for category, rules in self.reasonableness_rules.items():
            for rule in rules:
                pattern = rule["pattern"]
                check_type = rule.get("check_type", "pattern")
                
                if check_type == "percentage":
                    # 检查百分比是否合理
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        try:
                            percentage = float(match)
                            max_reasonable = rule.get("max_reasonable", 100)
                            if percentage > max_reasonable:
                                base_score -= 20
                                issues.append(f"{rule['description']}: {percentage}%超过合理范围({max_reasonable}%)")
                        except ValueError:
                            continue
                
                elif check_type == "time_limit":
                    # 检查时间限制是否合理
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        try:
                            time_limit = int(match)
                            min_reasonable = rule.get("min_reasonable", 1)
                            if time_limit < min_reasonable:
                                base_score -= 15
                                issues.append(f"{rule['description']}: {time_limit}天可能过短")
                        except ValueError:
                            continue
                
                else:
                    # 普通模式匹配
                    if re.search(pattern, content, re.IGNORECASE):
                        severity = rule.get("severity", "low")
                        description = rule["description"]
                        
                        if severity == "high":
                            base_score -= 20
                        elif severity == "medium":
                            base_score -= 10
                        else:
                            base_score -= 5
                        
                        issues.append(f"合理性问题: {description}")
        
        return max(0.0, base_score), issues
    
    def _generate_suggestions(self, result: ClauseAnalysisResult) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 基于合法性问题生成建议
        if result.legality_score < 80:
            suggestions.append("建议审查条款的合法性，删除或修改可能违法的内容")
        
        # 基于完整性问题生成建议
        if result.completeness_score < 70:
            suggestions.append("建议补充缺失的必要条款要素")
        
        # 基于合理性问题生成建议
        if result.reasonableness_score < 70:
            suggestions.append("建议调整条款内容，确保权利义务平衡")
        
        # 基于综合得分生成建议
        if result.overall_score < 60:
            suggestions.append("条款存在较多问题，建议重新起草")
        elif result.overall_score < 80:
            suggestions.append("条款基本可用，但建议进一步优化")
        
        return suggestions
    
    def _get_relevant_legal_references(self, result: ClauseAnalysisResult) -> List[str]:
        """获取相关法律依据"""
        references = []
        
        # 根据发现的问题添加相关法律依据
        for issue in result.legality_issues:
            if "税收" in issue:
                references.append("税收征收管理法")
            elif "垄断" in issue or "竞争" in issue:
                references.append("反垄断法")
            elif "无效" in issue or "违法" in issue:
                references.append("合同法第52条")
        
        return list(set(references))  # 去重
    
    def analyze_contract_clauses(self, clauses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析整个合同的所有条款
        
        Args:
            clauses: 条款列表
        
        Returns:
            整体分析结果
        """
        try:
            clause_results = []
            
            # 分析每个条款
            for clause in clauses:
                content = clause.get("content", "")
                clause_type = clause.get("clause_type", "")
                title = clause.get("title", "")
                
                if content:
                    result = self.analyze_clause(content, clause_type, title)
                    clause_results.append(result)
            
            # 计算整体统计
            overall_stats = self._calculate_overall_stats(clause_results)
            
            # 生成整体建议
            overall_suggestions = self._generate_overall_suggestions(clause_results, overall_stats)
            
            return {
                "clause_analyses": [result.to_dict() for result in clause_results],
                "overall_stats": overall_stats,
                "overall_suggestions": overall_suggestions,
                "analysis_timestamp": datetime.now().isoformat(),
                "total_clauses": len(clause_results)
            }
            
        except Exception as e:
            logger.error(f"合同条款分析失败: {e}")
            return {
                "error": str(e),
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    def _calculate_overall_stats(self, results: List[ClauseAnalysisResult]) -> Dict[str, Any]:
        """计算整体统计信息"""
        if not results:
            return {
                "average_scores": {"legality": 0, "completeness": 0, "reasonableness": 0, "overall": 0},
                "issue_counts": {"legality": 0, "completeness": 0, "reasonableness": 0},
                "quality_level": "unknown"
            }
        
        # 计算平均得分
        avg_legality = sum(r.legality_score for r in results) / len(results)
        avg_completeness = sum(r.completeness_score for r in results) / len(results)
        avg_reasonableness = sum(r.reasonableness_score for r in results) / len(results)
        avg_overall = sum(r.overall_score for r in results) / len(results)
        
        # 统计问题数量
        legality_issues = sum(len(r.legality_issues) for r in results)
        completeness_issues = sum(len(r.completeness_issues) for r in results)
        reasonableness_issues = sum(len(r.reasonableness_issues) for r in results)
        
        # 确定质量等级
        if avg_overall >= 90:
            quality_level = "excellent"
        elif avg_overall >= 80:
            quality_level = "good"
        elif avg_overall >= 70:
            quality_level = "fair"
        elif avg_overall >= 60:
            quality_level = "poor"
        else:
            quality_level = "very_poor"
        
        return {
            "average_scores": {
                "legality": round(avg_legality, 2),
                "completeness": round(avg_completeness, 2),
                "reasonableness": round(avg_reasonableness, 2),
                "overall": round(avg_overall, 2)
            },
            "issue_counts": {
                "legality": legality_issues,
                "completeness": completeness_issues,
                "reasonableness": reasonableness_issues
            },
            "quality_level": quality_level
        }
    
    def _generate_overall_suggestions(self, results: List[ClauseAnalysisResult], 
                                    stats: Dict[str, Any]) -> List[str]:
        """生成整体建议"""
        suggestions = []
        avg_scores = stats["average_scores"]
        
        if avg_scores["legality"] < 80:
            suggestions.append("合同存在合法性问题，建议法律专业人士审查")
        
        if avg_scores["completeness"] < 70:
            suggestions.append("合同条款不够完整，建议补充必要条款")
        
        if avg_scores["reasonableness"] < 70:
            suggestions.append("合同条款合理性有待提高，建议平衡双方权利义务")
        
        if avg_scores["overall"] < 60:
            suggestions.append("合同整体质量较低，建议重新起草")
        elif avg_scores["overall"] < 80:
            suggestions.append("合同基本可用，但建议进一步优化完善")
        else:
            suggestions.append("合同质量良好，可以使用")
        
        return suggestions


# 全局合同条款分析器实例
contract_clause_analyzer = ContractClauseAnalyzer()


def analyze_contract_clauses(clauses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析合同条款的便捷函数"""
    return contract_clause_analyzer.analyze_contract_clauses(clauses)


def analyze_single_clause(content: str, clause_type: str = "", title: str = "") -> Dict[str, Any]:
    """分析单个条款的便捷函数"""
    result = contract_clause_analyzer.analyze_clause(content, clause_type, title)
    return result.to_dict()
