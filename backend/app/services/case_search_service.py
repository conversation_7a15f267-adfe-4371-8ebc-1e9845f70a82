"""
案例检索服务
提供法律案例的搜索、索引、管理等功能
"""

import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from sqlalchemy.orm import Session

from app.core.elasticsearch_manager import get_elasticsearch_manager
from app.core.audit import get_audit_logger
from app.models.legal_case import LegalCase

logger = logging.getLogger(__name__)


class CaseSearchService:
    """案例检索服务"""
    
    def __init__(self, db: Session):
        """
        初始化案例检索服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.es_manager = get_elasticsearch_manager()
        self.audit_logger = get_audit_logger(db)
        
        # 索引名称
        self.index_name = "legal_cases"
        
        # 初始化索引
        self._ensure_index_exists()
    
    def _ensure_index_exists(self):
        """确保索引存在"""
        mapping = self._get_case_mapping()
        settings = self._get_case_settings()
        
        self.es_manager.create_index(
            index_name=self.index_name,
            mapping=mapping,
            settings=settings
        )
    
    def _get_case_mapping(self) -> Dict[str, Any]:
        """获取案例索引映射"""
        return {
            "properties": {
                "case_id": {
                    "type": "keyword"
                },
                "case_number": {
                    "type": "keyword"
                },
                "title": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                        }
                    }
                },
                "court": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                        }
                    }
                },
                "court_level": {
                    "type": "keyword"
                },
                "case_type": {
                    "type": "keyword"
                },
                "legal_area": {
                    "type": "keyword"
                },
                "parties": {
                    "type": "nested",
                    "properties": {
                        "name": {
                            "type": "text",
                            "analyzer": "ik_max_word"
                        },
                        "role": {
                            "type": "keyword"
                        },
                        "type": {
                            "type": "keyword"
                        }
                    }
                },
                "judges": {
                    "type": "text",
                    "analyzer": "ik_max_word"
                },
                "case_summary": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart"
                },
                "case_facts": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart"
                },
                "court_opinion": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart"
                },
                "judgment_result": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart"
                },
                "legal_basis": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart"
                },
                "keywords": {
                    "type": "keyword"
                },
                "tags": {
                    "type": "keyword"
                },
                "judgment_date": {
                    "type": "date",
                    "format": "yyyy-MM-dd||yyyy-MM-dd HH:mm:ss||epoch_millis"
                },
                "case_status": {
                    "type": "keyword"
                },
                "appeal_status": {
                    "type": "keyword"
                },
                "monetary_amount": {
                    "type": "double"
                },
                "currency": {
                    "type": "keyword"
                },
                "source_url": {
                    "type": "keyword",
                    "index": False
                },
                "source_platform": {
                    "type": "keyword"
                },
                "crawl_date": {
                    "type": "date"
                },
                "indexed_at": {
                    "type": "date"
                },
                "updated_at": {
                    "type": "date"
                },
                "full_text": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart"
                },
                "similarity_hash": {
                    "type": "keyword"
                },
                "importance_score": {
                    "type": "float"
                },
                "citation_count": {
                    "type": "integer"
                },
                "view_count": {
                    "type": "integer"
                }
            }
        }
    
    def _get_case_settings(self) -> Dict[str, Any]:
        """获取案例索引设置"""
        return {
            "number_of_shards": 3,
            "number_of_replicas": 1,
            "analysis": {
                "analyzer": {
                    "ik_max_word": {
                        "type": "ik_max_word"
                    },
                    "ik_smart": {
                        "type": "ik_smart"
                    }
                }
            },
            "max_result_window": 50000
        }
    
    def index_case(self, case_data: Dict[str, Any]) -> bool:
        """
        索引单个案例
        
        Args:
            case_data: 案例数据
            
        Returns:
            是否索引成功
        """
        try:
            case_id = case_data.get("case_id") or str(uuid.uuid4())
            
            # 预处理案例数据
            processed_data = self._preprocess_case_data(case_data)
            
            # 索引到Elasticsearch
            success = self.es_manager.index_document(
                index_name=self.index_name,
                doc_id=case_id,
                document=processed_data
            )
            
            if success:
                logger.info(f"案例索引成功: {case_id}")
            else:
                logger.error(f"案例索引失败: {case_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"索引案例异常: {e}")
            return False
    
    def bulk_index_cases(self, cases_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量索引案例
        
        Args:
            cases_data: 案例数据列表
            
        Returns:
            批量索引结果
        """
        try:
            # 预处理案例数据
            processed_cases = []
            for case_data in cases_data:
                case_id = case_data.get("case_id") or str(uuid.uuid4())
                processed_data = self._preprocess_case_data(case_data)
                processed_data["_id"] = case_id
                processed_cases.append(processed_data)
            
            # 批量索引
            result = self.es_manager.bulk_index_documents(
                index_name=self.index_name,
                documents=processed_cases
            )
            
            logger.info(f"批量索引完成: 成功 {result['success']}, 失败 {result['failed']}")
            return result
            
        except Exception as e:
            logger.error(f"批量索引案例异常: {e}")
            return {"success": 0, "failed": len(cases_data), "errors": [str(e)]}
    
    def _preprocess_case_data(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理案例数据
        
        Args:
            case_data: 原始案例数据
            
        Returns:
            处理后的案例数据
        """
        processed = case_data.copy()
        
        # 生成全文搜索字段
        full_text_parts = []
        
        for field in ["title", "case_summary", "case_facts", "court_opinion", "judgment_result", "legal_basis"]:
            if processed.get(field):
                full_text_parts.append(str(processed[field]))
        
        processed["full_text"] = " ".join(full_text_parts)
        
        # 提取关键词
        if not processed.get("keywords"):
            processed["keywords"] = self._extract_keywords(processed["full_text"])
        
        # 计算重要性分数
        if not processed.get("importance_score"):
            processed["importance_score"] = self._calculate_importance_score(processed)
        
        # 生成相似性哈希
        if not processed.get("similarity_hash"):
            processed["similarity_hash"] = self._generate_similarity_hash(processed["full_text"])
        
        # 设置默认值
        processed.setdefault("view_count", 0)
        processed.setdefault("citation_count", 0)
        processed.setdefault("case_status", "closed")
        processed.setdefault("appeal_status", "none")
        
        return processed
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        提取关键词
        
        Args:
            text: 文本内容
            
        Returns:
            关键词列表
        """
        # 这里应该使用实际的关键词提取算法
        # 目前返回模拟数据
        keywords = ["合同纠纷", "违约责任", "损害赔偿", "民事诉讼"]
        return keywords
    
    def _calculate_importance_score(self, case_data: Dict[str, Any]) -> float:
        """
        计算案例重要性分数
        
        Args:
            case_data: 案例数据
            
        Returns:
            重要性分数
        """
        score = 0.0
        
        # 法院级别权重
        court_level_weights = {
            "supreme": 10.0,
            "high": 8.0,
            "intermediate": 6.0,
            "basic": 4.0
        }
        score += court_level_weights.get(case_data.get("court_level", "basic"), 4.0)
        
        # 案例类型权重
        case_type_weights = {
            "criminal": 8.0,
            "civil": 6.0,
            "administrative": 7.0,
            "commercial": 7.0
        }
        score += case_type_weights.get(case_data.get("case_type", "civil"), 6.0)
        
        # 引用次数权重
        citation_count = case_data.get("citation_count", 0)
        score += min(citation_count * 0.1, 5.0)
        
        # 查看次数权重
        view_count = case_data.get("view_count", 0)
        score += min(view_count * 0.01, 3.0)
        
        return min(score, 100.0)
    
    def _generate_similarity_hash(self, text: str) -> str:
        """
        生成相似性哈希
        
        Args:
            text: 文本内容
            
        Returns:
            相似性哈希值
        """
        import hashlib
        
        # 简单的哈希实现，实际应该使用更复杂的算法
        normalized_text = text.lower().replace(" ", "")
        return hashlib.md5(normalized_text.encode()).hexdigest()
    
    def search_cases(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "relevance",
        page: int = 1,
        page_size: int = 20,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        搜索案例
        
        Args:
            query: 搜索查询
            filters: 过滤条件
            sort_by: 排序方式
            page: 页码
            page_size: 每页大小
            user_id: 用户ID（用于审计）
            
        Returns:
            搜索结果
        """
        try:
            # 构建搜索查询
            es_query = self._build_search_query(query, filters)
            
            # 构建排序条件
            sort_conditions = self._build_sort_conditions(sort_by)
            
            # 构建高亮设置
            highlight = {
                "fields": {
                    "title": {},
                    "case_summary": {},
                    "case_facts": {},
                    "court_opinion": {},
                    "judgment_result": {}
                },
                "pre_tags": ["<mark>"],
                "post_tags": ["</mark>"]
            }
            
            # 计算分页参数
            from_index = (page - 1) * page_size
            
            # 执行搜索
            search_result = self.es_manager.search_documents(
                index_name=self.index_name,
                query=es_query,
                size=page_size,
                from_=from_index,
                sort=sort_conditions,
                highlight=highlight
            )
            
            # 记录搜索审计日志
            if user_id:
                self.audit_logger.log_action(
                    action="CASE_SEARCH",
                    user_id=user_id,
                    resource_type="LEGAL_CASE",
                    resource_id=None,
                    details={
                        "query": query,
                        "filters": filters,
                        "results_count": search_result["total"],
                        "page": page,
                        "page_size": page_size
                    },
                    success=True
                )
            
            # 处理搜索结果
            processed_results = []
            for result in search_result["results"]:
                case_info = result["source"]
                case_info["search_score"] = result["score"]
                
                # 添加高亮信息
                if "highlight" in result:
                    case_info["highlight"] = result["highlight"]
                
                processed_results.append(case_info)
            
            return {
                "query": query,
                "filters": filters,
                "total_count": search_result["total"],
                "page": page,
                "page_size": page_size,
                "total_pages": (search_result["total"] + page_size - 1) // page_size,
                "results": processed_results,
                "took": search_result["took"],
                "max_score": search_result["max_score"]
            }
            
        except Exception as e:
            logger.error(f"搜索案例失败: {e}")
            
            # 记录失败的审计日志
            if user_id:
                self.audit_logger.log_action(
                    action="CASE_SEARCH_FAILED",
                    user_id=user_id,
                    resource_type="LEGAL_CASE",
                    resource_id=None,
                    details={
                        "query": query,
                        "error": str(e)
                    },
                    success=False
                )
            
            return {
                "query": query,
                "filters": filters,
                "total_count": 0,
                "page": page,
                "page_size": page_size,
                "total_pages": 0,
                "results": [],
                "took": 0,
                "max_score": 0,
                "error": str(e)
            }
    
    def _build_search_query(self, query: str, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        构建搜索查询
        
        Args:
            query: 搜索查询
            filters: 过滤条件
            
        Returns:
            Elasticsearch查询
        """
        must_clauses = []
        filter_clauses = []
        
        # 主查询
        if query and query.strip():
            must_clauses.append({
                "multi_match": {
                    "query": query,
                    "fields": [
                        "title^3",
                        "case_summary^2",
                        "case_facts^1.5",
                        "court_opinion^1.5",
                        "judgment_result^2",
                        "legal_basis^1.5",
                        "full_text"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        else:
            must_clauses.append({"match_all": {}})
        
        # 过滤条件
        if filters:
            for key, value in filters.items():
                if value is not None:
                    if key == "court_level":
                        filter_clauses.append({"term": {"court_level": value}})
                    elif key == "case_type":
                        filter_clauses.append({"term": {"case_type": value}})
                    elif key == "legal_area":
                        filter_clauses.append({"term": {"legal_area": value}})
                    elif key == "judgment_date_from":
                        filter_clauses.append({"range": {"judgment_date": {"gte": value}}})
                    elif key == "judgment_date_to":
                        filter_clauses.append({"range": {"judgment_date": {"lte": value}}})
                    elif key == "court":
                        filter_clauses.append({"match": {"court": value}})
                    elif key == "keywords":
                        if isinstance(value, list):
                            filter_clauses.append({"terms": {"keywords": value}})
                        else:
                            filter_clauses.append({"term": {"keywords": value}})
        
        # 构建最终查询
        if filter_clauses:
            return {
                "bool": {
                    "must": must_clauses,
                    "filter": filter_clauses
                }
            }
        else:
            return {
                "bool": {
                    "must": must_clauses
                }
            }
    
    def _build_sort_conditions(self, sort_by: str) -> List[Dict[str, Any]]:
        """
        构建排序条件
        
        Args:
            sort_by: 排序方式
            
        Returns:
            排序条件列表
        """
        sort_options = {
            "relevance": [{"_score": {"order": "desc"}}],
            "date_desc": [{"judgment_date": {"order": "desc"}}, {"_score": {"order": "desc"}}],
            "date_asc": [{"judgment_date": {"order": "asc"}}, {"_score": {"order": "desc"}}],
            "importance": [{"importance_score": {"order": "desc"}}, {"_score": {"order": "desc"}}],
            "citation": [{"citation_count": {"order": "desc"}}, {"_score": {"order": "desc"}}]
        }
        
        return sort_options.get(sort_by, sort_options["relevance"])
    
    def get_case_by_id(self, case_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        根据ID获取案例详情
        
        Args:
            case_id: 案例ID
            user_id: 用户ID（用于审计）
            
        Returns:
            案例详情
        """
        try:
            result = self.es_manager.get_document(self.index_name, case_id)
            
            if result:
                # 增加查看次数
                self.es_manager.update_document(
                    self.index_name,
                    case_id,
                    {"view_count": result["source"].get("view_count", 0) + 1}
                )
                
                # 记录查看审计日志
                if user_id:
                    self.audit_logger.log_action(
                        action="CASE_VIEWED",
                        user_id=user_id,
                        resource_type="LEGAL_CASE",
                        resource_id=case_id,
                        details={},
                        success=True
                    )
                
                return result["source"]
            
            return None
            
        except Exception as e:
            logger.error(f"获取案例详情失败: {case_id}, 错误: {e}")
            return None
    
    def get_similar_cases(self, case_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取相似案例
        
        Args:
            case_id: 案例ID
            limit: 返回数量限制
            
        Returns:
            相似案例列表
        """
        try:
            # 获取原案例
            original_case = self.es_manager.get_document(self.index_name, case_id)
            if not original_case:
                return []
            
            # 构建相似性查询
            query = {
                "more_like_this": {
                    "fields": ["title", "case_summary", "case_facts", "legal_basis"],
                    "like": [{"_index": self.index_name, "_id": case_id}],
                    "min_term_freq": 1,
                    "max_query_terms": 12,
                    "min_doc_freq": 1
                }
            }
            
            # 执行搜索
            search_result = self.es_manager.search_documents(
                index_name=self.index_name,
                query=query,
                size=limit
            )
            
            return [result["source"] for result in search_result["results"]]
            
        except Exception as e:
            logger.error(f"获取相似案例失败: {case_id}, 错误: {e}")
            return []
    
    def get_search_suggestions(self, query: str, limit: int = 10) -> List[str]:
        """
        获取搜索建议
        
        Args:
            query: 查询字符串
            limit: 建议数量限制
            
        Returns:
            搜索建议列表
        """
        try:
            # 使用聚合查询获取热门搜索词
            aggs = {
                "suggestions": {
                    "terms": {
                        "field": "keywords",
                        "size": limit,
                        "include": f".*{query}.*"
                    }
                }
            }
            
            search_result = self.es_manager.search_documents(
                index_name=self.index_name,
                query={"match_all": {}},
                size=0,
                aggregations=aggs
            )
            
            suggestions = []
            if "suggestions" in search_result["aggregations"]:
                for bucket in search_result["aggregations"]["suggestions"]["buckets"]:
                    suggestions.append(bucket["key"])
            
            return suggestions
            
        except Exception as e:
            logger.error(f"获取搜索建议失败: {query}, 错误: {e}")
            return []
