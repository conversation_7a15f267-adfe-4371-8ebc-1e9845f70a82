"""
合同修改建议生成器
基于风险分析和条款分析结果，自动生成合同修改建议和优化方案
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from datetime import datetime
import jieba

logger = logging.getLogger(__name__)


class ModificationSuggestion:
    """修改建议类"""
    
    def __init__(self, suggestion_id: str, suggestion_type: str, priority: str):
        """初始化修改建议
        
        Args:
            suggestion_id: 建议唯一标识
            suggestion_type: 建议类型
            priority: 优先级 (urgent/high/medium/low)
        """
        self.suggestion_id = suggestion_id
        self.suggestion_type = suggestion_type
        self.priority = priority
        self.title = ""
        self.description = ""
        self.original_text = ""
        self.suggested_text = ""
        self.reason = ""
        self.legal_basis = ""
        self.impact_assessment = ""
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "suggestion_id": self.suggestion_id,
            "suggestion_type": self.suggestion_type,
            "priority": self.priority,
            "title": self.title,
            "description": self.description,
            "original_text": self.original_text,
            "suggested_text": self.suggested_text,
            "reason": self.reason,
            "legal_basis": self.legal_basis,
            "impact_assessment": self.impact_assessment
        }


class ContractSuggestionGenerator:
    """合同修改建议生成器"""
    
    def __init__(self):
        """初始化建议生成器"""
        self.suggestion_templates = self._load_suggestion_templates()
        self.modification_patterns = self._load_modification_patterns()
        self.legal_templates = self._load_legal_templates()
        
        logger.info("合同修改建议生成器初始化完成")
    
    def _load_suggestion_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载建议模板"""
        return {
            "风险消除": {
                "title_template": "消除{risk_type}风险",
                "description_template": "建议修改或删除存在{risk_type}风险的条款",
                "priority": "high"
            },
            "条款补充": {
                "title_template": "补充{clause_type}条款",
                "description_template": "建议添加{clause_type}相关条款以完善合同",
                "priority": "medium"
            },
            "权利平衡": {
                "title_template": "平衡双方权利义务",
                "description_template": "建议调整条款以实现双方权利义务的平衡",
                "priority": "medium"
            },
            "合规优化": {
                "title_template": "合规性优化",
                "description_template": "建议修改条款以符合法律法规要求",
                "priority": "high"
            }
        }
    
    def _load_modification_patterns(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载修改模式"""
        return {
            "免责条款": [
                {
                    "pattern": r"甲方.*不承担.*责任",
                    "suggestion": "建议明确甲方免责的具体情形和范围，避免过于宽泛的免责声明",
                    "template": "甲方在以下情形下不承担责任：(1)不可抗力；(2)乙方违约导致的损失；(3)其他法律规定的免责情形。"
                }
            ],
            "违约责任": [
                {
                    "pattern": r"违约金.*(\d+)%",
                    "suggestion": "建议将违约金调整到合理范围内",
                    "template": "违约方应支付违约金，违约金数额不超过合同总金额的20%。"
                }
            ],
            "争议解决": [
                {
                    "pattern": r"甲方所在地.*管辖",
                    "suggestion": "建议选择对双方都公平的管辖法院",
                    "template": "因本合同发生的争议，由合同签订地或合同履行地人民法院管辖。"
                }
            ],
            "价格条款": [
                {
                    "pattern": r"甲方.*单方.*调整.*价格",
                    "suggestion": "建议明确价格调整的条件和程序",
                    "template": "价格调整应满足以下条件：(1)市场价格变动超过10%；(2)提前30天书面通知；(3)双方协商确定。"
                }
            ]
        }
    
    def _load_legal_templates(self) -> Dict[str, str]:
        """加载法律条款模板"""
        return {
            "不可抗力": """
            因不可抗力导致合同无法履行的，受影响方应及时通知对方，并在合理期限内提供相关证明。
            不可抗力包括但不限于：自然灾害、政府行为、法律法规变更等。
            因不可抗力造成的损失，双方各自承担。
            """,
            "保密条款": """
            双方应对在合同履行过程中知悉的对方商业秘密和机密信息予以保密。
            保密期限为合同终止后三年。
            违反保密义务的，应承担相应的法律责任。
            """,
            "知识产权": """
            合同履行过程中产生的知识产权归属应明确约定。
            一方使用另一方知识产权的，应获得书面授权。
            侵犯第三方知识产权的责任承担应明确约定。
            """,
            "合同变更": """
            合同的变更应经双方书面同意。
            重大变更应重新签署补充协议。
            口头变更不产生法律效力。
            """
        }
    
    def generate_suggestions(self, risk_analysis: Dict[str, Any], 
                           clause_analysis: Dict[str, Any] = None,
                           contract_text: str = "") -> Dict[str, Any]:
        """生成修改建议
        
        Args:
            risk_analysis: 风险分析结果
            clause_analysis: 条款分析结果（可选）
            contract_text: 合同文本（可选）
        
        Returns:
            修改建议结果
        """
        try:
            suggestions = []
            
            # 1. 基于风险分析生成建议
            risk_suggestions = self._generate_risk_based_suggestions(risk_analysis)
            suggestions.extend(risk_suggestions)
            
            # 2. 基于条款分析生成建议
            if clause_analysis:
                clause_suggestions = self._generate_clause_based_suggestions(clause_analysis)
                suggestions.extend(clause_suggestions)
            
            # 3. 基于合同文本生成建议
            if contract_text:
                text_suggestions = self._generate_text_based_suggestions(contract_text)
                suggestions.extend(text_suggestions)
            
            # 4. 生成标准条款建议
            standard_suggestions = self._generate_standard_clause_suggestions(contract_text)
            suggestions.extend(standard_suggestions)
            
            # 5. 按优先级排序
            suggestions = self._prioritize_suggestions(suggestions)
            
            # 6. 生成修改方案
            modification_plan = self._generate_modification_plan(suggestions)
            
            # 7. 生成实施指南
            implementation_guide = self._generate_implementation_guide(suggestions)
            
            return {
                "suggestions": [s.to_dict() for s in suggestions],
                "modification_plan": modification_plan,
                "implementation_guide": implementation_guide,
                "summary": {
                    "total_suggestions": len(suggestions),
                    "urgent_count": len([s for s in suggestions if s.priority == "urgent"]),
                    "high_count": len([s for s in suggestions if s.priority == "high"]),
                    "medium_count": len([s for s in suggestions if s.priority == "medium"]),
                    "low_count": len([s for s in suggestions if s.priority == "low"])
                },
                "generation_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成修改建议失败: {e}")
            return {
                "error": str(e),
                "generation_timestamp": datetime.now().isoformat()
            }
    
    def _generate_risk_based_suggestions(self, risk_analysis: Dict[str, Any]) -> List[ModificationSuggestion]:
        """基于风险分析生成建议"""
        suggestions = []
        risks = risk_analysis.get("risks", [])
        
        suggestion_id = 1
        for risk in risks:
            risk_type = risk.get("risk_type", "")
            severity = risk.get("severity", "low")
            description = risk.get("description", "")
            suggestion_text = risk.get("suggestion", "")
            
            # 确定优先级
            if severity == "critical":
                priority = "urgent"
            elif severity == "high":
                priority = "high"
            elif severity == "medium":
                priority = "medium"
            else:
                priority = "low"
            
            suggestion = ModificationSuggestion(
                suggestion_id=f"risk_{suggestion_id}",
                suggestion_type="风险消除",
                priority=priority
            )
            
            suggestion.title = f"消除{risk_type}风险"
            suggestion.description = description
            suggestion.reason = f"存在{severity}级别的{risk_type}风险"
            suggestion.suggested_text = suggestion_text
            suggestion.impact_assessment = self._assess_risk_impact(severity)
            
            suggestions.append(suggestion)
            suggestion_id += 1
        
        return suggestions
    
    def _generate_clause_based_suggestions(self, clause_analysis: Dict[str, Any]) -> List[ModificationSuggestion]:
        """基于条款分析生成建议"""
        suggestions = []
        clause_analyses = clause_analysis.get("clause_analyses", [])
        
        suggestion_id = 1000
        for clause in clause_analyses:
            scores = clause.get("scores", {})
            issues = clause.get("issues", {})
            clause_title = clause.get("clause_title", "")
            
            # 基于得分生成建议
            if scores.get("legality", 100) < 80:
                suggestion = ModificationSuggestion(
                    suggestion_id=f"clause_{suggestion_id}",
                    suggestion_type="合规优化",
                    priority="high"
                )
                suggestion.title = f"优化{clause_title}的合法性"
                suggestion.description = "条款存在合法性问题，需要修改"
                suggestion.reason = f"合法性得分: {scores.get('legality', 0)}"
                suggestions.append(suggestion)
                suggestion_id += 1
            
            if scores.get("completeness", 100) < 70:
                suggestion = ModificationSuggestion(
                    suggestion_id=f"clause_{suggestion_id}",
                    suggestion_type="条款补充",
                    priority="medium"
                )
                suggestion.title = f"完善{clause_title}的内容"
                suggestion.description = "条款内容不够完整，需要补充"
                suggestion.reason = f"完整性得分: {scores.get('completeness', 0)}"
                suggestions.append(suggestion)
                suggestion_id += 1
            
            if scores.get("reasonableness", 100) < 70:
                suggestion = ModificationSuggestion(
                    suggestion_id=f"clause_{suggestion_id}",
                    suggestion_type="权利平衡",
                    priority="medium"
                )
                suggestion.title = f"调整{clause_title}的合理性"
                suggestion.description = "条款合理性有待提高，需要调整"
                suggestion.reason = f"合理性得分: {scores.get('reasonableness', 0)}"
                suggestions.append(suggestion)
                suggestion_id += 1
        
        return suggestions
    
    def _generate_text_based_suggestions(self, contract_text: str) -> List[ModificationSuggestion]:
        """基于合同文本生成建议"""
        suggestions = []
        suggestion_id = 2000
        
        for category, patterns in self.modification_patterns.items():
            for pattern_info in patterns:
                pattern = pattern_info["pattern"]
                matches = list(re.finditer(pattern, contract_text, re.IGNORECASE))
                
                for match in matches:
                    suggestion = ModificationSuggestion(
                        suggestion_id=f"text_{suggestion_id}",
                        suggestion_type="条款优化",
                        priority="medium"
                    )
                    
                    suggestion.title = f"优化{category}"
                    suggestion.description = pattern_info["suggestion"]
                    suggestion.original_text = match.group(0)
                    suggestion.suggested_text = pattern_info["template"]
                    suggestion.reason = f"发现{category}相关问题"
                    
                    suggestions.append(suggestion)
                    suggestion_id += 1
        
        return suggestions
    
    def _generate_standard_clause_suggestions(self, contract_text: str) -> List[ModificationSuggestion]:
        """生成标准条款建议"""
        suggestions = []
        suggestion_id = 3000
        
        # 检查是否缺少标准条款
        standard_clauses = {
            "不可抗力": [r"不可抗力", r"force majeure"],
            "保密条款": [r"保密", r"机密", r"商业秘密"],
            "知识产权": [r"知识产权", r"专利", r"商标", r"著作权"],
            "合同变更": [r"变更", r"修改", r"补充协议"]
        }
        
        for clause_name, patterns in standard_clauses.items():
            found = any(re.search(pattern, contract_text, re.IGNORECASE) for pattern in patterns)
            
            if not found:
                suggestion = ModificationSuggestion(
                    suggestion_id=f"standard_{suggestion_id}",
                    suggestion_type="条款补充",
                    priority="low"
                )
                
                suggestion.title = f"添加{clause_name}条款"
                suggestion.description = f"建议添加{clause_name}相关条款"
                suggestion.suggested_text = self.legal_templates.get(clause_name, "")
                suggestion.reason = f"合同缺少{clause_name}条款"
                suggestion.legal_basis = "完善合同条款，降低法律风险"
                
                suggestions.append(suggestion)
                suggestion_id += 1
        
        return suggestions
    
    def _prioritize_suggestions(self, suggestions: List[ModificationSuggestion]) -> List[ModificationSuggestion]:
        """按优先级排序建议"""
        priority_order = {"urgent": 4, "high": 3, "medium": 2, "low": 1}
        
        return sorted(suggestions, key=lambda s: priority_order.get(s.priority, 0), reverse=True)
    
    def _generate_modification_plan(self, suggestions: List[ModificationSuggestion]) -> Dict[str, Any]:
        """生成修改方案"""
        plan = {
            "phase_1_urgent": [],
            "phase_2_high": [],
            "phase_3_medium": [],
            "phase_4_low": [],
            "estimated_timeline": "",
            "implementation_order": []
        }
        
        # 按优先级分组
        for suggestion in suggestions:
            if suggestion.priority == "urgent":
                plan["phase_1_urgent"].append(suggestion.suggestion_id)
            elif suggestion.priority == "high":
                plan["phase_2_high"].append(suggestion.suggestion_id)
            elif suggestion.priority == "medium":
                plan["phase_3_medium"].append(suggestion.suggestion_id)
            else:
                plan["phase_4_low"].append(suggestion.suggestion_id)
        
        # 估算时间线
        urgent_count = len(plan["phase_1_urgent"])
        high_count = len(plan["phase_2_high"])
        medium_count = len(plan["phase_3_medium"])
        low_count = len(plan["phase_4_low"])
        
        estimated_days = urgent_count * 1 + high_count * 2 + medium_count * 3 + low_count * 1
        plan["estimated_timeline"] = f"预计需要{estimated_days}个工作日完成所有修改"
        
        # 实施顺序
        plan["implementation_order"] = [
            "第一阶段：处理紧急问题（立即执行）",
            "第二阶段：解决高优先级问题（1-3天内）",
            "第三阶段：优化中等优先级问题（1周内）",
            "第四阶段：完善低优先级问题（2周内）"
        ]
        
        return plan
    
    def _generate_implementation_guide(self, suggestions: List[ModificationSuggestion]) -> Dict[str, Any]:
        """生成实施指南"""
        guide = {
            "preparation_steps": [
                "备份原始合同文档",
                "准备相关法律法规资料",
                "确定修改责任人和审核流程",
                "制定修改时间表"
            ],
            "modification_process": [
                "按优先级顺序逐项修改",
                "每项修改后进行法律合规性检查",
                "重要修改需要法律专业人士审核",
                "修改完成后进行整体一致性检查"
            ],
            "quality_control": [
                "修改前后对比检查",
                "法律风险评估",
                "业务影响分析",
                "相关方确认"
            ],
            "finalization_steps": [
                "生成修改对照表",
                "准备修改说明文档",
                "安排相关方签署确认",
                "归档修改记录"
            ],
            "注意事项": [
                "重大修改可能需要重新谈判",
                "某些修改可能影响合同整体结构",
                "建议分批次实施，避免一次性大幅修改",
                "保持与相关方的充分沟通"
            ]
        }
        
        return guide
    
    def _assess_risk_impact(self, severity: str) -> str:
        """评估风险影响"""
        impact_map = {
            "critical": "可能导致合同无效或面临严重法律后果",
            "high": "可能导致重大经济损失或法律纠纷",
            "medium": "可能影响合同履行或增加争议风险",
            "low": "对合同履行影响较小，但建议优化"
        }
        
        return impact_map.get(severity, "影响程度待评估")


# 全局合同修改建议生成器实例
contract_suggestion_generator = ContractSuggestionGenerator()


def generate_contract_suggestions(risk_analysis: Dict[str, Any], 
                                clause_analysis: Dict[str, Any] = None,
                                contract_text: str = "") -> Dict[str, Any]:
    """生成合同修改建议的便捷函数"""
    return contract_suggestion_generator.generate_suggestions(risk_analysis, clause_analysis, contract_text)
