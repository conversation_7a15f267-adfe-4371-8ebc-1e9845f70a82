"""
案例爬取服务
提供法律案例数据的爬取、清洗、存储功能
"""

import asyncio
import aiohttp
import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import re
import time
import random

from app.core.database import get_db
from app.core.audit import get_audit_logger
from app.services.case_search_service import CaseSearchService

logger = logging.getLogger(__name__)


class CaseCrawlerConfig:
    """爬虫配置"""
    
    def __init__(self):
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        self.request_delay = (1, 3)  # 请求间隔（秒）
        self.timeout = 30
        self.max_retries = 3
        self.concurrent_requests = 5
        self.respect_robots_txt = True
        
        # 目标网站配置
        self.target_sites = {
            "court_gov_cn": {
                "base_url": "https://wenshu.court.gov.cn",
                "search_url": "https://wenshu.court.gov.cn/website/wenshu/181107ANFZ0W5J/index.html",
                "enabled": True,
                "rate_limit": 2  # 每秒最多2个请求
            },
            "pkulaw": {
                "base_url": "https://www.pkulaw.com",
                "search_url": "https://www.pkulaw.com/case",
                "enabled": False,  # 需要付费账户
                "rate_limit": 1
            }
        }


class CaseCrawlerService:
    """案例爬取服务"""
    
    def __init__(self, db_session=None):
        """
        初始化爬取服务
        
        Args:
            db_session: 数据库会话
        """
        self.db = db_session or next(get_db())
        self.config = CaseCrawlerConfig()
        self.audit_logger = get_audit_logger(self.db)
        self.case_search_service = CaseSearchService(self.db)
        
        # 爬取统计
        self.stats = {
            "total_crawled": 0,
            "successful": 0,
            "failed": 0,
            "duplicates": 0,
            "start_time": None,
            "end_time": None
        }
    
    async def crawl_cases(
        self,
        site_name: str,
        search_params: Dict[str, Any],
        max_pages: int = 10,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        爬取案例数据
        
        Args:
            site_name: 目标网站名称
            search_params: 搜索参数
            max_pages: 最大爬取页数
            user_id: 用户ID（用于审计）
            
        Returns:
            爬取结果
        """
        try:
            self.stats["start_time"] = datetime.utcnow()
            
            # 检查网站配置
            if site_name not in self.config.target_sites:
                raise ValueError(f"不支持的网站: {site_name}")
            
            site_config = self.config.target_sites[site_name]
            if not site_config["enabled"]:
                raise ValueError(f"网站已禁用: {site_name}")
            
            logger.info(f"开始爬取案例: {site_name}, 参数: {search_params}")
            
            # 记录爬取开始审计日志
            if user_id:
                self.audit_logger.log_action(
                    action="CASE_CRAWL_STARTED",
                    user_id=user_id,
                    resource_type="CASE_CRAWLER",
                    resource_id=site_name,
                    details={
                        "site_name": site_name,
                        "search_params": search_params,
                        "max_pages": max_pages
                    },
                    success=True
                )
            
            # 执行爬取
            if site_name == "court_gov_cn":
                results = await self._crawl_court_gov_cn(search_params, max_pages)
            else:
                raise ValueError(f"未实现的爬取器: {site_name}")
            
            self.stats["end_time"] = datetime.utcnow()
            
            # 记录爬取完成审计日志
            if user_id:
                self.audit_logger.log_action(
                    action="CASE_CRAWL_COMPLETED",
                    user_id=user_id,
                    resource_type="CASE_CRAWLER",
                    resource_id=site_name,
                    details={
                        "stats": self.stats,
                        "duration": (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
                    },
                    success=True
                )
            
            logger.info(f"爬取完成: {site_name}, 统计: {self.stats}")
            
            return {
                "site_name": site_name,
                "search_params": search_params,
                "stats": self.stats,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"爬取失败: {site_name}, 错误: {e}")
            
            # 记录失败的审计日志
            if user_id:
                self.audit_logger.log_action(
                    action="CASE_CRAWL_FAILED",
                    user_id=user_id,
                    resource_type="CASE_CRAWLER",
                    resource_id=site_name,
                    details={
                        "error": str(e),
                        "search_params": search_params
                    },
                    success=False
                )
            
            raise
    
    async def _crawl_court_gov_cn(
        self,
        search_params: Dict[str, Any],
        max_pages: int
    ) -> List[Dict[str, Any]]:
        """
        爬取中国裁判文书网案例
        
        Args:
            search_params: 搜索参数
            max_pages: 最大页数
            
        Returns:
            案例列表
        """
        results = []
        
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout),
            headers={"User-Agent": self.config.user_agent}
        ) as session:
            
            for page in range(1, max_pages + 1):
                try:
                    logger.info(f"爬取第 {page} 页")
                    
                    # 构建搜索URL
                    search_url = self._build_search_url("court_gov_cn", search_params, page)
                    
                    # 发送请求
                    async with session.get(search_url) as response:
                        if response.status != 200:
                            logger.warning(f"请求失败: {search_url}, 状态码: {response.status}")
                            continue
                        
                        html_content = await response.text()
                        
                        # 解析页面
                        page_results = self._parse_court_gov_cn_page(html_content, search_url)
                        
                        if not page_results:
                            logger.info(f"第 {page} 页无结果，停止爬取")
                            break
                        
                        # 处理每个案例
                        for case_info in page_results:
                            try:
                                # 获取案例详情
                                case_detail = await self._get_case_detail(session, case_info["detail_url"])
                                if case_detail:
                                    case_info.update(case_detail)
                                    results.append(case_info)
                                    self.stats["successful"] += 1
                                else:
                                    self.stats["failed"] += 1
                                
                                self.stats["total_crawled"] += 1
                                
                                # 随机延迟
                                delay = random.uniform(*self.config.request_delay)
                                await asyncio.sleep(delay)
                                
                            except Exception as e:
                                logger.error(f"处理案例失败: {case_info.get('title', 'Unknown')}, 错误: {e}")
                                self.stats["failed"] += 1
                    
                    # 页面间延迟
                    await asyncio.sleep(random.uniform(2, 5))
                    
                except Exception as e:
                    logger.error(f"爬取第 {page} 页失败: {e}")
                    continue
        
        return results
    
    def _build_search_url(
        self,
        site_name: str,
        search_params: Dict[str, Any],
        page: int
    ) -> str:
        """
        构建搜索URL
        
        Args:
            site_name: 网站名称
            search_params: 搜索参数
            page: 页码
            
        Returns:
            搜索URL
        """
        if site_name == "court_gov_cn":
            # 这里应该根据实际的API构建URL
            # 由于中国裁判文书网使用复杂的JavaScript和API，这里提供模拟实现
            base_url = self.config.target_sites[site_name]["search_url"]
            
            # 构建查询参数
            params = {
                "keyword": search_params.get("keyword", ""),
                "court": search_params.get("court", ""),
                "case_type": search_params.get("case_type", ""),
                "page": page
            }
            
            # 实际实现中需要根据网站的具体API格式构建
            return f"{base_url}?{'&'.join([f'{k}={v}' for k, v in params.items() if v])}"
        
        return ""
    
    def _parse_court_gov_cn_page(self, html_content: str, page_url: str) -> List[Dict[str, Any]]:
        """
        解析中国裁判文书网页面
        
        Args:
            html_content: HTML内容
            page_url: 页面URL
            
        Returns:
            案例信息列表
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            cases = []
            
            # 这里需要根据实际的HTML结构进行解析
            # 由于网站结构可能变化，这里提供模拟实现
            
            # 模拟解析结果
            for i in range(10):  # 假设每页10个结果
                case_info = {
                    "case_id": str(uuid.uuid4()),
                    "title": f"模拟案例标题 {i+1}",
                    "court": "某某人民法院",
                    "case_number": f"(2024)某民初{1000+i}号",
                    "judgment_date": "2024-08-01",
                    "case_type": "民事案件",
                    "detail_url": f"https://example.com/case/{i+1}",
                    "source_url": page_url,
                    "source_platform": "court_gov_cn",
                    "crawl_date": datetime.utcnow().isoformat()
                }
                cases.append(case_info)
            
            return cases
            
        except Exception as e:
            logger.error(f"解析页面失败: {e}")
            return []
    
    async def _get_case_detail(
        self,
        session: aiohttp.ClientSession,
        detail_url: str
    ) -> Optional[Dict[str, Any]]:
        """
        获取案例详情
        
        Args:
            session: HTTP会话
            detail_url: 详情页URL
            
        Returns:
            案例详情
        """
        try:
            async with session.get(detail_url) as response:
                if response.status != 200:
                    return None
                
                html_content = await response.text()
                return self._parse_case_detail(html_content)
                
        except Exception as e:
            logger.error(f"获取案例详情失败: {detail_url}, 错误: {e}")
            return None
    
    def _parse_case_detail(self, html_content: str) -> Dict[str, Any]:
        """
        解析案例详情页面
        
        Args:
            html_content: HTML内容
            
        Returns:
            案例详情
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 这里需要根据实际的HTML结构进行解析
            # 模拟解析结果
            detail = {
                "case_summary": "这是一个关于合同纠纷的案例摘要...",
                "case_facts": "案例事实：当事人甲与当事人乙签订合同...",
                "court_opinion": "法院认为：根据相关法律规定...",
                "judgment_result": "判决结果：驳回原告诉讼请求...",
                "legal_basis": "法律依据：《合同法》第XX条...",
                "parties": [
                    {"name": "张某", "role": "原告", "type": "个人"},
                    {"name": "某某公司", "role": "被告", "type": "企业"}
                ],
                "judges": "审判长：李某，审判员：王某、赵某",
                "keywords": ["合同纠纷", "违约责任", "损害赔偿"],
                "legal_area": "contract",
                "court_level": "basic",
                "monetary_amount": 100000.0,
                "currency": "CNY"
            }
            
            return detail
            
        except Exception as e:
            logger.error(f"解析案例详情失败: {e}")
            return {}
    
    def clean_case_data(self, raw_case: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗案例数据
        
        Args:
            raw_case: 原始案例数据
            
        Returns:
            清洗后的案例数据
        """
        try:
            cleaned = raw_case.copy()
            
            # 清理标题
            if "title" in cleaned:
                cleaned["title"] = re.sub(r'\s+', ' ', cleaned["title"]).strip()
            
            # 清理案例号
            if "case_number" in cleaned:
                cleaned["case_number"] = re.sub(r'[^\w\(\)\-年初终字号]', '', cleaned["case_number"])
            
            # 标准化日期格式
            if "judgment_date" in cleaned:
                cleaned["judgment_date"] = self._normalize_date(cleaned["judgment_date"])
            
            # 清理文本内容
            text_fields = ["case_summary", "case_facts", "court_opinion", "judgment_result", "legal_basis"]
            for field in text_fields:
                if field in cleaned and cleaned[field]:
                    cleaned[field] = self._clean_text(cleaned[field])
            
            # 提取和标准化关键词
            if "keywords" in cleaned:
                if isinstance(cleaned["keywords"], str):
                    cleaned["keywords"] = [kw.strip() for kw in cleaned["keywords"].split(",") if kw.strip()]
            
            # 设置默认值
            cleaned.setdefault("case_status", "closed")
            cleaned.setdefault("appeal_status", "none")
            cleaned.setdefault("importance_score", 0.0)
            cleaned.setdefault("citation_count", 0)
            cleaned.setdefault("view_count", 0)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"清洗案例数据失败: {e}")
            return raw_case
    
    def _normalize_date(self, date_str: str) -> str:
        """标准化日期格式"""
        try:
            # 尝试多种日期格式
            date_formats = [
                "%Y-%m-%d",
                "%Y年%m月%d日",
                "%Y/%m/%d",
                "%Y.%m.%d"
            ]
            
            for fmt in date_formats:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime("%Y-%m-%d")
                except ValueError:
                    continue
            
            return date_str
            
        except Exception:
            return date_str
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        try:
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            
            # 移除特殊字符
            text = re.sub(r'[^\w\s\u4e00-\u9fff\.,;:!?()（）【】""''、。，；：！？]', '', text)
            
            return text.strip()
            
        except Exception:
            return text
    
    async def save_crawled_cases(self, cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        保存爬取的案例到搜索索引
        
        Args:
            cases: 案例列表
            
        Returns:
            保存结果
        """
        try:
            # 清洗数据
            cleaned_cases = []
            for case in cases:
                cleaned_case = self.clean_case_data(case)
                cleaned_cases.append(cleaned_case)
            
            # 批量索引到Elasticsearch
            result = self.case_search_service.bulk_index_cases(cleaned_cases)
            
            logger.info(f"案例保存完成: 成功 {result['success']}, 失败 {result['failed']}")
            
            return result
            
        except Exception as e:
            logger.error(f"保存案例失败: {e}")
            return {"success": 0, "failed": len(cases), "errors": [str(e)]}
    
    def get_crawl_statistics(self) -> Dict[str, Any]:
        """获取爬取统计信息"""
        return {
            "current_stats": self.stats,
            "supported_sites": list(self.config.target_sites.keys()),
            "enabled_sites": [
                name for name, config in self.config.target_sites.items()
                if config["enabled"]
            ]
        }
