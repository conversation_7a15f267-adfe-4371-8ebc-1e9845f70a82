"""
问答引擎服务
实现基于规则的问答匹配和简单的法律问答功能
"""

import re
import uuid
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text
from sqlalchemy.orm import selectinload
import logging
from datetime import datetime

from app.models.qa import QARecord
from app.models.legal_case import LegalCase
from app.core.exceptions import ValidationException
from app.core.audit import get_audit_logger, AuditEventType

logger = logging.getLogger(__name__)


class QAEngine:
    """问答引擎"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.confidence_threshold = 0.6  # 置信度阈值
        
        # 预定义的法律问答规则
        self.qa_rules = [
            {
                "keywords": ["合同", "违约", "责任"],
                "category": "合同法",
                "template": "根据《合同法》相关规定，合同违约责任主要包括：1. 继续履行；2. 采取补救措施；3. 赔偿损失。具体责任承担需要根据合同条款和实际情况确定。",
                "confidence": 0.8
            },
            {
                "keywords": ["劳动", "工资", "加班"],
                "category": "劳动法",
                "template": "根据《劳动法》规定，用人单位应当按照劳动合同约定和国家规定，向劳动者及时足额支付劳动报酬。加班工资标准为：工作日加班不低于150%，休息日加班不低于200%，法定节假日加班不低于300%。",
                "confidence": 0.8
            },
            {
                "keywords": ["交通事故", "赔偿", "责任"],
                "category": "交通法",
                "template": "交通事故赔偿包括：1. 医疗费；2. 误工费；3. 护理费；4. 交通费；5. 住宿费；6. 住院伙食补助费；7. 必要的营养费等。具体赔偿标准需要根据事故责任认定和实际损失确定。",
                "confidence": 0.8
            },
            {
                "keywords": ["离婚", "财产", "分割"],
                "category": "婚姻法",
                "template": "根据《民法典》婚姻家庭编规定，离婚时夫妻共同财产由双方协议处理；协议不成的，由人民法院根据财产的具体情况，按照照顾子女、女方和无过错方权益的原则判决。",
                "confidence": 0.8
            },
            {
                "keywords": ["房屋", "买卖", "过户"],
                "category": "房地产法",
                "template": "房屋买卖过户需要：1. 签订房屋买卖合同；2. 办理网签备案；3. 缴纳相关税费；4. 办理产权转移登记。建议在专业律师指导下进行交易，确保权益保障。",
                "confidence": 0.8
            },
            {
                "keywords": ["借款", "欠款", "还款"],
                "category": "债权债务",
                "template": "借款纠纷处理建议：1. 保留借款凭证；2. 及时催收并保留证据；3. 必要时申请财产保全；4. 通过诉讼途径维权。借款利率不得超过法定标准。",
                "confidence": 0.7
            },
            {
                "keywords": ["公司", "股权", "转让"],
                "category": "公司法",
                "template": "股权转让需要注意：1. 其他股东的优先购买权；2. 公司章程的相关规定；3. 办理工商变更登记；4. 相关税务处理。建议咨询专业律师确保合规操作。",
                "confidence": 0.7
            },
            {
                "keywords": ["知识产权", "专利", "侵权"],
                "category": "知识产权法",
                "template": "知识产权侵权处理：1. 收集侵权证据；2. 发送律师函；3. 申请行政处理；4. 提起民事诉讼。可以要求停止侵权、赔偿损失、消除影响等。",
                "confidence": 0.7
            }
        ]
    
    async def process_question(
        self,
        question: str,
        user_id: uuid.UUID,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """处理用户问题"""
        
        logger.info(f"处理用户问题: {question[:50]}...")
        
        # 清理和预处理问题
        cleaned_question = self._clean_question(question)
        
        # 基于规则的匹配
        rule_result = await self._match_rules(cleaned_question)
        
        # 基于案例的匹配
        case_result = await self._match_cases(cleaned_question)
        
        # 合并结果
        final_result = await self._merge_results(rule_result, case_result)
        
        # 记录问答记录
        qa_record = await self._save_qa_record(
            user_id=user_id,
            question=question,
            answer=final_result.get("answer", ""),
            category=final_result.get("category", "其他"),
            confidence=final_result.get("confidence", 0.0),
            sources=final_result.get("sources", []),
            context=context
        )
        
        # 记录审计日志
        audit_logger = await get_audit_logger(self.db)
        await audit_logger.log_event(
            event_type=AuditEventType.DATA_READ,
            message=f"用户提问: {question[:50]}...",
            user_id=user_id,
            resource_type="qa",
            resource_id=str(qa_record.id),
            action="ask_question",
            details={
                "question_length": len(question),
                "category": final_result.get("category"),
                "confidence": final_result.get("confidence")
            }
        )
        
        return {
            "id": str(qa_record.id),
            "question": question,
            "answer": final_result.get("answer", ""),
            "category": final_result.get("category", "其他"),
            "confidence": final_result.get("confidence", 0.0),
            "sources": final_result.get("sources", []),
            "timestamp": qa_record.created_at.isoformat(),
            "suggestions": final_result.get("suggestions", [])
        }
    
    def _clean_question(self, question: str) -> str:
        """清理和预处理问题"""
        # 去除多余空格
        question = re.sub(r'\s+', ' ', question.strip())
        
        # 去除特殊字符（保留中文、英文、数字、常用标点）
        question = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9，。？！；：""''（）【】\s]', '', question)
        
        return question
    
    async def _match_rules(self, question: str) -> Dict[str, Any]:
        """基于规则的匹配"""
        best_match = None
        best_score = 0.0
        
        for rule in self.qa_rules:
            score = self._calculate_rule_score(question, rule["keywords"])
            if score > best_score and score >= self.confidence_threshold:
                best_score = score
                best_match = rule
        
        if best_match:
            return {
                "answer": best_match["template"],
                "category": best_match["category"],
                "confidence": best_score,
                "source_type": "rule",
                "sources": [
                    {
                        "title": f"{best_match['category']}相关法律条文",
                        "type": "法律条文",
                        "confidence": best_score
                    }
                ]
            }
        
        return {
            "answer": "抱歉，我无法准确理解您的问题。建议您：1. 详细描述具体情况；2. 咨询专业律师；3. 查阅相关法律条文。",
            "category": "其他",
            "confidence": 0.1,
            "source_type": "default",
            "sources": []
        }
    
    def _calculate_rule_score(self, question: str, keywords: List[str]) -> float:
        """计算规则匹配分数"""
        question_lower = question.lower()
        matched_keywords = 0
        total_keywords = len(keywords)
        
        for keyword in keywords:
            if keyword.lower() in question_lower:
                matched_keywords += 1
        
        # 基础匹配分数
        base_score = matched_keywords / total_keywords if total_keywords > 0 else 0
        
        # 长度惩罚（问题太短可能不够具体）
        length_factor = min(len(question) / 20, 1.0)
        
        # 关键词密度奖励
        density_factor = matched_keywords / max(len(question.split()), 1)
        
        final_score = base_score * 0.7 + length_factor * 0.2 + density_factor * 0.1
        
        return min(final_score, 1.0)
    
    async def _match_cases(self, question: str) -> Dict[str, Any]:
        """基于案例的匹配"""
        try:
            # 提取关键词进行案例搜索
            keywords = self._extract_keywords(question)
            
            if not keywords:
                return {"sources": []}
            
            # 搜索相关案例
            stmt = select(LegalCase).where(
                or_(*[
                    LegalCase.title.ilike(f"%{keyword}%")
                    for keyword in keywords
                ])
            ).limit(3)
            
            result = await self.db.execute(stmt)
            cases = result.scalars().all()
            
            sources = []
            for case in cases:
                sources.append({
                    "title": case.title,
                    "type": "法律案例",
                    "url": f"/cases/{case.id}",
                    "case_number": case.case_number,
                    "court_name": case.court_name
                })
            
            return {"sources": sources}
            
        except Exception as e:
            logger.error(f"案例匹配失败: {e}")
            return {"sources": []}
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取（实际项目中可以使用更复杂的NLP技术）
        common_words = {"的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这"}
        
        words = re.findall(r'[\u4e00-\u9fa5]+', text)
        keywords = [word for word in words if len(word) >= 2 and word not in common_words]
        
        return keywords[:5]  # 返回前5个关键词
    
    async def _merge_results(
        self,
        rule_result: Dict[str, Any],
        case_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """合并规则匹配和案例匹配结果"""
        
        # 以规则匹配为主，案例匹配为辅
        merged_result = rule_result.copy()
        
        # 合并参考来源
        rule_sources = rule_result.get("sources", [])
        case_sources = case_result.get("sources", [])
        merged_result["sources"] = rule_sources + case_sources
        
        # 添加建议
        suggestions = []
        if merged_result.get("confidence", 0) < 0.8:
            suggestions.extend([
                "建议提供更详细的情况描述",
                "可以咨询专业律师获得更准确的建议",
                "查阅相关法律条文和司法解释"
            ])
        
        if case_sources:
            suggestions.append("可以参考相关案例的处理方式")
        
        merged_result["suggestions"] = suggestions
        
        return merged_result
    
    async def _save_qa_record(
        self,
        user_id: uuid.UUID,
        question: str,
        answer: str,
        category: str,
        confidence: float,
        sources: List[Dict[str, Any]],
        context: Dict[str, Any] = None
    ) -> QARecord:
        """保存问答记录"""
        
        qa_record = QARecord(
            user_id=user_id,
            question=question,
            answer=answer,
            category=category,
            confidence=confidence,
            sources=sources,
            context=context or {}
        )
        
        self.db.add(qa_record)
        await self.db.commit()
        await self.db.refresh(qa_record)
        
        return qa_record
    
    async def get_user_qa_history(
        self,
        user_id: uuid.UUID,
        limit: int = 20,
        offset: int = 0
    ) -> List[QARecord]:
        """获取用户问答历史"""
        
        stmt = select(QARecord).where(
            QARecord.user_id == user_id
        ).order_by(
            QARecord.created_at.desc()
        ).limit(limit).offset(offset)
        
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_popular_questions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热门问题"""
        
        stmt = select(
            QARecord.category,
            func.count(QARecord.id).label('count'),
            func.array_agg(QARecord.question).label('questions')
        ).group_by(
            QARecord.category
        ).order_by(
            func.count(QARecord.id).desc()
        ).limit(limit)
        
        result = await self.db.execute(stmt)
        popular_questions = []
        
        for row in result:
            popular_questions.append({
                "category": row.category,
                "count": row.count,
                "sample_questions": row.questions[:3] if row.questions else []
            })
        
        return popular_questions
    
    async def update_qa_feedback(
        self,
        qa_id: uuid.UUID,
        is_helpful: bool,
        feedback: str = None
    ) -> bool:
        """更新问答反馈"""
        
        stmt = select(QARecord).where(QARecord.id == qa_id)
        result = await self.db.execute(stmt)
        qa_record = result.scalar_one_or_none()
        
        if not qa_record:
            return False
        
        qa_record.is_helpful = is_helpful
        if feedback:
            qa_record.feedback = feedback
        
        await self.db.commit()
        return True
