"""
合同模板服务
"""

import uuid
import re
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text
from sqlalchemy.orm import selectinload
import logging
from datetime import datetime
from jinja2 import Template, Environment, BaseLoader

from app.models.contract import ContractReview
from app.core.exceptions import ValidationException, NotFoundException
from app.core.audit import get_audit_logger, AuditEventType

logger = logging.getLogger(__name__)


class ContractTemplateService:
    """合同模板服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.jinja_env = Environment(loader=BaseLoader())
        
        # 预定义的合同模板
        self.templates = {
            "labor_contract": {
                "name": "劳动合同模板",
                "category": "labor",
                "description": "标准劳动合同模板，适用于一般企业员工聘用",
                "difficulty_level": "easy",
                "required_fields": [
                    "employer_name", "employer_address", "employer_legal_representative",
                    "employee_name", "employee_id", "employee_address",
                    "position", "work_location", "contract_period",
                    "salary", "work_hours", "start_date"
                ],
                "optional_fields": [
                    "probation_period", "benefits", "overtime_policy",
                    "vacation_policy", "confidentiality_clause"
                ],
                "template_content": """
劳动合同

甲方（用人单位）：{{ employer_name }}
地址：{{ employer_address }}
法定代表人：{{ employer_legal_representative }}

乙方（劳动者）：{{ employee_name }}
身份证号：{{ employee_id }}
地址：{{ employee_address }}

根据《中华人民共和国劳动法》、《中华人民共和国劳动合同法》等法律法规的规定，甲乙双方遵循合法、公平、平等自愿、协商一致、诚实信用的原则，签订本合同。

第一条 合同期限
本合同为{% if contract_period == "固定期限" %}有固定期限劳动合同{% elif contract_period == "无固定期限" %}无固定期限劳动合同{% else %}以完成一定工作任务为期限的劳动合同{% endif %}。
合同期限自{{ start_date }}起{% if contract_period != "无固定期限" %}至{{ end_date }}止{% endif %}。

{% if probation_period %}
第二条 试用期
试用期为{{ probation_period }}，试用期包含在劳动合同期限内。
{% endif %}

第三条 工作内容和工作地点
1. 乙方同意根据甲方工作需要，担任{{ position }}工作。
2. 工作地点：{{ work_location }}。

第四条 工作时间和休息休假
1. 甲方安排乙方执行标准工时制度，每日工作时间不超过8小时，每周工作时间不超过40小时。
2. 甲方保证乙方每周至少休息一日。

第五条 劳动报酬
1. 乙方月工资为人民币{{ salary }}元。
2. 甲方每月{{ pay_date | default("15日") }}前以货币形式支付乙方工资。

{% if benefits %}
第六条 福利待遇
{{ benefits }}
{% endif %}

第七条 劳动保护、劳动条件和职业危害防护
甲方根据生产岗位的需要，按照国家有关劳动安全、职业卫生的规定为乙方提供必要的劳动防护用品，建立安全生产制度。

第八条 合同的变更、解除和终止
1. 经甲乙双方协商一致，可以变更本合同。
2. 有下列情形之一的，本合同终止：
   (1) 合同期满的；
   (2) 乙方开始依法享受基本养老保险待遇的；
   (3) 乙方死亡，或者被人民法院宣告死亡或者宣告失踪的；
   (4) 甲方被依法宣告破产的；
   (5) 法律、行政法规规定的其他情形。

第九条 违约责任
1. 任何一方违反本合同约定，应承担相应的违约责任。
2. 因乙方违约给甲方造成损失的，乙方应承担赔偿责任。

第十条 争议解决
因履行本合同发生的争议，双方可以协商解决，也可以申请调解；协商、调解不成的，可以向劳动争议仲裁委员会申请仲裁。

第十一条 其他约定
{% if confidentiality_clause %}
保密条款：{{ confidentiality_clause }}
{% endif %}

本合同一式两份，甲乙双方各执一份。

甲方（盖章）：                    乙方（签字）：
法定代表人（签字）：              
日期：                           日期：
"""
            },
            "sales_contract": {
                "name": "买卖合同模板",
                "category": "sales",
                "description": "通用买卖合同模板，适用于商品买卖交易",
                "difficulty_level": "medium",
                "required_fields": [
                    "buyer_name", "buyer_address", "buyer_contact",
                    "seller_name", "seller_address", "seller_contact",
                    "product_name", "product_specification", "quantity",
                    "unit_price", "total_amount", "delivery_date", "payment_method"
                ],
                "optional_fields": [
                    "quality_standard", "packaging_requirement", "delivery_method",
                    "warranty_period", "penalty_clause"
                ],
                "template_content": """
买卖合同

买方（甲方）：{{ buyer_name }}
地址：{{ buyer_address }}
联系方式：{{ buyer_contact }}

卖方（乙方）：{{ seller_name }}
地址：{{ seller_address }}
联系方式：{{ seller_contact }}

根据《中华人民共和国民法典》等相关法律法规，甲乙双方在平等、自愿、公平、诚信的基础上，就买卖事宜达成如下协议：

第一条 标的物
1. 商品名称：{{ product_name }}
2. 规格型号：{{ product_specification }}
3. 数量：{{ quantity }}
4. 单价：{{ unit_price }}元
5. 总金额：{{ total_amount }}元

{% if quality_standard %}
第二条 质量标准
{{ quality_standard }}
{% endif %}

第三条 交付
1. 交付时间：{{ delivery_date }}
2. 交付地点：{{ delivery_location | default(buyer_address) }}
{% if delivery_method %}
3. 交付方式：{{ delivery_method }}
{% endif %}

{% if packaging_requirement %}
第四条 包装要求
{{ packaging_requirement }}
{% endif %}

第五条 价款支付
1. 支付方式：{{ payment_method }}
2. 支付时间：{% if payment_method == "预付款" %}签订合同后3日内{% elif payment_method == "货到付款" %}收货时{% else %}{{ payment_time | default("合同约定时间") }}{% endif %}

{% if warranty_period %}
第六条 质量保证
乙方对所售商品提供{{ warranty_period }}质量保证。
{% endif %}

第七条 违约责任
1. 甲方逾期付款的，应按逾期金额每日万分之五向乙方支付违约金。
2. 乙方逾期交货的，应按逾期交货部分货款每日万分之五向甲方支付违约金。
{% if penalty_clause %}
3. {{ penalty_clause }}
{% endif %}

第八条 争议解决
本合同履行过程中发生争议，双方应协商解决；协商不成的，可向合同签订地人民法院起诉。

第九条 其他约定
1. 本合同自双方签字盖章之日起生效。
2. 本合同一式两份，甲乙双方各执一份，具有同等法律效力。

甲方（签字盖章）：               乙方（签字盖章）：
日期：                          日期：
"""
            },
            "service_contract": {
                "name": "服务合同模板",
                "category": "service",
                "description": "通用服务合同模板，适用于各类服务提供",
                "difficulty_level": "medium",
                "required_fields": [
                    "client_name", "client_address", "client_contact",
                    "service_provider_name", "service_provider_address", "service_provider_contact",
                    "service_description", "service_period", "service_fee", "payment_method"
                ],
                "optional_fields": [
                    "service_standard", "completion_criteria", "intellectual_property",
                    "confidentiality_clause", "liability_limitation"
                ],
                "template_content": """
服务合同

委托方（甲方）：{{ client_name }}
地址：{{ client_address }}
联系方式：{{ client_contact }}

服务方（乙方）：{{ service_provider_name }}
地址：{{ service_provider_address }}
联系方式：{{ service_provider_contact }}

根据《中华人民共和国民法典》等相关法律法规，甲乙双方就服务事宜达成如下协议：

第一条 服务内容
{{ service_description }}

第二条 服务期限
服务期限：{{ service_period }}

{% if service_standard %}
第三条 服务标准
{{ service_standard }}
{% endif %}

{% if completion_criteria %}
第四条 完成标准
{{ completion_criteria }}
{% endif %}

第五条 服务费用
1. 服务费用总额：{{ service_fee }}元
2. 支付方式：{{ payment_method }}

第六条 双方权利义务
甲方权利义务：
1. 按约定支付服务费用
2. 提供必要的配合和协助
3. 及时验收服务成果

乙方权利义务：
1. 按约定提供服务
2. 保证服务质量
3. 按时完成服务

{% if intellectual_property %}
第七条 知识产权
{{ intellectual_property }}
{% endif %}

{% if confidentiality_clause %}
第八条 保密条款
{{ confidentiality_clause }}
{% endif %}

第九条 违约责任
1. 任何一方违反本合同约定，应承担违约责任。
2. 因违约造成对方损失的，应承担赔偿责任。
{% if liability_limitation %}
3. {{ liability_limitation }}
{% endif %}

第十条 争议解决
本合同履行过程中发生争议，双方应协商解决；协商不成的，可向合同签订地人民法院起诉。

第十一条 其他约定
1. 本合同自双方签字盖章之日起生效。
2. 本合同一式两份，甲乙双方各执一份，具有同等法律效力。

甲方（签字盖章）：               乙方（签字盖章）：
日期：                          日期：
"""
            }
        }
    
    async def get_template_list(self, category: str = None) -> List[Dict[str, Any]]:
        """获取模板列表"""
        
        templates = []
        for template_id, template_data in self.templates.items():
            if category and template_data["category"] != category:
                continue
            
            templates.append({
                "id": template_id,
                "name": template_data["name"],
                "category": template_data["category"],
                "description": template_data["description"],
                "difficulty_level": template_data["difficulty_level"],
                "usage_count": 0,  # 可以从数据库统计
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            })
        
        return templates
    
    async def get_template_detail(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板详情"""
        
        if template_id not in self.templates:
            return None
        
        template_data = self.templates[template_id]
        return {
            "id": template_id,
            "name": template_data["name"],
            "category": template_data["category"],
            "description": template_data["description"],
            "difficulty_level": template_data["difficulty_level"],
            "required_fields": template_data["required_fields"],
            "optional_fields": template_data["optional_fields"],
            "template_content": template_data["template_content"],
            "usage_count": 0,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    
    async def generate_contract(
        self,
        template_id: str,
        variables: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Dict[str, Any]:
        """生成合同"""
        
        logger.info(f"用户 {user_id} 生成合同: {template_id}")
        
        # 获取模板
        template_data = self.templates.get(template_id)
        if not template_data:
            raise NotFoundException("模板不存在")
        
        # 验证必填字段
        missing_fields = []
        for field in template_data["required_fields"]:
            if field not in variables or not variables[field]:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationException(f"缺少必填字段: {', '.join(missing_fields)}")
        
        try:
            # 使用Jinja2渲染模板
            template = self.jinja_env.from_string(template_data["template_content"])
            rendered_content = template.render(**variables)
            
            # 清理渲染结果
            rendered_content = self._clean_rendered_content(rendered_content)
            
            # 保存合同生成记录
            contract_record = await self._save_contract_record(
                user_id=user_id,
                template_id=template_id,
                template_name=template_data["name"],
                variables=variables,
                content=rendered_content
            )
            
            # 记录审计日志
            audit_logger = await get_audit_logger(self.db)
            await audit_logger.log_event(
                event_type=AuditEventType.DATA_CREATE,
                message=f"生成合同: {template_data['name']}",
                user_id=user_id,
                resource_type="contract",
                resource_id=str(contract_record.id),
                action="generate",
                details={
                    "template_id": template_id,
                    "template_name": template_data["name"]
                }
            )
            
            return {
                "id": str(contract_record.id),
                "template_id": template_id,
                "template_name": template_data["name"],
                "content": rendered_content,
                "variables": variables,
                "created_at": contract_record.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"合同生成失败: {e}")
            raise ValidationException(f"合同生成失败: {str(e)}")
    
    def _clean_rendered_content(self, content: str) -> str:
        """清理渲染后的内容"""
        
        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 移除行首行尾空格
        lines = [line.strip() for line in content.split('\n')]
        content = '\n'.join(lines)
        
        # 移除开头和结尾的空行
        content = content.strip()
        
        return content
    
    async def _save_contract_record(
        self,
        user_id: uuid.UUID,
        template_id: str,
        template_name: str,
        variables: Dict[str, Any],
        content: str
    ) -> ContractReview:
        """保存合同记录"""
        
        contract_record = ContractReview(
            user_id=user_id,
            contract_type=template_id,
            contract_content=content,
            review_status="generated",
            metadata={
                "template_id": template_id,
                "template_name": template_name,
                "variables": variables,
                "generated": True
            }
        )
        
        self.db.add(contract_record)
        await self.db.commit()
        await self.db.refresh(contract_record)
        
        return contract_record
    
    async def get_user_contracts(
        self,
        user_id: uuid.UUID,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """获取用户合同列表"""
        
        # 构建查询
        stmt = select(ContractReview).where(
            and_(
                ContractReview.user_id == user_id,
                ContractReview.metadata.op('->>')('generated') == 'true'
            )
        ).order_by(ContractReview.created_at.desc())
        
        # 获取总数
        count_stmt = select(func.count(ContractReview.id)).where(
            and_(
                ContractReview.user_id == user_id,
                ContractReview.metadata.op('->>')('generated') == 'true'
            )
        )
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()
        
        # 分页查询
        paginated_stmt = stmt.limit(page_size).offset((page - 1) * page_size)
        result = await self.db.execute(paginated_stmt)
        contracts = result.scalars().all()
        
        # 转换为响应格式
        contract_list = []
        for contract in contracts:
            metadata = contract.metadata or {}
            contract_list.append({
                "id": str(contract.id),
                "template_id": metadata.get("template_id"),
                "template_name": metadata.get("template_name"),
                "contract_type": contract.contract_type,
                "review_status": contract.review_status,
                "created_at": contract.created_at.isoformat(),
                "updated_at": contract.updated_at.isoformat() if contract.updated_at else None
            })
        
        return {
            "contracts": contract_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
    
    async def get_contract_detail(
        self,
        contract_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """获取合同详情"""
        
        stmt = select(ContractReview).where(
            and_(
                ContractReview.id == contract_id,
                ContractReview.user_id == user_id
            )
        )
        result = await self.db.execute(stmt)
        contract = result.scalar_one_or_none()
        
        if not contract:
            return None
        
        metadata = contract.metadata or {}
        return {
            "id": str(contract.id),
            "template_id": metadata.get("template_id"),
            "template_name": metadata.get("template_name"),
            "contract_type": contract.contract_type,
            "content": contract.contract_content,
            "variables": metadata.get("variables", {}),
            "review_status": contract.review_status,
            "created_at": contract.created_at.isoformat(),
            "updated_at": contract.updated_at.isoformat() if contract.updated_at else None
        }
    
    def get_field_descriptions(self) -> Dict[str, str]:
        """获取字段描述"""
        
        return {
            # 通用字段
            "employer_name": "用人单位名称",
            "employer_address": "用人单位地址",
            "employer_legal_representative": "法定代表人",
            "employee_name": "员工姓名",
            "employee_id": "身份证号",
            "employee_address": "员工地址",
            "position": "职位",
            "work_location": "工作地点",
            "contract_period": "合同期限类型",
            "salary": "月工资",
            "start_date": "开始日期",
            "end_date": "结束日期",
            "probation_period": "试用期",
            "benefits": "福利待遇",
            "confidentiality_clause": "保密条款",
            
            # 买卖合同字段
            "buyer_name": "买方名称",
            "buyer_address": "买方地址",
            "buyer_contact": "买方联系方式",
            "seller_name": "卖方名称",
            "seller_address": "卖方地址",
            "seller_contact": "卖方联系方式",
            "product_name": "商品名称",
            "product_specification": "规格型号",
            "quantity": "数量",
            "unit_price": "单价",
            "total_amount": "总金额",
            "delivery_date": "交付日期",
            "payment_method": "支付方式",
            "quality_standard": "质量标准",
            "packaging_requirement": "包装要求",
            "delivery_method": "交付方式",
            "warranty_period": "质保期",
            "penalty_clause": "违约条款",
            
            # 服务合同字段
            "client_name": "委托方名称",
            "client_address": "委托方地址",
            "client_contact": "委托方联系方式",
            "service_provider_name": "服务方名称",
            "service_provider_address": "服务方地址",
            "service_provider_contact": "服务方联系方式",
            "service_description": "服务内容描述",
            "service_period": "服务期限",
            "service_fee": "服务费用",
            "service_standard": "服务标准",
            "completion_criteria": "完成标准",
            "intellectual_property": "知识产权条款",
            "liability_limitation": "责任限制条款"
        }
