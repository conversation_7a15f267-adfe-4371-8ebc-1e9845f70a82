"""
文书合规性检查器
检查生成的法律文书是否符合法律规范和格式要求
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from datetime import datetime
import jieba

logger = logging.getLogger(__name__)


class ComplianceIssue:
    """合规性问题类"""
    
    def __init__(self, issue_id: str, issue_type: str, severity: str, description: str):
        """初始化合规性问题
        
        Args:
            issue_id: 问题唯一标识
            issue_type: 问题类型
            severity: 严重程度 (critical/high/medium/low)
            description: 问题描述
        """
        self.issue_id = issue_id
        self.issue_type = issue_type
        self.severity = severity
        self.description = description
        self.location = ""
        self.suggestion = ""
        self.legal_basis = ""
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "issue_id": self.issue_id,
            "issue_type": self.issue_type,
            "severity": self.severity,
            "description": self.description,
            "location": self.location,
            "suggestion": self.suggestion,
            "legal_basis": self.legal_basis
        }


class DocumentComplianceChecker:
    """文书合规性检查器"""
    
    def __init__(self):
        """初始化合规性检查器"""
        self.format_rules = self._load_format_rules()
        self.content_rules = self._load_content_rules()
        self.legal_rules = self._load_legal_rules()
        self.template_requirements = self._load_template_requirements()
        
        logger.info("文书合规性检查器初始化完成")
    
    def _load_format_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载格式规范"""
        return {
            "标题格式": [
                {
                    "rule": "标题居中",
                    "pattern": r"^.*合同.*$|^.*协议.*$|^.*起诉状.*$|^.*答辩状.*$",
                    "description": "文书标题应居中显示",
                    "severity": "medium"
                }
            ],
            "当事人信息": [
                {
                    "rule": "当事人格式",
                    "pattern": r"(甲方|乙方|原告|被告|申请人|被申请人)[:：]\s*[^\n]+",
                    "description": "当事人信息格式应规范",
                    "severity": "high"
                },
                {
                    "rule": "联系方式",
                    "pattern": r"(电话|手机|联系电话)[:：]\s*\d{3,4}-?\d{7,8}|\d{11}",
                    "description": "联系方式格式应正确",
                    "severity": "medium"
                }
            ],
            "日期格式": [
                {
                    "rule": "日期标准格式",
                    "pattern": r"\d{4}年\d{1,2}月\d{1,2}日",
                    "description": "日期应使用标准格式",
                    "severity": "medium"
                }
            ],
            "签名位置": [
                {
                    "rule": "签名格式",
                    "pattern": r"(甲方|乙方|申请人|被申请人).*[:：]\s*_+",
                    "description": "签名位置应预留空白",
                    "severity": "low"
                }
            ]
        }
    
    def _load_content_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载内容规范"""
        return {
            "必要条款": [
                {
                    "rule": "当事人条款",
                    "required_elements": ["甲方", "乙方"],
                    "description": "必须包含当事人信息",
                    "severity": "critical"
                },
                {
                    "rule": "标的条款",
                    "required_patterns": [r"服务内容|标的物|争议事项"],
                    "description": "必须明确标的或争议事项",
                    "severity": "high"
                }
            ],
            "条款完整性": [
                {
                    "rule": "权利义务",
                    "required_patterns": [r"权利|义务|责任"],
                    "description": "应明确双方权利义务",
                    "severity": "medium"
                },
                {
                    "rule": "争议解决",
                    "required_patterns": [r"争议|仲裁|诉讼|管辖"],
                    "description": "应包含争议解决条款",
                    "severity": "medium"
                }
            ]
        }
    
    def _load_legal_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载法律合规规范"""
        return {
            "违法内容": [
                {
                    "rule": "违法条款",
                    "forbidden_patterns": [
                        r"规避.*税收|避税|逃税",
                        r"洗钱|非法.*资金",
                        r"垄断.*市场|限制.*竞争"
                    ],
                    "description": "不得包含违法内容",
                    "severity": "critical"
                }
            ],
            "无效条款": [
                {
                    "rule": "排除法律适用",
                    "forbidden_patterns": [
                        r"不受.*法律约束|排除.*法律适用",
                        r"法律.*无效|无视.*法律"
                    ],
                    "description": "不得排除法律适用",
                    "severity": "high"
                }
            ],
            "不公平条款": [
                {
                    "rule": "过度免责",
                    "warning_patterns": [
                        r"不承担.*任何.*责任",
                        r"免除.*全部.*责任"
                    ],
                    "description": "免责条款可能过于宽泛",
                    "severity": "medium"
                }
            ]
        }
    
    def _load_template_requirements(self) -> Dict[str, Dict[str, Any]]:
        """加载模板特定要求"""
        return {
            "合同": {
                "required_sections": ["当事人信息", "标的条款", "价款条款", "履行期限"],
                "optional_sections": ["违约责任", "争议解决", "其他约定"],
                "format_requirements": ["标题", "签名栏", "日期"]
            },
            "起诉状": {
                "required_sections": ["当事人信息", "诉讼请求", "事实和理由"],
                "optional_sections": ["证据清单", "法律依据"],
                "format_requirements": ["法院名称", "具状人", "起诉日期"]
            },
            "答辩状": {
                "required_sections": ["答辩人信息", "答辩意见", "事实和理由"],
                "optional_sections": ["反驳证据", "法律依据"],
                "format_requirements": ["法院名称", "答辩人", "答辩日期"]
            }
        }
    
    def check_document_compliance(self, document_content: str, 
                                document_type: str = "通用") -> Dict[str, Any]:
        """检查文书合规性
        
        Args:
            document_content: 文书内容
            document_type: 文书类型
        
        Returns:
            合规性检查结果
        """
        try:
            issues = []
            
            # 1. 格式检查
            format_issues = self._check_format_compliance(document_content)
            issues.extend(format_issues)
            
            # 2. 内容检查
            content_issues = self._check_content_compliance(document_content)
            issues.extend(content_issues)
            
            # 3. 法律合规检查
            legal_issues = self._check_legal_compliance(document_content)
            issues.extend(legal_issues)
            
            # 4. 模板特定检查
            if document_type in self.template_requirements:
                template_issues = self._check_template_compliance(document_content, document_type)
                issues.extend(template_issues)
            
            # 5. 计算合规性得分
            compliance_score = self._calculate_compliance_score(issues)
            
            # 6. 生成合规性报告
            compliance_report = self._generate_compliance_report(issues, compliance_score)
            
            return {
                "compliance_score": compliance_score,
                "total_issues": len(issues),
                "issues": [issue.to_dict() for issue in issues],
                "compliance_report": compliance_report,
                "check_timestamp": datetime.now().isoformat(),
                "document_type": document_type,
                "recommendations": self._generate_recommendations(issues)
            }
            
        except Exception as e:
            logger.error(f"合规性检查失败: {e}")
            return {
                "error": str(e),
                "check_timestamp": datetime.now().isoformat()
            }
    
    def _check_format_compliance(self, content: str) -> List[ComplianceIssue]:
        """检查格式合规性"""
        issues = []
        issue_id = 1
        
        for category, rules in self.format_rules.items():
            for rule in rules:
                rule_name = rule["rule"]
                pattern = rule["pattern"]
                description = rule["description"]
                severity = rule["severity"]
                
                if not re.search(pattern, content, re.MULTILINE | re.IGNORECASE):
                    issue = ComplianceIssue(
                        issue_id=f"format_{issue_id}",
                        issue_type="格式问题",
                        severity=severity,
                        description=f"{category}: {description}"
                    )
                    issue.suggestion = f"请检查{category}是否符合规范"
                    issues.append(issue)
                    issue_id += 1
        
        return issues
    
    def _check_content_compliance(self, content: str) -> List[ComplianceIssue]:
        """检查内容合规性"""
        issues = []
        issue_id = 1000
        
        for category, rules in self.content_rules.items():
            for rule in rules:
                rule_name = rule["rule"]
                description = rule["description"]
                severity = rule["severity"]
                
                # 检查必要元素
                if "required_elements" in rule:
                    missing_elements = []
                    for element in rule["required_elements"]:
                        if element not in content:
                            missing_elements.append(element)
                    
                    if missing_elements:
                        issue = ComplianceIssue(
                            issue_id=f"content_{issue_id}",
                            issue_type="内容缺失",
                            severity=severity,
                            description=f"{description}: 缺少{', '.join(missing_elements)}"
                        )
                        issue.suggestion = f"请添加{', '.join(missing_elements)}相关内容"
                        issues.append(issue)
                        issue_id += 1
                
                # 检查必要模式
                if "required_patterns" in rule:
                    found_patterns = []
                    for pattern in rule["required_patterns"]:
                        if re.search(pattern, content, re.IGNORECASE):
                            found_patterns.append(pattern)
                    
                    if not found_patterns:
                        issue = ComplianceIssue(
                            issue_id=f"content_{issue_id}",
                            issue_type="内容不完整",
                            severity=severity,
                            description=description
                        )
                        issue.suggestion = f"请补充{category}相关内容"
                        issues.append(issue)
                        issue_id += 1
        
        return issues
    
    def _check_legal_compliance(self, content: str) -> List[ComplianceIssue]:
        """检查法律合规性"""
        issues = []
        issue_id = 2000
        
        for category, rules in self.legal_rules.items():
            for rule in rules:
                rule_name = rule["rule"]
                description = rule["description"]
                severity = rule["severity"]
                
                # 检查禁止内容
                if "forbidden_patterns" in rule:
                    for pattern in rule["forbidden_patterns"]:
                        matches = list(re.finditer(pattern, content, re.IGNORECASE))
                        for match in matches:
                            issue = ComplianceIssue(
                                issue_id=f"legal_{issue_id}",
                                issue_type="法律合规问题",
                                severity=severity,
                                description=f"{description}: {match.group(0)}"
                            )
                            issue.location = f"位置: {match.start()}-{match.end()}"
                            issue.suggestion = "建议删除或修改相关内容"
                            issue.legal_basis = "相关法律法规"
                            issues.append(issue)
                            issue_id += 1
                
                # 检查警告内容
                if "warning_patterns" in rule:
                    for pattern in rule["warning_patterns"]:
                        matches = list(re.finditer(pattern, content, re.IGNORECASE))
                        for match in matches:
                            issue = ComplianceIssue(
                                issue_id=f"legal_{issue_id}",
                                issue_type="法律风险提醒",
                                severity=severity,
                                description=f"{description}: {match.group(0)}"
                            )
                            issue.location = f"位置: {match.start()}-{match.end()}"
                            issue.suggestion = "建议审查相关条款的合理性"
                            issues.append(issue)
                            issue_id += 1
        
        return issues
    
    def _check_template_compliance(self, content: str, document_type: str) -> List[ComplianceIssue]:
        """检查模板特定合规性"""
        issues = []
        issue_id = 3000
        
        requirements = self.template_requirements.get(document_type, {})
        
        # 检查必需章节
        required_sections = requirements.get("required_sections", [])
        for section in required_sections:
            if section not in content:
                issue = ComplianceIssue(
                    issue_id=f"template_{issue_id}",
                    issue_type="模板要求",
                    severity="high",
                    description=f"{document_type}缺少必需章节: {section}"
                )
                issue.suggestion = f"请添加{section}相关内容"
                issues.append(issue)
                issue_id += 1
        
        # 检查格式要求
        format_requirements = requirements.get("format_requirements", [])
        for format_req in format_requirements:
            # 简化的格式检查
            if format_req == "标题" and not re.search(r"^.*(合同|协议|起诉状|答辩状)", content, re.MULTILINE):
                issue = ComplianceIssue(
                    issue_id=f"template_{issue_id}",
                    issue_type="格式要求",
                    severity="medium",
                    description=f"{document_type}标题格式不规范"
                )
                issue.suggestion = "请确保文书标题清晰明确"
                issues.append(issue)
                issue_id += 1
        
        return issues
    
    def _calculate_compliance_score(self, issues: List[ComplianceIssue]) -> float:
        """计算合规性得分"""
        if not issues:
            return 100.0
        
        # 根据问题严重程度扣分
        deduction_map = {
            "critical": 20,
            "high": 10,
            "medium": 5,
            "low": 2
        }
        
        total_deduction = sum(deduction_map.get(issue.severity, 2) for issue in issues)
        score = max(0.0, 100.0 - total_deduction)
        
        return round(score, 2)
    
    def _generate_compliance_report(self, issues: List[ComplianceIssue], score: float) -> str:
        """生成合规性报告"""
        if not issues:
            return "文书合规性检查通过，未发现问题。"
        
        report_lines = [
            "文书合规性检查报告",
            "=" * 30,
            f"合规性得分: {score}/100",
            f"发现问题: {len(issues)}个",
            ""
        ]
        
        # 按严重程度分组
        severity_groups = defaultdict(list)
        for issue in issues:
            severity_groups[issue.severity].append(issue)
        
        severity_order = ["critical", "high", "medium", "low"]
        severity_names = {
            "critical": "严重问题",
            "high": "重要问题", 
            "medium": "一般问题",
            "low": "轻微问题"
        }
        
        for severity in severity_order:
            if severity in severity_groups:
                report_lines.append(f"{severity_names[severity]} ({len(severity_groups[severity])}个):")
                for issue in severity_groups[severity][:3]:  # 只显示前3个
                    report_lines.append(f"  - {issue.description}")
                    if issue.suggestion:
                        report_lines.append(f"    建议: {issue.suggestion}")
                report_lines.append("")
        
        if score >= 90:
            report_lines.append("总体评价: 合规性良好")
        elif score >= 70:
            report_lines.append("总体评价: 基本合规，建议优化")
        elif score >= 50:
            report_lines.append("总体评价: 存在合规风险，需要修改")
        else:
            report_lines.append("总体评价: 合规性较差，建议重新起草")
        
        return "\n".join(report_lines)
    
    def _generate_recommendations(self, issues: List[ComplianceIssue]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 按问题类型分组建议
        critical_issues = [i for i in issues if i.severity == "critical"]
        high_issues = [i for i in issues if i.severity == "high"]
        
        if critical_issues:
            recommendations.append("立即处理严重合规问题，避免法律风险")
        
        if high_issues:
            recommendations.append("尽快解决重要问题，完善文书内容")
        
        # 通用建议
        if len(issues) > 5:
            recommendations.append("问题较多，建议系统性审查和修改")
        
        if not recommendations:
            recommendations.append("文书质量良好，可以使用")
        
        return recommendations


# 全局文书合规性检查器实例
document_compliance_checker = DocumentComplianceChecker()


def check_document_compliance(document_content: str, document_type: str = "通用") -> Dict[str, Any]:
    """检查文书合规性的便捷函数"""
    return document_compliance_checker.check_document_compliance(document_content, document_type)
