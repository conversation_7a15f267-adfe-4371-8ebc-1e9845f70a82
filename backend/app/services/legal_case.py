"""
法律案例管理服务
"""

import uuid
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, date
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text
from sqlalchemy.orm import selectinload
import logging

from app.models.legal_case import LegalCase, CaseSimilarity, LegalArticle, CaseType, CaseStatus
from app.core.exceptions import NotFoundException, ValidationException, ConflictException
from app.core.logging import business_logger

logger = logging.getLogger(__name__)


class LegalCaseService:
    """法律案例管理服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_case(
        self,
        case_data: Dict[str, Any],
        created_by: Optional[uuid.UUID] = None
    ) -> LegalCase:
        """创建法律案例"""
        
        logger.info(f"创建法律案例: {case_data.get('title', '')}")
        
        # 检查案件编号是否已存在
        if case_data.get('case_number'):
            existing_case = await self.get_case_by_number(case_data['case_number'])
            if existing_case:
                raise ConflictException(f"案件编号 {case_data['case_number']} 已存在")
        
        # 验证必填字段
        required_fields = ['case_number', 'title', 'court_name', 'case_type']
        for field in required_fields:
            if not case_data.get(field):
                raise ValidationException(f"缺少必填字段: {field}")
        
        # 验证案例类型
        if case_data['case_type'] not in [ct.value for ct in CaseType]:
            raise ValidationException(f"无效的案例类型: {case_data['case_type']}")
        
        # 创建案例对象
        case = LegalCase(
            case_number=case_data['case_number'],
            title=case_data['title'],
            court_name=case_data['court_name'],
            case_type=CaseType(case_data['case_type']),
            judgment_date=case_data.get('judgment_date'),
            trial_procedure=case_data.get('trial_procedure'),
            parties=case_data.get('parties', []),
            case_summary=case_data.get('case_summary'),
            case_facts=case_data.get('case_facts'),
            dispute_focus=case_data.get('dispute_focus', []),
            court_opinion=case_data.get('court_opinion'),
            judgment_result=case_data.get('judgment_result'),
            related_articles=case_data.get('related_articles', []),
            legal_basis=case_data.get('legal_basis'),
            keywords=case_data.get('keywords', []),
            tags=case_data.get('tags', []),
            precedent_value=case_data.get('precedent_value'),
            citation_count=case_data.get('citation_count', '0'),
            is_public=case_data.get('is_public', True),
            source_url=case_data.get('source_url'),
            extra_data=case_data.get('extra_data', {}),
            created_by=created_by
        )
        
        self.db.add(case)
        await self.db.commit()
        await self.db.refresh(case)
        
        # 记录业务日志
        if created_by:
            business_logger.log_user_action(
                user_id=str(created_by),
                action="create_case",
                resource_type="legal_case",
                resource_id=str(case.id),
                details={
                    "case_number": case.case_number,
                    "title": case.title,
                    "case_type": case.case_type.value
                }
            )
        
        logger.info(f"法律案例创建成功: {case.case_number}")
        return case
    
    async def get_case_by_id(self, case_id: uuid.UUID) -> Optional[LegalCase]:
        """根据ID获取案例"""
        
        stmt = select(LegalCase).where(
            and_(
                LegalCase.id == case_id,
                LegalCase.deleted_at.is_(None)
            )
        ).options(selectinload(LegalCase.creator))
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_case_by_number(self, case_number: str) -> Optional[LegalCase]:
        """根据案件编号获取案例"""
        
        stmt = select(LegalCase).where(
            and_(
                LegalCase.case_number == case_number,
                LegalCase.deleted_at.is_(None)
            )
        )
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def search_cases(
        self,
        keyword: Optional[str] = None,
        case_type: Optional[str] = None,
        court_name: Optional[str] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        page: int = 1,
        page_size: int = 20,
        user_id: Optional[uuid.UUID] = None
    ) -> Tuple[List[LegalCase], int]:
        """搜索法律案例"""
        
        logger.info(f"搜索法律案例: keyword={keyword}, case_type={case_type}")
        
        # 构建基础查询
        stmt = select(LegalCase).where(
            LegalCase.deleted_at.is_(None)
        )
        
        # 添加搜索条件
        if keyword:
            # 使用PostgreSQL全文搜索
            stmt = stmt.where(
                or_(
                    LegalCase.title.contains(keyword),
                    LegalCase.case_summary.contains(keyword),
                    LegalCase.case_facts.contains(keyword),
                    text("to_tsvector('chinese', title) @@ plainto_tsquery('chinese', :keyword)").bindparam(keyword=keyword),
                    text("to_tsvector('chinese', case_summary) @@ plainto_tsquery('chinese', :keyword)").bindparam(keyword=keyword)
                )
            )
        
        if case_type:
            stmt = stmt.where(LegalCase.case_type == CaseType(case_type))
        
        if court_name:
            stmt = stmt.where(LegalCase.court_name.contains(court_name))
        
        if date_from:
            stmt = stmt.where(LegalCase.judgment_date >= date_from)
        
        if date_to:
            stmt = stmt.where(LegalCase.judgment_date <= date_to)
        
        # 只显示公开案例（除非是创建者）
        if user_id:
            stmt = stmt.where(
                or_(
                    LegalCase.is_public == True,
                    LegalCase.created_by == user_id
                )
            )
        else:
            stmt = stmt.where(LegalCase.is_public == True)
        
        # 获取总数
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()
        
        # 分页查询
        stmt = stmt.order_by(LegalCase.judgment_date.desc(), LegalCase.created_at.desc())
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        
        result = await self.db.execute(stmt)
        cases = result.scalars().all()
        
        # 记录搜索日志
        if user_id:
            business_logger.log_user_action(
                user_id=str(user_id),
                action="search_cases",
                details={
                    "keyword": keyword,
                    "case_type": case_type,
                    "results_count": len(cases),
                    "total_hits": total
                }
            )
        
        logger.info(f"案例搜索完成: 找到{total}个结果")
        return list(cases), total
    
    async def update_case(
        self,
        case_id: uuid.UUID,
        case_data: Dict[str, Any],
        user_id: Optional[uuid.UUID] = None
    ) -> LegalCase:
        """更新法律案例"""
        
        case = await self.get_case_by_id(case_id)
        if not case:
            raise NotFoundException("案例不存在")
        
        # 检查权限（只有创建者可以修改）
        if user_id and case.created_by != user_id:
            raise ValidationException("无权限修改此案例")
        
        # 更新字段
        for field, value in case_data.items():
            if hasattr(case, field) and field not in ['id', 'created_at', 'created_by']:
                setattr(case, field, value)
        
        case.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(case)
        
        # 记录业务日志
        if user_id:
            business_logger.log_user_action(
                user_id=str(user_id),
                action="update_case",
                resource_type="legal_case",
                resource_id=str(case.id),
                details={"updated_fields": list(case_data.keys())}
            )
        
        logger.info(f"法律案例更新成功: {case.case_number}")
        return case
    
    async def soft_delete_case(
        self,
        case_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None
    ) -> bool:
        """软删除法律案例"""
        
        case = await self.get_case_by_id(case_id)
        if not case:
            raise NotFoundException("案例不存在")
        
        # 检查权限（只有创建者可以删除）
        if user_id and case.created_by != user_id:
            raise ValidationException("无权限删除此案例")
        
        case.deleted_at = datetime.utcnow()
        case.status = CaseStatus.DELETED
        
        await self.db.commit()
        
        # 记录业务日志
        if user_id:
            business_logger.log_user_action(
                user_id=str(user_id),
                action="delete_case",
                resource_type="legal_case",
                resource_id=str(case.id),
                details={"case_number": case.case_number}
            )
        
        logger.info(f"法律案例删除成功: {case.case_number}")
        return True
    
    async def get_case_statistics(self) -> Dict[str, Any]:
        """获取案例统计信息"""
        
        # 总案例数
        total_stmt = select(func.count(LegalCase.id)).where(
            and_(
                LegalCase.deleted_at.is_(None),
                LegalCase.is_public == True
            )
        )
        total_result = await self.db.execute(total_stmt)
        total_cases = total_result.scalar()
        
        # 按类型统计
        type_stmt = select(
            LegalCase.case_type,
            func.count(LegalCase.id).label('count')
        ).where(
            and_(
                LegalCase.deleted_at.is_(None),
                LegalCase.is_public == True
            )
        ).group_by(LegalCase.case_type)
        
        type_result = await self.db.execute(type_stmt)
        case_types = {row.case_type.value: row.count for row in type_result}
        
        # 按年份统计
        year_stmt = select(
            func.extract('year', LegalCase.judgment_date).label('year'),
            func.count(LegalCase.id).label('count')
        ).where(
            and_(
                LegalCase.deleted_at.is_(None),
                LegalCase.is_public == True,
                LegalCase.judgment_date.is_not(None)
            )
        ).group_by(func.extract('year', LegalCase.judgment_date)).order_by('year')
        
        year_result = await self.db.execute(year_stmt)
        case_years = {int(row.year): row.count for row in year_result}
        
        return {
            "total_cases": total_cases,
            "case_types": case_types,
            "case_years": case_years
        }
