"""
法律领域语义理解模块
基于TF-IDF和词向量的法律文本语义分析
"""

import logging
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict, Counter
import math
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import TruncatedSVD
from sklearn.cluster import KMeans

logger = logging.getLogger(__name__)


class LegalSemanticAnalyzer:
    """法律语义分析器"""
    
    def __init__(self):
        """初始化语义分析器"""
        self.tfidf_vectorizer = None
        self.concept_vectors = {}
        self.legal_concepts = self._load_legal_concepts()
        self.semantic_clusters = {}
        self.concept_similarity_cache = {}
        
        logger.info("法律语义分析器初始化完成")
    
    def _load_legal_concepts(self) -> Dict[str, List[str]]:
        """加载法律概念词汇表"""
        return {
            "合同法": [
                "合同", "协议", "约定", "条款", "违约", "履行", "解除", "终止",
                "要约", "承诺", "违约金", "定金", "格式条款", "免责条款",
                "合同效力", "合同解释", "合同变更", "合同转让"
            ],
            "劳动法": [
                "劳动合同", "用人单位", "劳动者", "工资", "加班费", "社会保险",
                "工伤", "职业病", "劳动争议", "仲裁", "辞职", "辞退",
                "试用期", "服务期", "竞业限制", "保密协议"
            ],
            "民法": [
                "民事权利", "民事义务", "民事责任", "人格权", "财产权",
                "物权", "债权", "侵权", "损害赔偿", "精神损害",
                "过错责任", "无过错责任", "公平责任"
            ],
            "刑法": [
                "犯罪", "刑罚", "犯罪构成", "故意", "过失", "正当防卫",
                "紧急避险", "共同犯罪", "累犯", "自首", "立功",
                "缓刑", "假释", "数罪并罚"
            ],
            "行政法": [
                "行政行为", "行政许可", "行政处罚", "行政强制", "行政复议",
                "行政诉讼", "行政赔偿", "政府信息公开", "行政程序"
            ],
            "诉讼程序": [
                "起诉", "应诉", "举证", "质证", "认证", "调解", "和解",
                "判决", "裁定", "执行", "上诉", "再审", "申诉"
            ]
        }
    
    def build_semantic_model(self, texts: List[str]) -> None:
        """构建语义模型
        
        Args:
            texts: 训练文本列表
        """
        try:
            # 使用TF-IDF构建基础向量空间
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=5000,
                ngram_range=(1, 2),
                min_df=2,
                max_df=0.8,
                stop_words=self._get_stop_words()
            )
            
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(texts)
            
            # 使用SVD进行降维，提取语义特征
            svd = TruncatedSVD(n_components=100, random_state=42)
            semantic_vectors = svd.fit_transform(tfidf_matrix)
            
            # 构建法律概念向量
            self._build_concept_vectors(texts, semantic_vectors)
            
            # 进行语义聚类
            self._perform_semantic_clustering(semantic_vectors)
            
            logger.info(f"语义模型构建完成，处理了{len(texts)}个文本")
            
        except Exception as e:
            logger.error(f"构建语义模型失败: {e}")
            raise
    
    def _get_stop_words(self) -> List[str]:
        """获取停用词列表"""
        return [
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人",
            "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去",
            "你", "会", "着", "没有", "看", "好", "自己", "这", "那", "个",
            "们", "这个", "来", "他", "时候", "可以", "还", "把", "年", "大"
        ]
    
    def _build_concept_vectors(self, texts: List[str], semantic_vectors: np.ndarray) -> None:
        """构建法律概念向量"""
        for concept_category, concept_words in self.legal_concepts.items():
            concept_vector = np.zeros(semantic_vectors.shape[1])
            concept_count = 0
            
            for i, text in enumerate(texts):
                # 检查文本中是否包含该概念的词汇
                concept_score = sum(1 for word in concept_words if word in text)
                if concept_score > 0:
                    concept_vector += semantic_vectors[i] * concept_score
                    concept_count += concept_score
            
            if concept_count > 0:
                self.concept_vectors[concept_category] = concept_vector / concept_count
            
            logger.debug(f"构建概念向量: {concept_category}")
    
    def _perform_semantic_clustering(self, semantic_vectors: np.ndarray) -> None:
        """执行语义聚类"""
        try:
            # 使用K-means进行聚类
            n_clusters = min(10, len(semantic_vectors) // 5)  # 动态确定聚类数
            if n_clusters < 2:
                n_clusters = 2
            
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(semantic_vectors)
            
            # 存储聚类结果
            for i, label in enumerate(cluster_labels):
                if label not in self.semantic_clusters:
                    self.semantic_clusters[label] = []
                self.semantic_clusters[label].append(i)
            
            logger.info(f"语义聚类完成，生成{n_clusters}个聚类")
            
        except Exception as e:
            logger.warning(f"语义聚类失败: {e}")
    
    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """计算语义相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
        
        Returns:
            语义相似度分数 (0-1)
        """
        try:
            if not self.tfidf_vectorizer:
                logger.warning("语义模型未构建，使用基础相似度计算")
                return self._basic_similarity(text1, text2)
            
            # 使用TF-IDF向量计算相似度
            vectors = self.tfidf_vectorizer.transform([text1, text2])
            similarity = cosine_similarity(vectors[0:1], vectors[1:2])[0, 0]
            
            # 结合概念相似度
            concept_sim = self._calculate_concept_similarity(text1, text2)
            
            # 加权平均
            final_similarity = 0.7 * similarity + 0.3 * concept_sim
            
            return float(final_similarity)
            
        except Exception as e:
            logger.error(f"计算语义相似度失败: {e}")
            return self._basic_similarity(text1, text2)
    
    def _basic_similarity(self, text1: str, text2: str) -> float:
        """基础相似度计算（Jaccard相似度）"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_concept_similarity(self, text1: str, text2: str) -> float:
        """计算概念相似度"""
        concepts1 = self.extract_legal_concepts(text1)
        concepts2 = self.extract_legal_concepts(text2)
        
        if not concepts1 and not concepts2:
            return 0.0
        
        # 计算概念重叠度
        common_concepts = set(concepts1.keys()).intersection(set(concepts2.keys()))
        total_concepts = set(concepts1.keys()).union(set(concepts2.keys()))
        
        if not total_concepts:
            return 0.0
        
        return len(common_concepts) / len(total_concepts)
    
    def extract_legal_concepts(self, text: str) -> Dict[str, float]:
        """提取法律概念
        
        Args:
            text: 输入文本
        
        Returns:
            概念及其权重字典
        """
        concepts = {}
        
        for concept_category, concept_words in self.legal_concepts.items():
            concept_score = 0
            for word in concept_words:
                if word in text:
                    # 根据词汇长度和重要性计算权重
                    weight = len(word) / 10.0 + 0.5
                    concept_score += weight
            
            if concept_score > 0:
                concepts[concept_category] = concept_score
        
        # 归一化
        if concepts:
            max_score = max(concepts.values())
            concepts = {k: v / max_score for k, v in concepts.items()}
        
        return concepts
    
    def find_semantic_neighbors(self, text: str, candidate_texts: List[str], top_k: int = 5) -> List[Tuple[int, float]]:
        """查找语义相似的文本
        
        Args:
            text: 查询文本
            candidate_texts: 候选文本列表
            top_k: 返回最相似的文本数量
        
        Returns:
            相似文本的索引和相似度分数列表
        """
        similarities = []
        
        for i, candidate in enumerate(candidate_texts):
            similarity = self.calculate_semantic_similarity(text, candidate)
            similarities.append((i, similarity))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    def analyze_semantic_structure(self, text: str) -> Dict[str, Any]:
        """分析文本的语义结构
        
        Args:
            text: 输入文本
        
        Returns:
            语义结构分析结果
        """
        result = {
            "legal_concepts": self.extract_legal_concepts(text),
            "semantic_complexity": self._calculate_semantic_complexity(text),
            "concept_distribution": self._analyze_concept_distribution(text),
            "semantic_coherence": self._calculate_semantic_coherence(text)
        }
        
        return result
    
    def _calculate_semantic_complexity(self, text: str) -> float:
        """计算语义复杂度"""
        # 基于概念数量、句子长度、词汇多样性等因素
        concepts = self.extract_legal_concepts(text)
        concept_count = len(concepts)
        
        sentences = re.split(r'[。！？；]', text)
        avg_sentence_length = sum(len(s) for s in sentences) / len(sentences) if sentences else 0
        
        words = text.split()
        unique_words = set(words)
        lexical_diversity = len(unique_words) / len(words) if words else 0
        
        # 综合计算复杂度
        complexity = (
            concept_count * 0.4 +
            min(avg_sentence_length / 50, 1.0) * 0.3 +
            lexical_diversity * 0.3
        )
        
        return min(complexity, 1.0)
    
    def _analyze_concept_distribution(self, text: str) -> Dict[str, float]:
        """分析概念分布"""
        concepts = self.extract_legal_concepts(text)
        
        if not concepts:
            return {}
        
        total_score = sum(concepts.values())
        distribution = {k: v / total_score for k, v in concepts.items()}
        
        return distribution
    
    def _calculate_semantic_coherence(self, text: str) -> float:
        """计算语义连贯性"""
        sentences = re.split(r'[。！？；]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if len(sentences) < 2:
            return 1.0
        
        # 计算相邻句子的语义相似度
        coherence_scores = []
        for i in range(len(sentences) - 1):
            similarity = self.calculate_semantic_similarity(sentences[i], sentences[i + 1])
            coherence_scores.append(similarity)
        
        return sum(coherence_scores) / len(coherence_scores) if coherence_scores else 0.0
    
    def get_concept_explanation(self, concept: str) -> str:
        """获取法律概念解释
        
        Args:
            concept: 法律概念名称
        
        Returns:
            概念解释
        """
        explanations = {
            "合同法": "调整平等主体的自然人、法人、其他组织之间设立、变更、终止民事权利义务关系的协议的法律规范",
            "劳动法": "调整劳动关系以及与劳动关系密切联系的社会关系的法律规范",
            "民法": "调整平等主体的自然人、法人和非法人组织之间的人身关系和财产关系的法律规范",
            "刑法": "规定犯罪、刑事责任和刑罚的法律规范",
            "行政法": "调整行政关系的法律规范的总称",
            "诉讼程序": "当事人进行诉讼活动所应遵循的步骤和方式"
        }
        
        return explanations.get(concept, f"关于{concept}的法律概念")


# 全局语义分析器实例
semantic_analyzer = LegalSemanticAnalyzer()


def analyze_semantic_similarity(text1: str, text2: str) -> float:
    """分析语义相似度的便捷函数"""
    return semantic_analyzer.calculate_semantic_similarity(text1, text2)


def extract_legal_concepts(text: str) -> Dict[str, float]:
    """提取法律概念的便捷函数"""
    return semantic_analyzer.extract_legal_concepts(text)


def analyze_semantic_structure(text: str) -> Dict[str, Any]:
    """分析语义结构的便捷函数"""
    return semantic_analyzer.analyze_semantic_structure(text)
