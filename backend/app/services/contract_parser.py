"""
合同文本解析引擎
支持多种文档格式的合同解析，提取合同条款和关键信息
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import json
from datetime import datetime
import jieba

logger = logging.getLogger(__name__)


class ContractClause:
    """合同条款类"""
    
    def __init__(self, clause_id: str, title: str, content: str, clause_type: str = "general"):
        """初始化合同条款
        
        Args:
            clause_id: 条款唯一标识
            title: 条款标题
            content: 条款内容
            clause_type: 条款类型
        """
        self.clause_id = clause_id
        self.title = title
        self.content = content
        self.clause_type = clause_type
        self.key_terms = []  # 关键术语
        self.entities = {}   # 提取的实体
        self.risk_level = "low"  # 风险等级
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "clause_id": self.clause_id,
            "title": self.title,
            "content": self.content,
            "clause_type": self.clause_type,
            "key_terms": self.key_terms,
            "entities": self.entities,
            "risk_level": self.risk_level
        }


class ContractParser:
    """合同解析器"""
    
    def __init__(self):
        """初始化合同解析器"""
        self.clause_patterns = self._load_clause_patterns()
        self.entity_patterns = self._load_entity_patterns()
        self.contract_types = self._load_contract_types()
        
        logger.info("合同解析器初始化完成")
    
    def _load_clause_patterns(self) -> Dict[str, List[str]]:
        """加载条款识别模式"""
        return {
            "标题条款": [
                r"第[一二三四五六七八九十\d]+条",
                r"\d+\.\d+",
                r"第\d+章",
                r"[一二三四五六七八九十]+、",
                r"\(\d+\)"
            ],
            "定义条款": [
                r"本合同所称.*是指",
                r".*定义如下",
                r"术语解释",
                r"定义与解释"
            ],
            "权利义务": [
                r"甲方.*权利",
                r"乙方.*义务",
                r"双方.*责任",
                r"当事人.*应当"
            ],
            "违约责任": [
                r"违约责任",
                r"违约金",
                r"损害赔偿",
                r"承担责任"
            ],
            "争议解决": [
                r"争议解决",
                r"仲裁",
                r"诉讼",
                r"管辖法院"
            ],
            "生效条件": [
                r"生效条件",
                r"合同生效",
                r"签署生效",
                r"履行期限"
            ]
        }
    
    def _load_entity_patterns(self) -> Dict[str, str]:
        """加载实体识别模式"""
        return {
            "合同金额": r"(?:金额|价款|费用).*?(\d+(?:\.\d+)?(?:万|千|百)?元)",
            "合同期限": r"(?:期限|有效期).*?(\d+(?:年|月|日|天))",
            "违约金": r"违约金.*?(\d+(?:\.\d+)?(?:万|千|百)?元|百分之\d+)",
            "利率": r"(?:利率|年利率).*?(\d+(?:\.\d+)?%)",
            "保证金": r"(?:保证金|押金).*?(\d+(?:\.\d+)?(?:万|千|百)?元)",
            "交付时间": r"(?:交付|交货|完成).*?(\d{4}年\d{1,2}月\d{1,2}日|\d+(?:个)?(?:工作日|日|月))",
            "付款方式": r"付款方式.*?([^。！？]*)",
            "管辖法院": r"(?:管辖|诉讼).*?([^，。！？]*法院)",
            "仲裁机构": r"仲裁.*?([^，。！？]*仲裁[^，。！？]*)",
            "当事人": r"(甲方|乙方|丙方)[:：]\s*([^，。！？\n]*)"
        }
    
    def _load_contract_types(self) -> Dict[str, List[str]]:
        """加载合同类型识别关键词"""
        return {
            "买卖合同": ["买卖", "购销", "采购", "销售", "商品"],
            "租赁合同": ["租赁", "出租", "承租", "租金", "房屋"],
            "服务合同": ["服务", "委托", "咨询", "技术", "劳务"],
            "借款合同": ["借款", "贷款", "融资", "利息", "还款"],
            "劳动合同": ["劳动", "雇佣", "工作", "薪资", "员工"],
            "建设工程合同": ["建设", "工程", "施工", "承包", "建筑"],
            "运输合同": ["运输", "货运", "物流", "承运", "托运"],
            "保险合同": ["保险", "投保", "理赔", "保费", "受益人"],
            "合作协议": ["合作", "协议", "合伙", "联营", "共同"],
            "保密协议": ["保密", "机密", "商业秘密", "信息", "泄露"]
        }
    
    def parse_contract_text(self, text: str) -> Dict[str, Any]:
        """解析合同文本
        
        Args:
            text: 合同文本内容
        
        Returns:
            解析结果
        """
        try:
            # 1. 基本信息提取
            basic_info = self._extract_basic_info(text)
            
            # 2. 合同类型识别
            contract_type = self._identify_contract_type(text)
            
            # 3. 条款分割和分类
            clauses = self._extract_clauses(text)
            
            # 4. 实体提取
            entities = self._extract_entities(text)
            
            # 5. 关键术语提取
            key_terms = self._extract_key_terms(text)
            
            # 6. 结构化分析
            structure_analysis = self._analyze_structure(clauses)
            
            return {
                "basic_info": basic_info,
                "contract_type": contract_type,
                "clauses": [clause.to_dict() for clause in clauses],
                "entities": entities,
                "key_terms": key_terms,
                "structure_analysis": structure_analysis,
                "parsing_timestamp": datetime.now().isoformat(),
                "text_length": len(text),
                "clause_count": len(clauses)
            }
            
        except Exception as e:
            logger.error(f"合同解析失败: {e}")
            return {
                "error": str(e),
                "parsing_timestamp": datetime.now().isoformat()
            }
    
    def _extract_basic_info(self, text: str) -> Dict[str, Any]:
        """提取合同基本信息"""
        basic_info = {
            "title": "",
            "parties": [],
            "date": "",
            "place": ""
        }
        
        # 提取合同标题（通常在文档开头）
        title_patterns = [
            r"^([^。！？\n]*合同[^。！？\n]*)",
            r"^([^。！？\n]*协议[^。！？\n]*)",
            r"^([^。！？\n]*书[^。！？\n]*)"
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, text.strip(), re.MULTILINE)
            if match:
                basic_info["title"] = match.group(1).strip()
                break
        
        # 提取当事人信息
        party_pattern = r"(甲方|乙方|丙方)[:：]\s*([^，。！？\n]*)"
        parties = re.findall(party_pattern, text)
        basic_info["parties"] = [{"role": role, "name": name.strip()} for role, name in parties]
        
        # 提取签署日期
        date_patterns = [
            r"(\d{4}年\d{1,2}月\d{1,2}日)",
            r"(\d{4}-\d{1,2}-\d{1,2})",
            r"(\d{4}/\d{1,2}/\d{1,2})"
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                basic_info["date"] = match.group(1)
                break
        
        # 提取签署地点
        place_pattern = r"(?:签署地|签订地|履行地)[:：]\s*([^，。！？\n]*)"
        place_match = re.search(place_pattern, text)
        if place_match:
            basic_info["place"] = place_match.group(1).strip()
        
        return basic_info
    
    def _identify_contract_type(self, text: str) -> Dict[str, Any]:
        """识别合同类型"""
        type_scores = defaultdict(int)
        
        # 基于关键词计算各类型得分
        for contract_type, keywords in self.contract_types.items():
            for keyword in keywords:
                count = text.count(keyword)
                type_scores[contract_type] += count
        
        # 确定最可能的合同类型
        if type_scores:
            best_type = max(type_scores, key=type_scores.get)
            confidence = type_scores[best_type] / sum(type_scores.values())
        else:
            best_type = "通用合同"
            confidence = 0.0
        
        return {
            "primary_type": best_type,
            "confidence": confidence,
            "all_scores": dict(type_scores)
        }
    
    def _extract_clauses(self, text: str) -> List[ContractClause]:
        """提取和分类合同条款"""
        clauses = []
        
        # 按段落分割文本
        paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
        
        clause_id = 1
        for paragraph in paragraphs:
            if len(paragraph) < 10:  # 跳过过短的段落
                continue
            
            # 识别条款类型
            clause_type = self._classify_clause(paragraph)
            
            # 提取条款标题
            title = self._extract_clause_title(paragraph)
            
            # 创建条款对象
            clause = ContractClause(
                clause_id=f"clause_{clause_id}",
                title=title,
                content=paragraph,
                clause_type=clause_type
            )
            
            # 提取条款中的关键术语
            clause.key_terms = self._extract_clause_terms(paragraph)
            
            # 提取条款中的实体
            clause.entities = self._extract_clause_entities(paragraph)
            
            clauses.append(clause)
            clause_id += 1
        
        return clauses
    
    def _classify_clause(self, text: str) -> str:
        """分类条款类型"""
        for clause_type, patterns in self.clause_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    return clause_type
        
        # 基于内容关键词进行分类
        if any(keyword in text for keyword in ["定义", "术语", "解释"]):
            return "定义条款"
        elif any(keyword in text for keyword in ["权利", "义务", "责任"]):
            return "权利义务"
        elif any(keyword in text for keyword in ["违约", "赔偿", "损失"]):
            return "违约责任"
        elif any(keyword in text for keyword in ["争议", "仲裁", "诉讼"]):
            return "争议解决"
        elif any(keyword in text for keyword in ["生效", "终止", "解除"]):
            return "生效条件"
        else:
            return "一般条款"
    
    def _extract_clause_title(self, text: str) -> str:
        """提取条款标题"""
        # 尝试提取条款编号和标题
        title_patterns = [
            r"^(第[一二三四五六七八九十\d]+条[^。！？]*)",
            r"^(\d+\.\d+[^。！？]*)",
            r"^([一二三四五六七八九十]+、[^。！？]*)",
            r"^(\(\d+\)[^。！？]*)"
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, text.strip())
            if match:
                return match.group(1).strip()
        
        # 如果没有明确标题，使用前30个字符
        return text[:30] + "..." if len(text) > 30 else text
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取合同实体"""
        entities = defaultdict(list)
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                entities[entity_type].extend(matches)
        
        # 去重
        for entity_type in entities:
            entities[entity_type] = list(set(entities[entity_type]))
        
        return dict(entities)
    
    def _extract_clause_entities(self, text: str) -> Dict[str, List[str]]:
        """提取单个条款中的实体"""
        return self._extract_entities(text)
    
    def _extract_key_terms(self, text: str) -> List[Dict[str, Any]]:
        """提取关键术语"""
        # 使用jieba分词
        tokens = list(jieba.cut(text))
        
        # 过滤法律相关术语
        legal_terms = []
        legal_keywords = [
            "合同", "协议", "条款", "当事人", "甲方", "乙方", "权利", "义务",
            "责任", "违约", "赔偿", "损失", "仲裁", "诉讼", "生效", "终止"
        ]
        
        for token in tokens:
            if len(token) > 1 and (token in legal_keywords or 
                                 any(char in token for char in ["法", "律", "权", "责"])):
                legal_terms.append({
                    "term": token,
                    "frequency": tokens.count(token),
                    "type": "legal"
                })
        
        # 去重并按频率排序
        unique_terms = {}
        for term in legal_terms:
            if term["term"] not in unique_terms:
                unique_terms[term["term"]] = term
        
        return sorted(unique_terms.values(), key=lambda x: x["frequency"], reverse=True)
    
    def _extract_clause_terms(self, text: str) -> List[str]:
        """提取条款关键术语"""
        terms = self._extract_key_terms(text)
        return [term["term"] for term in terms[:5]]  # 返回前5个关键术语
    
    def _analyze_structure(self, clauses: List[ContractClause]) -> Dict[str, Any]:
        """分析合同结构"""
        structure = {
            "total_clauses": len(clauses),
            "clause_types": defaultdict(int),
            "completeness_score": 0.0,
            "missing_clauses": [],
            "structure_quality": "good"
        }
        
        # 统计条款类型
        for clause in clauses:
            structure["clause_types"][clause.clause_type] += 1
        
        # 检查合同完整性
        essential_clauses = [
            "定义条款", "权利义务", "违约责任", "争议解决", "生效条件"
        ]
        
        present_types = set(structure["clause_types"].keys())
        missing_types = [ct for ct in essential_clauses if ct not in present_types]
        
        structure["missing_clauses"] = missing_types
        structure["completeness_score"] = (len(essential_clauses) - len(missing_types)) / len(essential_clauses)
        
        # 评估结构质量
        if structure["completeness_score"] >= 0.8:
            structure["structure_quality"] = "excellent"
        elif structure["completeness_score"] >= 0.6:
            structure["structure_quality"] = "good"
        elif structure["completeness_score"] >= 0.4:
            structure["structure_quality"] = "fair"
        else:
            structure["structure_quality"] = "poor"
        
        return dict(structure)
    
    def parse_contract_file(self, file_path: str) -> Dict[str, Any]:
        """解析合同文件（支持多种格式）"""
        try:
            # 根据文件扩展名选择解析方法
            if file_path.lower().endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
            else:
                # 对于其他格式，返回模拟结果
                text = "这是一个模拟的合同文本内容，用于演示解析功能。"
                logger.warning(f"文件格式暂不支持，使用模拟内容: {file_path}")
            
            return self.parse_contract_text(text)
            
        except Exception as e:
            logger.error(f"文件解析失败: {e}")
            return {
                "error": f"文件解析失败: {str(e)}",
                "file_path": file_path
            }


# 全局合同解析器实例
contract_parser = ContractParser()


def parse_contract(text: str) -> Dict[str, Any]:
    """解析合同的便捷函数"""
    return contract_parser.parse_contract_text(text)


def parse_contract_file(file_path: str) -> Dict[str, Any]:
    """解析合同文件的便捷函数"""
    return contract_parser.parse_contract_file(file_path)
