"""
智能法律问答引擎
集成NLP处理、语义分析、意图识别和知识图谱的高级问答系统
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import re
import jieba

logger = logging.getLogger(__name__)


class IntelligentLegalQA:
    """智能法律问答引擎"""
    
    def __init__(self):
        """初始化问答引擎"""
        # 初始化各个组件（简化版本，避免依赖问题）
        self.nlp_processor = self._init_nlp_processor()
        self.semantic_analyzer = self._init_semantic_analyzer()
        self.intent_classifier = self._init_intent_classifier()
        self.knowledge_graph = self._init_knowledge_graph()
        
        # 问答模板和规则
        self.qa_templates = self._load_qa_templates()
        self.answer_patterns = self._load_answer_patterns()
        
        # 对话上下文管理
        self.conversation_context = {}
        
        logger.info("智能法律问答引擎初始化完成")
    
    def _init_nlp_processor(self):
        """初始化NLP处理器（简化版）"""
        return {
            "legal_keywords": {
                "合同": ["合同", "协议", "约定", "条款", "违约", "履行"],
                "劳动": ["劳动", "工作", "雇佣", "工资", "加班", "辞职"],
                "婚姻": ["婚姻", "离婚", "结婚", "财产", "抚养", "赡养"],
                "房产": ["房屋", "房产", "买卖", "租赁", "物业", "装修"],
                "刑事": ["犯罪", "刑事", "盗窃", "诈骗", "故意", "过失"]
            }
        }
    
    def _init_semantic_analyzer(self):
        """初始化语义分析器（简化版）"""
        return {
            "legal_concepts": {
                "合同法": ["合同", "协议", "约定", "条款", "违约", "履行"],
                "劳动法": ["劳动合同", "用人单位", "劳动者", "工资", "加班费"],
                "民法": ["民事权利", "民事义务", "侵权", "损害赔偿"],
                "刑法": ["犯罪", "刑罚", "犯罪构成", "故意", "过失"]
            }
        }
    
    def _init_intent_classifier(self):
        """初始化意图分类器（简化版）"""
        return {
            "intent_patterns": {
                "咨询": [r"请问|咨询|想了解|想知道|如何|怎么|什么是"],
                "投诉": [r"投诉|举报|控告|违法|违规|不合理"],
                "申请": [r"申请|办理|提交|如何申请|申请流程"],
                "查询": [r"查询|查看|查找|搜索|案件进度"],
                "求助": [r"求助|帮助|救助|紧急|急需|怎么办"]
            }
        }
    
    def _init_knowledge_graph(self):
        """初始化知识图谱（简化版）"""
        return {
            "entities": {
                "民法典": {"type": "法律法规", "properties": {"颁布时间": "2020年"}},
                "劳动合同法": {"type": "法律法规", "properties": {"颁布时间": "2007年"}},
                "合同": {"type": "法律概念", "properties": {"定义": "当事人之间设立权利义务关系的协议"}},
                "侵权": {"type": "法律概念", "properties": {"定义": "侵害他人合法权益的行为"}},
                "自然人": {"type": "法律主体", "properties": {"权利能力": "出生至死亡"}},
                "法人": {"type": "法律主体", "properties": {"类型": "营利法人、非营利法人"}}
            },
            "relationships": {
                ("民法典", "规定", "合同"),
                ("民法典", "规定", "侵权"),
                ("合同", "涉及", "自然人"),
                ("合同", "涉及", "法人")
            }
        }
    
    def _load_qa_templates(self) -> Dict[str, List[str]]:
        """加载问答模板"""
        return {
            "法律定义": [
                "{concept}是指{definition}",
                "根据法律规定，{concept}的定义是{definition}",
                "{concept}在法律上是指{definition}"
            ],
            "法律条文": [
                "根据《{law}》第{article}条规定，{content}",
                "《{law}》第{article}条明确规定：{content}",
                "依据{law}相关条款，{content}"
            ],
            "程序指导": [
                "办理{procedure}需要以下步骤：{steps}",
                "{procedure}的具体流程如下：{steps}",
                "您可以按照以下步骤进行{procedure}：{steps}"
            ],
            "建议回复": [
                "建议您{suggestion}",
                "在这种情况下，建议{suggestion}",
                "根据您的情况，我建议{suggestion}"
            ]
        }
    
    def _load_answer_patterns(self) -> Dict[str, Dict[str, Any]]:
        """加载答案模式"""
        return {
            "合同相关": {
                "keywords": ["合同", "协议", "约定", "条款", "违约"],
                "answers": {
                    "定义": "合同是民事主体之间设立、变更、终止民事法律关系的协议。",
                    "要素": "合同的基本要素包括：当事人、标的、价款或报酬等。",
                    "效力": "合同依法成立，自成立时生效，但法律另有规定或当事人另有约定的除外。",
                    "违约": "当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担违约责任。"
                }
            },
            "劳动法相关": {
                "keywords": ["劳动", "工作", "雇佣", "工资", "加班", "辞职"],
                "answers": {
                    "劳动合同": "建立劳动关系，应当订立书面劳动合同。",
                    "工资支付": "工资应当以货币形式按月支付给劳动者本人。",
                    "工作时间": "国家实行劳动者每日工作时间不超过八小时、平均每周工作时间不超过四十四小时的工时制度。",
                    "解除合同": "劳动者提前三十日以书面形式通知用人单位，可以解除劳动合同。"
                }
            },
            "婚姻法相关": {
                "keywords": ["婚姻", "离婚", "结婚", "财产", "抚养"],
                "answers": {
                    "结婚条件": "结婚应当男女双方完全自愿，禁止任何一方对另一方加以强迫。",
                    "夫妻财产": "夫妻在婚姻关系存续期间所得的财产，为夫妻的共同财产。",
                    "离婚条件": "夫妻感情确已破裂，调解无效的，应当准予离婚。",
                    "子女抚养": "父母对未成年子女负有抚养、教育和保护的义务。"
                }
            }
        }
    
    def process_question(self, question: str, user_id: str = "default") -> Dict[str, Any]:
        """处理法律问题
        
        Args:
            question: 用户问题
            user_id: 用户ID，用于上下文管理
        
        Returns:
            问答结果
        """
        try:
            # 1. 预处理问题
            processed_question = self._preprocess_question(question)
            
            # 2. 意图识别
            intent_result = self._classify_intent(processed_question)
            
            # 3. 实体提取
            entities = self._extract_entities(processed_question)
            
            # 4. 语义分析
            semantic_info = self._analyze_semantics(processed_question)
            
            # 5. 知识检索
            knowledge_results = self._retrieve_knowledge(processed_question, entities)
            
            # 6. 答案生成
            answer = self._generate_answer(
                processed_question, 
                intent_result, 
                entities, 
                semantic_info, 
                knowledge_results
            )
            
            # 7. 更新对话上下文
            self._update_context(user_id, question, answer, intent_result)
            
            return {
                "question": question,
                "intent": intent_result,
                "entities": entities,
                "semantic_info": semantic_info,
                "knowledge_results": knowledge_results,
                "answer": answer,
                "confidence": self._calculate_confidence(intent_result, entities, knowledge_results),
                "suggestions": self._generate_suggestions(intent_result["intent"])
            }
            
        except Exception as e:
            logger.error(f"处理问题失败: {e}")
            return {
                "question": question,
                "answer": "抱歉，我无法理解您的问题。请您重新描述或提供更多详细信息。",
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _preprocess_question(self, question: str) -> str:
        """预处理问题"""
        # 去除多余空格和标点
        question = re.sub(r'\s+', ' ', question.strip())
        question = re.sub(r'[？?]+', '？', question)
        question = re.sub(r'[！!]+', '！', question)
        
        return question
    
    def _classify_intent(self, question: str) -> Dict[str, Any]:
        """分类意图（简化版）"""
        intent_scores = {}
        
        for intent, patterns in self.intent_classifier["intent_patterns"].items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, question, re.IGNORECASE):
                    score += 1
            intent_scores[intent] = score
        
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            confidence = intent_scores[best_intent] / sum(intent_scores.values()) if sum(intent_scores.values()) > 0 else 0
        else:
            best_intent = "未知"
            confidence = 0.0
        
        return {
            "intent": best_intent,
            "confidence": confidence,
            "scores": intent_scores
        }
    
    def _extract_entities(self, question: str) -> List[Dict[str, Any]]:
        """提取实体（简化版）"""
        entities = []
        
        # 检查知识图谱中的实体
        for entity_name, entity_info in self.knowledge_graph["entities"].items():
            if entity_name in question:
                entities.append({
                    "name": entity_name,
                    "type": entity_info["type"],
                    "properties": entity_info.get("properties", {})
                })
        
        # 提取金额
        amounts = re.findall(r'\d+(?:\.\d+)?(?:万|千|百)?元', question)
        for amount in amounts:
            entities.append({
                "name": amount,
                "type": "金额",
                "properties": {}
            })
        
        # 提取时间
        times = re.findall(r'\d{4}年\d{1,2}月\d{1,2}日|\d{1,2}(?:个)?(?:月|年|日|天)', question)
        for time in times:
            entities.append({
                "name": time,
                "type": "时间",
                "properties": {}
            })
        
        return entities
    
    def _analyze_semantics(self, question: str) -> Dict[str, Any]:
        """语义分析（简化版）"""
        # 分词
        tokens = list(jieba.cut(question))
        
        # 识别法律概念
        legal_concepts = []
        for concept_category, concept_words in self.semantic_analyzer["legal_concepts"].items():
            for word in concept_words:
                if word in tokens:
                    legal_concepts.append({
                        "concept": word,
                        "category": concept_category
                    })
        
        return {
            "tokens": tokens,
            "legal_concepts": legal_concepts,
            "complexity": len(set(tokens)) / len(tokens) if tokens else 0
        }
    
    def _retrieve_knowledge(self, question: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检索知识（简化版）"""
        results = []
        
        # 基于实体检索
        for entity in entities:
            entity_name = entity["name"]
            if entity_name in self.knowledge_graph["entities"]:
                entity_info = self.knowledge_graph["entities"][entity_name]
                results.append({
                    "type": "entity_info",
                    "entity": entity_name,
                    "info": entity_info
                })
        
        # 基于关键词检索答案模式
        for category, pattern_info in self.answer_patterns.items():
            keywords = pattern_info["keywords"]
            if any(keyword in question for keyword in keywords):
                results.append({
                    "type": "answer_pattern",
                    "category": category,
                    "answers": pattern_info["answers"]
                })
        
        return results
    
    def _generate_answer(self, question: str, intent_result: Dict[str, Any], 
                        entities: List[Dict[str, Any]], semantic_info: Dict[str, Any],
                        knowledge_results: List[Dict[str, Any]]) -> str:
        """生成答案"""
        intent = intent_result["intent"]
        
        # 基于知识检索结果生成答案
        if knowledge_results:
            for result in knowledge_results:
                if result["type"] == "answer_pattern":
                    answers = result["answers"]
                    
                    # 根据问题内容选择最合适的答案
                    for key, answer in answers.items():
                        if any(keyword in question for keyword in [key, key.lower()]):
                            return answer
                    
                    # 如果没有精确匹配，返回第一个答案
                    return list(answers.values())[0]
                
                elif result["type"] == "entity_info":
                    entity_info = result["info"]
                    if "properties" in entity_info and "定义" in entity_info["properties"]:
                        return f"{result['entity']}是指{entity_info['properties']['定义']}"
        
        # 基于意图生成通用答案
        if intent == "咨询":
            if any(keyword in question for keyword in ["合同", "协议"]):
                return "合同是民事主体之间设立、变更、终止民事法律关系的协议。合同依法成立，自成立时生效。如需了解具体条款，建议详细描述您的情况。"
            elif any(keyword in question for keyword in ["劳动", "工作"]):
                return "劳动法保护劳动者的合法权益。建立劳动关系应当订立书面劳动合同。如有具体问题，请详细描述您的情况。"
            else:
                return "根据您的咨询，建议您详细描述具体情况，以便我为您提供更准确的法律建议。"
        elif intent == "投诉":
            return "如果您需要投诉，建议您收集相关证据，并向相关监管部门举报或寻求法律援助。"
        elif intent == "申请":
            return "关于您要申请的事项，建议您先了解相关的申请条件和所需材料，必要时咨询专业律师。"
        elif intent == "查询":
            return "您可以通过相关官方渠道查询所需信息，注意保护个人隐私。"
        elif intent == "求助":
            return "如遇紧急情况请立即报警，其他法律问题建议寻求专业法律援助。"
        else:
            return "抱歉，我无法完全理解您的问题。请您提供更多详细信息，或者重新描述您的问题。"
    
    def _calculate_confidence(self, intent_result: Dict[str, Any], 
                            entities: List[Dict[str, Any]], 
                            knowledge_results: List[Dict[str, Any]]) -> float:
        """计算置信度"""
        intent_confidence = intent_result.get("confidence", 0.0)
        entity_score = min(len(entities) * 0.2, 1.0)  # 实体数量贡献
        knowledge_score = min(len(knowledge_results) * 0.3, 1.0)  # 知识匹配贡献
        
        # 加权平均
        confidence = 0.4 * intent_confidence + 0.3 * entity_score + 0.3 * knowledge_score
        
        return min(confidence, 1.0)
    
    def _generate_suggestions(self, intent: str) -> List[str]:
        """生成建议"""
        suggestions_map = {
            "咨询": [
                "请详细描述您的具体问题",
                "您可以提供相关的时间、地点和人员信息",
                "建议说明您希望了解的具体法律条文"
            ],
            "投诉": [
                "请保留相关证据材料",
                "建议先尝试协商解决",
                "可以向相关监管部门举报"
            ],
            "申请": [
                "请了解申请的具体条件和要求",
                "准备相关申请材料",
                "建议咨询专业律师"
            ],
            "查询": [
                "请提供准确的查询信息",
                "可以通过官方渠道查询",
                "注意保护个人隐私信息"
            ],
            "求助": [
                "如遇紧急情况请立即报警",
                "可以寻求法律援助",
                "建议联系专业律师"
            ]
        }
        
        return suggestions_map.get(intent, ["建议提供更多详细信息"])
    
    def _update_context(self, user_id: str, question: str, answer: str, intent_result: Dict[str, Any]):
        """更新对话上下文"""
        if user_id not in self.conversation_context:
            self.conversation_context[user_id] = []
        
        context_entry = {
            "question": question,
            "answer": answer,
            "intent": intent_result["intent"],
            "timestamp": "now"  # 简化时间戳
        }
        
        self.conversation_context[user_id].append(context_entry)
        
        # 保持最近10轮对话
        if len(self.conversation_context[user_id]) > 10:
            self.conversation_context[user_id] = self.conversation_context[user_id][-10:]


# 全局智能问答引擎实例
intelligent_qa = IntelligentLegalQA()


def ask_legal_question(question: str, user_id: str = "default") -> Dict[str, Any]:
    """询问法律问题的便捷函数"""
    return intelligent_qa.process_question(question, user_id)
