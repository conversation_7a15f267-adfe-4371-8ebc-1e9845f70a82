"""
用户服务
提供用户管理的核心业务逻辑
"""

import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import logging

from app.models.user import User, UserProfile, UserSession, UserPreference
from app.core.auth import get_password_hash, verify_password, create_access_token
from app.core.encryption import get_encryption_manager
from app.core.audit import get_audit_logger
from app.core.privacy import get_privacy_manager, ConsentType
from app.schemas.user import UserCreate, UserUpdate, UserResponse

logger = logging.getLogger(__name__)


class UserService:
    """用户服务类"""
    
    def __init__(self, db: Session):
        """
        初始化用户服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.encryption_manager = get_encryption_manager()
        self.audit_logger = get_audit_logger(db)
        self.privacy_manager = get_privacy_manager(db)
    
    def create_user(
        self, 
        user_data: UserCreate, 
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建新用户
        
        Args:
            user_data: 用户创建数据
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            创建结果
        """
        try:
            # 检查邮箱是否已存在
            existing_user = self.db.query(User).filter(User.email == user_data.email).first()
            if existing_user:
                raise ValueError("邮箱地址已被注册")
            
            # 检查手机号是否已存在（如果提供）
            if user_data.phone:
                existing_phone = self.db.query(User).filter(User.phone == user_data.phone).first()
                if existing_phone:
                    raise ValueError("手机号码已被注册")
            
            # 创建用户
            user = User(
                id=uuid.uuid4(),
                email=user_data.email,
                password_hash=get_password_hash(user_data.password),
                full_name=user_data.full_name,
                phone=user_data.phone,
                user_type=user_data.user_type or "regular",
                is_active=True,
                email_verified=False,
                phone_verified=False,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.db.add(user)
            self.db.flush()  # 获取用户ID
            
            # 创建用户资料
            profile = UserProfile(
                user_id=user.id,
                avatar_url=None,
                bio=None,
                location=None,
                website=None,
                company=None,
                job_title=None,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.db.add(profile)
            
            # 创建默认用户偏好
            preferences = UserPreference(
                user_id=user.id,
                language="zh-CN",
                timezone="Asia/Shanghai",
                email_notifications=True,
                sms_notifications=False,
                marketing_emails=False,
                theme="light",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.db.add(preferences)
            
            # 记录默认隐私同意
            self.privacy_manager.record_consent(
                user_id=str(user.id),
                consent_type=ConsentType.PRIVACY_POLICY,
                consented=True,
                consent_version="1.0",
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            self.privacy_manager.record_consent(
                user_id=str(user.id),
                consent_type=ConsentType.TERMS_OF_SERVICE,
                consented=True,
                consent_version="1.0",
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            self.db.commit()
            
            # 记录审计日志
            self.audit_logger.log_action(
                action="USER_CREATED",
                user_id=str(user.id),
                resource_type="USER",
                resource_id=str(user.id),
                details={
                    "email": user.email,
                    "full_name": user.full_name,
                    "user_type": user.user_type,
                    "ip_address": ip_address
                },
                success=True
            )
            
            logger.info(f"用户创建成功: {user.email}")
            
            return {
                "user_id": str(user.id),
                "email": user.email,
                "full_name": user.full_name,
                "user_type": user.user_type,
                "created_at": user.created_at.isoformat(),
                "status": "created"
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"用户创建失败: {e}")
            
            # 记录失败的审计日志
            self.audit_logger.log_action(
                action="USER_CREATE_FAILED",
                user_id=None,
                resource_type="USER",
                resource_id=None,
                details={
                    "email": user_data.email,
                    "error": str(e),
                    "ip_address": ip_address
                },
                success=False
            )
            
            raise
    
    def authenticate_user(
        self, 
        email: str, 
        password: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        用户认证
        
        Args:
            email: 邮箱
            password: 密码
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            认证结果
        """
        try:
            # 查找用户
            user = self.db.query(User).filter(User.email == email).first()
            if not user:
                raise ValueError("用户不存在")
            
            # 检查用户状态
            if not user.is_active:
                raise ValueError("用户账户已被禁用")
            
            # 验证密码
            if not verify_password(password, user.password_hash):
                # 记录失败的登录尝试
                self.audit_logger.log_action(
                    action="LOGIN_FAILED",
                    user_id=str(user.id),
                    resource_type="USER_SESSION",
                    resource_id=None,
                    details={
                        "email": email,
                        "reason": "invalid_password",
                        "ip_address": ip_address
                    },
                    success=False
                )
                raise ValueError("密码错误")
            
            # 创建访问令牌
            access_token = create_access_token(
                data={"sub": str(user.id), "email": user.email, "user_type": user.user_type}
            )
            
            # 创建用户会话
            session = UserSession(
                id=uuid.uuid4(),
                user_id=user.id,
                session_token=access_token,
                ip_address=ip_address,
                user_agent=user_agent,
                created_at=datetime.utcnow(),
                expires_at=datetime.utcnow() + timedelta(hours=24),
                is_active=True
            )
            
            self.db.add(session)
            
            # 更新用户最后登录时间
            user.last_login_at = datetime.utcnow()
            user.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            # 记录成功的审计日志
            self.audit_logger.log_action(
                action="USER_LOGIN",
                user_id=str(user.id),
                resource_type="USER_SESSION",
                resource_id=str(session.id),
                details={
                    "email": email,
                    "ip_address": ip_address,
                    "user_agent": user_agent
                },
                success=True
            )
            
            logger.info(f"用户登录成功: {email}")
            
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": 86400,  # 24小时
                "user": {
                    "id": str(user.id),
                    "email": user.email,
                    "full_name": user.full_name,
                    "user_type": user.user_type,
                    "is_active": user.is_active,
                    "email_verified": user.email_verified
                }
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"用户认证失败: {e}")
            raise
    
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """
        根据ID获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户对象
        """
        try:
            user = self.db.query(User).filter(User.id == uuid.UUID(user_id)).first()
            return user
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户完整资料
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户资料
        """
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                raise ValueError("用户不存在")
            
            # 获取用户资料
            profile = self.db.query(UserProfile).filter(UserProfile.user_id == user.id).first()
            
            # 获取用户偏好
            preferences = self.db.query(UserPreference).filter(UserPreference.user_id == user.id).first()
            
            # 构建响应数据
            user_data = {
                "id": str(user.id),
                "email": user.email,
                "full_name": user.full_name,
                "phone": user.phone,
                "user_type": user.user_type,
                "is_active": user.is_active,
                "email_verified": user.email_verified,
                "phone_verified": user.phone_verified,
                "created_at": user.created_at.isoformat(),
                "updated_at": user.updated_at.isoformat(),
                "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None
            }
            
            if profile:
                user_data["profile"] = {
                    "avatar_url": profile.avatar_url,
                    "bio": profile.bio,
                    "location": profile.location,
                    "website": profile.website,
                    "company": profile.company,
                    "job_title": profile.job_title
                }
            
            if preferences:
                user_data["preferences"] = {
                    "language": preferences.language,
                    "timezone": preferences.timezone,
                    "email_notifications": preferences.email_notifications,
                    "sms_notifications": preferences.sms_notifications,
                    "marketing_emails": preferences.marketing_emails,
                    "theme": preferences.theme
                }
            
            return user_data
            
        except Exception as e:
            logger.error(f"获取用户资料失败: {e}")
            raise
    
    def update_user(self, user_id: str, update_data: UserUpdate) -> Dict[str, Any]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            update_data: 更新数据
            
        Returns:
            更新结果
        """
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                raise ValueError("用户不存在")
            
            # 更新用户基本信息
            if update_data.full_name is not None:
                user.full_name = update_data.full_name
            
            if update_data.phone is not None:
                # 检查手机号是否已被其他用户使用
                existing_phone = self.db.query(User).filter(
                    and_(User.phone == update_data.phone, User.id != user.id)
                ).first()
                if existing_phone:
                    raise ValueError("手机号码已被其他用户使用")
                user.phone = update_data.phone
                user.phone_verified = False  # 需要重新验证
            
            user.updated_at = datetime.utcnow()
            
            # 更新用户资料
            if hasattr(update_data, 'profile') and update_data.profile:
                profile = self.db.query(UserProfile).filter(UserProfile.user_id == user.id).first()
                if not profile:
                    profile = UserProfile(user_id=user.id, created_at=datetime.utcnow())
                    self.db.add(profile)
                
                for key, value in update_data.profile.dict(exclude_unset=True).items():
                    setattr(profile, key, value)
                profile.updated_at = datetime.utcnow()
            
            # 更新用户偏好
            if hasattr(update_data, 'preferences') and update_data.preferences:
                preferences = self.db.query(UserPreference).filter(UserPreference.user_id == user.id).first()
                if not preferences:
                    preferences = UserPreference(user_id=user.id, created_at=datetime.utcnow())
                    self.db.add(preferences)
                
                for key, value in update_data.preferences.dict(exclude_unset=True).items():
                    setattr(preferences, key, value)
                preferences.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            # 记录审计日志
            self.audit_logger.log_action(
                action="USER_UPDATED",
                user_id=user_id,
                resource_type="USER",
                resource_id=user_id,
                details={
                    "updated_fields": list(update_data.dict(exclude_unset=True).keys())
                },
                success=True
            )
            
            logger.info(f"用户信息更新成功: {user.email}")
            
            return self.get_user_profile(user_id)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"用户信息更新失败: {e}")
            raise
    
    def deactivate_user(self, user_id: str, reason: str = "") -> bool:
        """
        停用用户账户
        
        Args:
            user_id: 用户ID
            reason: 停用原因
            
        Returns:
            是否成功
        """
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                raise ValueError("用户不存在")
            
            user.is_active = False
            user.updated_at = datetime.utcnow()
            
            # 停用所有活跃会话
            self.db.query(UserSession).filter(
                and_(UserSession.user_id == user.id, UserSession.is_active == True)
            ).update({"is_active": False})
            
            self.db.commit()
            
            # 记录审计日志
            self.audit_logger.log_action(
                action="USER_DEACTIVATED",
                user_id=user_id,
                resource_type="USER",
                resource_id=user_id,
                details={"reason": reason},
                success=True
            )
            
            logger.info(f"用户账户已停用: {user.email}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"用户账户停用失败: {e}")
            return False
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取用户会话列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            会话列表
        """
        try:
            sessions = self.db.query(UserSession).filter(
                UserSession.user_id == uuid.UUID(user_id)
            ).order_by(UserSession.created_at.desc()).all()
            
            return [
                {
                    "id": str(session.id),
                    "ip_address": session.ip_address,
                    "user_agent": session.user_agent,
                    "created_at": session.created_at.isoformat(),
                    "expires_at": session.expires_at.isoformat(),
                    "is_active": session.is_active
                }
                for session in sessions
            ]
            
        except Exception as e:
            logger.error(f"获取用户会话失败: {e}")
            return []
    
    def revoke_session(self, user_id: str, session_id: str) -> bool:
        """
        撤销用户会话
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            是否成功
        """
        try:
            session = self.db.query(UserSession).filter(
                and_(
                    UserSession.id == uuid.UUID(session_id),
                    UserSession.user_id == uuid.UUID(user_id)
                )
            ).first()
            
            if not session:
                raise ValueError("会话不存在")
            
            session.is_active = False
            self.db.commit()
            
            # 记录审计日志
            self.audit_logger.log_action(
                action="SESSION_REVOKED",
                user_id=user_id,
                resource_type="USER_SESSION",
                resource_id=session_id,
                details={},
                success=True
            )
            
            logger.info(f"用户会话已撤销: {session_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"撤销用户会话失败: {e}")
            return False
