"""
权限管理服务
"""

import uuid
from typing import Optional, List, Dict, Any, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text
from sqlalchemy.orm import selectinload
import logging

from app.models.permission import Permission, Role, ResourcePermission, PermissionGroup
from app.models.user import User
from app.core.exceptions import NotFoundException, ValidationException, ConflictException
from app.core.audit import get_audit_logger, AuditEventType

logger = logging.getLogger(__name__)


class PermissionService:
    """权限管理服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_permission(
        self,
        name: str,
        code: str,
        description: str = None,
        category: str = None,
        resource: str = None,
        action: str = None,
        level: int = 1
    ) -> Permission:
        """创建权限"""
        
        logger.info(f"创建权限: {name} ({code})")
        
        # 检查权限代码是否已存在
        existing = await self.get_permission_by_code(code)
        if existing:
            raise ConflictException(f"权限代码 {code} 已存在")
        
        permission = Permission(
            name=name,
            code=code,
            description=description,
            category=category,
            resource=resource,
            action=action,
            level=level
        )
        
        self.db.add(permission)
        await self.db.commit()
        await self.db.refresh(permission)
        
        logger.info(f"权限创建成功: {permission.code}")
        return permission
    
    async def get_permission_by_code(self, code: str) -> Optional[Permission]:
        """根据代码获取权限"""
        
        stmt = select(Permission).where(Permission.code == code)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def create_role(
        self,
        name: str,
        code: str,
        description: str = None,
        level: int = 1,
        parent_role_id: Optional[uuid.UUID] = None,
        permission_codes: List[str] = None
    ) -> Role:
        """创建角色"""
        
        logger.info(f"创建角色: {name} ({code})")
        
        # 检查角色代码是否已存在
        existing = await self.get_role_by_code(code)
        if existing:
            raise ConflictException(f"角色代码 {code} 已存在")
        
        role = Role(
            name=name,
            code=code,
            description=description,
            level=level,
            parent_role_id=parent_role_id
        )
        
        # 添加权限
        if permission_codes:
            permissions = await self.get_permissions_by_codes(permission_codes)
            role.permissions = permissions
        
        self.db.add(role)
        await self.db.commit()
        await self.db.refresh(role)
        
        logger.info(f"角色创建成功: {role.code}")
        return role
    
    async def get_role_by_code(self, code: str) -> Optional[Role]:
        """根据代码获取角色"""
        
        stmt = select(Role).where(Role.code == code).options(
            selectinload(Role.permissions)
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_permissions_by_codes(self, codes: List[str]) -> List[Permission]:
        """根据代码列表获取权限"""
        
        stmt = select(Permission).where(Permission.code.in_(codes))
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
    
    async def assign_role_to_user(
        self,
        user_id: uuid.UUID,
        role_id: uuid.UUID,
        assigned_by: Optional[uuid.UUID] = None
    ) -> bool:
        """为用户分配角色"""
        
        logger.info(f"为用户 {user_id} 分配角色 {role_id}")
        
        # 检查用户是否存在
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("用户不存在")
        
        # 检查角色是否存在
        role = await self.get_role_by_id(role_id)
        if not role:
            raise NotFoundException("角色不存在")
        
        # 检查是否已分配
        if role in user.roles:
            logger.warning(f"用户 {user_id} 已拥有角色 {role_id}")
            return False
        
        # 分配角色
        user.roles.append(role)
        await self.db.commit()
        
        # 记录审计日志
        audit_logger = await get_audit_logger(self.db)
        await audit_logger.log_event(
            event_type=AuditEventType.USER_UPDATE,
            message=f"为用户分配角色: {role.name}",
            user_id=user_id,
            username=user.username,
            resource_type="role",
            resource_id=str(role_id),
            action="assign_role",
            details={
                "role_name": role.name,
                "role_code": role.code,
                "assigned_by": str(assigned_by) if assigned_by else None
            }
        )
        
        logger.info(f"角色分配成功: 用户 {user_id} -> 角色 {role_id}")
        return True
    
    async def remove_role_from_user(
        self,
        user_id: uuid.UUID,
        role_id: uuid.UUID,
        removed_by: Optional[uuid.UUID] = None
    ) -> bool:
        """移除用户角色"""
        
        logger.info(f"移除用户 {user_id} 的角色 {role_id}")
        
        # 检查用户是否存在
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("用户不存在")
        
        # 检查角色是否存在
        role = await self.get_role_by_id(role_id)
        if not role:
            raise NotFoundException("角色不存在")
        
        # 检查是否已分配
        if role not in user.roles:
            logger.warning(f"用户 {user_id} 没有角色 {role_id}")
            return False
        
        # 移除角色
        user.roles.remove(role)
        await self.db.commit()
        
        # 记录审计日志
        audit_logger = await get_audit_logger(self.db)
        await audit_logger.log_event(
            event_type=AuditEventType.USER_UPDATE,
            message=f"移除用户角色: {role.name}",
            user_id=user_id,
            username=user.username,
            resource_type="role",
            resource_id=str(role_id),
            action="remove_role",
            details={
                "role_name": role.name,
                "role_code": role.code,
                "removed_by": str(removed_by) if removed_by else None
            }
        )
        
        logger.info(f"角色移除成功: 用户 {user_id} -> 角色 {role_id}")
        return True
    
    async def get_user_by_id(self, user_id: uuid.UUID) -> Optional[User]:
        """根据ID获取用户"""
        
        stmt = select(User).where(User.id == user_id).options(
            selectinload(User.roles).selectinload(Role.permissions)
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_role_by_id(self, role_id: uuid.UUID) -> Optional[Role]:
        """根据ID获取角色"""
        
        stmt = select(Role).where(Role.id == role_id).options(
            selectinload(Role.permissions)
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_permissions(self, user_id: uuid.UUID) -> Set[str]:
        """获取用户的所有权限代码"""
        
        user = await self.get_user_by_id(user_id)
        if not user:
            return set()
        
        permissions = set()
        for role in user.roles:
            if role.is_active:
                for permission in role.permissions:
                    if permission.is_active:
                        permissions.add(permission.code)
        
        return permissions
    
    async def check_user_permission(
        self,
        user_id: uuid.UUID,
        permission_code: str
    ) -> bool:
        """检查用户是否拥有指定权限"""
        
        user_permissions = await self.get_user_permissions(user_id)
        return permission_code in user_permissions
    
    async def check_user_permissions(
        self,
        user_id: uuid.UUID,
        permission_codes: List[str],
        require_all: bool = True
    ) -> bool:
        """检查用户是否拥有指定权限列表"""
        
        user_permissions = await self.get_user_permissions(user_id)
        
        if require_all:
            # 需要拥有所有权限
            return all(code in user_permissions for code in permission_codes)
        else:
            # 只需要拥有任一权限
            return any(code in user_permissions for code in permission_codes)
    
    async def get_user_roles(self, user_id: uuid.UUID) -> List[Role]:
        """获取用户的所有角色"""
        
        user = await self.get_user_by_id(user_id)
        if not user:
            return []
        
        return [role for role in user.roles if role.is_active]
    
    async def create_resource_permission(
        self,
        subject_type: str,
        subject_id: uuid.UUID,
        resource_type: str,
        resource_id: Optional[str],
        actions: List[str],
        effect: str = "allow",
        conditions: Dict[str, Any] = None,
        created_by: Optional[uuid.UUID] = None
    ) -> ResourcePermission:
        """创建资源权限"""
        
        logger.info(f"创建资源权限: {subject_type}:{subject_id} -> {resource_type}:{resource_id}")
        
        resource_permission = ResourcePermission(
            subject_type=subject_type,
            subject_id=subject_id,
            resource_type=resource_type,
            resource_id=resource_id,
            actions=actions,
            effect=effect,
            conditions=conditions or {},
            created_by=created_by
        )
        
        self.db.add(resource_permission)
        await self.db.commit()
        await self.db.refresh(resource_permission)
        
        logger.info(f"资源权限创建成功: {resource_permission.id}")
        return resource_permission
    
    async def check_resource_permission(
        self,
        user_id: uuid.UUID,
        resource_type: str,
        resource_id: Optional[str],
        action: str
    ) -> bool:
        """检查用户对特定资源的权限"""
        
        # 检查用户直接权限
        stmt = select(ResourcePermission).where(
            and_(
                ResourcePermission.subject_type == "user",
                ResourcePermission.subject_id == user_id,
                ResourcePermission.resource_type == resource_type,
                or_(
                    ResourcePermission.resource_id == resource_id,
                    ResourcePermission.resource_id.is_(None)  # 通配符权限
                ),
                ResourcePermission.is_active == True
            )
        )
        
        result = await self.db.execute(stmt)
        permissions = result.scalars().all()
        
        for permission in permissions:
            if action in permission.actions:
                return permission.effect == "allow"
        
        # 检查角色权限
        user_roles = await self.get_user_roles(user_id)
        for role in user_roles:
            stmt = select(ResourcePermission).where(
                and_(
                    ResourcePermission.subject_type == "role",
                    ResourcePermission.subject_id == role.id,
                    ResourcePermission.resource_type == resource_type,
                    or_(
                        ResourcePermission.resource_id == resource_id,
                        ResourcePermission.resource_id.is_(None)
                    ),
                    ResourcePermission.is_active == True
                )
            )
            
            result = await self.db.execute(stmt)
            permissions = result.scalars().all()
            
            for permission in permissions:
                if action in permission.actions:
                    return permission.effect == "allow"
        
        return False
