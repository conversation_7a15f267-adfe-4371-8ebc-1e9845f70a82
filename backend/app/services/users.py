"""
用户相关业务服务
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.orm import selectinload
import logging

from app.core.security import verify_password, get_password_hash, generate_session_token
from app.core.exceptions import NotFoundException, ConflictException, ValidationException
from app.models.user import User, UserProfile, UserSession, UserType, UserStatus
from app.models.log import SystemLog
from app.schemas.user import UserCreate, UserUpdate, UserProfileCreate, UserProfileUpdate

logger = logging.getLogger(__name__)


class UserService:
    """用户服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_user(self, user_create: UserCreate) -> User:
        """创建用户"""
        logger.info(f"创建用户: {user_create.email}")
        
        # 检查用户是否已存在
        existing_user = await self.get_user_by_email(user_create.email)
        if existing_user:
            raise ConflictException("邮箱已被注册")
        
        existing_user = await self.get_user_by_username(user_create.username)
        if existing_user:
            raise ConflictException("用户名已被使用")
        
        # 创建用户对象
        user = User(
            id=uuid.uuid4(),
            username=user_create.username,
            email=user_create.email,
            password_hash=get_password_hash(user_create.password),
            full_name=user_create.full_name,
            phone=user_create.phone,
            user_type=UserType(user_create.user_type) if isinstance(user_create.user_type, str) else user_create.user_type,
            status=UserStatus.ACTIVE,
        )
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        # 创建用户配置
        await self.create_user_profile(user.id)
        
        logger.info(f"用户创建成功: {user.email}")
        return user
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """根据ID获取用户"""
        try:
            user_uuid = uuid.UUID(user_id)
        except ValueError:
            return None
        
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.profile))
            .where(User.id == user_uuid)
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.profile))
            .where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.profile))
            .where(User.username == username)
        )
        return result.scalar_one_or_none()
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """验证用户凭据"""
        # 尝试用户名登录
        user = await self.get_user_by_username(username)
        
        # 如果用户名不存在，尝试邮箱登录
        if not user:
            user = await self.get_user_by_email(username)
        
        if not user:
            logger.warning(f"用户不存在: {username}")
            return None
        
        if not verify_password(password, user.password_hash):
            logger.warning(f"密码错误: {username}")
            return None
        
        if user.status != UserStatus.ACTIVE:
            logger.warning(f"用户状态异常: {username}, status: {user.status}")
            return None
        
        return user
    
    async def update_user(self, user_id: str, user_update: UserUpdate) -> User:
        """更新用户信息"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("用户不存在")
        
        # 更新字段
        for field, value in user_update.dict(exclude_unset=True).items():
            setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info(f"用户信息更新成功: {user.email}")
        return user
    
    async def update_user_password(self, user_id: str, current_password: str, new_password: str) -> bool:
        """更新用户密码"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("用户不存在")
        
        # 验证当前密码
        if not verify_password(current_password, user.password_hash):
            raise ValidationException("当前密码错误")
        
        # 更新密码
        user.password_hash = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        
        await self.db.commit()
        
        # 使所有会话失效
        await self.deactivate_user_sessions(user.id)
        
        logger.info(f"用户密码更新成功: {user.email}")
        return True
    
    async def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("用户不存在")
        
        await self.db.delete(user)
        await self.db.commit()
        
        logger.info(f"用户删除成功: {user.email}")
        return True
    
    async def create_user_profile(self, user_id: uuid.UUID, profile_data: Optional[UserProfileCreate] = None) -> UserProfile:
        """创建用户配置"""
        profile = UserProfile(
            id=uuid.uuid4(),
            user_id=user_id,
            **(profile_data.dict() if profile_data else {})
        )
        
        self.db.add(profile)
        await self.db.commit()
        await self.db.refresh(profile)
        
        return profile
    
    async def update_user_profile(self, user_id: str, profile_update: UserProfileUpdate) -> UserProfile:
        """更新用户配置"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("用户不存在")
        
        if not user.profile:
            # 如果配置不存在，创建一个
            profile = await self.create_user_profile(user.id, UserProfileCreate(**profile_update.dict()))
        else:
            profile = user.profile
            # 更新字段
            for field, value in profile_update.dict(exclude_unset=True).items():
                setattr(profile, field, value)
            
            profile.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(profile)
        
        logger.info(f"用户配置更新成功: {user.email}")
        return profile
    
    async def create_user_session(
        self,
        user_id: uuid.UUID,
        access_token: str,
        refresh_token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        device_info: Optional[Dict[str, Any]] = None
    ) -> UserSession:
        """创建用户会话"""
        session = UserSession(
            id=uuid.uuid4(),
            user_id=user_id,
            session_token=access_token,
            refresh_token=refresh_token,
            expires_at=datetime.utcnow() + timedelta(minutes=30),  # 30分钟后过期
            ip_address=ip_address,
            user_agent=user_agent,
            device_info=device_info or {},
            is_active=True
        )
        
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        
        return session
    
    async def get_user_session_by_refresh_token(self, refresh_token: str) -> Optional[UserSession]:
        """根据刷新令牌获取用户会话"""
        result = await self.db.execute(
            select(UserSession).where(
                and_(
                    UserSession.refresh_token == refresh_token,
                    UserSession.is_active == True
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def update_user_session_token(self, session_id: uuid.UUID, new_access_token: str) -> bool:
        """更新用户会话令牌"""
        result = await self.db.execute(
            select(UserSession).where(UserSession.id == session_id)
        )
        session = result.scalar_one_or_none()
        
        if session:
            session.session_token = new_access_token
            session.last_accessed_at = datetime.utcnow()
            await self.db.commit()
            return True
        
        return False
    
    async def deactivate_user_sessions(self, user_id: uuid.UUID) -> bool:
        """使用户所有会话失效"""
        result = await self.db.execute(
            select(UserSession).where(
                and_(
                    UserSession.user_id == user_id,
                    UserSession.is_active == True
                )
            )
        )
        sessions = result.scalars().all()
        
        for session in sessions:
            session.is_active = False
        
        await self.db.commit()
        return True
    
    async def update_last_login(self, user_id: uuid.UUID) -> bool:
        """更新最后登录时间"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.last_login_at = datetime.utcnow()
            await self.db.commit()
            return True
        
        return False
    
    async def verify_user_email(self, user_id: uuid.UUID) -> bool:
        """验证用户邮箱"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.email_verified = True
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            return True
        
        return False
    
    async def verify_user_phone(self, user_id: uuid.UUID) -> bool:
        """验证用户手机"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.phone_verified = True
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            return True
        
        return False
    
    async def get_users_list(
        self,
        skip: int = 0,
        limit: int = 20,
        user_type: Optional[UserType] = None,
        status: Optional[UserStatus] = None,
        search: Optional[str] = None
    ) -> tuple[List[User], int]:
        """获取用户列表"""
        query = select(User).options(selectinload(User.profile))
        
        # 添加过滤条件
        conditions = []
        if user_type:
            conditions.append(User.user_type == user_type)
        if status:
            conditions.append(User.status == status)
        if search:
            conditions.append(
                or_(
                    User.username.ilike(f"%{search}%"),
                    User.email.ilike(f"%{search}%"),
                    User.full_name.ilike(f"%{search}%")
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count(User.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = query.order_by(desc(User.created_at)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        users = result.scalars().all()
        
        return list(users), total
