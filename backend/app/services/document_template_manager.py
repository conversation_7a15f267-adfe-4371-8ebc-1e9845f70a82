"""
法律文书模板管理系统
实现常用法律文书模板的管理和维护，包括合同、起诉书、答辩书等
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional
from collections import defaultdict
from datetime import datetime
import os

logger = logging.getLogger(__name__)


class DocumentTemplate:
    """法律文书模板类"""
    
    def __init__(self, template_id: str, name: str, category: str, content: str):
        """初始化文书模板
        
        Args:
            template_id: 模板唯一标识
            name: 模板名称
            category: 模板分类
            content: 模板内容
        """
        self.template_id = template_id
        self.name = name
        self.category = category
        self.content = content
        self.variables = []  # 模板变量
        self.description = ""
        self.usage_count = 0
        self.created_time = datetime.now().isoformat()
        self.updated_time = datetime.now().isoformat()
        self.tags = []
        
        # 自动提取模板变量
        self._extract_variables()
    
    def _extract_variables(self):
        """提取模板中的变量"""
        # 查找 {{variable}} 格式的变量
        pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(pattern, self.content)
        self.variables = list(set(matches))  # 去重
    
    def render(self, variables: Dict[str, str]) -> str:
        """渲染模板
        
        Args:
            variables: 变量值字典
        
        Returns:
            渲染后的文档内容
        """
        rendered_content = self.content
        
        for var_name, var_value in variables.items():
            placeholder = f"{{{{{var_name}}}}}"
            rendered_content = rendered_content.replace(placeholder, str(var_value))
        
        return rendered_content
    
    def validate_variables(self, variables: Dict[str, str]) -> List[str]:
        """验证变量完整性
        
        Args:
            variables: 变量值字典
        
        Returns:
            缺失的变量列表
        """
        missing_vars = []
        for var in self.variables:
            if var not in variables or not variables[var]:
                missing_vars.append(var)
        
        return missing_vars
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "template_id": self.template_id,
            "name": self.name,
            "category": self.category,
            "content": self.content,
            "variables": self.variables,
            "description": self.description,
            "usage_count": self.usage_count,
            "created_time": self.created_time,
            "updated_time": self.updated_time,
            "tags": self.tags
        }


class DocumentTemplateManager:
    """法律文书模板管理器"""
    
    def __init__(self):
        """初始化模板管理器"""
        self.templates = {}  # 模板存储
        self.categories = defaultdict(list)  # 分类索引
        self.tags_index = defaultdict(list)  # 标签索引
        
        # 初始化内置模板
        self._initialize_builtin_templates()
        
        logger.info("法律文书模板管理器初始化完成")
    
    def _initialize_builtin_templates(self):
        """初始化内置模板"""
        builtin_templates = [
            {
                "template_id": "contract_service_001",
                "name": "服务合同模板",
                "category": "合同",
                "description": "标准服务合同模板",
                "content": """
服务合同

甲方：{{甲方名称}}
地址：{{甲方地址}}
联系电话：{{甲方电话}}

乙方：{{乙方名称}}
地址：{{乙方地址}}
联系电话：{{乙方电话}}

根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿的基础上，就{{服务内容}}事宜达成如下协议：

第一条 服务内容
乙方为甲方提供{{具体服务内容}}服务。

第二条 服务期限
服务期限为{{服务期限}}，自{{开始日期}}起至{{结束日期}}止。

第三条 服务费用
服务费用总计人民币{{服务费用}}元（大写：{{费用大写}}）。

第四条 付款方式
{{付款方式}}

第五条 双方权利义务
甲方权利义务：
1. 按约定支付服务费用
2. 提供必要的配合和支持
3. {{甲方其他义务}}

乙方权利义务：
1. 按约定提供服务
2. 保证服务质量
3. {{乙方其他义务}}

第六条 违约责任
任何一方违反本合同约定，应承担违约责任，向守约方支付违约金{{违约金数额}}元。

第七条 争议解决
因本合同发生争议，双方应协商解决；协商不成的，提交{{仲裁机构}}仲裁。

第八条 其他约定
{{其他约定}}

本合同一式两份，甲乙双方各执一份，自双方签字盖章之日起生效。

甲方（盖章）：_____________    乙方（盖章）：_____________

法定代表人：_______________    法定代表人：_______________

签订日期：{{签订日期}}        签订地点：{{签订地点}}
                """,
                "tags": ["合同", "服务", "标准"]
            },
            {
                "template_id": "lawsuit_civil_001",
                "name": "民事起诉状模板",
                "category": "诉讼文书",
                "description": "民事诉讼起诉状模板",
                "content": """
民事起诉状

原告：{{原告姓名}}，{{原告性别}}，{{原告年龄}}岁，{{原告民族}}族，{{原告职业}}，住址：{{原告住址}}，联系电话：{{原告电话}}。

被告：{{被告姓名}}，{{被告性别}}，{{被告年龄}}岁，{{被告民族}}族，{{被告职业}}，住址：{{被告住址}}，联系电话：{{被告电话}}。

诉讼请求：
{{诉讼请求}}

事实和理由：
{{事实和理由}}

此致
{{法院名称}}

具状人：{{原告姓名}}
{{起诉日期}}

附：
1. 本状副本{{副本份数}}份
2. 证据材料{{证据份数}}份
                """,
                "tags": ["起诉状", "民事", "诉讼"]
            },
            {
                "template_id": "answer_civil_001",
                "name": "民事答辩状模板",
                "category": "诉讼文书",
                "description": "民事诉讼答辩状模板",
                "content": """
民事答辩状

答辩人：{{答辩人姓名}}，{{答辩人性别}}，{{答辩人年龄}}岁，{{答辩人民族}}族，{{答辩人职业}}，住址：{{答辩人住址}}，联系电话：{{答辩人电话}}。

针对原告{{原告姓名}}诉{{案件性质}}一案，答辩如下：

答辩意见：
{{答辩意见}}

事实和理由：
{{答辩事实和理由}}

综上所述，请求人民法院依法{{答辩请求}}。

此致
{{法院名称}}

答辩人：{{答辩人姓名}}
{{答辩日期}}

附：
1. 本状副本{{副本份数}}份
2. 证据材料{{证据份数}}份
                """,
                "tags": ["答辩状", "民事", "诉讼"]
            },
            {
                "template_id": "power_attorney_001",
                "name": "授权委托书模板",
                "category": "委托文书",
                "description": "法律事务授权委托书模板",
                "content": """
授权委托书

委托人：{{委托人姓名}}，{{委托人性别}}，{{委托人年龄}}岁，{{委托人民族}}族，{{委托人职业}}，住址：{{委托人住址}}，身份证号：{{委托人身份证号}}，联系电话：{{委托人电话}}。

受托人：{{受托人姓名}}，{{受托人性别}}，{{受托人年龄}}岁，{{受托人职业}}，工作单位：{{受托人单位}}，住址：{{受托人住址}}，联系电话：{{受托人电话}}。

现委托上述受托人在{{委托事项}}中，作为我的诉讼代理人。

代理权限：{{代理权限}}

委托期限：{{委托期限}}

委托人：{{委托人姓名}}（签字）
{{委托日期}}
                """,
                "tags": ["委托书", "授权", "代理"]
            }
        ]
        
        for template_data in builtin_templates:
            template = DocumentTemplate(
                template_id=template_data["template_id"],
                name=template_data["name"],
                category=template_data["category"],
                content=template_data["content"]
            )
            template.description = template_data["description"]
            template.tags = template_data["tags"]
            
            self.add_template(template)
    
    def add_template(self, template: DocumentTemplate) -> bool:
        """添加模板
        
        Args:
            template: 文书模板对象
        
        Returns:
            是否添加成功
        """
        try:
            self.templates[template.template_id] = template
            self.categories[template.category].append(template.template_id)
            
            # 更新标签索引
            for tag in template.tags:
                self.tags_index[tag].append(template.template_id)
            
            logger.info(f"添加模板成功: {template.name}")
            return True
            
        except Exception as e:
            logger.error(f"添加模板失败: {e}")
            return False
    
    def get_template(self, template_id: str) -> Optional[DocumentTemplate]:
        """获取模板
        
        Args:
            template_id: 模板ID
        
        Returns:
            模板对象或None
        """
        return self.templates.get(template_id)
    
    def get_templates_by_category(self, category: str) -> List[DocumentTemplate]:
        """按分类获取模板
        
        Args:
            category: 模板分类
        
        Returns:
            模板列表
        """
        template_ids = self.categories.get(category, [])
        return [self.templates[tid] for tid in template_ids if tid in self.templates]
    
    def get_templates_by_tag(self, tag: str) -> List[DocumentTemplate]:
        """按标签获取模板
        
        Args:
            tag: 标签
        
        Returns:
            模板列表
        """
        template_ids = self.tags_index.get(tag, [])
        return [self.templates[tid] for tid in template_ids if tid in self.templates]
    
    def search_templates(self, keyword: str) -> List[DocumentTemplate]:
        """搜索模板
        
        Args:
            keyword: 搜索关键词
        
        Returns:
            匹配的模板列表
        """
        results = []
        keyword_lower = keyword.lower()
        
        for template in self.templates.values():
            # 在名称、描述、标签中搜索
            if (keyword_lower in template.name.lower() or
                keyword_lower in template.description.lower() or
                any(keyword_lower in tag.lower() for tag in template.tags)):
                results.append(template)
        
        return results
    
    def update_template(self, template_id: str, updates: Dict[str, Any]) -> bool:
        """更新模板
        
        Args:
            template_id: 模板ID
            updates: 更新内容
        
        Returns:
            是否更新成功
        """
        try:
            template = self.templates.get(template_id)
            if not template:
                return False
            
            # 更新字段
            for field, value in updates.items():
                if hasattr(template, field):
                    setattr(template, field, value)
            
            template.updated_time = datetime.now().isoformat()
            
            # 如果内容更新，重新提取变量
            if 'content' in updates:
                template._extract_variables()
            
            logger.info(f"更新模板成功: {template.name}")
            return True
            
        except Exception as e:
            logger.error(f"更新模板失败: {e}")
            return False
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板
        
        Args:
            template_id: 模板ID
        
        Returns:
            是否删除成功
        """
        try:
            template = self.templates.get(template_id)
            if not template:
                return False
            
            # 从各种索引中移除
            self.categories[template.category].remove(template_id)
            for tag in template.tags:
                if template_id in self.tags_index[tag]:
                    self.tags_index[tag].remove(template_id)
            
            # 删除模板
            del self.templates[template_id]
            
            logger.info(f"删除模板成功: {template.name}")
            return True
            
        except Exception as e:
            logger.error(f"删除模板失败: {e}")
            return False
    
    def get_all_categories(self) -> List[str]:
        """获取所有分类"""
        return list(self.categories.keys())
    
    def get_all_tags(self) -> List[str]:
        """获取所有标签"""
        return list(self.tags_index.keys())
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        stats = {
            "total_templates": len(self.templates),
            "categories": {},
            "tags": {},
            "most_used": [],
            "recent_added": []
        }
        
        # 分类统计
        for category, template_ids in self.categories.items():
            stats["categories"][category] = len(template_ids)
        
        # 标签统计
        for tag, template_ids in self.tags_index.items():
            stats["tags"][tag] = len(template_ids)
        
        # 最常用模板
        templates_by_usage = sorted(
            self.templates.values(),
            key=lambda t: t.usage_count,
            reverse=True
        )
        stats["most_used"] = [
            {"id": t.template_id, "name": t.name, "usage_count": t.usage_count}
            for t in templates_by_usage[:5]
        ]
        
        # 最近添加的模板
        templates_by_time = sorted(
            self.templates.values(),
            key=lambda t: t.created_time,
            reverse=True
        )
        stats["recent_added"] = [
            {"id": t.template_id, "name": t.name, "created_time": t.created_time}
            for t in templates_by_time[:5]
        ]
        
        return stats
    
    def export_templates(self, template_ids: List[str] = None) -> Dict[str, Any]:
        """导出模板
        
        Args:
            template_ids: 要导出的模板ID列表，None表示导出全部
        
        Returns:
            导出的模板数据
        """
        if template_ids is None:
            template_ids = list(self.templates.keys())
        
        export_data = {
            "export_time": datetime.now().isoformat(),
            "templates": []
        }
        
        for template_id in template_ids:
            template = self.templates.get(template_id)
            if template:
                export_data["templates"].append(template.to_dict())
        
        return export_data
    
    def import_templates(self, import_data: Dict[str, Any]) -> Dict[str, Any]:
        """导入模板
        
        Args:
            import_data: 导入的模板数据
        
        Returns:
            导入结果
        """
        result = {
            "success_count": 0,
            "failed_count": 0,
            "errors": []
        }
        
        templates_data = import_data.get("templates", [])
        
        for template_data in templates_data:
            try:
                template = DocumentTemplate(
                    template_id=template_data["template_id"],
                    name=template_data["name"],
                    category=template_data["category"],
                    content=template_data["content"]
                )
                
                # 设置其他属性
                for attr in ["description", "tags", "usage_count"]:
                    if attr in template_data:
                        setattr(template, attr, template_data[attr])
                
                if self.add_template(template):
                    result["success_count"] += 1
                else:
                    result["failed_count"] += 1
                    result["errors"].append(f"添加模板失败: {template.name}")
                    
            except Exception as e:
                result["failed_count"] += 1
                result["errors"].append(f"解析模板数据失败: {str(e)}")
        
        return result


# 全局文书模板管理器实例
document_template_manager = DocumentTemplateManager()


def get_template(template_id: str) -> Optional[DocumentTemplate]:
    """获取模板的便捷函数"""
    return document_template_manager.get_template(template_id)


def search_templates(keyword: str) -> List[DocumentTemplate]:
    """搜索模板的便捷函数"""
    return document_template_manager.search_templates(keyword)


def get_templates_by_category(category: str) -> List[DocumentTemplate]:
    """按分类获取模板的便捷函数"""
    return document_template_manager.get_templates_by_category(category)
