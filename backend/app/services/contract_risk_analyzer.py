"""
合同风险识别系统
基于机器学习和规则的合同风险点识别，包括不公平条款、法律风险和商业风险评估
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from datetime import datetime
import jieba

logger = logging.getLogger(__name__)


class RiskItem:
    """风险项类"""
    
    def __init__(self, risk_id: str, risk_type: str, severity: str, 
                 description: str, location: str = "", suggestion: str = ""):
        """初始化风险项
        
        Args:
            risk_id: 风险唯一标识
            risk_type: 风险类型
            severity: 严重程度 (low/medium/high/critical)
            description: 风险描述
            location: 风险位置
            suggestion: 修改建议
        """
        self.risk_id = risk_id
        self.risk_type = risk_type
        self.severity = severity
        self.description = description
        self.location = location
        self.suggestion = suggestion
        self.confidence = 0.0  # 识别置信度
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "risk_id": self.risk_id,
            "risk_type": self.risk_type,
            "severity": self.severity,
            "description": self.description,
            "location": self.location,
            "suggestion": self.suggestion,
            "confidence": self.confidence
        }


class ContractRiskAnalyzer:
    """合同风险分析器"""
    
    def __init__(self):
        """初始化风险分析器"""
        self.risk_patterns = self._load_risk_patterns()
        self.unfair_clause_patterns = self._load_unfair_clause_patterns()
        self.legal_risk_patterns = self._load_legal_risk_patterns()
        self.commercial_risk_patterns = self._load_commercial_risk_patterns()
        
        # 风险权重配置
        self.risk_weights = {
            "critical": 10,
            "high": 7,
            "medium": 4,
            "low": 1
        }
        
        logger.info("合同风险分析器初始化完成")
    
    def _load_risk_patterns(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载风险识别模式"""
        return {
            "不公平条款": [
                {
                    "pattern": r"甲方.*免责|甲方不承担.*责任",
                    "severity": "high",
                    "description": "甲方免责条款可能过于宽泛",
                    "suggestion": "建议明确甲方免责的具体情形和范围"
                },
                {
                    "pattern": r"乙方承担.*全部责任|乙方负责.*所有",
                    "severity": "medium",
                    "description": "乙方责任条款可能过重",
                    "suggestion": "建议平衡双方责任分配"
                },
                {
                    "pattern": r"单方.*解除|单方.*终止",
                    "severity": "medium",
                    "description": "存在单方解除权条款",
                    "suggestion": "建议明确单方解除的条件和程序"
                }
            ],
            "法律风险": [
                {
                    "pattern": r"违反.*法律|不符合.*规定",
                    "severity": "critical",
                    "description": "可能存在违法条款",
                    "suggestion": "建议删除或修改违法内容"
                },
                {
                    "pattern": r"排除.*适用|不适用.*法律",
                    "severity": "high",
                    "description": "试图排除法律适用",
                    "suggestion": "建议删除排除法律适用的条款"
                },
                {
                    "pattern": r"无效.*条款|条款.*无效",
                    "severity": "medium",
                    "description": "可能存在无效条款",
                    "suggestion": "建议审查条款的有效性"
                }
            ],
            "商业风险": [
                {
                    "pattern": r"价格.*调整|费用.*变更",
                    "severity": "medium",
                    "description": "存在价格调整风险",
                    "suggestion": "建议明确价格调整的条件和幅度"
                },
                {
                    "pattern": r"延期.*交付|推迟.*履行",
                    "severity": "medium",
                    "description": "存在履行延期风险",
                    "suggestion": "建议设置明确的履行期限和违约责任"
                },
                {
                    "pattern": r"不可抗力",
                    "severity": "low",
                    "description": "不可抗力条款需要关注",
                    "suggestion": "建议明确不可抗力的范围和处理程序"
                }
            ]
        }
    
    def _load_unfair_clause_patterns(self) -> List[Dict[str, Any]]:
        """加载不公平条款模式"""
        return [
            {
                "pattern": r"甲方有权.*单方.*决定",
                "severity": "high",
                "description": "甲方单方决定权过大",
                "suggestion": "建议增加乙方的参与权或异议权"
            },
            {
                "pattern": r"乙方不得.*异议|乙方无权.*反对",
                "severity": "medium",
                "description": "限制乙方异议权",
                "suggestion": "建议保留乙方合理的异议权"
            },
            {
                "pattern": r"违约金.*不超过|违约金.*上限",
                "severity": "low",
                "description": "违约金条款需要审查",
                "suggestion": "建议确保违约金数额合理"
            },
            {
                "pattern": r"保证金.*不退还|押金.*没收",
                "severity": "medium",
                "description": "保证金条款可能不公平",
                "suggestion": "建议明确保证金退还条件"
            }
        ]
    
    def _load_legal_risk_patterns(self) -> List[Dict[str, Any]]:
        """加载法律风险模式"""
        return [
            {
                "pattern": r"规避.*税收|避税",
                "severity": "critical",
                "description": "可能涉及税收规避",
                "suggestion": "建议删除涉税规避条款"
            },
            {
                "pattern": r"垄断.*市场|限制.*竞争",
                "severity": "high",
                "description": "可能违反反垄断法",
                "suggestion": "建议审查是否违反竞争法规"
            },
            {
                "pattern": r"内幕.*信息|操纵.*价格",
                "severity": "critical",
                "description": "可能涉及内幕交易",
                "suggestion": "建议删除相关条款"
            },
            {
                "pattern": r"洗钱|资金.*来源不明",
                "severity": "critical",
                "description": "可能涉及洗钱风险",
                "suggestion": "建议增加资金来源合法性条款"
            }
        ]
    
    def _load_commercial_risk_patterns(self) -> List[Dict[str, Any]]:
        """加载商业风险模式"""
        return [
            {
                "pattern": r"汇率.*风险|外汇.*波动",
                "severity": "medium",
                "description": "存在汇率风险",
                "suggestion": "建议增加汇率风险分担条款"
            },
            {
                "pattern": r"市场.*变化|需求.*下降",
                "severity": "medium",
                "description": "存在市场风险",
                "suggestion": "建议增加市场变化应对机制"
            },
            {
                "pattern": r"技术.*过时|产品.*淘汰",
                "severity": "medium",
                "description": "存在技术风险",
                "suggestion": "建议增加技术更新条款"
            },
            {
                "pattern": r"信用.*风险|资金.*短缺",
                "severity": "high",
                "description": "存在信用风险",
                "suggestion": "建议增加信用保障措施"
            }
        ]
    
    def analyze_contract_risks(self, contract_text: str, contract_clauses: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """分析合同风险
        
        Args:
            contract_text: 合同文本
            contract_clauses: 合同条款列表（可选）
        
        Returns:
            风险分析结果
        """
        try:
            # 1. 基于规则的风险识别
            rule_based_risks = self._identify_rule_based_risks(contract_text)
            
            # 2. 不公平条款识别
            unfair_clause_risks = self._identify_unfair_clauses(contract_text)
            
            # 3. 法律风险识别
            legal_risks = self._identify_legal_risks(contract_text)
            
            # 4. 商业风险识别
            commercial_risks = self._identify_commercial_risks(contract_text)
            
            # 5. 条款级别风险分析（如果提供了条款）
            clause_risks = []
            if contract_clauses:
                clause_risks = self._analyze_clause_risks(contract_clauses)
            
            # 6. 综合风险评估
            all_risks = rule_based_risks + unfair_clause_risks + legal_risks + commercial_risks + clause_risks
            risk_summary = self._calculate_risk_summary(all_risks)
            
            # 7. 生成风险报告
            risk_report = self._generate_risk_report(all_risks, risk_summary)
            
            return {
                "risks": [risk.to_dict() for risk in all_risks],
                "risk_summary": risk_summary,
                "risk_report": risk_report,
                "analysis_timestamp": datetime.now().isoformat(),
                "total_risks": len(all_risks),
                "high_severity_count": len([r for r in all_risks if r.severity in ["high", "critical"]])
            }
            
        except Exception as e:
            logger.error(f"合同风险分析失败: {e}")
            return {
                "error": str(e),
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    def _identify_rule_based_risks(self, text: str) -> List[RiskItem]:
        """基于规则识别风险"""
        risks = []
        risk_id = 1
        
        for risk_category, patterns in self.risk_patterns.items():
            for pattern_info in patterns:
                pattern = pattern_info["pattern"]
                matches = list(re.finditer(pattern, text, re.IGNORECASE))
                
                for match in matches:
                    risk = RiskItem(
                        risk_id=f"rule_{risk_id}",
                        risk_type=risk_category,
                        severity=pattern_info["severity"],
                        description=pattern_info["description"],
                        location=f"位置: {match.start()}-{match.end()}",
                        suggestion=pattern_info["suggestion"]
                    )
                    risk.confidence = 0.8  # 规则匹配的置信度
                    risks.append(risk)
                    risk_id += 1
        
        return risks
    
    def _identify_unfair_clauses(self, text: str) -> List[RiskItem]:
        """识别不公平条款"""
        risks = []
        risk_id = 1000
        
        for pattern_info in self.unfair_clause_patterns:
            pattern = pattern_info["pattern"]
            matches = list(re.finditer(pattern, text, re.IGNORECASE))
            
            for match in matches:
                risk = RiskItem(
                    risk_id=f"unfair_{risk_id}",
                    risk_type="不公平条款",
                    severity=pattern_info["severity"],
                    description=pattern_info["description"],
                    location=f"位置: {match.start()}-{match.end()}",
                    suggestion=pattern_info["suggestion"]
                )
                risk.confidence = 0.7
                risks.append(risk)
                risk_id += 1
        
        return risks
    
    def _identify_legal_risks(self, text: str) -> List[RiskItem]:
        """识别法律风险"""
        risks = []
        risk_id = 2000
        
        for pattern_info in self.legal_risk_patterns:
            pattern = pattern_info["pattern"]
            matches = list(re.finditer(pattern, text, re.IGNORECASE))
            
            for match in matches:
                risk = RiskItem(
                    risk_id=f"legal_{risk_id}",
                    risk_type="法律风险",
                    severity=pattern_info["severity"],
                    description=pattern_info["description"],
                    location=f"位置: {match.start()}-{match.end()}",
                    suggestion=pattern_info["suggestion"]
                )
                risk.confidence = 0.9  # 法律风险置信度较高
                risks.append(risk)
                risk_id += 1
        
        return risks
    
    def _identify_commercial_risks(self, text: str) -> List[RiskItem]:
        """识别商业风险"""
        risks = []
        risk_id = 3000
        
        for pattern_info in self.commercial_risk_patterns:
            pattern = pattern_info["pattern"]
            matches = list(re.finditer(pattern, text, re.IGNORECASE))
            
            for match in matches:
                risk = RiskItem(
                    risk_id=f"commercial_{risk_id}",
                    risk_type="商业风险",
                    severity=pattern_info["severity"],
                    description=pattern_info["description"],
                    location=f"位置: {match.start()}-{match.end()}",
                    suggestion=pattern_info["suggestion"]
                )
                risk.confidence = 0.6  # 商业风险置信度中等
                risks.append(risk)
                risk_id += 1
        
        return risks
    
    def _analyze_clause_risks(self, clauses: List[Dict[str, Any]]) -> List[RiskItem]:
        """分析条款级别风险"""
        risks = []
        risk_id = 4000
        
        for clause in clauses:
            clause_content = clause.get("content", "")
            clause_type = clause.get("clause_type", "")
            
            # 根据条款类型进行特定风险检查
            if clause_type == "违约责任":
                # 检查违约金是否过高
                if re.search(r"违约金.*(\d+)%", clause_content):
                    percentage_match = re.search(r"违约金.*(\d+)%", clause_content)
                    if percentage_match:
                        percentage = int(percentage_match.group(1))
                        if percentage > 30:  # 违约金超过30%可能过高
                            risk = RiskItem(
                                risk_id=f"clause_{risk_id}",
                                risk_type="违约责任风险",
                                severity="medium",
                                description=f"违约金比例过高({percentage}%)",
                                location=f"条款: {clause.get('title', '')}",
                                suggestion="建议将违约金调整到合理范围内"
                            )
                            risk.confidence = 0.8
                            risks.append(risk)
                            risk_id += 1
            
            elif clause_type == "争议解决":
                # 检查管辖条款是否合理
                if "管辖" in clause_content and "甲方所在地" in clause_content:
                    risk = RiskItem(
                        risk_id=f"clause_{risk_id}",
                        risk_type="争议解决风险",
                        severity="low",
                        description="管辖法院可能对乙方不利",
                        location=f"条款: {clause.get('title', '')}",
                        suggestion="建议选择中立的管辖法院"
                    )
                    risk.confidence = 0.6
                    risks.append(risk)
                    risk_id += 1
        
        return risks
    
    def _calculate_risk_summary(self, risks: List[RiskItem]) -> Dict[str, Any]:
        """计算风险摘要"""
        if not risks:
            return {
                "total_risks": 0,
                "risk_score": 0,
                "risk_level": "low",
                "severity_distribution": {},
                "type_distribution": {}
            }
        
        # 统计严重程度分布
        severity_counts = defaultdict(int)
        for risk in risks:
            severity_counts[risk.severity] += 1
        
        # 统计风险类型分布
        type_counts = defaultdict(int)
        for risk in risks:
            type_counts[risk.risk_type] += 1
        
        # 计算风险得分
        total_score = sum(self.risk_weights.get(risk.severity, 1) for risk in risks)
        
        # 确定整体风险等级
        if total_score >= 50:
            risk_level = "critical"
        elif total_score >= 30:
            risk_level = "high"
        elif total_score >= 15:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        return {
            "total_risks": len(risks),
            "risk_score": total_score,
            "risk_level": risk_level,
            "severity_distribution": dict(severity_counts),
            "type_distribution": dict(type_counts)
        }
    
    def _generate_risk_report(self, risks: List[RiskItem], summary: Dict[str, Any]) -> str:
        """生成风险报告"""
        if not risks:
            return "本合同未发现明显风险点。"
        
        report_lines = [
            f"合同风险分析报告",
            f"=" * 30,
            f"总体风险等级: {summary['risk_level'].upper()}",
            f"风险总数: {summary['total_risks']}",
            f"风险得分: {summary['risk_score']}",
            "",
            "风险分布:",
        ]
        
        # 添加严重程度分布
        for severity, count in summary['severity_distribution'].items():
            report_lines.append(f"  {severity}: {count}个")
        
        report_lines.append("")
        report_lines.append("主要风险点:")
        
        # 按严重程度排序风险
        sorted_risks = sorted(risks, key=lambda x: self.risk_weights.get(x.severity, 1), reverse=True)
        
        for i, risk in enumerate(sorted_risks[:10], 1):  # 只显示前10个风险
            report_lines.extend([
                f"{i}. [{risk.severity.upper()}] {risk.description}",
                f"   建议: {risk.suggestion}",
                ""
            ])
        
        if len(risks) > 10:
            report_lines.append(f"... 还有 {len(risks) - 10} 个其他风险点")
        
        return "\n".join(report_lines)
    
    def get_risk_suggestions(self, risks: List[RiskItem]) -> List[Dict[str, Any]]:
        """获取风险修改建议"""
        suggestions = []
        
        # 按严重程度分组建议
        critical_risks = [r for r in risks if r.severity == "critical"]
        high_risks = [r for r in risks if r.severity == "high"]
        
        if critical_risks:
            suggestions.append({
                "priority": "urgent",
                "title": "紧急修改建议",
                "items": [risk.suggestion for risk in critical_risks]
            })
        
        if high_risks:
            suggestions.append({
                "priority": "high",
                "title": "重要修改建议", 
                "items": [risk.suggestion for risk in high_risks]
            })
        
        return suggestions


# 全局合同风险分析器实例
contract_risk_analyzer = ContractRiskAnalyzer()


def analyze_contract_risks(contract_text: str, contract_clauses: List[Dict[str, Any]] = None) -> Dict[str, Any]:
    """分析合同风险的便捷函数"""
    return contract_risk_analyzer.analyze_contract_risks(contract_text, contract_clauses)
