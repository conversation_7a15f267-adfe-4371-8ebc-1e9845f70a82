"""
日志配置系统
统一配置应用程序的日志记录，支持多种日志级别和输出格式
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional
import json


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        if hasattr(record, 'endpoint'):
            log_entry['endpoint'] = record.endpoint
        
        return json.dumps(log_entry, ensure_ascii=False)


class LoggingConfig:
    """日志配置类"""
    
    def __init__(self, log_dir: str = "logs", app_name: str = "ai_legal_assistant"):
        """初始化日志配置
        
        Args:
            log_dir: 日志目录
            app_name: 应用名称
        """
        self.log_dir = log_dir
        self.app_name = app_name
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 日志文件路径
        self.log_files = {
            'app': os.path.join(log_dir, f"{app_name}.log"),
            'error': os.path.join(log_dir, f"{app_name}_error.log"),
            'access': os.path.join(log_dir, f"{app_name}_access.log"),
            'performance': os.path.join(log_dir, f"{app_name}_performance.log"),
            'security': os.path.join(log_dir, f"{app_name}_security.log")
        }
    
    def setup_logging(self, level: str = "INFO", enable_console: bool = True, 
                     enable_json: bool = False, max_bytes: int = 10*1024*1024, 
                     backup_count: int = 5):
        """设置日志配置
        
        Args:
            level: 日志级别
            enable_console: 是否启用控制台输出
            enable_json: 是否使用JSON格式
            max_bytes: 日志文件最大大小
            backup_count: 备份文件数量
        """
        # 设置根日志级别
        logging.root.setLevel(getattr(logging, level.upper()))
        
        # 清除现有处理器
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        
        # 创建格式化器
        if enable_json:
            formatter = JSONFormatter()
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        # 控制台处理器
        if enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            if enable_json:
                console_handler.setFormatter(formatter)
            else:
                console_handler.setFormatter(ColoredFormatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                ))
            logging.root.addHandler(console_handler)
        
        # 应用日志文件处理器
        app_handler = logging.handlers.RotatingFileHandler(
            self.log_files['app'],
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        app_handler.setFormatter(formatter)
        logging.root.addHandler(app_handler)
        
        # 错误日志文件处理器
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_files['error'],
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logging.root.addHandler(error_handler)
        
        # 设置特定日志记录器
        self._setup_specific_loggers(formatter, max_bytes, backup_count)
        
        logging.info(f"日志系统初始化完成 - 级别: {level}, JSON格式: {enable_json}")
    
    def _setup_specific_loggers(self, formatter, max_bytes: int, backup_count: int):
        """设置特定的日志记录器"""
        
        # 访问日志记录器
        access_logger = logging.getLogger('access')
        access_handler = logging.handlers.RotatingFileHandler(
            self.log_files['access'],
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        access_handler.setFormatter(formatter)
        access_logger.addHandler(access_handler)
        access_logger.setLevel(logging.INFO)
        access_logger.propagate = False
        
        # 性能日志记录器
        performance_logger = logging.getLogger('performance')
        performance_handler = logging.handlers.RotatingFileHandler(
            self.log_files['performance'],
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        performance_handler.setFormatter(formatter)
        performance_logger.addHandler(performance_handler)
        performance_logger.setLevel(logging.INFO)
        performance_logger.propagate = False
        
        # 安全日志记录器
        security_logger = logging.getLogger('security')
        security_handler = logging.handlers.RotatingFileHandler(
            self.log_files['security'],
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        security_handler.setFormatter(formatter)
        security_logger.addHandler(security_handler)
        security_logger.setLevel(logging.WARNING)
        security_logger.propagate = False
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志记录器"""
        return logging.getLogger(name)
    
    def log_request(self, method: str, url: str, status_code: int, 
                   response_time: float, user_id: str = None, 
                   request_id: str = None):
        """记录请求日志"""
        access_logger = logging.getLogger('access')
        
        log_data = {
            'method': method,
            'url': url,
            'status_code': status_code,
            'response_time': response_time,
            'timestamp': datetime.now().isoformat()
        }
        
        if user_id:
            log_data['user_id'] = user_id
        if request_id:
            log_data['request_id'] = request_id
        
        access_logger.info(json.dumps(log_data, ensure_ascii=False))
    
    def log_performance(self, operation: str, duration: float, 
                       success: bool = True, details: Dict[str, Any] = None):
        """记录性能日志"""
        performance_logger = logging.getLogger('performance')
        
        log_data = {
            'operation': operation,
            'duration': duration,
            'success': success,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            log_data.update(details)
        
        performance_logger.info(json.dumps(log_data, ensure_ascii=False))
    
    def log_security_event(self, event_type: str, severity: str, 
                          description: str, user_id: str = None, 
                          ip_address: str = None, details: Dict[str, Any] = None):
        """记录安全事件日志"""
        security_logger = logging.getLogger('security')
        
        log_data = {
            'event_type': event_type,
            'severity': severity,
            'description': description,
            'timestamp': datetime.now().isoformat()
        }
        
        if user_id:
            log_data['user_id'] = user_id
        if ip_address:
            log_data['ip_address'] = ip_address
        if details:
            log_data.update(details)
        
        level = getattr(logging, severity.upper(), logging.WARNING)
        security_logger.log(level, json.dumps(log_data, ensure_ascii=False))
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            'log_files': {},
            'total_size': 0
        }
        
        for log_type, log_file in self.log_files.items():
            if os.path.exists(log_file):
                size = os.path.getsize(log_file)
                stats['log_files'][log_type] = {
                    'file': log_file,
                    'size_bytes': size,
                    'size_mb': round(size / 1024 / 1024, 2),
                    'modified': datetime.fromtimestamp(os.path.getmtime(log_file)).isoformat()
                }
                stats['total_size'] += size
        
        stats['total_size_mb'] = round(stats['total_size'] / 1024 / 1024, 2)
        
        return stats
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        import glob
        from datetime import timedelta
        
        cutoff_time = datetime.now() - timedelta(days=days)
        cleaned_files = []
        
        # 查找所有日志备份文件
        for log_file in self.log_files.values():
            pattern = f"{log_file}.*"
            for backup_file in glob.glob(pattern):
                if os.path.isfile(backup_file):
                    file_time = datetime.fromtimestamp(os.path.getmtime(backup_file))
                    if file_time < cutoff_time:
                        try:
                            os.remove(backup_file)
                            cleaned_files.append(backup_file)
                        except Exception as e:
                            logging.error(f"删除旧日志文件失败 {backup_file}: {e}")
        
        if cleaned_files:
            logging.info(f"清理了 {len(cleaned_files)} 个旧日志文件")
        
        return cleaned_files


# 全局日志配置实例
logging_config = LoggingConfig()


def setup_logging(level: str = "INFO", enable_console: bool = True, 
                 enable_json: bool = False):
    """设置日志的便捷函数"""
    logging_config.setup_logging(level, enable_console, enable_json)


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器的便捷函数"""
    return logging_config.get_logger(name)


def log_request(method: str, url: str, status_code: int, response_time: float, 
               user_id: str = None, request_id: str = None):
    """记录请求日志的便捷函数"""
    logging_config.log_request(method, url, status_code, response_time, user_id, request_id)


def log_performance(operation: str, duration: float, success: bool = True, 
                   details: Dict[str, Any] = None):
    """记录性能日志的便捷函数"""
    logging_config.log_performance(operation, duration, success, details)


def log_security_event(event_type: str, severity: str, description: str, 
                      user_id: str = None, ip_address: str = None, 
                      details: Dict[str, Any] = None):
    """记录安全事件日志的便捷函数"""
    logging_config.log_security_event(event_type, severity, description, user_id, ip_address, details)
