"""
缓存管理系统
提供多级缓存支持，包括内存缓存和文件缓存，提升系统性能
"""

import logging
import json
import hashlib
import pickle
import os
import time
from typing import Any, Optional, Dict, List
from collections import OrderedDict
from datetime import datetime, timedelta
import threading

logger = logging.getLogger(__name__)


class LRUCache:
    """LRU（最近最少使用）缓存实现"""
    
    def __init__(self, max_size: int = 1000):
        """初始化LRU缓存
        
        Args:
            max_size: 最大缓存条目数
        """
        self.max_size = max_size
        self.cache = OrderedDict()
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                self.hits += 1
                return value
            else:
                self.misses += 1
                return None
    
    def put(self, key: str, value: Any):
        """设置缓存值"""
        with self.lock:
            if key in self.cache:
                # 更新现有值
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # 删除最久未使用的项
                self.cache.popitem(last=False)
            
            self.cache[key] = value
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.hits = 0
            self.misses = 0
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'size': self.size(),
            'max_size': self.max_size,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': self.hit_rate()
        }


class FileCache:
    """文件缓存实现"""
    
    def __init__(self, cache_dir: str = "cache", max_age: int = 3600):
        """初始化文件缓存
        
        Args:
            cache_dir: 缓存目录
            max_age: 缓存最大存活时间（秒）
        """
        self.cache_dir = cache_dir
        self.max_age = max_age
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_path(self, key: str) -> str:
        """获取缓存文件路径"""
        # 使用MD5哈希作为文件名
        hash_key = hashlib.md5(key.encode('utf-8')).hexdigest()
        return os.path.join(self.cache_dir, f"{hash_key}.cache")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        cache_path = self._get_cache_path(key)
        
        try:
            if not os.path.exists(cache_path):
                return None
            
            # 检查文件是否过期
            file_time = os.path.getmtime(cache_path)
            if time.time() - file_time > self.max_age:
                os.remove(cache_path)
                return None
            
            # 读取缓存数据
            with open(cache_path, 'rb') as f:
                return pickle.load(f)
                
        except Exception as e:
            logger.error(f"读取文件缓存失败 {key}: {e}")
            return None
    
    def put(self, key: str, value: Any):
        """设置缓存值"""
        cache_path = self._get_cache_path(key)
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(value, f)
        except Exception as e:
            logger.error(f"写入文件缓存失败 {key}: {e}")
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        cache_path = self._get_cache_path(key)
        
        try:
            if os.path.exists(cache_path):
                os.remove(cache_path)
                return True
            return False
        except Exception as e:
            logger.error(f"删除文件缓存失败 {key}: {e}")
            return False
    
    def clear(self):
        """清空缓存"""
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.cache'):
                    os.remove(os.path.join(self.cache_dir, filename))
        except Exception as e:
            logger.error(f"清空文件缓存失败: {e}")
    
    def cleanup_expired(self):
        """清理过期缓存"""
        try:
            current_time = time.time()
            cleaned_count = 0
            
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.cache'):
                    file_path = os.path.join(self.cache_dir, filename)
                    file_time = os.path.getmtime(file_path)
                    
                    if current_time - file_time > self.max_age:
                        os.remove(file_path)
                        cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期缓存文件")
                
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.cache')]
            total_size = sum(
                os.path.getsize(os.path.join(self.cache_dir, f)) 
                for f in cache_files
            )
            
            return {
                'file_count': len(cache_files),
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / 1024 / 1024, 2),
                'cache_dir': self.cache_dir,
                'max_age': self.max_age
            }
        except Exception as e:
            logger.error(f"获取文件缓存统计失败: {e}")
            return {}


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, memory_cache_size: int = 1000, file_cache_dir: str = "cache", 
                 file_cache_max_age: int = 3600):
        """初始化缓存管理器
        
        Args:
            memory_cache_size: 内存缓存大小
            file_cache_dir: 文件缓存目录
            file_cache_max_age: 文件缓存最大存活时间
        """
        self.memory_cache = LRUCache(memory_cache_size)
        self.file_cache = FileCache(file_cache_dir, file_cache_max_age)
        
        # 启动定期清理任务
        self._start_cleanup_task()
        
        logger.info("缓存管理器初始化完成")
    
    def get(self, key: str, use_file_cache: bool = True) -> Optional[Any]:
        """获取缓存值
        
        Args:
            key: 缓存键
            use_file_cache: 是否使用文件缓存
        
        Returns:
            缓存值或None
        """
        # 首先尝试内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # 如果内存缓存未命中，尝试文件缓存
        if use_file_cache:
            value = self.file_cache.get(key)
            if value is not None:
                # 将文件缓存的值加载到内存缓存
                self.memory_cache.put(key, value)
                return value
        
        return None
    
    def put(self, key: str, value: Any, use_file_cache: bool = True):
        """设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            use_file_cache: 是否使用文件缓存
        """
        # 存储到内存缓存
        self.memory_cache.put(key, value)
        
        # 存储到文件缓存
        if use_file_cache:
            self.file_cache.put(key, value)
    
    def delete(self, key: str):
        """删除缓存项"""
        self.memory_cache.delete(key)
        self.file_cache.delete(key)
    
    def clear(self):
        """清空所有缓存"""
        self.memory_cache.clear()
        self.file_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'memory_cache': self.memory_cache.stats(),
            'file_cache': self.file_cache.stats(),
            'timestamp': datetime.now().isoformat()
        }
    
    def _start_cleanup_task(self):
        """启动定期清理任务"""
        def cleanup_task():
            while True:
                try:
                    time.sleep(3600)  # 每小时清理一次
                    self.file_cache.cleanup_expired()
                except Exception as e:
                    logger.error(f"缓存清理任务出错: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
        cleanup_thread.start()
    
    def cache_decorator(self, key_func=None, ttl: int = 3600, use_file_cache: bool = True):
        """缓存装饰器
        
        Args:
            key_func: 生成缓存键的函数
            ttl: 缓存生存时间
            use_file_cache: 是否使用文件缓存
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                # 生成缓存键
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    # 默认使用函数名和参数生成键
                    key_parts = [func.__name__]
                    key_parts.extend(str(arg) for arg in args)
                    key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                    cache_key = ":".join(key_parts)
                
                # 尝试从缓存获取
                cached_result = self.get(cache_key, use_file_cache)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                self.put(cache_key, result, use_file_cache)
                
                return result
            
            return wrapper
        return decorator


# 全局缓存管理器实例
cache_manager = CacheManager()


def get_cache(key: str, use_file_cache: bool = True) -> Optional[Any]:
    """获取缓存的便捷函数"""
    return cache_manager.get(key, use_file_cache)


def set_cache(key: str, value: Any, use_file_cache: bool = True):
    """设置缓存的便捷函数"""
    cache_manager.put(key, value, use_file_cache)


def delete_cache(key: str):
    """删除缓存的便捷函数"""
    cache_manager.delete(key)


def clear_cache():
    """清空缓存的便捷函数"""
    cache_manager.clear()


def cache_stats() -> Dict[str, Any]:
    """获取缓存统计的便捷函数"""
    return cache_manager.get_stats()


def cached(key_func=None, ttl: int = 3600, use_file_cache: bool = True):
    """缓存装饰器的便捷函数"""
    return cache_manager.cache_decorator(key_func, ttl, use_file_cache)
