"""
性能监控系统
监控系统性能指标，包括响应时间、内存使用、CPU使用率等
"""

import logging
import time
import psutil
import threading
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json
import os

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """性能指标类"""
    
    def __init__(self):
        self.response_times = deque(maxlen=1000)  # 最近1000次请求的响应时间
        self.memory_usage = deque(maxlen=100)     # 最近100次内存使用记录
        self.cpu_usage = deque(maxlen=100)        # 最近100次CPU使用记录
        self.error_count = defaultdict(int)       # 错误计数
        self.request_count = defaultdict(int)     # 请求计数
        self.start_time = datetime.now()
        
    def add_response_time(self, endpoint: str, response_time: float):
        """添加响应时间记录"""
        self.response_times.append({
            'endpoint': endpoint,
            'response_time': response_time,
            'timestamp': datetime.now().isoformat()
        })
        self.request_count[endpoint] += 1
    
    def add_error(self, endpoint: str, error_type: str):
        """添加错误记录"""
        error_key = f"{endpoint}:{error_type}"
        self.error_count[error_key] += 1
    
    def record_system_metrics(self):
        """记录系统指标"""
        try:
            # 内存使用
            memory = psutil.virtual_memory()
            self.memory_usage.append({
                'used_percent': memory.percent,
                'used_mb': memory.used / 1024 / 1024,
                'available_mb': memory.available / 1024 / 1024,
                'timestamp': datetime.now().isoformat()
            })
            
            # CPU使用
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage.append({
                'cpu_percent': cpu_percent,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"记录系统指标失败: {e}")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            'uptime': str(datetime.now() - self.start_time),
            'total_requests': sum(self.request_count.values()),
            'total_errors': sum(self.error_count.values()),
            'avg_response_time': 0.0,
            'current_memory_usage': 0.0,
            'current_cpu_usage': 0.0,
            'error_rate': 0.0
        }
        
        # 计算平均响应时间
        if self.response_times:
            total_time = sum(r['response_time'] for r in self.response_times)
            summary['avg_response_time'] = total_time / len(self.response_times)
        
        # 获取当前系统使用率
        if self.memory_usage:
            summary['current_memory_usage'] = self.memory_usage[-1]['used_percent']
        
        if self.cpu_usage:
            summary['current_cpu_usage'] = self.cpu_usage[-1]['cpu_percent']
        
        # 计算错误率
        total_requests = summary['total_requests']
        if total_requests > 0:
            summary['error_rate'] = summary['total_errors'] / total_requests * 100
        
        return summary


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, log_file: str = "logs/performance.log"):
        """初始化性能监控器
        
        Args:
            log_file: 性能日志文件路径
        """
        self.metrics = PerformanceMetrics()
        self.log_file = log_file
        self.monitoring = False
        self.monitor_thread = None
        
        # 确保日志目录存在
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # 配置性能日志
        self.perf_logger = logging.getLogger('performance')
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.perf_logger.addHandler(handler)
        self.perf_logger.setLevel(logging.INFO)
        
        logger.info("性能监控器初始化完成")
    
    def start_monitoring(self, interval: int = 60):
        """开始监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring:
            logger.warning("性能监控已在运行")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"性能监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("性能监控已停止")
    
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                # 记录系统指标
                self.metrics.record_system_metrics()
                
                # 生成性能报告
                summary = self.metrics.get_summary()
                self.perf_logger.info(f"性能摘要: {json.dumps(summary, ensure_ascii=False)}")
                
                # 检查性能警告
                self._check_performance_alerts(summary)
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"性能监控循环出错: {e}")
                time.sleep(interval)
    
    def _check_performance_alerts(self, summary: Dict[str, Any]):
        """检查性能警告"""
        alerts = []
        
        # 内存使用率警告
        if summary['current_memory_usage'] > 80:
            alerts.append(f"内存使用率过高: {summary['current_memory_usage']:.1f}%")
        
        # CPU使用率警告
        if summary['current_cpu_usage'] > 80:
            alerts.append(f"CPU使用率过高: {summary['current_cpu_usage']:.1f}%")
        
        # 响应时间警告
        if summary['avg_response_time'] > 2.0:
            alerts.append(f"平均响应时间过长: {summary['avg_response_time']:.2f}秒")
        
        # 错误率警告
        if summary['error_rate'] > 5.0:
            alerts.append(f"错误率过高: {summary['error_rate']:.1f}%")
        
        # 记录警告
        for alert in alerts:
            self.perf_logger.warning(f"性能警告: {alert}")
            logger.warning(f"性能警告: {alert}")
    
    def record_request(self, endpoint: str, response_time: float, success: bool = True, error_type: str = None):
        """记录请求性能
        
        Args:
            endpoint: 接口端点
            response_time: 响应时间
            success: 是否成功
            error_type: 错误类型（如果失败）
        """
        self.metrics.add_response_time(endpoint, response_time)
        
        if not success and error_type:
            self.metrics.add_error(endpoint, error_type)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        summary = self.metrics.get_summary()
        
        # 添加详细统计
        report = {
            'summary': summary,
            'endpoint_stats': self._get_endpoint_stats(),
            'system_trends': self._get_system_trends(),
            'error_analysis': self._get_error_analysis(),
            'recommendations': self._get_recommendations(summary)
        }
        
        return report
    
    def _get_endpoint_stats(self) -> Dict[str, Any]:
        """获取接口统计"""
        endpoint_stats = {}
        
        # 按接口分组统计响应时间
        endpoint_times = defaultdict(list)
        for record in self.metrics.response_times:
            endpoint_times[record['endpoint']].append(record['response_time'])
        
        for endpoint, times in endpoint_times.items():
            if times:
                endpoint_stats[endpoint] = {
                    'request_count': len(times),
                    'avg_response_time': sum(times) / len(times),
                    'min_response_time': min(times),
                    'max_response_time': max(times),
                    'p95_response_time': sorted(times)[int(len(times) * 0.95)] if len(times) > 20 else max(times)
                }
        
        return endpoint_stats
    
    def _get_system_trends(self) -> Dict[str, Any]:
        """获取系统趋势"""
        trends = {
            'memory_trend': 'stable',
            'cpu_trend': 'stable',
            'response_time_trend': 'stable'
        }
        
        # 分析内存趋势
        if len(self.metrics.memory_usage) >= 10:
            recent_memory = [m['used_percent'] for m in list(self.metrics.memory_usage)[-10:]]
            early_memory = [m['used_percent'] for m in list(self.metrics.memory_usage)[:10]]
            
            if sum(recent_memory) / len(recent_memory) > sum(early_memory) / len(early_memory) + 5:
                trends['memory_trend'] = 'increasing'
            elif sum(recent_memory) / len(recent_memory) < sum(early_memory) / len(early_memory) - 5:
                trends['memory_trend'] = 'decreasing'
        
        # 分析CPU趋势
        if len(self.metrics.cpu_usage) >= 10:
            recent_cpu = [c['cpu_percent'] for c in list(self.metrics.cpu_usage)[-10:]]
            early_cpu = [c['cpu_percent'] for c in list(self.metrics.cpu_usage)[:10]]
            
            if sum(recent_cpu) / len(recent_cpu) > sum(early_cpu) / len(early_cpu) + 10:
                trends['cpu_trend'] = 'increasing'
            elif sum(recent_cpu) / len(recent_cpu) < sum(early_cpu) / len(early_cpu) - 10:
                trends['cpu_trend'] = 'decreasing'
        
        return trends
    
    def _get_error_analysis(self) -> Dict[str, Any]:
        """获取错误分析"""
        error_analysis = {
            'total_errors': sum(self.metrics.error_count.values()),
            'error_types': dict(self.metrics.error_count),
            'top_error_endpoints': []
        }
        
        # 找出错误最多的接口
        endpoint_errors = defaultdict(int)
        for error_key, count in self.metrics.error_count.items():
            endpoint = error_key.split(':')[0]
            endpoint_errors[endpoint] += count
        
        # 排序并取前5个
        sorted_errors = sorted(endpoint_errors.items(), key=lambda x: x[1], reverse=True)
        error_analysis['top_error_endpoints'] = sorted_errors[:5]
        
        return error_analysis
    
    def _get_recommendations(self, summary: Dict[str, Any]) -> List[str]:
        """获取优化建议"""
        recommendations = []
        
        if summary['current_memory_usage'] > 70:
            recommendations.append("内存使用率较高，建议优化内存使用或增加内存")
        
        if summary['current_cpu_usage'] > 70:
            recommendations.append("CPU使用率较高，建议优化算法或增加CPU资源")
        
        if summary['avg_response_time'] > 1.0:
            recommendations.append("平均响应时间较长，建议优化数据库查询或添加缓存")
        
        if summary['error_rate'] > 2.0:
            recommendations.append("错误率较高，建议检查错误日志并修复相关问题")
        
        if summary['total_requests'] > 10000:
            recommendations.append("请求量较大，建议考虑负载均衡和水平扩展")
        
        if not recommendations:
            recommendations.append("系统运行良好，继续保持")
        
        return recommendations
    
    def export_metrics(self, file_path: str):
        """导出性能指标到文件"""
        try:
            report = self.get_performance_report()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"性能指标已导出到: {file_path}")
        except Exception as e:
            logger.error(f"导出性能指标失败: {e}")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def start_performance_monitoring(interval: int = 60):
    """启动性能监控的便捷函数"""
    performance_monitor.start_monitoring(interval)


def record_request_performance(endpoint: str, response_time: float, success: bool = True, error_type: str = None):
    """记录请求性能的便捷函数"""
    performance_monitor.record_request(endpoint, response_time, success, error_type)


def get_performance_summary() -> Dict[str, Any]:
    """获取性能摘要的便捷函数"""
    return performance_monitor.get_performance_report()
