"""
AI法律助手应用主入口文件
"""

from fastapi import FastAPI, Request, status
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import time
import logging
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.database import init_db
from app.api.v1.api import api_router
from app.core.exceptions import LegalAssistantException
from app.core.middleware import setup_middlewares
from app.middleware.api_gateway import APIGatewayMiddleware
from app.core.response import (
    success_response,
    error_response,
    validation_error_response,
    server_error_response
)

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 AI法律助手应用启动中...")
    
    # 初始化数据库
    await init_db()
    logger.info("✅ 数据库初始化完成")
    
    yield
    
    # 关闭时执行
    logger.info("🛑 AI法律助手应用关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="AI法律助手 - 智能法律服务平台",
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# 设置所有中间件
setup_middlewares(app, settings)

# 添加API网关中间件
app.add_middleware(
    APIGatewayMiddleware,
    enable_rate_limiting=True,
    enable_monitoring=True,
    rate_limit_requests=100,
    rate_limit_window=60
)

# 添加可信主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)


# 全局异常处理器
@app.exception_handler(LegalAssistantException)
async def legal_assistant_exception_handler(request: Request, exc: LegalAssistantException):
    """处理自定义业务异常"""
    logger.error(f"业务异常: {exc.message} - 错误码: {exc.error_code}")
    request_id = getattr(request.state, 'request_id', None)

    # 处理错误详情
    errors = None
    if exc.detail:
        if isinstance(exc.detail, dict) and "errors" in exc.detail:
            # 如果detail包含errors列表，转换为ErrorDetail格式
            errors = [
                {"field": None, "message": error, "code": str(exc.error_code)}
                for error in exc.detail["errors"]
            ]
        else:
            # 其他情况，将detail作为单个错误
            errors = [{"field": None, "message": str(exc.detail), "code": str(exc.error_code)}]

    return error_response(
        message=exc.message,
        code=exc.status_code,
        errors=errors,
        request_id=request_id
    )


@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """处理HTTP异常"""
    logger.error(f"HTTP异常: {exc.detail} - 状态码: {exc.status_code}")
    request_id = getattr(request.state, 'request_id', None)

    return error_response(
        message=exc.detail,
        code=exc.status_code,
        request_id=request_id
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证异常"""
    logger.error(f"请求验证异常: {exc.errors()}")
    request_id = getattr(request.state, 'request_id', None)

    # 转换验证错误格式
    errors = []
    for error in exc.errors():
        errors.append({
            "field": ".".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "code": error["type"]
        })

    return validation_error_response(
        errors=errors,
        request_id=request_id
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理通用异常"""
    logger.error(f"未处理异常: {str(exc)}", exc_info=True)
    request_id = getattr(request.state, 'request_id', None)

    return server_error_response(
        message="服务器内部错误，请稍后重试",
        request_id=request_id
    )


# 健康检查端点
@app.get("/health", tags=["健康检查"])
async def health_check(request: Request):
    """健康检查接口"""
    request_id = getattr(request.state, 'request_id', None)

    return success_response(
        data={
            "status": "healthy",
            "service": settings.PROJECT_NAME,
            "version": settings.VERSION,
        },
        message="服务运行正常",
        request_id=request_id
    )


# 根路径
@app.get("/", tags=["根路径"])
async def root(request: Request):
    """根路径接口"""
    request_id = getattr(request.state, 'request_id', None)

    return success_response(
        data={
            "service": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "docs": "/docs",
            "redoc": "/redoc",
        },
        message=f"欢迎使用{settings.PROJECT_NAME}",
        request_id=request_id
    )


# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
