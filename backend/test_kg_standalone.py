#!/usr/bin/env python3
"""
独立的知识图谱测试
"""

import json
from typing import Dict, List, Tuple, Set, Any, Optional
from collections import defaultdict, deque
import jieba
import jieba.posseg as pseg


class SimpleLegalEntity:
    """简化的法律实体类"""
    
    def __init__(self, entity_id: str, name: str, entity_type: str, properties: Dict[str, Any] = None):
        self.entity_id = entity_id
        self.name = name
        self.entity_type = entity_type
        self.properties = properties or {}
        self.relationships = []
    
    def add_relationship(self, relation_type: str, target_entity: str, properties: Dict[str, Any] = None):
        relationship = {
            "type": relation_type,
            "target": target_entity,
            "properties": properties or {}
        }
        self.relationships.append(relationship)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "entity_id": self.entity_id,
            "name": self.name,
            "type": self.entity_type,
            "properties": self.properties,
            "relationships": self.relationships
        }


class SimpleLegalKnowledgeGraph:
    """简化的法律知识图谱"""
    
    def __init__(self):
        self.entities = {}
        self.entity_index = defaultdict(list)
        self.name_index = {}
        self.relationship_index = defaultdict(list)
        
        self._initialize_legal_knowledge()
    
    def _initialize_legal_knowledge(self):
        """初始化基础法律知识"""
        # 法律法规
        laws = [
            ("民法典", "民法", {"颁布时间": "2020年"}),
            ("劳动合同法", "劳动法", {"颁布时间": "2007年"}),
            ("公司法", "商法", {"颁布时间": "1993年"}),
            ("刑法", "刑法", {"颁布时间": "1979年"})
        ]
        
        for name, category, properties in laws:
            entity_id = f"law_{len(self.entities)}"
            self.add_entity(entity_id, name, "法律法规", properties)
        
        # 法律概念
        concepts = [
            ("合同", "民法概念", {"定义": "当事人之间设立、变更、终止民事权利义务关系的协议"}),
            ("侵权", "民法概念", {"定义": "侵害他人合法权益的行为"}),
            ("犯罪", "刑法概念", {"定义": "危害社会，依法应受刑罚处罚的行为"}),
            ("劳动关系", "劳动法概念", {"定义": "劳动者与用人单位之间的法律关系"})
        ]
        
        for name, category, properties in concepts:
            entity_id = f"concept_{len(self.entities)}"
            self.add_entity(entity_id, name, "法律概念", properties)
        
        # 法律主体
        subjects = [
            ("自然人", "民事主体", {"权利能力": "出生至死亡"}),
            ("法人", "民事主体", {"类型": "营利法人、非营利法人"}),
            ("用人单位", "劳动主体", {"义务": "支付工资、缴纳社保"}),
            ("劳动者", "劳动主体", {"权利": "获得报酬、休息休假"})
        ]
        
        for name, category, properties in subjects:
            entity_id = f"subject_{len(self.entities)}"
            self.add_entity(entity_id, name, "法律主体", properties)
        
        # 建立关系
        self._build_initial_relationships()
    
    def _build_initial_relationships(self):
        """建立初始关系"""
        relationships = [
            ("民法典", "规定", "合同"),
            ("民法典", "规定", "侵权"),
            ("刑法", "规定", "犯罪"),
            ("劳动合同法", "规定", "劳动关系"),
            ("合同", "涉及", "自然人"),
            ("合同", "涉及", "法人")
        ]
        
        for source_name, relation_type, target_name in relationships:
            source_id = self.find_entity_by_name(source_name)
            target_id = self.find_entity_by_name(target_name)
            
            if source_id and target_id:
                self.add_relationship(source_id, relation_type, target_id)
    
    def add_entity(self, entity_id: str, name: str, entity_type: str, properties: Dict[str, Any] = None) -> bool:
        try:
            entity = SimpleLegalEntity(entity_id, name, entity_type, properties)
            self.entities[entity_id] = entity
            self.entity_index[entity_type].append(entity_id)
            self.name_index[name] = entity_id
            return True
        except Exception:
            return False
    
    def add_relationship(self, source_id: str, relation_type: str, target_id: str, properties: Dict[str, Any] = None) -> bool:
        try:
            if source_id not in self.entities or target_id not in self.entities:
                return False
            
            self.entities[source_id].add_relationship(relation_type, target_id, properties)
            self.relationship_index[relation_type].append((source_id, target_id))
            return True
        except Exception:
            return False
    
    def find_entity_by_name(self, name: str) -> Optional[str]:
        return self.name_index.get(name)
    
    def get_entity(self, entity_id: str) -> Optional[SimpleLegalEntity]:
        return self.entities.get(entity_id)
    
    def get_entities_by_type(self, entity_type: str) -> List[SimpleLegalEntity]:
        entity_ids = self.entity_index.get(entity_type, [])
        return [self.entities[eid] for eid in entity_ids if eid in self.entities]
    
    def get_neighbors(self, entity_id: str, direction: str = "out") -> List[Tuple[str, str]]:
        neighbors = []
        
        if direction in ["out", "both"]:
            entity = self.get_entity(entity_id)
            if entity:
                for rel in entity.relationships:
                    neighbors.append((rel["target"], rel["type"]))
        
        if direction in ["in", "both"]:
            for rel_type, relations in self.relationship_index.items():
                for src, tgt in relations:
                    if tgt == entity_id:
                        neighbors.append((src, rel_type))
        
        return neighbors
    
    def extract_entities_from_text(self, text: str) -> List[Tuple[str, str, str]]:
        extracted_entities = []
        
        # 检查已知实体
        for name in self.name_index:
            if name in text:
                entity_id = self.name_index[name]
                entity = self.get_entity(entity_id)
                if entity:
                    extracted_entities.append((name, entity.entity_type, entity_id))
        
        return extracted_entities
    
    def get_statistics(self) -> Dict[str, Any]:
        entity_type_counts = {}
        for entity_type, entity_ids in self.entity_index.items():
            entity_type_counts[entity_type] = len(entity_ids)
        
        relation_type_counts = {}
        for relation_type, relations in self.relationship_index.items():
            relation_type_counts[relation_type] = len(relations)
        
        return {
            "total_entities": len(self.entities),
            "total_relationships": sum(len(relations) for relations in self.relationship_index.values()),
            "entity_types": entity_type_counts,
            "relation_types": relation_type_counts
        }


def test_entity_operations():
    """测试实体操作"""
    kg = SimpleLegalKnowledgeGraph()
    
    # 测试添加实体
    success = kg.add_entity("test_001", "测试法律", "法律法规", {"年份": "2023"})
    assert success, "添加实体失败"
    
    # 测试查找实体
    entity_id = kg.find_entity_by_name("测试法律")
    assert entity_id == "test_001", "查找实体失败"
    
    # 测试获取实体
    entity = kg.get_entity("test_001")
    assert entity is not None, "获取实体失败"
    assert entity.name == "测试法律", "实体名称不匹配"
    
    print("✓ 实体操作测试通过")
    return True


def test_relationship_operations():
    """测试关系操作"""
    kg = SimpleLegalKnowledgeGraph()
    
    # 添加测试实体
    kg.add_entity("entity_1", "实体1", "法律概念")
    kg.add_entity("entity_2", "实体2", "法律概念")
    
    # 测试添加关系
    success = kg.add_relationship("entity_1", "关联", "entity_2", {"强度": "高"})
    assert success, "添加关系失败"
    
    # 测试获取邻居
    neighbors = kg.get_neighbors("entity_1")
    assert len(neighbors) > 0, "获取邻居失败"
    assert neighbors[0][0] == "entity_2", "邻居不匹配"
    
    print("✓ 关系操作测试通过")
    return True


def test_initial_knowledge():
    """测试初始知识"""
    kg = SimpleLegalKnowledgeGraph()
    
    # 检查初始实体
    stats = kg.get_statistics()
    print(f"✓ 初始知识统计: {stats}")
    
    assert stats["total_entities"] > 0, "没有初始实体"
    assert "法律法规" in stats["entity_types"], "缺少法律法规类型"
    assert "法律概念" in stats["entity_types"], "缺少法律概念类型"
    
    # 检查特定实体
    civil_code_id = kg.find_entity_by_name("民法典")
    assert civil_code_id is not None, "找不到民法典"
    
    contract_id = kg.find_entity_by_name("合同")
    assert contract_id is not None, "找不到合同概念"
    
    print("✓ 初始知识测试通过")
    return True


def test_entity_extraction():
    """测试实体提取"""
    kg = SimpleLegalKnowledgeGraph()
    
    # 测试文本
    text = "根据民法典规定，合同是当事人之间设立权利义务关系的协议"
    
    entities = kg.extract_entities_from_text(text)
    print(f"✓ 提取的实体: {entities}")
    
    # 检查是否提取到了预期实体
    entity_names = [entity[0] for entity in entities]
    assert "民法典" in entity_names, "未提取到民法典"
    assert "合同" in entity_names, "未提取到合同"
    
    print("✓ 实体提取测试通过")
    return True


def test_entity_types():
    """测试实体类型"""
    kg = SimpleLegalKnowledgeGraph()
    
    # 获取各类型实体
    laws = kg.get_entities_by_type("法律法规")
    concepts = kg.get_entities_by_type("法律概念")
    subjects = kg.get_entities_by_type("法律主体")
    
    print(f"✓ 实体类型统计:")
    print(f"  法律法规: {len(laws)}个")
    print(f"  法律概念: {len(concepts)}个")
    print(f"  法律主体: {len(subjects)}个")
    
    assert len(laws) > 0, "没有法律法规实体"
    assert len(concepts) > 0, "没有法律概念实体"
    
    print("✓ 实体类型测试通过")
    return True


def test_neighbors():
    """测试邻居查找"""
    kg = SimpleLegalKnowledgeGraph()
    
    # 查找民法典的邻居
    civil_code_id = kg.find_entity_by_name("民法典")
    if civil_code_id:
        neighbors = kg.get_neighbors(civil_code_id, direction="both")
        print(f"✓ 民法典的邻居: {len(neighbors)}个")
        
        for neighbor_id, relation_type in neighbors:
            neighbor = kg.get_entity(neighbor_id)
            if neighbor:
                print(f"  - {neighbor.name} ({relation_type})")
    
    print("✓ 邻居查找测试通过")
    return True


def test_jieba_integration():
    """测试jieba集成"""
    try:
        text = "根据劳动合同法规定，用人单位应当与劳动者签订书面劳动合同"
        
        # 测试分词
        tokens = list(jieba.cut(text))
        print(f"✓ jieba分词: {tokens}")
        
        # 测试词性标注
        words = list(pseg.cut(text))
        pos_tags = [(w.word, w.flag) for w in words]
        print(f"✓ 词性标注: {pos_tags[:5]}...")  # 只显示前5个
        
        return True
        
    except Exception as e:
        print(f"✗ jieba集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始独立知识图谱测试...")
    print("=" * 50)
    
    tests = [
        ("实体操作测试", test_entity_operations),
        ("关系操作测试", test_relationship_operations),
        ("初始知识测试", test_initial_knowledge),
        ("实体提取测试", test_entity_extraction),
        ("实体类型测试", test_entity_types),
        ("邻居查找测试", test_neighbors),
        ("jieba集成测试", test_jieba_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
