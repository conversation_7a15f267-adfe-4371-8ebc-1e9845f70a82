#!/usr/bin/env python3
"""
独立的智能问答引擎测试
"""

import re
from typing import Dict, List, Any, Optional
import jieba


class SimpleIntelligentQA:
    """简化的智能问答引擎"""
    
    def __init__(self):
        self.intent_patterns = {
            "咨询": [r"请问|咨询|想了解|想知道|如何|怎么|什么是"],
            "投诉": [r"投诉|举报|控告|违法|违规|不合理"],
            "申请": [r"申请|办理|提交|如何申请|申请流程"],
            "查询": [r"查询|查看|查找|搜索|案件进度"],
            "求助": [r"求助|帮助|救助|紧急|急需|怎么办"]
        }
        
        self.knowledge_base = {
            "合同": "合同是民事主体之间设立、变更、终止民事法律关系的协议。",
            "劳动合同": "建立劳动关系，应当订立书面劳动合同。",
            "违约": "当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担违约责任。",
            "工资": "工资应当以货币形式按月支付给劳动者本人。",
            "离婚": "夫妻感情确已破裂，调解无效的，应当准予离婚。"
        }
        
        self.entities = {
            "民法典": {"type": "法律法规", "properties": {"颁布时间": "2020年"}},
            "劳动合同法": {"type": "法律法规", "properties": {"颁布时间": "2007年"}},
            "合同": {"type": "法律概念", "properties": {"定义": "当事人之间设立权利义务关系的协议"}},
            "侵权": {"type": "法律概念", "properties": {"定义": "侵害他人合法权益的行为"}}
        }
    
    def classify_intent(self, question: str) -> Dict[str, Any]:
        """分类意图"""
        intent_scores = {}
        
        for intent, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, question, re.IGNORECASE):
                    score += 1
            intent_scores[intent] = score
        
        if intent_scores and max(intent_scores.values()) > 0:
            best_intent = max(intent_scores, key=intent_scores.get)
            confidence = intent_scores[best_intent] / sum(intent_scores.values())
        else:
            best_intent = "未知"
            confidence = 0.0
        
        return {
            "intent": best_intent,
            "confidence": confidence,
            "scores": intent_scores
        }
    
    def extract_entities(self, question: str) -> List[Dict[str, Any]]:
        """提取实体"""
        entities = []
        
        # 检查知识库中的实体
        for entity_name, entity_info in self.entities.items():
            if entity_name in question:
                entities.append({
                    "name": entity_name,
                    "type": entity_info["type"],
                    "properties": entity_info.get("properties", {})
                })
        
        # 提取金额
        amounts = re.findall(r'\d+(?:\.\d+)?(?:万|千|百)?元', question)
        for amount in amounts:
            entities.append({
                "name": amount,
                "type": "金额",
                "properties": {}
            })
        
        return entities
    
    def analyze_semantics(self, question: str) -> Dict[str, Any]:
        """语义分析"""
        tokens = list(jieba.cut(question))
        
        legal_concepts = []
        legal_keywords = ["法律", "法规", "条例", "规定", "权利", "义务", "责任"]
        
        for token in tokens:
            if token in legal_keywords or token in self.knowledge_base:
                legal_concepts.append(token)
        
        return {
            "tokens": tokens,
            "legal_concepts": legal_concepts,
            "complexity": len(set(tokens)) / len(tokens) if tokens else 0
        }
    
    def retrieve_knowledge(self, question: str, entities: List[Dict[str, Any]]) -> List[str]:
        """检索知识"""
        results = []
        
        # 基于实体检索
        for entity in entities:
            entity_name = entity["name"]
            if entity_name in self.knowledge_base:
                results.append(self.knowledge_base[entity_name])
        
        # 基于关键词检索
        for keyword, answer in self.knowledge_base.items():
            if keyword in question and answer not in results:
                results.append(answer)
        
        return results
    
    def generate_answer(self, question: str, intent: str, entities: List[Dict[str, Any]], knowledge: List[str]) -> str:
        """生成答案"""
        # 如果有知识匹配，优先使用
        if knowledge:
            return knowledge[0]
        
        # 基于意图生成答案
        if intent == "咨询":
            if any(keyword in question for keyword in ["合同", "协议"]):
                return "合同是民事主体之间设立、变更、终止民事法律关系的协议。合同依法成立，自成立时生效。"
            elif any(keyword in question for keyword in ["劳动", "工作"]):
                return "劳动法保护劳动者的合法权益。建立劳动关系应当订立书面劳动合同。"
            else:
                return "根据您的咨询，建议您详细描述具体情况，以便我为您提供更准确的法律建议。"
        elif intent == "投诉":
            return "如果您需要投诉，建议您收集相关证据，并向相关监管部门举报或寻求法律援助。"
        elif intent == "申请":
            return "关于您要申请的事项，建议您先了解相关的申请条件和所需材料，必要时咨询专业律师。"
        elif intent == "查询":
            return "您可以通过相关官方渠道查询所需信息，注意保护个人隐私。"
        elif intent == "求助":
            return "如遇紧急情况请立即报警，其他法律问题建议寻求专业法律援助。"
        else:
            return "抱歉，我无法完全理解您的问题。请您提供更多详细信息，或者重新描述您的问题。"
    
    def calculate_confidence(self, intent_confidence: float, entity_count: int, knowledge_count: int) -> float:
        """计算置信度"""
        entity_score = min(entity_count * 0.2, 1.0)
        knowledge_score = min(knowledge_count * 0.3, 1.0)
        
        confidence = 0.4 * intent_confidence + 0.3 * entity_score + 0.3 * knowledge_score
        return min(confidence, 1.0)
    
    def process_question(self, question: str) -> Dict[str, Any]:
        """处理问题"""
        # 意图识别
        intent_result = self.classify_intent(question)
        
        # 实体提取
        entities = self.extract_entities(question)
        
        # 语义分析
        semantic_info = self.analyze_semantics(question)
        
        # 知识检索
        knowledge = self.retrieve_knowledge(question, entities)
        
        # 答案生成
        answer = self.generate_answer(question, intent_result["intent"], entities, knowledge)
        
        # 置信度计算
        confidence = self.calculate_confidence(
            intent_result["confidence"],
            len(entities),
            len(knowledge)
        )
        
        return {
            "question": question,
            "intent": intent_result,
            "entities": entities,
            "semantic_info": semantic_info,
            "knowledge": knowledge,
            "answer": answer,
            "confidence": confidence
        }


def test_intent_classification():
    """测试意图分类"""
    qa = SimpleIntelligentQA()
    
    test_cases = [
        ("请问劳动合同法是怎么规定的？", "咨询"),
        ("我要投诉这家公司违法用工", "投诉"),
        ("如何申请劳动仲裁？", "申请"),
        ("查询我的案件处理进度", "查询"),
        ("紧急求助，不知道该怎么办", "求助")
    ]
    
    correct = 0
    print("✓ 意图分类测试:")
    for question, expected in test_cases:
        result = qa.classify_intent(question)
        predicted = result["intent"]
        confidence = result["confidence"]
        
        is_correct = predicted == expected
        if is_correct:
            correct += 1
        
        status = "✓" if is_correct else "✗"
        print(f"  {status} '{question[:25]}...' -> {predicted} (置信度: {confidence:.2f})")
    
    accuracy = correct / len(test_cases)
    print(f"  准确率: {accuracy:.2%}")
    
    return accuracy >= 0.6


def test_entity_extraction():
    """测试实体提取"""
    qa = SimpleIntelligentQA()
    
    test_questions = [
        "根据民法典规定，合同的效力如何？",
        "我需要赔偿5000元，这合理吗？",
        "劳动合同法对工资支付有什么要求？"
    ]
    
    print("✓ 实体提取测试:")
    for question in test_questions:
        entities = qa.extract_entities(question)
        print(f"  '{question[:30]}...' -> {len(entities)}个实体")
        for entity in entities:
            print(f"    - {entity['name']} ({entity['type']})")
    
    return True


def test_semantic_analysis():
    """测试语义分析"""
    qa = SimpleIntelligentQA()
    
    test_questions = [
        "根据劳动合同法规定，用人单位应当支付工资",
        "民法典对合同的定义是什么？"
    ]
    
    print("✓ 语义分析测试:")
    for question in test_questions:
        semantic_info = qa.analyze_semantics(question)
        print(f"  '{question[:30]}...'")
        print(f"    分词: {semantic_info['tokens'][:5]}...")
        print(f"    法律概念: {semantic_info['legal_concepts']}")
        print(f"    复杂度: {semantic_info['complexity']:.2f}")
    
    return True


def test_knowledge_retrieval():
    """测试知识检索"""
    qa = SimpleIntelligentQA()
    
    test_questions = [
        "什么是合同？",
        "劳动合同有什么规定？",
        "违约责任如何承担？"
    ]
    
    print("✓ 知识检索测试:")
    for question in test_questions:
        entities = qa.extract_entities(question)
        knowledge = qa.retrieve_knowledge(question, entities)
        
        print(f"  '{question}' -> {len(knowledge)}个知识结果")
        for k in knowledge:
            print(f"    - {k[:40]}...")
    
    return True


def test_answer_generation():
    """测试答案生成"""
    qa = SimpleIntelligentQA()
    
    test_questions = [
        "什么是合同？",
        "请问劳动法有什么规定？",
        "我要投诉公司",
        "如何申请仲裁？"
    ]
    
    print("✓ 答案生成测试:")
    for question in test_questions:
        result = qa.process_question(question)
        
        print(f"  问题: {question}")
        print(f"  答案: {result['answer'][:50]}...")
        print(f"  置信度: {result['confidence']:.2f}")
        print()
    
    return True


def test_comprehensive_qa():
    """测试综合问答"""
    qa = SimpleIntelligentQA()
    
    complex_questions = [
        "根据民法典规定，合同的效力如何确定？",
        "我是员工，公司拖欠工资怎么办？",
        "离婚时财产如何分割？"
    ]
    
    print("✓ 综合问答测试:")
    for question in complex_questions:
        result = qa.process_question(question)
        
        print(f"  问题: {question}")
        print(f"  意图: {result['intent']['intent']} (置信度: {result['intent']['confidence']:.2f})")
        print(f"  实体: {len(result['entities'])}个")
        print(f"  知识匹配: {len(result['knowledge'])}个")
        print(f"  答案: {result['answer'][:60]}...")
        print(f"  总置信度: {result['confidence']:.2f}")
        print()
    
    return True


def test_jieba_integration():
    """测试jieba集成"""
    try:
        text = "根据劳动合同法规定，用人单位应当与劳动者签订书面劳动合同"
        tokens = list(jieba.cut(text))
        
        print(f"✓ jieba分词测试: {tokens[:8]}...")
        
        # 检查关键词是否正确分词
        expected_tokens = ["劳动合同法", "用人单位", "劳动者", "劳动合同"]
        found_tokens = [token for token in expected_tokens if token in tokens]
        
        print(f"  关键法律术语识别: {found_tokens}")
        
        return len(found_tokens) > 0
        
    except Exception as e:
        print(f"✗ jieba集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始独立智能问答引擎测试...")
    print("=" * 50)
    
    tests = [
        ("意图分类测试", test_intent_classification),
        ("实体提取测试", test_entity_extraction),
        ("语义分析测试", test_semantic_analysis),
        ("知识检索测试", test_knowledge_retrieval),
        ("答案生成测试", test_answer_generation),
        ("综合问答测试", test_comprehensive_qa),
        ("jieba集成测试", test_jieba_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
