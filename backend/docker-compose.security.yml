version: '3.8'

services:
  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-legal-assistant-app
    restart: unless-stopped
    environment:
      # 数据库配置
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      - DATABASE_SSL_MODE=require
      
      # Redis配置
      - REDIS_URL=redis://redis:6379/0
      - REDIS_SSL=false
      
      # 加密配置
      - ENCRYPTION_MASTER_KEY=${ENCRYPTION_MASTER_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      
      # HTTPS和SSL配置
      - FORCE_HTTPS=true
      - SSL_CERT_PATH=/app/certs/cert.pem
      - SSL_KEY_PATH=/app/certs/private.key
      
      # 安全配置
      - TRUSTED_HOSTS=localhost,127.0.0.1,*.yourdomain.com
      - CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
      - CORS_ALLOW_CREDENTIALS=true
      
      # 速率限制配置
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_REQUESTS=100
      - RATE_LIMIT_WINDOW=60
      
      # 安全头配置
      - SECURITY_HEADERS_ENABLED=true
      
      # 审计日志配置
      - AUDIT_LOG_ENABLED=true
      - AUDIT_LOG_LEVEL=INFO
      - AUDIT_LOG_FILE=/app/logs/audit.log
      
      # 数据脱敏配置
      - DATA_MASKING_ENABLED=true
      - MASK_EMAIL=true
      - MASK_PHONE=true
      - MASK_ID_CARD=true
      - MASK_BANK_CARD=true
      
      # 合规配置
      - GDPR_COMPLIANCE=true
      - CCPA_COMPLIANCE=true
      - DATA_ANONYMIZATION=true
      
      # 监控配置
      - SECURITY_MONITORING_ENABLED=true
      - FAILED_LOGIN_THRESHOLD=5
      - FAILED_LOGIN_WINDOW=300
      - ALERT_EMAIL=${ALERT_EMAIL}
      
      # 数据保留策略
      - DATA_RETENTION_DAYS=2555  # 7年
      - LOG_RETENTION_DAYS=365    # 1年
      - SESSION_RETENTION_DAYS=30 # 30天
      
    volumes:
      - ./certs:/app/certs:ro
      - ./logs:/app/logs
      - ./backups:/app/backups
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    networks:
      - ai-legal-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: ai-legal-assistant-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db-init:/docker-entrypoint-initdb.d
      - ./db-certs:/var/lib/postgresql/certs:ro
    ports:
      - "5432:5432"
    networks:
      - ai-legal-network
    command: >
      postgres
      -c ssl=on
      -c ssl_cert_file=/var/lib/postgresql/certs/server.crt
      -c ssl_key_file=/var/lib/postgresql/certs/server.key
      -c ssl_ca_file=/var/lib/postgresql/certs/ca.crt
      -c log_statement=all
      -c log_destination=stderr
      -c logging_collector=on
      -c log_directory=/var/log/postgresql
      -c log_filename=postgresql-%Y-%m-%d.log
      -c log_rotation_age=1d
      -c log_rotation_size=100MB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ai-legal-assistant-redis
    restart: unless-stopped
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - ai-legal-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch (用于日志分析)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: ai-legal-assistant-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD}
      - xpack.security.http.ssl.enabled=true
      - xpack.security.http.ssl.key=/usr/share/elasticsearch/config/certs/elasticsearch.key
      - xpack.security.http.ssl.certificate=/usr/share/elasticsearch/config/certs/elasticsearch.crt
      - xpack.security.http.ssl.certificate_authorities=/usr/share/elasticsearch/config/certs/ca.crt
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - ./elastic-certs:/usr/share/elasticsearch/config/certs:ro
    ports:
      - "9200:9200"
    networks:
      - ai-legal-network
    healthcheck:
      test: ["CMD-SHELL", "curl -s --cacert /usr/share/elasticsearch/config/certs/ca.crt -u elastic:${ELASTIC_PASSWORD} https://localhost:9200 | grep -q 'cluster_name'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana (日志可视化)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: ai-legal-assistant-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=https://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=${ELASTIC_PASSWORD}
      - ELASTICSEARCH_SSL_CERTIFICATEAUTHORITIES=/usr/share/kibana/config/certs/ca.crt
      - SERVER_SSL_ENABLED=true
      - SERVER_SSL_CERTIFICATE=/usr/share/kibana/config/certs/kibana.crt
      - SERVER_SSL_KEY=/usr/share/kibana/config/certs/kibana.key
    volumes:
      - ./elastic-certs:/usr/share/kibana/config/certs:ro
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - ai-legal-network
    healthcheck:
      test: ["CMD-SHELL", "curl -s -I --cacert /usr/share/kibana/config/certs/ca.crt https://localhost:5601 | grep -q 'HTTP/1.1 200 OK'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ai-legal-assistant-nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./certs:/etc/nginx/certs:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - app
    networks:
      - ai-legal-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务 (Prometheus)
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-legal-assistant-prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ai-legal-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # 告警管理 (Alertmanager)
  alertmanager:
    image: prom/alertmanager:latest
    container_name: ai-legal-assistant-alertmanager
    restart: unless-stopped
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    ports:
      - "9093:9093"
    networks:
      - ai-legal-network

  # 备份服务
  backup:
    image: postgres:15-alpine
    container_name: ai-legal-assistant-backup
    restart: "no"
    environment:
      - PGPASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./backup-scripts:/scripts:ro
    depends_on:
      - db
    networks:
      - ai-legal-network
    entrypoint: ["/scripts/backup.sh"]

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  alertmanager_data:
    driver: local

networks:
  ai-legal-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
