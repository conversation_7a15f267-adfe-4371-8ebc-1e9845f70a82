#!/usr/bin/env python3
"""
智能问答引擎测试
"""

import sys
import os

# 添加app目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_qa_engine_import():
    """测试问答引擎导入"""
    try:
        from services.intelligent_qa import IntelligentLegalQA, intelligent_qa, ask_legal_question
        print("✓ 智能问答引擎导入成功")
        return True
    except ImportError as e:
        print(f"✗ 智能问答引擎导入失败: {e}")
        return False

def test_basic_qa():
    """测试基本问答功能"""
    try:
        from services.intelligent_qa import ask_legal_question
        
        test_questions = [
            "请问什么是合同？",
            "我想咨询劳动法相关问题",
            "如何申请劳动仲裁？",
            "我要投诉公司违法行为",
            "查询案件进度怎么办？"
        ]
        
        print("✓ 基本问答测试:")
        for question in test_questions:
            result = ask_legal_question(question)
            
            print(f"  问题: {question}")
            print(f"  意图: {result['intent']['intent']} (置信度: {result['intent']['confidence']:.2f})")
            print(f"  答案: {result['answer'][:50]}...")
            print(f"  置信度: {result['confidence']:.2f}")
            print()
        
        return True
        
    except Exception as e:
        print(f"✗ 基本问答测试失败: {e}")
        return False

def test_intent_classification():
    """测试意图分类"""
    try:
        from services.intelligent_qa import intelligent_qa
        
        test_cases = [
            ("请问劳动合同法是怎么规定的？", "咨询"),
            ("我要投诉这家公司违法用工", "投诉"),
            ("如何申请劳动仲裁？", "申请"),
            ("查询我的案件处理进度", "查询"),
            ("紧急求助，不知道该怎么办", "求助")
        ]
        
        correct_predictions = 0
        total_predictions = len(test_cases)
        
        print("✓ 意图分类测试:")
        for question, expected_intent in test_cases:
            intent_result = intelligent_qa._classify_intent(question)
            predicted_intent = intent_result["intent"]
            confidence = intent_result["confidence"]
            
            is_correct = predicted_intent == expected_intent
            if is_correct:
                correct_predictions += 1
            
            status = "✓" if is_correct else "✗"
            print(f"  {status} '{question[:25]}...' -> {predicted_intent} (置信度: {confidence:.2f})")
        
        accuracy = correct_predictions / total_predictions
        print(f"  准确率: {accuracy:.2%} ({correct_predictions}/{total_predictions})")
        
        return accuracy >= 0.6
        
    except Exception as e:
        print(f"✗ 意图分类测试失败: {e}")
        return False

def test_entity_extraction():
    """测试实体提取"""
    try:
        from services.intelligent_qa import intelligent_qa
        
        test_questions = [
            "根据民法典规定，合同的效力如何？",
            "劳动合同法对工资支付有什么要求？",
            "我需要赔偿5000元，这合理吗？",
            "合同期限为2年，可以提前解除吗？"
        ]
        
        print("✓ 实体提取测试:")
        for question in test_questions:
            entities = intelligent_qa._extract_entities(question)
            print(f"  '{question[:30]}...' -> {len(entities)}个实体")
            for entity in entities:
                print(f"    - {entity['name']} ({entity['type']})")
        
        return True
        
    except Exception as e:
        print(f"✗ 实体提取测试失败: {e}")
        return False

def test_semantic_analysis():
    """测试语义分析"""
    try:
        from services.intelligent_qa import intelligent_qa
        
        test_questions = [
            "根据劳动合同法规定，用人单位应当支付工资",
            "民法典对合同的定义是什么？",
            "刑法中关于故意犯罪的规定"
        ]
        
        print("✓ 语义分析测试:")
        for question in test_questions:
            semantic_info = intelligent_qa._analyze_semantics(question)
            print(f"  '{question[:30]}...'")
            print(f"    分词: {semantic_info['tokens'][:5]}...")
            print(f"    法律概念: {len(semantic_info['legal_concepts'])}个")
            print(f"    复杂度: {semantic_info['complexity']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 语义分析测试失败: {e}")
        return False

def test_knowledge_retrieval():
    """测试知识检索"""
    try:
        from services.intelligent_qa import intelligent_qa
        
        test_questions = [
            "什么是合同？",
            "民法典的内容是什么？",
            "劳动法有哪些规定？"
        ]
        
        print("✓ 知识检索测试:")
        for question in test_questions:
            entities = intelligent_qa._extract_entities(question)
            knowledge_results = intelligent_qa._retrieve_knowledge(question, entities)
            
            print(f"  '{question}' -> {len(knowledge_results)}个知识结果")
            for result in knowledge_results:
                print(f"    - {result['type']}: {list(result.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ 知识检索测试失败: {e}")
        return False

def test_answer_generation():
    """测试答案生成"""
    try:
        from services.intelligent_qa import intelligent_qa
        
        test_questions = [
            "什么是合同？",
            "请问劳动法有什么规定？",
            "我要投诉公司",
            "如何申请仲裁？"
        ]
        
        print("✓ 答案生成测试:")
        for question in test_questions:
            intent_result = intelligent_qa._classify_intent(question)
            entities = intelligent_qa._extract_entities(question)
            semantic_info = intelligent_qa._analyze_semantics(question)
            knowledge_results = intelligent_qa._retrieve_knowledge(question, entities)
            
            answer = intelligent_qa._generate_answer(
                question, intent_result, entities, semantic_info, knowledge_results
            )
            
            print(f"  问题: {question}")
            print(f"  答案: {answer[:60]}...")
            print()
        
        return True
        
    except Exception as e:
        print(f"✗ 答案生成测试失败: {e}")
        return False

def test_confidence_calculation():
    """测试置信度计算"""
    try:
        from services.intelligent_qa import intelligent_qa
        
        # 模拟不同质量的结果
        test_cases = [
            # 高质量：明确意图 + 多个实体 + 知识匹配
            {
                "intent_result": {"intent": "咨询", "confidence": 0.9},
                "entities": [{"name": "合同", "type": "法律概念"}],
                "knowledge_results": [{"type": "answer_pattern", "category": "合同相关"}]
            },
            # 中等质量：模糊意图 + 少量实体
            {
                "intent_result": {"intent": "咨询", "confidence": 0.5},
                "entities": [],
                "knowledge_results": []
            },
            # 低质量：未知意图 + 无实体 + 无知识匹配
            {
                "intent_result": {"intent": "未知", "confidence": 0.0},
                "entities": [],
                "knowledge_results": []
            }
        ]
        
        print("✓ 置信度计算测试:")
        for i, case in enumerate(test_cases):
            confidence = intelligent_qa._calculate_confidence(
                case["intent_result"],
                case["entities"],
                case["knowledge_results"]
            )
            print(f"  案例{i+1}: 置信度 {confidence:.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 置信度计算测试失败: {e}")
        return False

def test_suggestions_generation():
    """测试建议生成"""
    try:
        from services.intelligent_qa import intelligent_qa
        
        intents = ["咨询", "投诉", "申请", "查询", "求助"]
        
        print("✓ 建议生成测试:")
        for intent in intents:
            suggestions = intelligent_qa._generate_suggestions(intent)
            print(f"  {intent}: {len(suggestions)}个建议")
            for suggestion in suggestions[:2]:  # 只显示前2个
                print(f"    - {suggestion}")
        
        return True
        
    except Exception as e:
        print(f"✗ 建议生成测试失败: {e}")
        return False

def test_context_management():
    """测试上下文管理"""
    try:
        from services.intelligent_qa import intelligent_qa
        
        user_id = "test_user"
        
        # 模拟多轮对话
        conversations = [
            ("什么是合同？", "咨询"),
            ("合同的效力如何？", "咨询"),
            ("我要投诉违约行为", "投诉")
        ]
        
        print("✓ 上下文管理测试:")
        for question, intent in conversations:
            intelligent_qa._update_context(
                user_id, 
                question, 
                f"关于{intent}的回答", 
                {"intent": intent}
            )
        
        context = intelligent_qa.conversation_context.get(user_id, [])
        print(f"  用户{user_id}的对话历史: {len(context)}轮")
        
        for i, entry in enumerate(context):
            print(f"    {i+1}. {entry['question'][:20]}... -> {entry['intent']}")
        
        return len(context) == 3
        
    except Exception as e:
        print(f"✗ 上下文管理测试失败: {e}")
        return False

def test_comprehensive_qa():
    """测试综合问答"""
    try:
        from services.intelligent_qa import ask_legal_question
        
        complex_questions = [
            "根据民法典规定，如果合同中约定的违约金过高，法院会如何处理？",
            "我是一名员工，公司要求我签署竞业限制协议，这合法吗？",
            "离婚时夫妻共同财产如何分割？有什么法律依据？"
        ]
        
        print("✓ 综合问答测试:")
        for question in complex_questions:
            result = ask_legal_question(question)
            
            print(f"  问题: {question[:40]}...")
            print(f"  意图: {result['intent']['intent']}")
            print(f"  实体数: {len(result['entities'])}")
            print(f"  知识匹配: {len(result['knowledge_results'])}")
            print(f"  置信度: {result['confidence']:.2f}")
            print(f"  答案: {result['answer'][:80]}...")
            print(f"  建议数: {len(result['suggestions'])}")
            print()
        
        return True
        
    except Exception as e:
        print(f"✗ 综合问答测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始智能问答引擎测试...")
    print("=" * 60)
    
    tests = [
        ("问答引擎导入测试", test_qa_engine_import),
        ("基本问答测试", test_basic_qa),
        ("意图分类测试", test_intent_classification),
        ("实体提取测试", test_entity_extraction),
        ("语义分析测试", test_semantic_analysis),
        ("知识检索测试", test_knowledge_retrieval),
        ("答案生成测试", test_answer_generation),
        ("置信度计算测试", test_confidence_calculation),
        ("建议生成测试", test_suggestions_generation),
        ("上下文管理测试", test_context_management),
        ("综合问答测试", test_comprehensive_qa),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
