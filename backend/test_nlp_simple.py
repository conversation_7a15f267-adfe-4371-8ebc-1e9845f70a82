#!/usr/bin/env python3
"""
简化的NLP功能测试
"""

import sys
import os

# 添加app目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_basic_imports():
    """测试基本导入"""
    try:
        import jieba
        import numpy as np
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.metrics.pairwise import cosine_similarity
        print("✓ 基本依赖导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_jieba_tokenization():
    """测试jieba分词"""
    try:
        import jieba
        
        # 测试基本分词
        text = "我想咨询劳动合同的相关法律问题"
        tokens = list(jieba.cut(text))
        
        print(f"✓ 分词测试成功: {tokens}")
        
        # 测试词性标注
        import jieba.posseg as pseg
        words = list(pseg.cut(text))
        print(f"✓ 词性标注测试成功: {[(w.word, w.flag) for w in words]}")
        
        return True
    except Exception as e:
        print(f"✗ jieba测试失败: {e}")
        return False

def test_tfidf_similarity():
    """测试TF-IDF和余弦相似度"""
    try:
        import jieba
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.metrics.pairwise import cosine_similarity
        
        # 测试文本
        texts = [
            "劳动合同纠纷处理方法",
            "处理劳动合同争议的步骤",
            "房屋买卖合同问题"
        ]
        
        # 使用jieba分词
        def tokenize(text):
            return ' '.join(jieba.cut(text))
        
        tokenized_texts = [tokenize(text) for text in texts]
        
        # TF-IDF向量化
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform(tokenized_texts)
        
        # 计算相似度
        similarity_matrix = cosine_similarity(tfidf_matrix)
        
        print("✓ TF-IDF和余弦相似度测试成功")
        print(f"相似度矩阵:\n{similarity_matrix}")
        
        # 验证第一个和第二个文本的相似度应该比第一个和第三个文本的相似度高
        sim_12 = similarity_matrix[0, 1]
        sim_13 = similarity_matrix[0, 2]
        
        if sim_12 > sim_13:
            print("✓ 相似度计算逻辑正确")
        else:
            print("⚠ 相似度计算可能有问题")
        
        return True
    except Exception as e:
        print(f"✗ TF-IDF测试失败: {e}")
        return False

def test_legal_dict_loading():
    """测试法律词典加载"""
    try:
        import jieba
        
        # 检查法律词典文件是否存在
        legal_dict_path = "../data/legal_dict.txt"
        if os.path.exists(legal_dict_path):
            jieba.load_userdict(legal_dict_path)
            print("✓ 法律词典加载成功")
        else:
            print("⚠ 法律词典文件不存在，跳过加载")
        
        # 测试法律术语分词
        legal_text = "根据劳动合同法第三十九条规定，用人单位可以解除劳动合同"
        tokens = list(jieba.cut(legal_text))
        print(f"✓ 法律文本分词: {tokens}")
        
        # 检查是否正确识别了法律术语
        legal_terms = ["劳动合同法", "用人单位", "劳动合同"]
        found_terms = [term for term in legal_terms if term in tokens]
        print(f"✓ 识别的法律术语: {found_terms}")
        
        return True
    except Exception as e:
        print(f"✗ 法律词典测试失败: {e}")
        return False

def test_nlp_service():
    """测试NLP服务类"""
    try:
        from services.nlp import LegalNLPProcessor
        
        nlp = LegalNLPProcessor()
        
        # 测试分词
        text = "我想咨询劳动合同违约金的问题"
        tokens = nlp.tokenize(text)
        print(f"✓ NLP服务分词测试: {tokens}")
        
        # 测试关键词提取
        keywords = nlp.extract_keywords(text, top_k=3)
        print(f"✓ 关键词提取测试: {keywords}")
        
        # 测试问题分类
        classification = nlp.classify_question(text)
        print(f"✓ 问题分类测试: {classification}")
        
        # 测试相似度计算
        text1 = "劳动合同纠纷"
        text2 = "劳动争议处理"
        similarity = nlp.similarity(text1, text2)
        print(f"✓ 相似度计算测试: {similarity}")
        
        return True
    except Exception as e:
        print(f"✗ NLP服务测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始NLP功能测试...")
    print("=" * 50)
    
    tests = [
        ("基本导入测试", test_basic_imports),
        ("jieba分词测试", test_jieba_tokenization),
        ("TF-IDF相似度测试", test_tfidf_similarity),
        ("法律词典测试", test_legal_dict_loading),
        ("NLP服务测试", test_nlp_service),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
