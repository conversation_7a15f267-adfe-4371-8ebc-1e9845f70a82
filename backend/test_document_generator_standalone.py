#!/usr/bin/env python3
"""
独立的智能文书生成器测试
"""

import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from collections import defaultdict


class SimpleDocumentTemplate:
    """简化的文档模板类"""
    
    def __init__(self, template_id: str, name: str, category: str, content: str):
        self.template_id = template_id
        self.name = name
        self.category = category
        self.content = content
        self.variables = []
        self.usage_count = 0
        
        self._extract_variables()
    
    def _extract_variables(self):
        pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(pattern, self.content)
        self.variables = list(set(matches))
    
    def render(self, variables: Dict[str, str]) -> str:
        rendered_content = self.content
        for var_name, var_value in variables.items():
            placeholder = f"{{{{{var_name}}}}}"
            rendered_content = rendered_content.replace(placeholder, str(var_value))
        return rendered_content
    
    def validate_variables(self, variables: Dict[str, str]) -> List[str]:
        missing_vars = []
        for var in self.variables:
            if var not in variables or not variables[var]:
                missing_vars.append(var)
        return missing_vars


class SimpleTemplateManager:
    """简化的模板管理器"""
    
    def __init__(self):
        self.templates = {}
        self._init_templates()
    
    def _init_templates(self):
        # 服务合同模板
        contract_template = SimpleDocumentTemplate(
            template_id="contract_001",
            name="服务合同",
            category="合同",
            content="""
服务合同

甲方：{{甲方名称}}
乙方：{{乙方名称}}

服务内容：{{服务内容}}
服务期限：{{服务期限}}
服务费用：{{服务费用}}元

签订日期：{{签订日期}}
            """
        )
        self.templates["contract_001"] = contract_template
    
    def get_template(self, template_id: str) -> Optional[SimpleDocumentTemplate]:
        return self.templates.get(template_id)


class SimpleGeneratedDocument:
    """简化的生成文档类"""
    
    def __init__(self, document_id: str, template_id: str, content: str):
        self.document_id = document_id
        self.template_id = template_id
        self.content = content
        self.generated_time = datetime.now().isoformat()
        self.variables_used = {}
        self.customizations = {}
        self.validation_results = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "document_id": self.document_id,
            "template_id": self.template_id,
            "content": self.content,
            "generated_time": self.generated_time,
            "variables_used": self.variables_used,
            "customizations": self.customizations,
            "validation_results": self.validation_results
        }


class SimpleDocumentGenerator:
    """简化的智能文书生成器"""
    
    def __init__(self, template_manager):
        self.template_manager = template_manager
        self.generated_documents = {}
        self.generation_rules = {
            "auto_fill_rules": {
                "当前日期": lambda: datetime.now().strftime("%Y年%m月%d日"),
                "当前年份": lambda: str(datetime.now().year)
            },
            "format_rules": {
                "金额大写": self._convert_amount_to_chinese,
                "日期格式化": self._format_date
            }
        }
    
    def generate_document(self, template_id: str, variables: Dict[str, str], 
                         customizations: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成法律文书"""
        try:
            # 1. 获取模板
            template = self.template_manager.get_template(template_id)
            if not template:
                return {"error": f"模板不存在: {template_id}"}
            
            # 2. 预处理变量
            processed_variables = self._preprocess_variables(variables, template)
            
            # 3. 验证变量
            validation_result = self._validate_variables(processed_variables, template)
            if validation_result["has_errors"]:
                return {
                    "error": "变量验证失败",
                    "validation_result": validation_result
                }
            
            # 4. 渲染文档
            content = template.render(processed_variables)
            
            # 5. 应用定制选项
            if customizations:
                content = self._apply_customizations(content, customizations)
            
            # 6. 后处理
            content = self._post_process_content(content)
            
            # 7. 创建生成的文档对象
            document_id = f"doc_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.generated_documents)}"
            generated_doc = SimpleGeneratedDocument(document_id, template_id, content)
            generated_doc.variables_used = processed_variables
            generated_doc.customizations = customizations or {}
            generated_doc.validation_results = validation_result
            
            # 8. 存储生成的文档
            self.generated_documents[document_id] = generated_doc
            
            # 9. 更新模板使用统计
            template.usage_count += 1
            
            return {
                "success": True,
                "document_id": document_id,
                "content": content,
                "template_name": template.name,
                "variables_used": processed_variables,
                "validation_result": validation_result,
                "generation_time": generated_doc.generated_time
            }
            
        except Exception as e:
            return {"error": f"生成文书失败: {str(e)}"}
    
    def _preprocess_variables(self, variables: Dict[str, str], template) -> Dict[str, str]:
        """预处理变量"""
        processed = variables.copy()
        
        # 自动填充规则
        auto_fill_rules = self.generation_rules["auto_fill_rules"]
        for var_name in template.variables:
            if var_name not in processed or not processed[var_name]:
                if var_name in auto_fill_rules:
                    processed[var_name] = auto_fill_rules[var_name]()
        
        return processed
    
    def _validate_variables(self, variables: Dict[str, str], template) -> Dict[str, Any]:
        """验证变量"""
        result = {
            "has_errors": False,
            "has_warnings": False,
            "errors": [],
            "warnings": [],
            "missing_variables": []
        }
        
        # 检查缺失变量
        missing_vars = template.validate_variables(variables)
        if missing_vars:
            result["has_errors"] = True
            result["missing_variables"] = missing_vars
            result["errors"].append(f"缺少必填变量: {', '.join(missing_vars)}")
        
        return result
    
    def _apply_customizations(self, content: str, customizations: Dict[str, Any]) -> str:
        """应用定制选项"""
        if customizations:
            customization_info = f"\n<!-- 定制选项: {customizations} -->\n"
            content = customization_info + content
        return content
    
    def _post_process_content(self, content: str) -> str:
        """后处理内容"""
        # 清理多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        return content.strip()
    
    def _convert_amount_to_chinese(self, amount: str) -> str:
        """将金额转换为中文大写（简化版）"""
        try:
            num = float(amount.replace(",", ""))
            if num == 0:
                return "零元整"
            return f"{amount}元（大写转换）"
        except ValueError:
            return amount
    
    def _format_date(self, date_str: str) -> str:
        """格式化日期"""
        if re.match(r'\d{4}-\d{1,2}-\d{1,2}', date_str):
            parts = date_str.split('-')
            return f"{parts[0]}年{int(parts[1])}月{int(parts[2])}日"
        return date_str
    
    def get_generated_document(self, document_id: str) -> Optional[SimpleGeneratedDocument]:
        """获取生成的文档"""
        return self.generated_documents.get(document_id)
    
    def list_generated_documents(self, limit: int = 10) -> List[Dict[str, Any]]:
        """列出生成的文档"""
        documents = list(self.generated_documents.values())
        documents.sort(key=lambda d: d.generated_time, reverse=True)
        
        return [
            {
                "document_id": doc.document_id,
                "template_id": doc.template_id,
                "generated_time": doc.generated_time,
                "content_preview": doc.content[:50] + "..." if len(doc.content) > 50 else doc.content
            }
            for doc in documents[:limit]
        ]
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        stats = {
            "total_generated": len(self.generated_documents),
            "template_usage": defaultdict(int)
        }
        
        for doc in self.generated_documents.values():
            stats["template_usage"][doc.template_id] += 1
        
        return dict(stats)


def test_document_generation():
    """测试文档生成"""
    template_manager = SimpleTemplateManager()
    generator = SimpleDocumentGenerator(template_manager)
    
    # 准备变量
    variables = {
        "甲方名称": "北京科技有限公司",
        "乙方名称": "上海服务有限公司",
        "服务内容": "软件开发服务",
        "服务期限": "6个月",
        "服务费用": "100000",
        "签订日期": "2023-12-01"
    }
    
    # 生成文档
    result = generator.generate_document("contract_001", variables)
    
    print("✓ 文档生成测试:")
    print(f"  生成成功: {result.get('success', False)}")
    print(f"  文档ID: {result.get('document_id', 'None')}")
    print(f"  模板名称: {result.get('template_name', 'None')}")
    print("  生成内容预览:")
    content = result.get('content', '')
    print(content[:200] + "..." if len(content) > 200 else content)
    
    assert result.get('success') == True
    assert "北京科技有限公司" in result.get('content', '')
    assert "软件开发服务" in result.get('content', '')
    
    return True


def test_variable_validation():
    """测试变量验证"""
    template_manager = SimpleTemplateManager()
    generator = SimpleDocumentGenerator(template_manager)
    
    # 不完整的变量
    incomplete_variables = {
        "甲方名称": "公司A",
        "乙方名称": "公司B"
        # 缺少其他必需变量
    }
    
    result = generator.generate_document("contract_001", incomplete_variables)
    
    print("✓ 变量验证测试:")
    print(f"  验证失败: {result.get('error') is not None}")
    if 'validation_result' in result:
        validation = result['validation_result']
        print(f"  缺失变量: {validation.get('missing_variables', [])}")
    
    assert result.get('error') is not None
    
    return True


def test_auto_fill_variables():
    """测试自动填充变量"""
    template_manager = SimpleTemplateManager()
    generator = SimpleDocumentGenerator(template_manager)
    
    # 包含自动填充变量的模板
    auto_template = SimpleDocumentTemplate(
        template_id="auto_001",
        name="自动填充测试",
        category="测试",
        content="当前日期：{{当前日期}}，当前年份：{{当前年份}}"
    )
    template_manager.templates["auto_001"] = auto_template
    
    # 不提供自动填充变量
    variables = {}
    
    result = generator.generate_document("auto_001", variables)
    
    print("✓ 自动填充变量测试:")
    print(f"  生成成功: {result.get('success', False)}")
    print(f"  生成内容: {result.get('content', '')}")
    
    assert result.get('success') == True
    content = result.get('content', '')
    assert "年" in content  # 应该包含当前日期
    
    return True


def test_customizations():
    """测试定制选项"""
    template_manager = SimpleTemplateManager()
    generator = SimpleDocumentGenerator(template_manager)
    
    variables = {
        "甲方名称": "公司A",
        "乙方名称": "公司B",
        "服务内容": "测试服务",
        "服务期限": "1个月",
        "服务费用": "10000",
        "签订日期": "2023-12-01"
    }
    
    customizations = {
        "字体": "宋体",
        "字号": "小四",
        "页面": "A4"
    }
    
    result = generator.generate_document("contract_001", variables, customizations)
    
    print("✓ 定制选项测试:")
    print(f"  生成成功: {result.get('success', False)}")
    content = result.get('content', '')
    print(f"  包含定制信息: {'定制选项' in content}")
    
    assert result.get('success') == True
    assert "定制选项" in content
    
    return True


def test_document_retrieval():
    """测试文档检索"""
    template_manager = SimpleTemplateManager()
    generator = SimpleDocumentGenerator(template_manager)
    
    # 生成一个文档
    variables = {
        "甲方名称": "测试公司",
        "乙方名称": "服务公司",
        "服务内容": "测试服务",
        "服务期限": "1个月",
        "服务费用": "5000",
        "签订日期": "2023-12-01"
    }
    
    result = generator.generate_document("contract_001", variables)
    document_id = result.get('document_id')
    
    # 检索文档
    retrieved_doc = generator.get_generated_document(document_id)
    
    print("✓ 文档检索测试:")
    print(f"  文档ID: {document_id}")
    print(f"  检索成功: {retrieved_doc is not None}")
    print(f"  内容匹配: {'测试公司' in retrieved_doc.content if retrieved_doc else False}")
    
    assert retrieved_doc is not None
    assert "测试公司" in retrieved_doc.content
    
    return True


def test_document_listing():
    """测试文档列表"""
    template_manager = SimpleTemplateManager()
    generator = SimpleDocumentGenerator(template_manager)
    
    # 生成多个文档
    for i in range(3):
        variables = {
            "甲方名称": f"公司{i}",
            "乙方名称": f"服务公司{i}",
            "服务内容": f"服务{i}",
            "服务期限": "1个月",
            "服务费用": "1000",
            "签订日期": "2023-12-01"
        }
        generator.generate_document("contract_001", variables)
    
    # 获取文档列表
    doc_list = generator.list_generated_documents()
    
    print("✓ 文档列表测试:")
    print(f"  文档数量: {len(doc_list)}")
    for doc in doc_list:
        print(f"    - {doc['document_id']}: {doc['content_preview']}")
    
    assert len(doc_list) >= 3
    
    return True


def test_generation_statistics():
    """测试生成统计"""
    template_manager = SimpleTemplateManager()
    generator = SimpleDocumentGenerator(template_manager)
    
    # 生成一些文档
    variables = {
        "甲方名称": "统计测试公司",
        "乙方名称": "服务公司",
        "服务内容": "统计测试",
        "服务期限": "1个月",
        "服务费用": "1000",
        "签订日期": "2023-12-01"
    }
    
    generator.generate_document("contract_001", variables)
    generator.generate_document("contract_001", variables)
    
    # 获取统计信息
    stats = generator.get_generation_statistics()
    
    print("✓ 生成统计测试:")
    print(f"  总生成数: {stats['total_generated']}")
    print(f"  模板使用统计: {dict(stats['template_usage'])}")
    
    assert stats['total_generated'] >= 2
    assert stats['template_usage']['contract_001'] >= 2
    
    return True


def main():
    """主测试函数"""
    print("开始独立智能文书生成器测试...")
    print("=" * 50)
    
    tests = [
        ("文档生成测试", test_document_generation),
        ("变量验证测试", test_variable_validation),
        ("自动填充变量测试", test_auto_fill_variables),
        ("定制选项测试", test_customizations),
        ("文档检索测试", test_document_retrieval),
        ("文档列表测试", test_document_listing),
        ("生成统计测试", test_generation_statistics),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:
        print("🎉 测试基本通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
